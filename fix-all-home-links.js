/**
 * Script pour corriger TOUS les liens de retour à l'accueil
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 === CORRECTION COMPLÈTE DES LIENS ACCUEIL ===');

// Lister tous les fichiers HTML dans applications-originales
const appsDir = './applications-originales';
const files = fs.readdirSync(appsDir).filter(file => file.endsWith('.html'));

console.log(`📁 Trouvé ${files.length} applications à vérifier`);

let corrected = 0;
let errors = 0;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Sauvegarder le contenu original
        const originalContent = content;
        
        // Corrections spécifiques et robustes
        const corrections = [
            // Pattern 1: href="/" avec ou sans attributs
            {
                pattern: /href=["']\/["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // Pattern 2: href="/" avec >
            {
                pattern: /href=["']\/["']>/g,
                replacement: 'href="../interface-originale-complete.html">'
            },
            // Pattern 3: window.location.href = "/"
            {
                pattern: /window\.location\.href\s*=\s*["']\/["']/g,
                replacement: 'window.location.href = "../interface-originale-complete.html"'
            },
            // Pattern 4: location.href = "/"
            {
                pattern: /location\.href\s*=\s*["']\/["']/g,
                replacement: 'location.href = "../interface-originale-complete.html"'
            },
            // Pattern 5: href="../" 
            {
                pattern: /href=["']\.\.\/["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // Pattern 6: href="index.html"
            {
                pattern: /href=["']index\.html["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // Pattern 7: href="../index.html"
            {
                pattern: /href=["']\.\.\/index\.html["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            }
        ];
        
        // Appliquer toutes les corrections
        corrections.forEach(correction => {
            const beforeCount = (content.match(correction.pattern) || []).length;
            content = content.replace(correction.pattern, correction.replacement);
            const afterCount = (content.match(correction.pattern) || []).length;
            
            if (beforeCount > 0) {
                console.log(`   ${file}: ${beforeCount} liens corrigés (pattern: ${correction.pattern.source})`);
            }
        });
        
        // Vérifier si des changements ont été faits
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ ${file} - Navigation corrigée`);
            corrected++;
        } else {
            console.log(`ℹ️ ${file} - Aucune correction nécessaire`);
        }
        
    } catch (error) {
        console.error(`❌ Erreur avec ${file}:`, error.message);
        errors++;
    }
});

console.log('\n📊 === RÉSULTATS FINAUX ===');
console.log(`✅ Applications corrigées: ${corrected}`);
console.log(`ℹ️ Applications sans changement: ${files.length - corrected - errors}`);
console.log(`❌ Erreurs: ${errors}`);

// Test final - vérifier qu'il n'y a plus de liens vers "/"
console.log('\n🔍 === VÉRIFICATION FINALE ===');
let remainingBadLinks = 0;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Chercher les liens problématiques restants
        const badPatterns = [
            /href=["']\/["']/g,
            /window\.location\.href\s*=\s*["']\/["']/g,
            /location\.href\s*=\s*["']\/["']/g
        ];
        
        badPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                console.log(`⚠️ ${file}: ${matches.length} liens problématiques restants`);
                remainingBadLinks += matches.length;
            }
        });
        
    } catch (error) {
        console.error(`❌ Erreur vérification ${file}:`, error.message);
    }
});

if (remainingBadLinks === 0) {
    console.log('🎉 PARFAIT ! Tous les liens de retour sont maintenant corrects !');
    console.log('🏠 Vous pouvez maintenant revenir à l\'accueil depuis toutes les applications !');
} else {
    console.log(`⚠️ Il reste ${remainingBadLinks} liens à corriger manuellement`);
}

console.log('\n🧠 === CORRECTION NAVIGATION TERMINÉE ===');
