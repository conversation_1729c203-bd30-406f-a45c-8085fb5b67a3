#!/bin/bash

# 🛑 ARRÊT LOUNA AI - LANCEUR DESKTOP
# Application macOS pour arrêt depuis le bureau
# Version: 2.0.0 - Juin 2025

# Configuration
LOUNA_DIR="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"

# Fonction d'affichage de notification
show_notification() {
    local title="$1"
    local message="$2"
    local sound="${3:-Glass}"
    
    osascript -e "display notification \"$message\" with title \"$title\" sound name \"$sound\""
}

# Fonction d'affichage de dialogue
show_dialog() {
    local title="$1"
    local message="$2"
    local buttons="${3:-OK}"
    
    osascript -e "display dialog \"$message\" with title \"$title\" buttons {\"$buttons\"} default button 1 with icon caution"
}

# Fonction de confirmation
confirm_stop() {
    local result=$(osascript -e 'display dialog "Êtes-vous sûr de vouloir arrêter LOUNA AI ?" with title "🛑 Confirmation Arrêt" buttons {"Annuler", "Arrêter"} default button 2 with icon caution')
    
    if [[ $result == *"Arrêter"* ]]; then
        return 0
    else
        return 1
    fi
}

# Fonction d'arrêt
stop_louna() {
    show_notification "🛑 LOUNA AI" "Arrêt en cours..." "Basso"
    
    # Changer vers le répertoire LOUNA AI
    cd "$LOUNA_DIR"
    
    # Lancer le script d'arrêt
    if ./stop-louna-ai.sh; then
        show_notification "✅ LOUNA AI" "Système arrêté avec succès" "Glass"
        show_dialog "✅ LOUNA AI Arrêté" "Le système LOUNA AI a été arrêté avec succès.\n\n🔌 Port 3000 libéré\n🛑 Tous les processus terminés\n💾 Données sauvegardées"
    else
        show_notification "❌ LOUNA AI" "Erreur d'arrêt" "Basso"
        show_dialog "❌ Erreur d'Arrêt" "Problème lors de l'arrêt.\n\nCertains processus peuvent encore être actifs."
    fi
}

# Fonction principale
main() {
    # Vérifier que LOUNA AI est installé
    if [ ! -d "$LOUNA_DIR" ]; then
        show_dialog "❌ Erreur" "LOUNA AI non trouvé.\n\nVérifiez que le disque Seagate est connecté."
        exit 1
    fi
    
    # Vérifier que le script d'arrêt existe
    if [ ! -f "$LOUNA_DIR/stop-louna-ai.sh" ]; then
        show_dialog "❌ Erreur" "Script d'arrêt non trouvé."
        exit 1
    fi
    
    # Demander confirmation
    if confirm_stop; then
        stop_louna
    else
        show_notification "ℹ️ LOUNA AI" "Arrêt annulé" "Ping"
    fi
}

# Gestion des erreurs
set -e
trap 'show_notification "❌ LOUNA AI" "Erreur inattendue lors de l\'arrêt" "Basso"' ERR

# Exécution
main "$@"
