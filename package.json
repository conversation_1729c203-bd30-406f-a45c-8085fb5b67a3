{"name": "louna-ai-electron", "version": "4.0.0-SECURISE-ULTIME", "description": "LOUNA AI sécurisé avec MCP avancé et protection VPN", "main": "main.js", "scripts": {"start": "electron main.js", "dev": "NODE_ENV=development electron main.js", "real-server": "node start-real-data-server.js", "validate": "node validation-complete-interfaces.js", "test": "npm run validate", "setup": "npm install && npm run validate"}, "dependencies": {"express": "^4.18.0", "socket.io": "^4.7.0", "cors": "^2.8.5"}, "author": "LOUNA AI Team", "license": "MIT", "devDependencies": {"electron": "^36.4.0"}}