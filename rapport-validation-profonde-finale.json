{"timestamp": "2025-06-11T00:39:39.277Z", "summary": {"totalFiles": 14, "realFiles": 11, "simulatedFiles": 3, "dataFiles": 2, "realDataFiles": 1, "realPercentage": 78.57142857142857, "status": "FAILED"}, "details": {"issues": [{"file": "real-thermal-memory-system.js", "description": "Système Mémoire Thermique", "issues": [{"pattern": "Math.random() - Génération aléatoire", "severity": "HIGH", "match": "Math.random()", "lineNumber": 361, "context": "const random = Math.random().toString(36).substr(2, 9);", "isInComment": false}], "realityRatio": 90.9090909090909, "codeQuality": 10}, {"file": "validation-code-reel-final.js", "description": "Script Validation", "issues": [{"pattern": "Math.random() - Génération aléatoire", "severity": "HIGH", "match": "Math.random()", "lineNumber": 19, "context": "{ pattern: /Math\\.random\\(\\)/g, description: 'Math.random() - Génération aléatoire' },", "isInComment": false}, {"pattern": "Math.floor(Math.random) - Sélection aléatoire", "severity": "HIGH", "match": "Math.floor(Math.random", "lineNumber": 20, "context": "{ pattern: /Math\\.floor\\(Math\\.random/g, description: 'Math.floor(Math.random) - Sélection aléatoire", "isInComment": false}, {"pattern": "setTimeout avec Math.random", "severity": "HIGH", "match": "setTimeout.*Math\\.random/g, description: 'setTimeout avec Math.random", "lineNumber": 21, "context": "{ pattern: /setTimeout.*Math\\.random/g, description: 'setTimeout avec Math.random' },", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "fake", "lineNumber": 22, "context": "{ pattern: /fake|mock|dummy|placeholder/gi, description: 'Mots-clés simulés' },", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "mock", "lineNumber": 22, "context": "{ pattern: /fake|mock|dummy|placeholder/gi, description: 'Mots-clés simulés' },", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "dummy", "lineNumber": 22, "context": "{ pattern: /fake|mock|dummy|placeholder/gi, description: 'Mots-clés simulés' },", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "<PERSON><PERSON>", "lineNumber": 25, "context": "{ pattern: /console\\.log.*Mock/gi, description: 'Logs de simulation' },", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 23, "context": "{ pattern: /simulation|simulé|fictif/gi, description: 'Références à simulation' },", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "fictif", "lineNumber": 23, "context": "{ pattern: /simulation|simulé|fictif/gi, description: 'Références à simulation' },", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 23, "context": "{ pattern: /simulation|simulé|fictif/gi, description: 'Références à simulation' },", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 23, "context": "{ pattern: /simulation|simulé|fictif/gi, description: 'Références à simulation' },", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "SIMULÉ", "lineNumber": 95, "context": "console.log(`❌ CODE SIMULÉ DÉTECTÉ:`);", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "SIMULÉ", "lineNumber": 95, "context": "console.log(`❌ CODE SIMULÉ DÉTECTÉ:`);", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "SIMULÉ", "lineNumber": 95, "context": "console.log(`❌ CODE SIMULÉ DÉTECTÉ:`);", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 23, "context": "{ pattern: /simulation|simulé|fictif/gi, description: 'Références à simulation' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 24, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "FIXME", "lineNumber": 24, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "NOT_IMPLEMENTED", "lineNumber": 24, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Erreurs non implémentées", "severity": "HIGH", "match": "throw new Error.*not implemented", "lineNumber": 27, "context": "{ pattern: /throw new Error.*not implemented/gi, description: 'Erreurs non implémentées' }", "isInComment": false}], "realityRatio": 17.857142857142858, "codeQuality": 9}, {"file": "validation-profonde-code-reel.js", "description": "Script Validation Profonde", "issues": [{"pattern": "Math.random() - Génération aléatoire", "severity": "HIGH", "match": "Math.random()", "lineNumber": 22, "context": "{ pattern: /Math\\.random\\(\\)/g, severity: 'HIGH', description: 'Math.random() - Génération aléatoire", "isInComment": false}, {"pattern": "Math.random() - Génération aléatoire", "severity": "HIGH", "match": "Math.random()", "lineNumber": 22, "context": "{ pattern: /Math\\.random\\(\\)/g, severity: 'HIGH', description: 'Math.random() - Génération aléatoire", "isInComment": false}, {"pattern": "Math.floor(Math.random) - Sélection aléatoire", "severity": "HIGH", "match": "Math.floor(Math.random", "lineNumber": 23, "context": "{ pattern: /Math\\.floor\\(Math\\.random/g, severity: 'HIGH', description: 'Math.floor(Math.random) - S", "isInComment": false}, {"pattern": "setTimeout avec Math.random", "severity": "HIGH", "match": "setTimeout.*Math\\.random/g, severity: 'HIGH', description: 'setTimeout avec Math.random", "lineNumber": 24, "context": "{ pattern: /setTimeout.*Math\\.random/g, severity: 'HIGH', description: 'setTimeout avec Math.random'", "isInComment": false}, {"pattern": "setInterval avec Math.random", "severity": "HIGH", "match": "setInterval.*Math\\.random/g, severity: 'HIGH', description: 'setInterval avec Math.random", "lineNumber": 25, "context": "{ pattern: /setInterval.*Math\\.random/g, severity: 'HIGH', description: 'setInterval avec Math.rando", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "fake", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "mock", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "dummy", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "fake", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "mock", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "dummy", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "<PERSON><PERSON>", "lineNumber": 29, "context": "{ pattern: /console\\.log.*Mock/gi, severity: 'HIGH', description: 'Logs de simulation' },", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "fake", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "mock", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "<PERSON>ts-c<PERSON>s simulé<PERSON> (fake/mock/dummy)", "severity": "HIGH", "match": "dummy", "lineNumber": 26, "context": "{ pattern: /fake|mock|dummy/gi, severity: 'HIGH', description: 'Mots-clés simulés (fake/mock/dummy)'", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 27, "context": "{ pattern: /simulation|simulé|fictif/gi, severity: 'MEDIUM', description: 'Références à simulation (", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "fictif", "lineNumber": 27, "context": "{ pattern: /simulation|simulé|fictif/gi, severity: 'MEDIUM', description: 'Références à simulation (", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 27, "context": "{ pattern: /simulation|simulé|fictif/gi, severity: 'MEDIUM', description: 'Références à simulation (", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 27, "context": "{ pattern: /simulation|simulé|fictif/gi, severity: 'MEDIUM', description: 'Références à simulation (", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "SIMULÉ", "lineNumber": 131, "context": "console.log(`❌ CODE SIMULÉ DÉTECTÉ (${simulatedIssues.length} problèmes):`);", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "SIMULÉ", "lineNumber": 131, "context": "console.log(`❌ CODE SIMULÉ DÉTECTÉ (${simulatedIssues.length} problèmes):`);", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 27, "context": "{ pattern: /simulation|simulé|fictif/gi, severity: 'MEDIUM', description: 'Références à simulation (", "isInComment": false}, {"pattern": "Références à simulation (vérifier contexte)", "severity": "MEDIUM", "match": "simulation", "lineNumber": 27, "context": "{ pattern: /simulation|simulé|fictif/gi, severity: 'MEDIUM', description: 'Références à simulation (", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "FIXME", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "NOT_IMPLEMENTED", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "TODO", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Code non implémenté", "severity": "MEDIUM", "match": "FIXME", "lineNumber": 28, "context": "{ pattern: /TODO|FIXME|NOT_IMPLEMENTED/gi, severity: 'MEDIUM', description: 'Code non implémenté' },", "isInComment": false}, {"pattern": "Logs de simulation", "severity": "HIGH", "match": "console.log(`   - <PERSON><PERSON><PERSON> les mots-clés fake/mock", "lineNumber": 441, "context": "console.log(`   - <PERSON><PERSON><PERSON> les mots-clés fake/mock/dummy`);", "isInComment": false}, {"pattern": "Erreurs non implémentées", "severity": "HIGH", "match": "throw new Error.*not implemented", "lineNumber": 31, "context": "{ pattern: /throw new Error.*not implemented/gi, severity: 'HIGH', description: 'Erreurs non impléme", "isInComment": false}, {"pattern": "Valeurs placeholder", "severity": "MEDIUM", "match": "placeholder.*value", "lineNumber": 34, "context": "{ pattern: /placeholder.*value/gi, severity: 'MEDIUM', description: 'Valeurs placeholder' },", "isInComment": false}, {"pattern": "Donn<PERSON> de test (vérifier)", "severity": "LOW", "match": "test.*data", "lineNumber": 35, "context": "{ pattern: /test.*data/gi, severity: 'LOW', description: 'Données de test (vérifier)' },", "isInComment": false}, {"pattern": "Valeurs d'exemple", "severity": "MEDIUM", "match": "example.*value", "lineNumber": 36, "context": "{ pattern: /example.*value/gi, severity: 'MEDIUM', description: 'Valeurs d\\'exemple' }", "isInComment": false}], "realityRatio": 14.285714285714285, "codeQuality": 9}], "validations": [{"file": "interface-originale-complete.html", "description": "Interface <PERSON>", "realityRatio": 97.36842105263158, "codeQuality": 9}, {"file": "main.js", "description": "Application Electron Principale", "realityRatio": 100, "codeQuality": 9}, {"file": "api-deepseek-real.js", "description": "API DeepSeek Réelle", "realityRatio": 100, "codeQuality": 10}, {"file": "real-thermal-memory-complete.js", "description": "Mémoire Thermique Complète", "realityRatio": 100, "codeQuality": 10}, {"file": "real-memory-connector.js", "description": "Connecteur <PERSON><PERSON><PERSON><PERSON>", "realityRatio": 100, "codeQuality": 9}, {"file": "real-neural-network-system.js", "description": "R<PERSON>eau Neuronal Réel", "realityRatio": 100, "codeQuality": 10}, {"file": "modules/real-mobius-thought-system.js", "description": "<PERSON><PERSON>t<PERSON> Mö<PERSON>", "realityRatio": 100, "codeQuality": 9}, {"file": "modules/real-cpu-temperature-sensor.js", "description": "Capteur <PERSON><PERSON>", "realityRatio": 100, "codeQuality": 10}, {"file": "modules/deepseek-direct-connector.js", "description": "Connecteur DeepSeek Direct", "realityRatio": 100, "codeQuality": 10}, {"file": "neural-kyber-api-server.js", "description": "Serveur API Neural-KYBER", "realityRatio": 100, "codeQuality": 9}, {"file": "real-data-backend-unified.js", "description": "Backend Données Unifiées", "realityRatio": 100, "codeQuality": 10}]}}