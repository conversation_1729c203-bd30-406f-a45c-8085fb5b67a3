/**
 * Test du connecteur mémoire thermique réelle
 */

const RealMemoryConnector = require('./real-memory-connector');

console.log('🧪 === TEST CONNECTEUR MÉMOIRE THERMIQUE RÉELLE ===');

async function testRealMemory() {
    try {
        console.log('🔗 Création connecteur...');
        const connector = new RealMemoryConnector();
        
        console.log('🚀 Démarrage connecteur...');
        connector.start();
        
        // Attendre 3 secondes pour laisser le système se stabiliser
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log('📊 Récupération état actuel...');
        const state = connector.getCurrentState();
        
        console.log('\n🎯 === RÉSULTATS ===');
        console.log(`✅ Neurones total: ${state.neural.totalNeurons.toLocaleString()}`);
        console.log(`✅ Synapses: ${state.metrics.synapses.toLocaleString()}`);
        console.log(`✅ QI Jean-Luc: ${state.metrics.qiLevel}`);
        console.log(`✅ Température: ${state.thermal.temperature.toFixed(1)}°C`);
        console.log(`✅ Zone actuelle: ${state.thermal.zoneActuelle}`);
        console.log(`✅ Position curseur: ${state.thermal.curseurPosition.toFixed(1)}°C`);
        console.log(`✅ Tiroirs: ${state.metrics.tiroirs}`);
        console.log(`✅ Souvenirs: ${state.metrics.souvenirs}`);
        
        console.log('\n🔥 === ZONES THERMIQUES ===');
        Object.entries(state.thermal.zones).forEach(([zone, data]) => {
            console.log(`${zone}: ${data.temp}°C - ${data.nom} (${data.neurones} neurones)`);
        });
        
        console.log('\n🧬 === TEST NEUROGENÈSE ===');
        const initialNeurons = state.neural.totalNeurons;
        
        // Forcer neurogenèse
        connector.performRealNeurogenesis();
        
        const newState = connector.getCurrentState();
        const newNeurons = newState.neural.totalNeurons;
        
        if (newNeurons > initialNeurons) {
            console.log(`✅ Neurogenèse réussie: +${newNeurons - initialNeurons} neurones`);
        } else {
            console.log('⚠️ Neurogenèse: aucun nouveau neurone (normal)');
        }
        
        console.log('\n🔴 Arrêt connecteur...');
        connector.stop();
        
        console.log('\n🎉 === TEST TERMINÉ ===');
        console.log('✅ Connecteur mémoire thermique réelle fonctionnel');
        console.log(`📊 ${newNeurons.toLocaleString()} neurones détectés`);
        console.log(`🧠 QI ${state.metrics.qiLevel} confirmé`);
        
    } catch (error) {
        console.error('❌ Erreur test:', error.message);
        console.error(error.stack);
    }
}

testRealMemory();
