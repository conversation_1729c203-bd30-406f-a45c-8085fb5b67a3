#!/bin/bash

# 🧪 TEST LANCEUR DESKTOP LOUNA AI
# Vérifie que les applications desktop fonctionnent
# Version: 2.0.0 - Juin 2025

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Test des applications desktop
test_desktop_apps() {
    print_message $BLUE "🧪 === TEST APPLICATIONS DESKTOP ==="
    
    # Vérifier l'application de lancement
    if [ -d ~/Desktop/"LOUNA AI.app" ]; then
        print_message $GREEN "✅ Application 'LOUNA AI' trouvée sur le bureau"
        
        # Vérifier l'exécutable
        if [ -x ~/Desktop/"LOUNA AI.app/Contents/MacOS/LOUNA AI" ]; then
            print_message $GREEN "✅ Exécutable de lancement fonctionnel"
        else
            print_message $RED "❌ Exécutable de lancement non fonctionnel"
        fi
        
        # Vérifier Info.plist
        if [ -f ~/Desktop/"LOUNA AI.app/Contents/Info.plist" ]; then
            print_message $GREEN "✅ Info.plist présent"
        else
            print_message $RED "❌ Info.plist manquant"
        fi
    else
        print_message $RED "❌ Application 'LOUNA AI' non trouvée sur le bureau"
    fi
    
    # Vérifier l'application d'arrêt
    if [ -d ~/Desktop/"Arrêter LOUNA AI.app" ]; then
        print_message $GREEN "✅ Application 'Arrêter LOUNA AI' trouvée sur le bureau"
        
        # Vérifier l'exécutable
        if [ -x ~/Desktop/"Arrêter LOUNA AI.app/Contents/MacOS/Arrêter LOUNA AI" ]; then
            print_message $GREEN "✅ Exécutable d'arrêt fonctionnel"
        else
            print_message $RED "❌ Exécutable d'arrêt non fonctionnel"
        fi
    else
        print_message $RED "❌ Application 'Arrêter LOUNA AI' non trouvée sur le bureau"
    fi
}

# Test des scripts de base
test_base_scripts() {
    print_message $BLUE "🧪 === TEST SCRIPTS DE BASE ==="
    
    LOUNA_DIR="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"
    
    # Vérifier le script de lancement
    if [ -f "$LOUNA_DIR/launch-louna-ai.sh" ]; then
        if [ -x "$LOUNA_DIR/launch-louna-ai.sh" ]; then
            print_message $GREEN "✅ Script de lancement exécutable"
        else
            print_message $RED "❌ Script de lancement non exécutable"
        fi
    else
        print_message $RED "❌ Script de lancement manquant"
    fi
    
    # Vérifier le script d'arrêt
    if [ -f "$LOUNA_DIR/stop-louna-ai.sh" ]; then
        if [ -x "$LOUNA_DIR/stop-louna-ai.sh" ]; then
            print_message $GREEN "✅ Script d'arrêt exécutable"
        else
            print_message $RED "❌ Script d'arrêt non exécutable"
        fi
    else
        print_message $RED "❌ Script d'arrêt manquant"
    fi
    
    # Vérifier le serveur
    if [ -f "$LOUNA_DIR/simple-real-server.js" ]; then
        print_message $GREEN "✅ Serveur LOUNA AI présent"
    else
        print_message $RED "❌ Serveur LOUNA AI manquant"
    fi
}

# Test des prérequis système
test_system_requirements() {
    print_message $BLUE "🧪 === TEST PRÉREQUIS SYSTÈME ==="
    
    # Vérifier Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        print_message $GREEN "✅ Node.js installé: $node_version"
    else
        print_message $RED "❌ Node.js non installé"
    fi
    
    # Vérifier npm
    if command -v npm &> /dev/null; then
        local npm_version=$(npm --version)
        print_message $GREEN "✅ npm installé: $npm_version"
    else
        print_message $RED "❌ npm non installé"
    fi
    
    # Vérifier osascript (pour les notifications)
    if command -v osascript &> /dev/null; then
        print_message $GREEN "✅ osascript disponible (notifications macOS)"
    else
        print_message $RED "❌ osascript non disponible"
    fi
    
    # Vérifier curl
    if command -v curl &> /dev/null; then
        print_message $GREEN "✅ curl disponible"
    else
        print_message $RED "❌ curl non disponible"
    fi
}

# Test de connectivité
test_connectivity() {
    print_message $BLUE "🧪 === TEST CONNECTIVITÉ ==="
    
    # Vérifier si le port 3000 est libre
    if lsof -ti:3000 > /dev/null 2>&1; then
        print_message $YELLOW "⚠️ Port 3000 occupé (LOUNA AI peut-être déjà en cours)"
    else
        print_message $GREEN "✅ Port 3000 libre"
    fi
    
    # Vérifier l'accès au répertoire LOUNA AI
    LOUNA_DIR="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"
    if [ -d "$LOUNA_DIR" ]; then
        print_message $GREEN "✅ Répertoire LOUNA AI accessible"
        
        # Vérifier les permissions
        if [ -w "$LOUNA_DIR" ]; then
            print_message $GREEN "✅ Permissions d'écriture OK"
        else
            print_message $YELLOW "⚠️ Permissions d'écriture limitées"
        fi
    else
        print_message $RED "❌ Répertoire LOUNA AI non accessible"
        print_message $YELLOW "   Vérifiez que le disque Seagate est connecté"
    fi
}

# Instructions d'utilisation
show_usage_instructions() {
    print_message $BLUE "📋 === INSTRUCTIONS D'UTILISATION ==="
    print_message $GREEN "
🔥 APPLICATIONS DESKTOP CRÉÉES:

📱 Sur votre bureau, vous avez maintenant:
   🚀 'LOUNA AI.app' - Pour démarrer le système
   🛑 'Arrêter LOUNA AI.app' - Pour arrêter le système

🎯 UTILISATION:
   1. Double-cliquez sur 'LOUNA AI.app' pour démarrer
   2. Le système s'ouvrira automatiquement dans votre navigateur
   3. Double-cliquez sur 'Arrêter LOUNA AI.app' pour arrêter

✨ FONCTIONNALITÉS:
   📢 Notifications macOS intégrées
   🔍 Vérification automatique des prérequis
   🌐 Ouverture automatique du navigateur
   🛡️ Gestion d'erreurs complète
   📋 Logs détaillés

🔗 INTERFACES DISPONIBLES:
   🏠 Interface Principale
   📊 Dashboard Principal
   🎛️ Contrôle Système
   🔥 Mémoire Thermique
"
}

# Fonction principale
main() {
    print_message $BLUE "🧪 === TEST COMPLET LANCEUR DESKTOP LOUNA AI ==="
    print_message $BLUE "📅 $(date)"
    
    test_system_requirements
    echo
    test_base_scripts
    echo
    test_desktop_apps
    echo
    test_connectivity
    echo
    show_usage_instructions
    
    print_message $GREEN "🎉 Test terminé ! Vos applications sont prêtes sur le bureau."
}

main "$@"
