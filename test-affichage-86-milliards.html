<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Affichage 86 Milliards</title>
    <style>
        body {
            background: #000;
            color: #00ff88;
            font-family: 'Courier New', monospace;
            padding: 20px;
        }
        .test-container {
            border: 2px solid #00ff88;
            padding: 20px;
            margin: 10px 0;
            border-radius: 10px;
        }
        .success { color: #00ff88; }
        .error { color: #ff0000; }
        .warning { color: #ffaa00; }
    </style>
</head>
<body>
    <h1>🔍 TEST AFFICHAGE 86 MILLIARDS DE NEURONES</h1>
    
    <div class="test-container">
        <h2>📊 Valeurs Testées</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>🎯 Éléments DOM</h2>
        <div>Neurones Actifs: <span id="neurons-active">86,000,007,133</span></div>
        <div>Neurones Count: <span id="neuroneCount">86,000,007,133</span></div>
        <div>QI: <span id="qi-value">199</span></div>
    </div>

    <script>
        // Données système identiques à l'interface
        let systemMetrics = {
            neurones: 86000007133,
            synapses: 602000000000000,
            qi: 199,
            efficacite: 95
        };

        function testerAffichage() {
            console.log('🔍 === TEST AFFICHAGE 86 MILLIARDS ===');
            
            const resultatsDiv = document.getElementById('test-results');
            let resultats = [];

            // Test 1: Vérifier systemMetrics
            const test1 = systemMetrics.neurones === 86000007133;
            resultats.push(`Test 1 - SystemMetrics: ${test1 ? '✅ PASS' : '❌ FAIL'} (${systemMetrics.neurones.toLocaleString()})`);

            // Test 2: Vérifier formatage
            const formatted = systemMetrics.neurones.toLocaleString();
            const test2 = formatted === '86,000,007,133';
            resultats.push(`Test 2 - Formatage: ${test2 ? '✅ PASS' : '❌ FAIL'} (${formatted})`);

            // Test 3: Vérifier éléments DOM
            const neuronsActiveElement = document.getElementById('neurons-active');
            const test3 = neuronsActiveElement && neuronsActiveElement.textContent === '86,000,007,133';
            resultats.push(`Test 3 - DOM neurons-active: ${test3 ? '✅ PASS' : '❌ FAIL'} (${neuronsActiveElement ? neuronsActiveElement.textContent : 'NULL'})`);

            const neuroneCountElement = document.getElementById('neuroneCount');
            const test4 = neuroneCountElement && neuroneCountElement.textContent === '86,000,007,133';
            resultats.push(`Test 4 - DOM neuroneCount: ${test4 ? '✅ PASS' : '❌ FAIL'} (${neuroneCountElement ? neuroneCountElement.textContent : 'NULL'})`);

            const qiElement = document.getElementById('qi-value');
            const test5 = qiElement && qiElement.textContent === '199';
            resultats.push(`Test 5 - DOM QI: ${test5 ? '✅ PASS' : '❌ FAIL'} (${qiElement ? qiElement.textContent : 'NULL'})`);

            // Test 6: Mise à jour dynamique
            neuronsActiveElement.textContent = systemMetrics.neurones.toLocaleString();
            neuroneCountElement.textContent = systemMetrics.neurones.toLocaleString();
            qiElement.textContent = systemMetrics.qi;

            const test6 = neuronsActiveElement.textContent === '86,000,007,133';
            resultats.push(`Test 6 - Mise à jour dynamique: ${test6 ? '✅ PASS' : '❌ FAIL'}`);

            // Afficher résultats
            resultatsDiv.innerHTML = resultats.map(r => `<div class="${r.includes('✅') ? 'success' : 'error'}">${r}</div>`).join('');

            // Logs console
            console.log('📊 === RÉSULTATS TESTS ===');
            resultats.forEach(r => console.log(r));

            const allPassed = resultats.every(r => r.includes('✅'));
            console.log(`🎯 Résultat global: ${allPassed ? '✅ TOUS LES TESTS PASSÉS' : '❌ CERTAINS TESTS ÉCHOUÉS'}`);

            if (allPassed) {
                console.log('🎉 L\'AFFICHAGE DES 86 MILLIARDS FONCTIONNE !');
                document.body.style.borderColor = '#00ff88';
            } else {
                console.log('⚠️ PROBLÈMES DÉTECTÉS DANS L\'AFFICHAGE');
                document.body.style.borderColor = '#ff0000';
            }

            return allPassed;
        }

        // Exécuter test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Démarrage test affichage...');
            setTimeout(testerAffichage, 500);
        });

        // Test manuel
        function testManuel() {
            console.log('🔧 === TEST MANUEL ===');
            
            // Forcer mise à jour
            document.getElementById('neurons-active').textContent = '86,000,007,133';
            document.getElementById('neuroneCount').textContent = '86,000,007,133';
            document.getElementById('qi-value').textContent = '199';
            
            console.log('✅ Mise à jour forcée effectuée');
            console.log('🧠 Neurones: 86,000,007,133');
            console.log('🧠 QI: 199');
        }

        // Exposer fonction pour test manuel
        window.testManuel = testManuel;
    </script>

    <div class="test-container">
        <h2>🔧 Test Manuel</h2>
        <button onclick="testManuel()" style="background: #00ff88; color: #000; padding: 10px; border: none; border-radius: 5px; cursor: pointer;">
            Forcer Mise à Jour
        </button>
    </div>

    <div class="test-container">
        <h2>📋 Instructions</h2>
        <div>1. Ouvrez la console (F12)</div>
        <div>2. Vérifiez que tous les tests passent (✅)</div>
        <div>3. Si problème, cliquez "Forcer Mise à Jour"</div>
        <div>4. Les valeurs doivent afficher 86,000,007,133 neurones</div>
    </div>
</body>
</html>
