/**
 * VÉRIFICATION COMPLÈTE DE TOUS LES CHIFFRES DU CERVEAU
 * Analyse exhaustive pour identifier les incohérences
 */

const fs = require('fs');
const path = require('path');

class VerificationCompleteCerveau {
    constructor() {
        this.donnees = {
            compteurs_json: null,
            fichiers_neurones: 0,
            zones_cerebrales: {},
            zones_thermiques: {},
            curseur_thermique: null,
            interface_html: {},
            incoherences: []
        };
    }

    // Analyser compteurs.json
    analyserCompteursJson() {
        console.log('📊 === ANALYSE COMPTEURS.JSON ===');
        
        const compteursPath = 'MEMOIRE-REELLE/compteurs.json';
        if (!fs.existsSync(compteursPath)) {
            console.log('❌ Fichier compteurs.json NON TROUVÉ !');
            this.donnees.incoherences.push('Fichier compteurs.json manquant');
            return;
        }

        try {
            const compteurs = JSON.parse(fs.readFileSync(compteursPath, 'utf8'));
            this.donnees.compteurs_json = compteurs;
            
            console.log('✅ Fichier compteurs.json lu avec succès');
            console.log(`🧠 Neurones total: ${compteurs.neurones_total?.toLocaleString() || 'UNDEFINED'}`);
            console.log(`🔗 Synapses total: ${compteurs.synapses_total?.toLocaleString() || 'UNDEFINED'}`);
            console.log(`📅 Dernière MAJ: ${new Date(compteurs.derniere_mise_a_jour).toLocaleString()}`);
            
            // Vérifier structure
            if (!compteurs.neurones_total) {
                this.donnees.incoherences.push('neurones_total manquant dans compteurs.json');
            }
            if (!compteurs.synapses_total) {
                this.donnees.incoherences.push('synapses_total manquant dans compteurs.json');
            }
            if (!compteurs.neurones_par_zone) {
                this.donnees.incoherences.push('neurones_par_zone manquant dans compteurs.json');
            }
            
            // Analyser zones cérébrales
            if (compteurs.neurones_par_zone) {
                console.log('\n🧠 === ZONES CÉRÉBRALES COMPTEURS.JSON ===');
                let somme_zones = 0;
                Object.entries(compteurs.neurones_par_zone).forEach(([zone, neurones]) => {
                    console.log(`   ${zone}: ${neurones.toLocaleString()} neurones`);
                    somme_zones += neurones;
                });
                
                console.log(`📊 Somme zones: ${somme_zones.toLocaleString()}`);
                console.log(`📊 Total déclaré: ${compteurs.neurones_total.toLocaleString()}`);
                
                const difference = compteurs.neurones_total - somme_zones;
                console.log(`📊 Différence: ${difference.toLocaleString()}`);
                
                if (Math.abs(difference) > 1000000) {
                    this.donnees.incoherences.push(`Différence importante entre total (${compteurs.neurones_total.toLocaleString()}) et somme zones (${somme_zones.toLocaleString()})`);
                }
            }
            
        } catch (error) {
            console.log(`❌ Erreur lecture compteurs.json: ${error.message}`);
            this.donnees.incoherences.push(`Erreur lecture compteurs.json: ${error.message}`);
        }
    }

    // Compter fichiers neurones réels
    compterFichiersNeurones() {
        console.log('\n📁 === COMPTAGE FICHIERS NEURONES ===');
        
        const zones = [
            'MEMOIRE-REELLE/neurones',
            'MEMOIRE-REELLE/zones-thermiques'
        ];
        
        let total_fichiers = 0;
        
        zones.forEach(zonePath => {
            if (fs.existsSync(zonePath)) {
                const sousZones = fs.readdirSync(zonePath);
                
                sousZones.forEach(sousZone => {
                    const sousZonePath = path.join(zonePath, sousZone);
                    if (fs.statSync(sousZonePath).isDirectory()) {
                        const fichiers = fs.readdirSync(sousZonePath).filter(f => f.endsWith('.json'));
                        total_fichiers += fichiers.length;
                        
                        console.log(`   ${sousZone}: ${fichiers.length} fichiers`);
                        
                        if (zonePath.includes('neurones')) {
                            this.donnees.zones_cerebrales[sousZone] = fichiers.length;
                        } else {
                            this.donnees.zones_thermiques[sousZone] = fichiers.length;
                        }
                    }
                });
            } else {
                console.log(`⚠️ Dossier ${zonePath} non trouvé`);
            }
        });
        
        this.donnees.fichiers_neurones = total_fichiers;
        console.log(`📊 Total fichiers neurones: ${total_fichiers.toLocaleString()}`);
        
        return total_fichiers;
    }

    // Analyser curseur thermique
    analyserCurseurThermique() {
        console.log('\n🌡️ === ANALYSE CURSEUR THERMIQUE ===');
        
        const curseurPath = 'MEMOIRE-REELLE/curseur-thermique/position_curseur.json';
        if (!fs.existsSync(curseurPath)) {
            console.log('❌ Fichier curseur thermique NON TROUVÉ !');
            this.donnees.incoherences.push('Fichier curseur thermique manquant');
            return;
        }

        try {
            const curseur = JSON.parse(fs.readFileSync(curseurPath, 'utf8'));
            this.donnees.curseur_thermique = curseur;
            
            console.log('✅ Curseur thermique lu avec succès');
            console.log(`🌡️ Position: ${curseur.curseur?.position_actuelle}°C`);
            console.log(`📍 Zone: ${curseur.curseur?.zone_actuelle}`);
            console.log(`💻 CPU: ${curseur.curseur?.temperature_cpu_actuelle}°C`);
            
        } catch (error) {
            console.log(`❌ Erreur lecture curseur: ${error.message}`);
            this.donnees.incoherences.push(`Erreur lecture curseur: ${error.message}`);
        }
    }

    // Analyser interface HTML
    analyserInterfaceHtml() {
        console.log('\n🖥️ === ANALYSE INTERFACE HTML ===');
        
        const interfacePath = 'interface-originale-complete.html';
        if (!fs.existsSync(interfacePath)) {
            console.log('❌ Interface HTML NON TROUVÉE !');
            this.donnees.incoherences.push('Interface HTML manquante');
            return;
        }

        try {
            const contenu = fs.readFileSync(interfacePath, 'utf8');
            
            // Extraire systemMetrics
            const systemMetricsMatch = contenu.match(/let systemMetrics = \{[\s\S]*?\};/);
            if (systemMetricsMatch) {
                const systemMetricsStr = systemMetricsMatch[0];
                
                // Extraire valeurs spécifiques
                const neurones = systemMetricsStr.match(/neurones:\s*(\d+)/);
                const synapses = systemMetricsStr.match(/synapses:\s*(\d+)/);
                const qi = systemMetricsStr.match(/qi:\s*(\d+)/);
                const curseur = systemMetricsStr.match(/curseurThermique:\s*([\d.]+)/);
                
                this.donnees.interface_html = {
                    neurones: neurones ? parseInt(neurones[1]) : null,
                    synapses: synapses ? parseInt(synapses[1]) : null,
                    qi: qi ? parseInt(qi[1]) : null,
                    curseur: curseur ? parseFloat(curseur[1]) : null
                };
                
                console.log('✅ SystemMetrics extrait de l\'interface');
                console.log(`🧠 Neurones interface: ${this.donnees.interface_html.neurones?.toLocaleString() || 'NON TROUVÉ'}`);
                console.log(`🔗 Synapses interface: ${this.donnees.interface_html.synapses?.toLocaleString() || 'NON TROUVÉ'}`);
                console.log(`🧠 QI interface: ${this.donnees.interface_html.qi || 'NON TROUVÉ'}`);
                console.log(`🌡️ Curseur interface: ${this.donnees.interface_html.curseur || 'NON TROUVÉ'}°C`);
            } else {
                console.log('❌ SystemMetrics NON TROUVÉ dans l\'interface !');
                this.donnees.incoherences.push('SystemMetrics non trouvé dans interface HTML');
            }
            
            // Chercher valeurs statiques dans HTML
            const valeursStatiques = [
                { regex: /<div[^>]*id="neurons-active"[^>]*>([^<]+)<\/div>/, nom: 'neurons-active' },
                { regex: /<div[^>]*id="neuroneCount"[^>]*>([^<]+)<\/div>/, nom: 'neuroneCount' },
                { regex: /<div[^>]*id="qi-value"[^>]*>([^<]+)<\/div>/, nom: 'qi-value' }
            ];
            
            console.log('\n🔍 === VALEURS STATIQUES HTML ===');
            valeursStatiques.forEach(({ regex, nom }) => {
                const match = contenu.match(regex);
                if (match) {
                    console.log(`   ${nom}: ${match[1]}`);
                } else {
                    console.log(`   ${nom}: NON TROUVÉ`);
                }
            });
            
        } catch (error) {
            console.log(`❌ Erreur lecture interface: ${error.message}`);
            this.donnees.incoherences.push(`Erreur lecture interface: ${error.message}`);
        }
    }

    // Comparer toutes les sources
    comparerSources() {
        console.log('\n🔍 === COMPARAISON TOUTES SOURCES ===');
        
        const sources = {
            'Compteurs.json': this.donnees.compteurs_json?.neurones_total,
            'Fichiers neurones': this.donnees.fichiers_neurones,
            'Interface HTML': this.donnees.interface_html.neurones,
            'Zones cérébrales': Object.values(this.donnees.zones_cerebrales).reduce((a, b) => a + b, 0),
            'Zones thermiques': Object.values(this.donnees.zones_thermiques).reduce((a, b) => a + b, 0)
        };
        
        console.log('📊 === COMPARAISON NEURONES ===');
        Object.entries(sources).forEach(([source, valeur]) => {
            console.log(`   ${source}: ${valeur?.toLocaleString() || 'NON DISPONIBLE'}`);
        });
        
        // Identifier incohérences
        const valeurs = Object.values(sources).filter(v => v !== null && v !== undefined);
        const max = Math.max(...valeurs);
        const min = Math.min(...valeurs);
        
        if (max - min > 1000000) {
            this.donnees.incoherences.push(`Écart important entre sources: ${min.toLocaleString()} à ${max.toLocaleString()}`);
        }
        
        // Vérifier ratio synapses
        if (this.donnees.compteurs_json?.synapses_total && this.donnees.compteurs_json?.neurones_total) {
            const ratio = this.donnees.compteurs_json.synapses_total / this.donnees.compteurs_json.neurones_total;
            console.log(`\n🔗 Ratio synapses/neurones: ${ratio.toFixed(0)}:1`);
            
            if (ratio < 1000 || ratio > 15000) {
                this.donnees.incoherences.push(`Ratio synapses/neurones anormal: ${ratio.toFixed(0)}:1`);
            }
        }
    }

    // Calculer métriques dérivées
    calculerMetriquesDerivees() {
        console.log('\n🧮 === MÉTRIQUES DÉRIVÉES ===');
        
        const neurones = this.donnees.compteurs_json?.neurones_total || this.donnees.interface_html.neurones;
        const synapses = this.donnees.compteurs_json?.synapses_total || this.donnees.interface_html.synapses;
        
        if (neurones) {
            // QI calculé
            const qi_base = 100 + Math.log10(neurones / 1000000) * 20;
            const qi_avec_bonus = qi_base + 25; // Bonus accélérateurs
            console.log(`🧠 QI calculé: ${qi_avec_bonus.toFixed(0)}`);
            
            // Capacité de traitement
            const teraops = (neurones * 1000 * 5) / 1e12; // Avec accélérateurs ×5
            console.log(`⚡ Capacité: ${teraops.toFixed(2)} TeraOps/sec`);
            
            // Mémoire théorique
            if (synapses) {
                const memoire_gb = (synapses * 4) / (8 * 1024 * 1024 * 1024);
                console.log(`💾 Mémoire: ${memoire_gb.toFixed(2)} GB`);
            }
            
            // Neurogenèse
            const neurogenese_jour = 3500; // Avec accélérateurs
            const neurogenese_sec = neurogenese_jour / 86400;
            console.log(`🧬 Neurogenèse: ${neurogenese_jour}/jour (${neurogenese_sec.toFixed(4)}/sec)`);
        }
    }

    // Rapport final
    genererRapportFinal() {
        console.log('\n📋 === RAPPORT FINAL VÉRIFICATION ===');
        
        // Résumé données
        console.log('\n📊 === RÉSUMÉ DONNÉES ===');
        console.log(`🧠 Neurones (compteurs.json): ${this.donnees.compteurs_json?.neurones_total?.toLocaleString() || 'N/A'}`);
        console.log(`🔗 Synapses (compteurs.json): ${this.donnees.compteurs_json?.synapses_total?.toLocaleString() || 'N/A'}`);
        console.log(`📁 Fichiers neurones: ${this.donnees.fichiers_neurones.toLocaleString()}`);
        console.log(`🖥️ Neurones (interface): ${this.donnees.interface_html.neurones?.toLocaleString() || 'N/A'}`);
        console.log(`🌡️ Curseur: ${this.donnees.curseur_thermique?.curseur?.position_actuelle || 'N/A'}°C`);
        
        // Incohérences détectées
        console.log('\n⚠️ === INCOHÉRENCES DÉTECTÉES ===');
        if (this.donnees.incoherences.length === 0) {
            console.log('✅ Aucune incohérence majeure détectée !');
        } else {
            this.donnees.incoherences.forEach((incoherence, index) => {
                console.log(`${index + 1}. ❌ ${incoherence}`);
            });
        }
        
        // Recommandations
        console.log('\n💡 === RECOMMANDATIONS ===');
        
        if (this.donnees.compteurs_json?.neurones_total !== this.donnees.interface_html.neurones) {
            console.log('🔧 Synchroniser interface HTML avec compteurs.json');
        }
        
        const fichiers_vs_compteurs = Math.abs(this.donnees.fichiers_neurones - (this.donnees.compteurs_json?.neurones_total || 0));
        if (fichiers_vs_compteurs > 1000000) {
            console.log('🔧 Vérifier cohérence entre fichiers et compteurs');
        }
        
        if (this.donnees.incoherences.length > 0) {
            console.log('🔧 Corriger les incohérences détectées');
        } else {
            console.log('✅ Système cohérent - Aucune correction nécessaire');
        }
        
        return this.donnees;
    }

    // Vérification complète
    verificationComplete() {
        console.log('🔍 === VÉRIFICATION COMPLÈTE CHIFFRES CERVEAU ===\n');
        
        this.analyserCompteursJson();
        this.compterFichiersNeurones();
        this.analyserCurseurThermique();
        this.analyserInterfaceHtml();
        this.comparerSources();
        this.calculerMetriquesDerivees();
        
        return this.genererRapportFinal();
    }
}

// Exécution
const verificateur = new VerificationCompleteCerveau();
const resultats = verificateur.verificationComplete();

console.log('\n🎯 === VÉRIFICATION TERMINÉE ===');
console.log('Tous les chiffres ont été analysés et comparés !');

module.exports = { VerificationCompleteCerveau, resultats };
