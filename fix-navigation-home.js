/**
 * Script pour corriger la navigation de retour à l'accueil dans toutes les applications
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 === CORRECTION NAVIGATION RETOUR ACCUEIL ===');

// Lister tous les fichiers HTML dans applications-originales
const appsDir = './applications-originales';
const files = fs.readdirSync(appsDir).filter(file => file.endsWith('.html'));

console.log(`📁 Trouvé ${files.length} applications à corriger`);

let corrected = 0;
let errors = 0;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Sauvegarder le contenu original
        const originalContent = content;
        
        // Corrections multiples pour différents patterns
        const corrections = [
            // Corriger href="/" vers l'interface principale
            {
                pattern: /href="\/">/g,
                replacement: 'href="../interface-originale-complete.html">'
            },
            // Corriger href="/" avec attributs
            {
                pattern: /href="\/"/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // Corriger window.location.href = "/"
            {
                pattern: /window\.location\.href\s*=\s*["']\/["']/g,
                replacement: 'window.location.href = "../interface-originale-complete.html"'
            },
            // Corriger location.href = "/"
            {
                pattern: /location\.href\s*=\s*["']\/["']/g,
                replacement: 'location.href = "../interface-originale-complete.html"'
            },
            // Corriger les liens relatifs vers l'accueil
            {
                pattern: /href=["']\.\.\/["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            }
        ];
        
        // Appliquer toutes les corrections
        corrections.forEach(correction => {
            content = content.replace(correction.pattern, correction.replacement);
        });
        
        // Vérifier si des changements ont été faits
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ ${file} - Navigation corrigée`);
            corrected++;
        } else {
            console.log(`ℹ️ ${file} - Aucune correction nécessaire`);
        }
        
    } catch (error) {
        console.error(`❌ Erreur avec ${file}:`, error.message);
        errors++;
    }
});

console.log('\n📊 === RÉSULTATS ===');
console.log(`✅ Applications corrigées: ${corrected}`);
console.log(`ℹ️ Applications sans changement: ${files.length - corrected - errors}`);
console.log(`❌ Erreurs: ${errors}`);

if (corrected > 0) {
    console.log('\n🎉 Navigation de retour corrigée ! Maintenant vous pouvez revenir à l\'accueil depuis toutes les applications !');
} else {
    console.log('\n💡 Aucune correction nécessaire - la navigation semble déjà correcte.');
}
