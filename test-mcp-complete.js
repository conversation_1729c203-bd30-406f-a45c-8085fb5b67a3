/**
 * Test complet du mode MCP avec démarrage automatique
 */

const fs = require('fs');
const http = require('http');
const { spawn } = require('child_process');

console.log('🧪 === TEST COMPLET MODE MCP ===');

// Variables globales
let mcpServerProcess = null;
let testResults = {
    serverStart: false,
    statusCheck: false,
    internetTest: false,
    desktopTest: false,
    systemTest: false,
    configTest: false
};

// Fonction pour tester la connexion MCP
function testMCPConnection(port = 3002) {
    return new Promise((resolve) => {
        const req = http.get(`http://localhost:${port}/mcp/status`, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    resolve({ success: true, data: response });
                } catch (error) {
                    resolve({ success: false, error: error.message });
                }
            });
        });
        
        req.on('error', (error) => {
            resolve({ success: false, error: error.message });
        });
        
        req.setTimeout(3000, () => {
            req.destroy();
            resolve({ success: false, error: 'Timeout' });
        });
    });
}

// Fonction pour démarrer le serveur MCP local
function startMCPServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Démarrage du serveur MCP local...');
        
        mcpServerProcess = spawn('node', ['mcp-server-local.js'], {
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let serverReady = false;
        
        mcpServerProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log(`[MCP] ${output.trim()}`);
            
            if (output.includes('Serveur MCP Local démarré') && !serverReady) {
                serverReady = true;
                setTimeout(() => resolve(), 2000); // Attendre 2s pour stabilisation
            }
        });
        
        mcpServerProcess.stderr.on('data', (data) => {
            console.error(`[MCP ERROR] ${data.toString().trim()}`);
        });
        
        mcpServerProcess.on('error', (error) => {
            console.error('❌ Erreur démarrage serveur:', error.message);
            reject(error);
        });
        
        mcpServerProcess.on('close', (code) => {
            console.log(`🔴 Serveur MCP fermé (code: ${code})`);
        });
        
        // Timeout de démarrage
        setTimeout(() => {
            if (!serverReady) {
                reject(new Error('Timeout démarrage serveur'));
            }
        }, 10000);
    });
}

// Test du statut du serveur
async function testServerStatus() {
    console.log('\n1️⃣ === TEST STATUT SERVEUR ===');
    
    const result = await testMCPConnection();
    if (result.success) {
        console.log('✅ Serveur MCP accessible');
        console.log(`   Status: ${result.data.status}`);
        console.log(`   Version: ${result.data.version}`);
        console.log(`   Platform: ${result.data.system?.platform}`);
        testResults.statusCheck = true;
    } else {
        console.log(`❌ Serveur MCP inaccessible: ${result.error}`);
    }
    
    return result.success;
}

// Test de l'accès Internet
async function testInternetAccess() {
    console.log('\n2️⃣ === TEST ACCÈS INTERNET ===');
    
    try {
        const response = await fetch('http://localhost:3002/mcp/internet/test');
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Test Internet réussi');
            console.log(`   URL testée: ${data.testUrl}`);
            console.log(`   Latence: ${data.latency}`);
            testResults.internetTest = true;
        } else {
            console.log('❌ Test Internet échoué');
        }
    } catch (error) {
        console.log(`❌ Erreur test Internet: ${error.message}`);
    }
}

// Test de l'accès Bureau
async function testDesktopAccess() {
    console.log('\n3️⃣ === TEST ACCÈS BUREAU ===');
    
    try {
        const response = await fetch('http://localhost:3002/mcp/desktop/list');
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Test Bureau réussi');
            console.log(`   Fichiers détectés: ${data.totalCount}`);
            if (data.files && data.files.length > 0) {
                console.log('   Exemples:');
                data.files.slice(0, 3).forEach(file => {
                    console.log(`     - ${file.name} (${file.location})`);
                });
            }
            testResults.desktopTest = true;
        } else {
            console.log('❌ Test Bureau échoué');
        }
    } catch (error) {
        console.log(`❌ Erreur test Bureau: ${error.message}`);
    }
}

// Test des commandes système
async function testSystemCommands() {
    console.log('\n4️⃣ === TEST COMMANDES SYSTÈME ===');
    
    try {
        const response = await fetch('http://localhost:3002/mcp/system/execute', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ command: 'echo "Test MCP OK"' })
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ Test Commandes réussi');
            console.log(`   Sortie: ${data.stdout.trim()}`);
            testResults.systemTest = true;
        } else {
            console.log(`❌ Test Commandes échoué: ${data.error}`);
        }
    } catch (error) {
        console.log(`❌ Erreur test Commandes: ${error.message}`);
    }
}

// Test de la configuration
async function testConfiguration() {
    console.log('\n5️⃣ === TEST CONFIGURATION ===');
    
    try {
        const response = await fetch('http://localhost:3002/mcp/config');
        const data = await response.json();
        
        console.log('✅ Configuration récupérée');
        console.log(`   Internet: ${data.allowInternet ? 'Activé' : 'Désactivé'}`);
        console.log(`   Bureau: ${data.allowDesktop ? 'Activé' : 'Désactivé'}`);
        console.log(`   Commandes: ${data.allowSystemCommands ? 'Activé' : 'Désactivé'}`);
        console.log(`   Debug: ${data.debug ? 'Activé' : 'Désactivé'}`);
        testResults.configTest = true;
    } catch (error) {
        console.log(`❌ Erreur test Configuration: ${error.message}`);
    }
}

// Fonction principale de test
async function runCompleteTest() {
    try {
        // Vérifier si le serveur est déjà actif
        console.log('🔌 Vérification serveur existant...');
        const existingServer = await testMCPConnection();
        
        if (!existingServer.success) {
            // Démarrer le serveur local
            await startMCPServer();
            testResults.serverStart = true;
            console.log('✅ Serveur MCP démarré avec succès');
        } else {
            console.log('✅ Serveur MCP déjà actif');
            testResults.serverStart = true;
        }
        
        // Exécuter tous les tests
        await testServerStatus();
        await testInternetAccess();
        await testDesktopAccess();
        await testSystemCommands();
        await testConfiguration();
        
        // Résultats finaux
        console.log('\n🎯 === RÉSULTATS FINAUX ===');
        
        const totalTests = Object.keys(testResults).length;
        const passedTests = Object.values(testResults).filter(Boolean).length;
        const score = Math.round((passedTests / totalTests) * 100);
        
        console.log(`📊 Score: ${score}% (${passedTests}/${totalTests} tests réussis)`);
        
        Object.entries(testResults).forEach(([test, passed]) => {
            const icon = passed ? '✅' : '❌';
            const name = test.replace(/([A-Z])/g, ' $1').toLowerCase();
            console.log(`${icon} ${name}`);
        });
        
        if (score >= 90) {
            console.log('\n🎉 EXCELLENT ! Mode MCP parfaitement fonctionnel !');
        } else if (score >= 70) {
            console.log('\n👍 BIEN ! Mode MCP fonctionnel avec quelques limitations');
        } else {
            console.log('\n⚠️ PROBLÈMES ! Mode MCP nécessite des corrections');
        }
        
        console.log('\n📱 === INSTRUCTIONS D\'UTILISATION ===');
        console.log('1. Ouvrez interface-originale-complete.html');
        console.log('2. Cliquez sur "🔧 Mode MCP"');
        console.log('3. Testez toutes les fonctionnalités');
        console.log('4. Le serveur reste actif en arrière-plan');
        
        console.log('\n💡 Appuyez sur Ctrl+C pour arrêter le serveur');
        
    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
    }
}

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt des tests...');
    if (mcpServerProcess) {
        mcpServerProcess.kill('SIGINT');
    }
    process.exit(0);
});

// Démarrer les tests
runCompleteTest();
