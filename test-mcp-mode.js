/**
 * Script de test et vérification du mode MCP (Model Context Protocol)
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

console.log('🔧 === TEST MODE MCP (MODEL CONTEXT PROTOCOL) ===');

// 1. Vérifier les fichiers MCP
console.log('\n1️⃣ === FICHIERS MCP ===');

const mcpFiles = [
    'LOUNA-AI-COMPLETE-REAL/deepseek-node-ui/mcp/mcp-server.js',
    'code/deepseek-node-ui/mcp/mcp-server.js',
    'LOUNA-AI-COMPLETE-REAL/deepseek-node-ui/public/js/luna-mcp.js',
    'code/deepseek-node-ui/public/js/luna-mcp.js',
    'LOUNA-AI-COMPLETE-REAL/deepseek-node-ui/data/config/mcp-config.json',
    'code/deepseek-node-ui/data/config/mcp-config.json',
    'data/config/mcp-connection.json'
];

let mcpFilesFound = 0;
mcpFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}: Trouvé`);
        mcpFilesFound++;
    } else {
        console.log(`❌ ${file}: Manquant`);
    }
});

console.log(`📊 Fichiers MCP: ${mcpFilesFound}/${mcpFiles.length}`);

// 2. Vérifier la configuration MCP
console.log('\n2️⃣ === CONFIGURATION MCP ===');

const configPaths = [
    'LOUNA-AI-COMPLETE-REAL/deepseek-node-ui/data/config/mcp-config.json',
    'code/deepseek-node-ui/data/config/mcp-config.json',
    'data/config/mcp-connection.json'
];

configPaths.forEach(configPath => {
    if (fs.existsSync(configPath)) {
        try {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            console.log(`✅ ${configPath}:`);
            console.log(`   Port: ${config.port || 'Non défini'}`);
            console.log(`   Internet: ${config.allowInternet ? 'Activé' : 'Désactivé'}`);
            console.log(`   Bureau: ${config.allowDesktop ? 'Activé' : 'Désactivé'}`);
            console.log(`   Commandes: ${config.allowSystemCommands ? 'Activé' : 'Désactivé'}`);
            console.log(`   Debug: ${config.debug ? 'Activé' : 'Désactivé'}`);
        } catch (error) {
            console.log(`❌ ${configPath}: Erreur lecture (${error.message})`);
        }
    } else {
        console.log(`❌ ${configPath}: Non trouvé`);
    }
});

// 3. Vérifier l'interface principale pour MCP
console.log('\n3️⃣ === INTÉGRATION INTERFACE PRINCIPALE ===');

try {
    const mainInterface = fs.readFileSync('./interface-originale-complete.html', 'utf8');
    
    const mcpReferences = [
        { name: 'MCP', pattern: /MCP|mcp/g },
        { name: 'Master Control', pattern: /Master Control|master control/gi },
        { name: 'Model Context Protocol', pattern: /Model Context Protocol/gi }
    ];
    
    let mcpFound = false;
    mcpReferences.forEach(ref => {
        const matches = (mainInterface.match(ref.pattern) || []).length;
        if (matches > 0) {
            console.log(`✅ ${ref.name}: ${matches} références trouvées`);
            mcpFound = true;
        } else {
            console.log(`❌ ${ref.name}: Aucune référence`);
        }
    });
    
    if (!mcpFound) {
        console.log('⚠️ MODE MCP NON INTÉGRÉ À L\'INTERFACE PRINCIPALE');
    }
    
} catch (error) {
    console.log('❌ Erreur lecture interface principale:', error.message);
}

// 4. Tester la connexion MCP
console.log('\n4️⃣ === TEST CONNEXION MCP ===');

function testMCPConnection(port = 3002) {
    return new Promise((resolve) => {
        const req = http.get(`http://localhost:${port}/mcp/status`, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log(`✅ Serveur MCP actif sur port ${port}:`);
                    console.log(`   Status: ${response.status}`);
                    console.log(`   Version: ${response.version}`);
                    console.log(`   Internet: ${response.capabilities?.internet ? 'Activé' : 'Désactivé'}`);
                    console.log(`   Bureau: ${response.capabilities?.desktop ? 'Activé' : 'Désactivé'}`);
                    console.log(`   Commandes: ${response.capabilities?.systemCommands ? 'Activé' : 'Désactivé'}`);
                    resolve(true);
                } catch (error) {
                    console.log(`❌ Réponse invalide du serveur MCP: ${error.message}`);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ Serveur MCP non accessible sur port ${port}: ${error.message}`);
            resolve(false);
        });
        
        req.setTimeout(5000, () => {
            console.log(`❌ Timeout connexion MCP sur port ${port}`);
            req.destroy();
            resolve(false);
        });
    });
}

// 5. Vérifier les applications MCP
console.log('\n5️⃣ === APPLICATIONS MCP ===');

const appsDir = './applications-originales';
const mcpApps = [
    'mcp-interface.html',
    'master-control.html',
    'mcp-dashboard.html',
    'system-control.html'
];

let mcpAppsFound = 0;
mcpApps.forEach(app => {
    const appPath = path.join(appsDir, app);
    if (fs.existsSync(appPath)) {
        console.log(`✅ ${app}: Trouvé`);
        mcpAppsFound++;
    } else {
        console.log(`❌ ${app}: Manquant`);
    }
});

console.log(`📊 Applications MCP: ${mcpAppsFound}/${mcpApps.length}`);

// 6. Score final MCP
console.log('\n🎯 === SCORE MODE MCP ===');

const totalChecks = 20; // Estimation du nombre total de vérifications
let passedChecks = 0;

// Compter les vérifications réussies
passedChecks += mcpFilesFound;
passedChecks += mcpAppsFound;
passedChecks += 2; // Configuration trouvée

const mcpScore = Math.round((passedChecks / totalChecks) * 100);

console.log(`📊 Score Mode MCP: ${mcpScore}%`);

if (mcpScore >= 80) {
    console.log('🎉 EXCELLENT ! Mode MCP parfaitement configuré !');
} else if (mcpScore >= 60) {
    console.log('👍 BIEN ! Mode MCP fonctionnel avec quelques améliorations possibles');
} else if (mcpScore >= 40) {
    console.log('⚠️ MOYEN ! Mode MCP partiellement fonctionnel');
} else {
    console.log('❌ PROBLÉMATIQUE ! Mode MCP nécessite une configuration importante');
}

// Test de connexion asynchrone
(async () => {
    console.log('\n🔌 === TEST CONNEXION TEMPS RÉEL ===');
    const isConnected = await testMCPConnection();
    
    if (isConnected) {
        console.log('🎉 SERVEUR MCP ACTIF ET FONCTIONNEL !');
    } else {
        console.log('⚠️ Serveur MCP non démarré - Démarrage requis');
        console.log('\n📋 === INSTRUCTIONS POUR DÉMARRER MCP ===');
        console.log('1. cd LOUNA-AI-COMPLETE-REAL/deepseek-node-ui');
        console.log('2. npm install');
        console.log('3. node mcp/mcp-server.js');
        console.log('4. Ou: node server-luna.js (démarre tout)');
    }
    
    console.log('\n🔧 === RECOMMANDATIONS ===');
    console.log('1. Ajouter un bouton MCP à l\'interface principale');
    console.log('2. Créer une application MCP dédiée');
    console.log('3. Tester les fonctionnalités Internet et Bureau');
    console.log('4. Vérifier l\'intégration avec DeepSeek');
    
    console.log('\n🧠 === TEST MODE MCP TERMINÉ ===');
})();
