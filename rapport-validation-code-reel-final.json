{"timestamp": "2025-06-11T00:20:24.570Z", "summary": {"filesAnalyzed": 12, "simulatedCodeFound": 9, "realCodeValidated": 12, "errors": 0, "globalScore": 25}, "details": {"filesAnalyzed": 12, "simulatedCodeFound": [{"file": "interface-originale-complete.html", "description": "Interface <PERSON>", "issues": [{"pattern": "Mots-clés simulés", "count": 3, "examples": ["placeholder", "placeholder", "placeholder"]}], "realityRatio": "89.7"}, {"file": "main.js", "description": "Application Electron Principale", "issues": [{"pattern": "Math.random() - Génération aléatoire", "count": 1, "examples": ["Math.random()"]}, {"pattern": "Math.floor(Math.random) - Sélection aléatoire", "count": 1, "examples": ["Math.floor(Math.random"]}, {"pattern": "Mots-clés simulés", "count": 1, "examples": ["placeholder"]}], "realityRatio": "83.3"}, {"file": "api-deepseek-real.js", "description": "API DeepSeek Réelle", "issues": [{"pattern": "Références à simulation", "count": 1, "examples": ["simulation"]}], "realityRatio": "94.4"}, {"file": "real-thermal-memory-complete.js", "description": "Mémoire Thermique Complète", "issues": [{"pattern": "Math.random() - Génération aléatoire", "count": 3, "examples": ["Math.random()", "Math.random()", "Math.random()"]}, {"pattern": "Math.floor(Math.random) - Sélection aléatoire", "count": 2, "examples": ["Math.floor(Math.random", "Math.floor(Math.random"]}, {"pattern": "Références à simulation", "count": 1, "examples": ["simulation"]}], "realityRatio": "50.0"}, {"file": "real-thermal-memory-system.js", "description": "Système Mémoire Thermique", "issues": [{"pattern": "Math.random() - Génération aléatoire", "count": 1, "examples": ["Math.random()"]}, {"pattern": "Références à simulation", "count": 1, "examples": ["simulation"]}], "realityRatio": "87.5"}, {"file": "real-memory-connector.js", "description": "Connecteur <PERSON><PERSON><PERSON><PERSON>", "issues": [{"pattern": "Math.random() - Génération aléatoire", "count": 1, "examples": ["Math.random()"]}, {"pattern": "Math.floor(Math.random) - Sélection aléatoire", "count": 1, "examples": ["Math.floor(Math.random"]}], "realityRatio": "92.0"}, {"file": "real-neural-network-system.js", "description": "R<PERSON>eau Neuronal Réel", "issues": [{"pattern": "Math.random() - Génération aléatoire", "count": 2, "examples": ["Math.random()", "Math.random()"]}, {"pattern": "Références à simulation", "count": 2, "examples": ["simulé", "simulé"]}], "realityRatio": "50.0"}, {"file": "modules/deepseek-direct-connector.js", "description": "Connecteur DeepSeek Direct", "issues": [{"pattern": "Math.random() - Génération aléatoire", "count": 1, "examples": ["Math.random()"]}], "realityRatio": "92.3"}, {"file": "real-data-backend-unified.js", "description": "Backend Données Unifiées", "issues": [{"pattern": "Références à simulation", "count": 1, "examples": ["simulation"]}], "realityRatio": "96.8"}], "realCodeValidated": [{"file": "interface-originale-complete.html", "description": "Interface <PERSON>", "features": [{"feature": "Manipulation JSON", "count": 5}, {"feature": "Fonctions asynchrones", "count": 7}, {"feature": "Opérations await", "count": 11}, {"feature": "Promesses", "count": 3}], "realityRatio": "89.7"}, {"file": "main.js", "description": "Application Electron Principale", "features": [{"feature": "Utilisation filesystem", "count": 3}, {"feature": "Utilisation path", "count": 3}, {"feature": "Manipulation JSON", "count": 2}, {"feature": "Opérations fichiers", "count": 1}, {"feature": "Vérification existence fichiers", "count": 2}, {"feature": "Fonctions asynchrones", "count": 1}, {"feature": "Opérations await", "count": 2}, {"feature": "Promesses", "count": 1}], "realityRatio": "83.3"}, {"file": "api-deepseek-real.js", "description": "API DeepSeek Réelle", "features": [{"feature": "Utilisation filesystem", "count": 1}, {"feature": "Utilisation path", "count": 1}, {"feature": "Manipulation JSON", "count": 3}, {"feature": "Opérations fichiers", "count": 3}, {"feature": "Vérification existence fichiers", "count": 3}, {"feature": "Variables environnement", "count": 1}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 3}, {"feature": "Exports modules", "count": 1}], "realityRatio": "94.4"}, {"file": "real-thermal-memory-complete.js", "description": "Mémoire Thermique Complète", "features": [{"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 2}, {"feature": "Exports modules", "count": 1}], "realityRatio": "50.0"}, {"file": "real-thermal-memory-system.js", "description": "Système Mémoire Thermique", "features": [{"feature": "Utilisation filesystem", "count": 1}, {"feature": "Utilisation path", "count": 1}, {"feature": "Manipulation JSON", "count": 3}, {"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 5}, {"feature": "Exports modules", "count": 1}], "realityRatio": "87.5"}, {"file": "real-memory-connector.js", "description": "Connecteur <PERSON><PERSON><PERSON><PERSON>", "features": [{"feature": "Utilisation filesystem", "count": 1}, {"feature": "Utilisation path", "count": 1}, {"feature": "Manipulation JSON", "count": 5}, {"feature": "Opérations fichiers", "count": 5}, {"feature": "Vérification existence fichiers", "count": 5}, {"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 2}, {"feature": "Exports modules", "count": 1}], "realityRatio": "92.0"}, {"file": "real-neural-network-system.js", "description": "R<PERSON>eau Neuronal Réel", "features": [{"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Exports modules", "count": 1}], "realityRatio": "50.0"}, {"file": "modules/real-mobius-thought-system.js", "description": "<PERSON><PERSON>t<PERSON> Mö<PERSON>", "features": [{"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Exports modules", "count": 1}], "realityRatio": "100.0"}, {"file": "modules/real-cpu-temperature-sensor.js", "description": "Capteur <PERSON><PERSON>", "features": [{"feature": "Utilisation filesystem", "count": 1}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 15}, {"feature": "Exports modules", "count": 1}], "realityRatio": "100.0"}, {"file": "modules/deepseek-direct-connector.js", "description": "Connecteur DeepSeek Direct", "features": [{"feature": "Variables environnement", "count": 1}, {"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 7}, {"feature": "Exports modules", "count": 1}], "realityRatio": "92.3"}, {"file": "neural-kyber-api-server.js", "description": "Serveur API Neural-KYBER", "features": [{"feature": "Utilisation filesystem", "count": 1}, {"feature": "Utilisation path", "count": 1}, {"feature": "Manipulation JSON", "count": 5}, {"feature": "Classes définies", "count": 1}, {"feature": "Exports modules", "count": 1}, {"feature": "Promesses", "count": 3}], "realityRatio": "100.0"}, {"file": "real-data-backend-unified.js", "description": "Backend Données Unifiées", "features": [{"feature": "Utilisation filesystem", "count": 1}, {"feature": "Utilisation path", "count": 1}, {"feature": "Manipulation JSON", "count": 4}, {"feature": "Opérations fichiers", "count": 4}, {"feature": "Vérification existence fichiers", "count": 8}, {"feature": "Événements réels", "count": 2}, {"feature": "Classes définies", "count": 1}, {"feature": "Opérations await", "count": 8}, {"feature": "Exports modules", "count": 1}], "realityRatio": "96.8"}], "errors": []}}