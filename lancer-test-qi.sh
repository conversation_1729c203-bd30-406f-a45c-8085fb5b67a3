#!/bin/bash

echo "🧠 === LANCEMENT TEST QI LOUNA AI ==="
echo "🎯 Test de QI complexe niveau expert"
echo "📊 10 questions de difficulté maximale"
echo ""

# Ouvrir l'interface principale avec le test QI
echo "🚀 Ouverture de l'interface LOUNA AI avec test QI..."
open interface-originale-complete.html

echo ""
echo "✅ Interface ouverte !"
echo "👀 Regardez la console du navigateur pour voir le test QI"
echo "🧠 LOUNA AI va recevoir 10 questions complexes"
echo ""
echo "📋 Instructions :"
echo "1. Ouvrez les outils de développement (F12)"
echo "2. Allez dans l'onglet Console"
echo "3. Observez les questions présentées à LOUNA AI"
echo "4. Notez ses réponses et raisonnements"
echo "5. Utilisez les commandes pour analyser ses performances"
echo ""
echo "💡 Commandes utiles dans la console :"
echo "   • demarrerTestQI() - Relancer le test"
echo "   • testQIExpress() - Version rapide"
echo "   • analyserReponse(id, 'A', 'raisonnement', 8) - Analyser"
echo "   • afficherSolutions() - Voir les solutions"
echo ""
echo "⚠️ IMPORTANT: Ne répondez PAS à la place de LOUNA AI !"
echo "👀 Observez et analysez ses réponses seulement"
echo ""
echo "🎉 Test QI prêt ! Bonne évaluation !"
