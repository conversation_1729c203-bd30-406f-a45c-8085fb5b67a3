/**
 * Script de test pour la mémoire thermique
 * Ce script teste si les informations personnelles sont correctement mémorisées et récupérées
 */

const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// Configuration
const WS_URL = 'ws://localhost:3001';
const MEMORY_FILE = path.join(__dirname, 'data/memory/thermal_memory.json');

// Fonction pour attendre un certain temps
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonction principale de test
async function testThermalMemory() {
  console.log('=== TEST DE LA MÉMOIRE THERMIQUE ===');

  // 1. Vérifier si le fichier de mémoire existe
  try {
    const stats = fs.statSync(MEMORY_FILE);
    console.log(`Fichier de mémoire: ${MEMORY_FILE}`);
    console.log(`Taille initiale: ${stats.size} octets`);
    console.log(`Dernière modification: ${stats.mtime.toISOString()}`);
  } catch (error) {
    console.error(`Erreur lors de la vérification du fichier de mémoire: ${error.message}`);
    process.exit(1);
  }

  // 2. Envoyer un message pour stocker des informations personnelles
  const personalInfoMessage = "INFORMATION PERSONNELLE: Mon nom est Jean Passave et j'habite à Sainte-Anne en Guadeloupe (97180). Je suis d'origine africaine. Mémorise ces informations sur moi.";
  console.log(`Envoi du message: "${personalInfoMessage}"`);

  // Se connecter au serveur WebSocket
  const ws = new WebSocket(WS_URL);

  // Attendre la connexion
  await new Promise((resolve, reject) => {
    ws.on('open', () => {
      console.log('Connecté au serveur WebSocket');
      resolve();
    });
    ws.on('error', (error) => {
      console.error(`Erreur de connexion WebSocket: ${error.message}`);
      reject(error);
    });
  });

  // Envoyer le message
  ws.send(JSON.stringify({
    type: 'luna message',
    data: { message: personalInfoMessage }
  }));

  console.log('Utilisation du modèle par défaut (Agent Local LOUNA)');

  // Attendre la réponse
  const response = await new Promise((resolve) => {
    ws.on('message', (data) => {
      const message = JSON.parse(data);
      if (message.type === 'luna response') {
        resolve(message.data.message);
      }
    });
  });

  console.log(`Réponse reçue: "${response}"`);

  // Attendre que la mémoire soit mise à jour
  console.log('Attente de 5 secondes pour la mise à jour de la mémoire...');
  await sleep(5000);

  // Vérifier si le fichier de mémoire a été modifié
  try {
    const newStats = fs.statSync(MEMORY_FILE);
    if (newStats.mtime > fs.statSync(MEMORY_FILE).mtime || newStats.size !== fs.statSync(MEMORY_FILE).size) {
      console.log('✅ Le fichier de mémoire a été modifié');
    } else {
      console.log('❌ Le fichier de mémoire n\'a pas été modifié');
    }
  } catch (error) {
    console.error(`Erreur lors de la vérification du fichier de mémoire: ${error.message}`);
  }

  // 3. Rechercher les informations dans la mémoire thermique
  try {
    const memoryContent = fs.readFileSync(MEMORY_FILE, 'utf8');
    const memory = JSON.parse(memoryContent);

    console.log(`Recherche dans ${memory.memories.length} mémoires...`);

    const found = memory.memories.some(entry => {
      if (entry.messages && entry.messages.length > 0) {
        return entry.messages.some(msg =>
          msg.content && msg.content.includes(personalInfoMessage)
        );
      }
      return false;
    });

    if (found) {
      console.log('✅ Message trouvé dans la mémoire thermique');
    } else {
      console.log('❌ Message non trouvé dans la mémoire thermique');
    }
  } catch (error) {
    console.error(`Erreur lors de la recherche dans la mémoire: ${error.message}`);
  }

  // 4. Tester si le modèle peut récupérer les informations de la mémoire
  const questionMessage = "QUESTION DIRECTE: Quel est mon nom complet, où est-ce que j'habite exactement et quelle est mon origine? Tu dois utiliser ta mémoire thermique pour répondre.";
  console.log(`Envoi du message: "${questionMessage}"`);

  // Envoyer la question
  ws.send(JSON.stringify({
    type: 'luna message',
    data: { message: questionMessage }
  }));

  console.log('Utilisation du modèle par défaut (Agent Local LOUNA)');

  // Attendre la réponse
  const questionResponse = await new Promise((resolve) => {
    ws.on('message', (data) => {
      const message = JSON.parse(data);
      if (message.type === 'luna response') {
        resolve(message.data.message);
      }
    });
  });

  console.log(`Réponse reçue: "${questionResponse}"`);

  // Attendre que la mémoire soit mise à jour
  console.log('Attente de 5 secondes pour la mise à jour de la mémoire...');
  await sleep(5000);

  // Vérifier si le fichier de mémoire a été modifié
  try {
    const newStats = fs.statSync(MEMORY_FILE);
    if (newStats.mtime > fs.statSync(MEMORY_FILE).mtime || newStats.size !== fs.statSync(MEMORY_FILE).size) {
      console.log('✅ Le fichier de mémoire a été modifié');
    } else {
      console.log('❌ Le fichier de mémoire n\'a pas été modifié');
    }
  } catch (error) {
    console.error(`Erreur lors de la vérification du fichier de mémoire: ${error.message}`);
  }

  // 5. Rechercher la question dans la mémoire thermique
  try {
    const memoryContent = fs.readFileSync(MEMORY_FILE, 'utf8');
    const memory = JSON.parse(memoryContent);

    console.log(`Recherche dans ${memory.memories.length} mémoires...`);

    const found = memory.memories.some(entry => {
      if (entry.messages && entry.messages.length > 0) {
        return entry.messages.some(msg =>
          msg.content && msg.content.includes(questionMessage)
        );
      }
      return false;
    });

    if (found) {
      console.log('✅ Message trouvé dans la mémoire thermique');
    } else {
      console.log('❌ Message non trouvé dans la mémoire thermique');
    }
  } catch (error) {
    console.error(`Erreur lors de la recherche dans la mémoire: ${error.message}`);
  }

  // 6. Analyser la réponse pour voir si les informations ont été correctement récupérées
  console.log('=== RÉSULTATS DU TEST ===');
  console.log(`Fichier de mémoire modifié après le premier message: ${fs.statSync(MEMORY_FILE).mtime > fs.statSync(MEMORY_FILE).mtime ? 'Oui' : 'Non'}`);
  console.log(`Fichier de mémoire modifié après le deuxième message: ${fs.statSync(MEMORY_FILE).mtime > fs.statSync(MEMORY_FILE).mtime ? 'Oui' : 'Non'}`);
  console.log(`Premier message dans la mémoire: ${memory.memories.some(entry => entry.messages && entry.messages.some(msg => msg.content && msg.content.includes(personalInfoMessage))) ? 'Oui' : 'Non'}`);
  console.log(`Deuxième message dans la mémoire: ${memory.memories.some(entry => entry.messages && entry.messages.some(msg => msg.content && msg.content.includes(questionMessage))) ? 'Oui' : 'Non'}`);

  // Vérifier si la réponse contient les informations correctes
  const hasName = questionResponse.toLowerCase().includes('jean passave');
  const hasLocation = questionResponse.toLowerCase().includes('sainte-anne') && questionResponse.toLowerCase().includes('guadeloupe') && questionResponse.toLowerCase().includes('97180');
  const hasOrigin = questionResponse.toLowerCase().includes('africaine');

  console.log(`Se souvient du nom complet: ${hasName ? 'Oui' : 'Non'}`);
  console.log(`Se souvient de la ville et du pays: ${hasLocation ? 'Oui' : 'Non'}`);
  console.log(`Se souvient de l'origine: ${hasOrigin ? 'Oui' : 'Non'}`);

  console.log('\nRéponse complète du modèle:');
  console.log('----------------------------');
  console.log(questionResponse);
  console.log('----------------------------');

  if (hasName && hasLocation && hasOrigin) {
    console.log('✅ TEST RÉUSSI: La mémoire thermique fonctionne correctement');
  } else {
    console.log('❌ TEST ÉCHOUÉ: La mémoire thermique ne fonctionne pas correctement');
    console.log(`Le modèle ${!hasName ? 'ne se souvient pas du nom, ' : ''}${!hasLocation ? 'ne se souvient pas de la localisation, ' : ''}${!hasOrigin ? 'ne se souvient pas de l\'origine, ' : ''}ce qui indique un problème avec le contexte de mémoire`);
  }

  // Fermer la connexion WebSocket
  ws.close();
}

// Exécuter le test
testThermalMemory().catch(error => {
  console.error(`Erreur lors du test: ${error.message}`);
  process.exit(1);
});
