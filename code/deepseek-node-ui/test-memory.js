/**
 * Script de test pour la mémoire thermique de Luna
 * Ce script envoie des messages via WebSocket et vérifie si la mémoire fonctionne correctement
 */

const fs = require('fs');
const path = require('path');
const io = require('socket.io-client');

// Configuration
const LUNA_SOCKET_URL = 'http://localhost:3001';
const MEMORY_FILE_PATH = path.join(__dirname, 'data/memory/thermal_memory.json');

// Fonction pour envoyer un message à Luna via WebSocket
async function sendMessage(message) {
  return new Promise((resolve, reject) => {
    try {
      console.log(`Envoi du message: "${message}"`);

      // Connexion au serveur WebSocket
      const socket = io(LUNA_SOCKET_URL);

      // Gérer la connexion
      socket.on('connect', () => {
        console.log('Connecté au serveur WebSocket');

        // Utiliser le modèle par défaut (Agent Local LOUNA)
        console.log('Utilisation du modèle par défaut (Agent Local LOUNA)');

        // Attendre un peu avant d'envoyer le message
        setTimeout(() => {
          // Envoyer le message
          socket.emit('luna message', { message });
        }, 1000);
      });

      // Gérer la réponse
      socket.on('luna response', (data) => {
        console.log(`Réponse reçue: "${data.message}"`);
        socket.disconnect();
        resolve(data.message);
      });

      // Gérer les erreurs
      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion WebSocket:', error.message);
        socket.disconnect();
        reject(error);
      });

      // Timeout après 120 secondes
      setTimeout(() => {
        if (socket.connected) {
          socket.disconnect();
          reject(new Error('Timeout: Pas de réponse après 120 secondes'));
        }
      }, 120000);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error.message);
      reject(error);
    }
  });
}

// Fonction pour vérifier si un message est dans la mémoire thermique
function checkMemory(userMessage, agentResponse) {
  try {
    // Lire le fichier de mémoire thermique
    const memoryData = JSON.parse(fs.readFileSync(MEMORY_FILE_PATH, 'utf8'));

    console.log(`Recherche dans ${memoryData.memories.length} mémoires...`);

    // Chercher le message dans la mémoire
    const found = memoryData.memories.some(mem => {
      // Vérifier le format avec content.user et content.agent
      if (mem.content && mem.content.user && mem.content.agent) {
        return mem.content.user.includes(userMessage) &&
               mem.content.agent.includes(agentResponse);
      }

      // Vérifier le format avec messages (format utilisé par addConversation)
      if (mem.messages && Array.isArray(mem.messages) && mem.messages.length >= 2) {
        const userMsg = mem.messages.find(m => m.role === 'user');
        const assistantMsg = mem.messages.find(m => m.role === 'assistant');

        if (userMsg && assistantMsg) {
          return userMsg.content.includes(userMessage) &&
                 assistantMsg.content.includes(agentResponse);
        }
      }

      return false;
    });

    if (found) {
      console.log('✅ Message trouvé dans la mémoire thermique');
      return true;
    } else {
      console.log('❌ Message non trouvé dans la mémoire thermique');

      // Afficher les 3 premières mémoires pour le débogage
      console.log('Aperçu des mémoires disponibles:');
      memoryData.memories.slice(0, 3).forEach((mem, index) => {
        console.log(`Mémoire ${index + 1}:`);
        if (mem.content) {
          console.log(`- Format content: user="${mem.content.user?.substring(0, 30)}...", agent="${mem.content.agent?.substring(0, 30)}..."`);
        }
        if (mem.messages) {
          console.log(`- Format messages: ${mem.messages.length} messages`);
          mem.messages.forEach(m => {
            console.log(`  - ${m.role}: "${m.content?.substring(0, 30)}..."`);
          });
        }
      });

      return false;
    }
  } catch (error) {
    console.error('Erreur lors de la vérification de la mémoire:', error.message);
    return false;
  }
}

// Fonction pour vérifier si le fichier de mémoire a été modifié
function checkFileModified(initialStats) {
  try {
    const stats = fs.statSync(MEMORY_FILE_PATH);
    const modified = stats.mtimeMs !== initialStats.mtimeMs;

    if (modified) {
      console.log('✅ Le fichier de mémoire a été modifié');
    } else {
      console.log('❌ Le fichier de mémoire n\'a PAS été modifié');
    }

    return modified;
  } catch (error) {
    console.error('Erreur lors de la vérification de la modification du fichier:', error.message);
    return false;
  }
}

// Fonction principale de test
async function runMemoryTest() {
  console.log('=== TEST DE LA MÉMOIRE THERMIQUE ===');

  // Obtenir les statistiques initiales du fichier
  const initialStats = fs.statSync(MEMORY_FILE_PATH);
  console.log(`Fichier de mémoire: ${MEMORY_FILE_PATH}`);
  console.log(`Taille initiale: ${initialStats.size} octets`);
  console.log(`Dernière modification: ${new Date(initialStats.mtimeMs).toISOString()}`);

  // Premier message - information factuelle très claire
  const message1 = "INFORMATION PERSONNELLE: Mon nom est Jean Passave et j'habite à Sainte-Anne en Guadeloupe (97180). Je suis d'origine africaine. Mémorise ces informations sur moi.";
  const response1 = await sendMessage(message1);

  if (!response1) {
    console.error('Test échoué: Pas de réponse au premier message');
    return;
  }

  // Attendre un peu pour que la mémoire soit mise à jour
  console.log('Attente de 5 secondes pour la mise à jour de la mémoire...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Vérifier si le fichier a été modifié
  const fileModified1 = checkFileModified(initialStats);

  // Vérifier si le premier message est dans la mémoire
  const inMemory1 = checkMemory(message1, response1);

  // Deuxième message - référence au premier avec une question très directe
  const message2 = "QUESTION DIRECTE: Quel est mon nom complet, où est-ce que j'habite exactement et quelle est mon origine? Tu dois utiliser ta mémoire thermique pour répondre.";
  const response2 = await sendMessage(message2);

  if (!response2) {
    console.error('Test échoué: Pas de réponse au deuxième message');
    return;
  }

  // Attendre un peu pour que la mémoire soit mise à jour
  console.log('Attente de 5 secondes pour la mise à jour de la mémoire...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Vérifier si le fichier a été modifié depuis le premier message
  const fileModified2 = checkFileModified(initialStats);

  // Vérifier si le deuxième message est dans la mémoire
  const inMemory2 = checkMemory(message2, response2);

  // Vérifier si la réponse au deuxième message contient les informations du premier
  const remembersName = response2.toLowerCase().includes('jean') && response2.toLowerCase().includes('passave');
  const remembersCity = response2.toLowerCase().includes('sainte-anne') && response2.toLowerCase().includes('guadeloupe');
  const remembersOrigin = response2.toLowerCase().includes('africaine');

  console.log('=== RÉSULTATS DU TEST ===');
  console.log(`Fichier de mémoire modifié après le premier message: ${fileModified1 ? 'Oui' : 'Non'}`);
  console.log(`Fichier de mémoire modifié après le deuxième message: ${fileModified2 ? 'Oui' : 'Non'}`);
  console.log(`Premier message dans la mémoire: ${inMemory1 ? 'Oui' : 'Non'}`);
  console.log(`Deuxième message dans la mémoire: ${inMemory2 ? 'Oui' : 'Non'}`);
  console.log(`Se souvient du nom complet: ${remembersName ? 'Oui' : 'Non'}`);
  console.log(`Se souvient de la ville et du pays: ${remembersCity ? 'Oui' : 'Non'}`);
  console.log(`Se souvient de l'origine: ${remembersOrigin ? 'Oui' : 'Non'}`);

  // Afficher la réponse complète pour analyse
  console.log('\nRéponse complète du modèle:');
  console.log('----------------------------');
  console.log(response2);
  console.log('----------------------------\n');

  if (fileModified2 && (inMemory1 || inMemory2) && (remembersName || remembersCity || remembersOrigin)) {
    console.log('✅ TEST RÉUSSI: La mémoire thermique fonctionne correctement');

    if (!remembersName) {
      console.log('⚠️ Attention: Le modèle ne se souvient pas du nom complet, mais la mémoire fonctionne');
    }

    if (!remembersCity) {
      console.log('⚠️ Attention: Le modèle ne se souvient pas de la ville et du pays, mais la mémoire fonctionne');
    }

    if (!remembersOrigin) {
      console.log('⚠️ Attention: Le modèle ne se souvient pas de l\'origine, mais la mémoire fonctionne');
    }
  } else {
    console.log('❌ TEST ÉCHOUÉ: La mémoire thermique ne fonctionne pas correctement');

    if (!fileModified2) {
      console.log('Le fichier de mémoire n\'a pas été modifié, ce qui indique un problème avec la fonction saveMemories()');
    }

    if (!inMemory1 && !inMemory2) {
      console.log('Aucun message n\'a été trouvé dans la mémoire, ce qui indique un problème avec la fonction addConversation()');
    }

    if (!remembersName && !remembersCity) {
      console.log('Le modèle ne se souvient d\'aucune information, ce qui indique un problème avec le contexte de mémoire');
    }
  }
}

// Exécuter le test
runMemoryTest().catch(error => {
  console.error('Erreur lors de l\'exécution du test:', error);
});
