/**
 * 🧠💤 SYSTÈME D'ACTIVATION NEURONALE À LA DEMANDE
 * Neurones en veille + activation massive sur demande
 * Économie mémoire + Performance maximale quand nécessaire
 */

const EventEmitter = require('events');

class NeuralStandbyActivationSystem extends EventEmitter {
    constructor() {
        super();
        
        // 💤 CONFIGURATION VEILLE/ACTIVATION
        this.config = {
            totalNeurons: 86000000000,      // 86B neurones totaux
            standbyNeurons: 50000,          // 50k neurones en veille active
            maxActiveNeurons: 10000000,     // 10M neurones max en activation
            activationThreshold: 0.7,       // Seuil d'activation
            standbyMemoryUsage: 0.1,        // 10% mémoire en veille
            fullActivationMemoryUsage: 5.0, // 500% mémoire en activation
            activationSpeed: 2000           // 2000 neurones/ms
        };
        
        // 🧠 ÉTATS DES NEURONES
        this.neuronStates = {
            standby: new Map(),      // Neurones en veille
            active: new Map(),       // Neurones actifs
            hibernating: new Map(),  // Neurones en hibernation
            reserved: new Map()      // Neurones réservés pour activation
        };
        
        // ⚡ SYSTÈME D'ACTIVATION À LA DEMANDE
        this.activationSystem = {
            isActivating: false,
            activationQueue: [],
            currentActivationLevel: 0,
            targetActivationLevel: 0,
            activationStartTime: 0,
            activationRequests: new Map()
        };
        
        // 📊 MÉTRIQUES VEILLE/ACTIVATION
        this.metrics = {
            totalActivations: 0,
            totalStandbyTime: 0,
            averageActivationTime: 0,
            memoryEfficiency: 0,
            powerSavings: 0,
            responsiveness: 0
        };
        
        this.initializeStandbySystem();
    }

    /**
     * 💤 Initialise le système de veille
     */
    initializeStandbySystem() {
        console.log('💤 Initialisation système veille neuronale...');
        
        // Créer les neurones de veille
        this.createStandbyNeurons();
        
        // Démarrer la surveillance
        this.startStandbyMonitoring();
        
        console.log(`💤 ${this.config.standbyNeurons.toLocaleString()} neurones en veille active`);
        console.log(`🧠 ${(this.config.totalNeurons - this.config.standbyNeurons).toLocaleString()} neurones en hibernation`);
    }

    /**
     * 🧠 Crée les neurones de veille
     */
    createStandbyNeurons() {
        for (let i = 0; i < this.config.standbyNeurons; i++) {
            const neuronId = `standby_neuron_${i}`;
            const neuron = {
                id: neuronId,
                type: this.getStandbyNeuronType(),
                state: 'standby',
                membrane_potential: -70,
                threshold: -55,
                energyLevel: 0.3, // Énergie réduite en veille
                activationReadiness: 1.0,
                lastActivity: Date.now(),
                synapticConnections: new Set(),
                canActivate: true,
                activationPriority: Math.random(),
                created: Date.now()
            };
            
            this.neuronStates.standby.set(neuronId, neuron);
        }
        
        // Créer des connexions légères entre neurones de veille
        this.createStandbySynapses();
    }

    /**
     * 🔗 Crée des synapses légères pour la veille
     */
    createStandbySynapses() {
        const standbyNeuronIds = Array.from(this.neuronStates.standby.keys());
        
        standbyNeuronIds.forEach(preId => {
            const connectionsCount = Math.floor(Math.random() * 20) + 5; // 5-25 connexions légères
            
            for (let i = 0; i < connectionsCount; i++) {
                const postId = standbyNeuronIds[Math.floor(Math.random() * standbyNeuronIds.length)];
                
                if (preId !== postId) {
                    const neuron = this.neuronStates.standby.get(preId);
                    neuron.synapticConnections.add(postId);
                }
            }
        });
    }

    /**
     * ⚡ ACTIVATION MASSIVE À LA DEMANDE
     */
    async activateOnDemand(requestType, intensity = 1.0, duration = 30000) {
        console.log(`⚡ ACTIVATION À LA DEMANDE: ${requestType} (intensité: ${intensity})`);
        
        const requestId = `activation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const neuronsToActivate = Math.floor(this.config.maxActiveNeurons * intensity);
        
        const activationRequest = {
            id: requestId,
            type: requestType,
            intensity: intensity,
            neuronsRequested: neuronsToActivate,
            duration: duration,
            startTime: Date.now(),
            status: 'pending'
        };
        
        this.activationSystem.activationRequests.set(requestId, activationRequest);
        
        // Démarrer l'activation
        await this.executeActivation(activationRequest);
        
        // Programmer la désactivation
        setTimeout(() => {
            this.deactivateRequest(requestId);
        }, duration);
        
        return requestId;
    }

    /**
     * 🚀 Exécute l'activation des neurones
     */
    async executeActivation(request) {
        console.log(`🚀 Activation de ${request.neuronsRequested.toLocaleString()} neurones...`);
        
        this.activationSystem.isActivating = true;
        this.activationSystem.activationStartTime = Date.now();
        request.status = 'activating';
        
        const neuronsToActivate = Math.min(request.neuronsRequested, this.config.maxActiveNeurons);
        let activatedCount = 0;
        
        // Activer par batches pour éviter le blocage
        const batchSize = this.config.activationSpeed;
        
        while (activatedCount < neuronsToActivate && this.activationSystem.isActivating) {
            const currentBatch = Math.min(batchSize, neuronsToActivate - activatedCount);
            
            for (let i = 0; i < currentBatch; i++) {
                const neuron = this.createActiveNeuron(request.id, activatedCount + i);
                this.neuronStates.active.set(neuron.id, neuron);
                activatedCount++;
            }
            
            // Petite pause pour éviter le blocage
            await this.sleep(1);
            
            // Afficher le progrès
            if (activatedCount % 100000 === 0) {
                console.log(`⚡ ${activatedCount.toLocaleString()}/${neuronsToActivate.toLocaleString()} neurones activés`);
            }
        }
        
        // Créer des synapses entre neurones actifs
        await this.createActiveSynapses(request.id);
        
        request.status = 'active';
        this.activationSystem.isActivating = false;
        this.activationSystem.currentActivationLevel = activatedCount;
        
        const activationTime = Date.now() - this.activationSystem.activationStartTime;
        this.metrics.totalActivations++;
        this.metrics.averageActivationTime = (this.metrics.averageActivationTime + activationTime) / 2;
        
        console.log(`✅ ${activatedCount.toLocaleString()} neurones activés en ${activationTime}ms`);
        console.log(`🧠 Mémoire utilisée: ${this.calculateMemoryUsage().toFixed(1)}%`);
        
        this.emit('neuronsActivated', {
            requestId: request.id,
            neuronsActivated: activatedCount,
            activationTime: activationTime,
            memoryUsage: this.calculateMemoryUsage()
        });
    }

    /**
     * 🧬 Crée un neurone actif
     */
    createActiveNeuron(requestId, index) {
        const neuronId = `active_${requestId}_neuron_${index}`;
        
        return {
            id: neuronId,
            requestId: requestId,
            type: this.getActiveNeuronType(),
            state: 'active',
            membrane_potential: -70 + Math.random() * 10,
            threshold: -55 + Math.random() * 5,
            energyLevel: 1.0, // Énergie maximale en activation
            activationReadiness: 1.0,
            lastActivity: Date.now(),
            synapticConnections: new Set(),
            activationCount: 0,
            isHighPerformance: true,
            created: Date.now()
        };
    }

    /**
     * 🔗 Crée des synapses entre neurones actifs
     */
    async createActiveSynapses(requestId) {
        const activeNeurons = Array.from(this.neuronStates.active.values())
            .filter(n => n.requestId === requestId);
        
        console.log(`🔗 Création synapses pour ${activeNeurons.length.toLocaleString()} neurones actifs...`);
        
        let synapseCount = 0;
        const batchSize = 1000;
        
        for (let i = 0; i < activeNeurons.length; i += batchSize) {
            const batch = activeNeurons.slice(i, i + batchSize);
            
            batch.forEach(preNeuron => {
                const connectionsCount = Math.floor(Math.random() * 500) + 100; // 100-600 connexions
                
                for (let j = 0; j < connectionsCount && j < activeNeurons.length; j++) {
                    const postNeuron = activeNeurons[Math.floor(Math.random() * activeNeurons.length)];
                    
                    if (preNeuron.id !== postNeuron.id) {
                        preNeuron.synapticConnections.add(postNeuron.id);
                        synapseCount++;
                    }
                }
            });
            
            // Petite pause entre les batches
            await this.sleep(1);
        }
        
        console.log(`🔗 ${synapseCount.toLocaleString()} synapses créées`);
    }

    /**
     * 🛑 Désactive une demande d'activation
     */
    async deactivateRequest(requestId) {
        console.log(`🛑 Désactivation demande: ${requestId}`);
        
        const request = this.activationSystem.activationRequests.get(requestId);
        if (!request) return;
        
        // Déplacer les neurones actifs vers la veille ou hibernation
        const neuronsToDeactivate = Array.from(this.neuronStates.active.values())
            .filter(n => n.requestId === requestId);
        
        let deactivatedCount = 0;
        
        neuronsToDeactivate.forEach(neuron => {
            // 20% chance de rester en veille, 80% hibernation
            if (Math.random() < 0.2 && this.neuronStates.standby.size < this.config.standbyNeurons * 1.5) {
                neuron.state = 'standby';
                neuron.energyLevel = 0.3;
                this.neuronStates.standby.set(neuron.id, neuron);
            } else {
                neuron.state = 'hibernating';
                neuron.energyLevel = 0.1;
                this.neuronStates.hibernating.set(neuron.id, neuron);
            }
            
            this.neuronStates.active.delete(neuron.id);
            deactivatedCount++;
        });
        
        request.status = 'deactivated';
        this.activationSystem.currentActivationLevel -= deactivatedCount;
        
        console.log(`🛑 ${deactivatedCount.toLocaleString()} neurones désactivés`);
        console.log(`💤 Retour à la veille - Mémoire: ${this.calculateMemoryUsage().toFixed(1)}%`);
        
        this.emit('neuronsDeactivated', {
            requestId: requestId,
            neuronsDeactivated: deactivatedCount,
            memoryUsage: this.calculateMemoryUsage()
        });
    }

    /**
     * 📊 Surveille le système de veille
     */
    startStandbyMonitoring() {
        // Surveillance toutes les 10 secondes
        setInterval(() => {
            this.updateStandbyMetrics();
        }, 10000);
        
        // Maintenance des neurones en veille toutes les 30 secondes
        setInterval(() => {
            this.maintainStandbyNeurons();
        }, 30000);
        
        // Nettoyage des demandes expirées toutes les 60 secondes
        setInterval(() => {
            this.cleanupExpiredRequests();
        }, 60000);
        
        console.log('📊 Surveillance système veille démarrée');
    }

    /**
     * 🔧 Maintient les neurones en veille
     */
    maintainStandbyNeurons() {
        this.neuronStates.standby.forEach(neuron => {
            // Activité minimale pour maintenir la veille
            if (Math.random() < 0.1) {
                neuron.membrane_potential += Math.random() * 2 - 1;
                neuron.lastActivity = Date.now();
            }
            
            // Réajuster l'énergie
            neuron.energyLevel = Math.max(0.2, Math.min(0.4, neuron.energyLevel + (Math.random() * 0.1 - 0.05)));
        });
    }

    /**
     * 🧹 Nettoie les demandes expirées
     */
    cleanupExpiredRequests() {
        const now = Date.now();
        const expiredRequests = [];
        
        this.activationSystem.activationRequests.forEach((request, requestId) => {
            if (now - request.startTime > request.duration + 60000) { // +1 minute de grâce
                expiredRequests.push(requestId);
            }
        });
        
        expiredRequests.forEach(requestId => {
            this.activationSystem.activationRequests.delete(requestId);
        });
        
        if (expiredRequests.length > 0) {
            console.log(`🧹 ${expiredRequests.length} demandes expirées nettoyées`);
        }
    }

    /**
     * 📊 Met à jour les métriques
     */
    updateStandbyMetrics() {
        const memoryUsage = this.calculateMemoryUsage();
        const powerSavings = this.calculatePowerSavings();
        const responsiveness = this.calculateResponsiveness();
        
        this.metrics.memoryEfficiency = 100 - memoryUsage;
        this.metrics.powerSavings = powerSavings;
        this.metrics.responsiveness = responsiveness;
        
        // Afficher les métriques périodiquement
        if (this.metrics.totalActivations % 10 === 0 && this.metrics.totalActivations > 0) {
            console.log('\n💤 === MÉTRIQUES VEILLE/ACTIVATION ===');
            console.log(`🧠 Neurones en veille: ${this.neuronStates.standby.size.toLocaleString()}`);
            console.log(`⚡ Neurones actifs: ${this.neuronStates.active.size.toLocaleString()}`);
            console.log(`💤 Neurones hibernation: ${this.neuronStates.hibernating.size.toLocaleString()}`);
            console.log(`📊 Mémoire utilisée: ${memoryUsage.toFixed(1)}%`);
            console.log(`⚡ Économie énergie: ${powerSavings.toFixed(1)}%`);
            console.log(`🚀 Réactivité: ${responsiveness.toFixed(1)}%`);
        }
    }

    /**
     * 📊 Calcule l'utilisation mémoire
     */
    calculateMemoryUsage() {
        const standbyUsage = this.neuronStates.standby.size * 0.1; // 0.1KB par neurone veille
        const activeUsage = this.neuronStates.active.size * 2.0;   // 2KB par neurone actif
        const hibernatingUsage = this.neuronStates.hibernating.size * 0.05; // 0.05KB hibernation
        
        const totalUsage = standbyUsage + activeUsage + hibernatingUsage;
        const maxPossibleUsage = this.config.totalNeurons * 2.0; // Si tous actifs
        
        return (totalUsage / maxPossibleUsage) * 100;
    }

    /**
     * ⚡ Calcule les économies d'énergie
     */
    calculatePowerSavings() {
        const activeRatio = this.neuronStates.active.size / this.config.totalNeurons;
        const standbyRatio = this.neuronStates.standby.size / this.config.totalNeurons;
        const hibernatingRatio = this.neuronStates.hibernating.size / this.config.totalNeurons;
        
        const currentConsumption = (activeRatio * 1.0) + (standbyRatio * 0.3) + (hibernatingRatio * 0.1);
        const maxConsumption = 1.0; // Si tous actifs
        
        return ((maxConsumption - currentConsumption) / maxConsumption) * 100;
    }

    /**
     * 🚀 Calcule la réactivité
     */
    calculateResponsiveness() {
        if (this.metrics.averageActivationTime === 0) return 100;
        
        const targetActivationTime = 5000; // 5 secondes cible
        const responsiveness = Math.max(0, 100 - (this.metrics.averageActivationTime / targetActivationTime) * 100);
        
        return Math.min(100, responsiveness);
    }

    /**
     * 🧠 Obtient un type de neurone de veille
     */
    getStandbyNeuronType() {
        const types = ['standby_pyramidal', 'standby_interneuron', 'standby_monitor'];
        return types[Math.floor(Math.random() * types.length)];
    }

    /**
     * ⚡ Obtient un type de neurone actif
     */
    getActiveNeuronType() {
        const types = ['active_pyramidal', 'active_interneuron', 'active_dopaminergic', 'active_gabaergic'];
        return types[Math.floor(Math.random() * types.length)];
    }

    /**
     * 😴 Utilitaire de pause
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 📊 Obtient les métriques complètes
     */
    getCompleteMetrics() {
        this.updateStandbyMetrics();
        
        return {
            config: this.config,
            neuronStates: {
                standby: this.neuronStates.standby.size,
                active: this.neuronStates.active.size,
                hibernating: this.neuronStates.hibernating.size,
                reserved: this.neuronStates.reserved.size
            },
            activationSystem: {
                ...this.activationSystem,
                activeRequests: this.activationSystem.activationRequests.size
            },
            metrics: this.metrics,
            memoryUsage: this.calculateMemoryUsage(),
            powerSavings: this.calculatePowerSavings(),
            responsiveness: this.calculateResponsiveness()
        };
    }

    /**
     * 🛑 Arrêt d'urgence - désactive tout
     */
    emergencyShutdown() {
        console.log('🛑 ARRÊT D\'URGENCE - Désactivation de tous les neurones...');
        
        // Arrêter toutes les activations en cours
        this.activationSystem.isActivating = false;
        
        // Déplacer tous les neurones actifs en hibernation
        this.neuronStates.active.forEach((neuron, id) => {
            neuron.state = 'hibernating';
            neuron.energyLevel = 0.1;
            this.neuronStates.hibernating.set(id, neuron);
        });
        
        this.neuronStates.active.clear();
        this.activationSystem.currentActivationLevel = 0;
        
        console.log('🛑 Tous les neurones en hibernation - Mémoire minimale');
        this.emit('emergencyShutdown');
    }
}

module.exports = NeuralStandbyActivationSystem;
