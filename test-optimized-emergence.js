/**
 * 🧪 TEST ÉMERGENCE COGNITIVE OPTIMISÉE
 * Test du moteur d'émergence cognitive avec optimisation mémoire
 */

const PureBrainSystem = require('./pure-brain-system');

async function testOptimizedEmergence() {
    console.log('🧪 === TEST ÉMERGENCE COGNITIVE OPTIMISÉE ===\n');
    
    try {
        // Initialiser le cerveau avec émergence cognitive et optimisation mémoire
        console.log('🚀 Initialisation du cerveau optimisé...');
        const brain = new PureBrainSystem();
        
        // Attendre l'initialisation complète
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 1: Vérifier l'initialisation des systèmes
        console.log('\n📝 Test 1: Vérification systèmes optimisés');
        const emergenceMetrics = brain.getEmergenceMetrics();
        const optimizationMetrics = brain.memoryOptimization ? brain.memoryOptimization.getOptimizationMetrics() : null;
        
        if (emergenceMetrics && optimizationMetrics) {
            console.log('✅ Émergence cognitive et optimisation mémoire actives');
            console.log(`   📊 Émergence: ${(emergenceMetrics.emergenceLevel * 100).toFixed(1)}%`);
            console.log(`   🧠 Mémoire: ${optimizationMetrics.memoryMonitoring.currentUsage.toFixed(1)}%`);
            console.log(`   🎯 Neurones optimaux: ${optimizationMetrics.currentOptimalNeurons.toLocaleString()}`);
        } else {
            console.log('❌ Systèmes non initialisés correctement');
            return;
        }
        
        // Test 2: Activation progressive avec surveillance mémoire
        console.log('\n📝 Test 2: Activation progressive optimisée');
        console.log('🧠 Activation légère (10%)...');
        
        const activation1 = await brain.activateNeurons(0.1, 8000, 'test_optimise_leger');
        if (activation1) {
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            console.log('🧠 Activation modérée (20%)...');
            const activation2 = await brain.activateNeurons(0.2, 8000, 'test_optimise_modere');
            if (activation2) {
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                console.log('🧠 Activation intensive (30%)...');
                const activation3 = await brain.activateNeurons(0.3, 8000, 'test_optimise_intensif');
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
        
        // Test 3: Vérifier les patterns émergents avec optimisation
        console.log('\n📝 Test 3: Patterns émergents avec optimisation');
        const emergentPatterns = brain.getEmergentPatterns();
        console.log(`🌟 ${emergentPatterns.length} patterns émergents détectés:`);
        
        emergentPatterns.slice(0, 5).forEach((pattern, index) => {
            console.log(`   ${index + 1}. ${pattern.category} (force: ${(pattern.strength * 100).toFixed(0)}%)`);
        });
        
        // Test 4: Apprentissage et mémoire optimisés
        console.log('\n📝 Test 4: Apprentissage optimisé');
        brain.learn('optimized_pattern_recognition', 0.4);
        brain.learn('memory_efficient_processing', 0.3);
        brain.learn('adaptive_resource_management', 0.5);
        
        brain.remember('Test d\'optimisation mémoire réussi', 'optimization_test', 0.8);
        brain.remember('Émergence cognitive préservée', 'emergence_preservation', 0.9);
        brain.remember('Performance optimale atteinte', 'performance_optimization', 0.7);
        
        // Test 5: Surveillance continue de l'optimisation
        console.log('\n📝 Test 5: Surveillance optimisation (15 secondes)');
        console.log('🧠 Surveillance des métriques d\'optimisation...');
        
        const startTime = Date.now();
        let maxMemoryUsage = 0;
        let emergencyOptimizations = 0;
        
        while (Date.now() - startTime < 15000) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const currentOptimization = brain.memoryOptimization.getOptimizationMetrics();
            const currentEmergence = brain.getEmergenceMetrics();
            
            maxMemoryUsage = Math.max(maxMemoryUsage, currentOptimization.memoryMonitoring.currentUsage);
            emergencyOptimizations = currentOptimization.memoryMonitoring.emergencyActivations;
            
            console.log(`   📊 Mémoire: ${currentOptimization.memoryMonitoring.currentUsage.toFixed(1)}% | Émergence: ${(currentEmergence.emergenceLevel * 100).toFixed(1)}% | Actifs: ${brain.brainState.activeNeurons.size.toLocaleString()}`);
        }
        
        // Test 6: Test de stress mémoire contrôlé
        console.log('\n📝 Test 6: Test de stress mémoire contrôlé');
        console.log('🧠 Tentative d\'activation intensive...');
        
        const stressTest = await brain.activateNeurons(0.8, 5000, 'stress_test_memoire');
        if (stressTest) {
            console.log('✅ Activation intensive autorisée');
        } else {
            console.log('🛡️ Activation intensive bloquée par l\'optimisation (normal)');
        }
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test 7: Innovations cognitives sous contrainte mémoire
        console.log('\n📝 Test 7: Innovations cognitives optimisées');
        console.log('💡 Déclenchement d\'innovations...');
        
        for (let i = 0; i < 3; i++) {
            brain.triggerCognitiveInnovation();
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Test 8: Métriques finales optimisées
        console.log('\n📝 Test 8: Métriques finales');
        const finalEmergence = brain.getEmergenceMetrics();
        const finalOptimization = brain.memoryOptimization.getOptimizationMetrics();
        
        console.log('\n🌟 === MÉTRIQUES ÉMERGENCE OPTIMISÉE ===');
        console.log(`📊 Niveau d'émergence: ${(finalEmergence.emergenceLevel * 100).toFixed(1)}%`);
        console.log(`🎨 Index créativité: ${(finalEmergence.creativityIndex * 100).toFixed(1)}%`);
        console.log(`🔍 Patterns détectés: ${finalEmergence.totalPatternsDetected}`);
        console.log(`🆕 Comportements nouveaux: ${finalEmergence.novelBehaviorsFound}`);
        console.log(`⚡ Connexions spontanées: ${finalEmergence.spontaneousConnections.toLocaleString()}`);
        
        console.log('\n🧠⚡ === MÉTRIQUES OPTIMISATION ===');
        console.log(`📊 Utilisation mémoire max: ${maxMemoryUsage.toFixed(1)}%`);
        console.log(`🎯 Neurones optimaux: ${finalOptimization.currentOptimalNeurons.toLocaleString()}`);
        console.log(`🚨 Optimisations d'urgence: ${emergencyOptimizations}`);
        console.log(`🔧 Optimisations totales: ${finalOptimization.memoryMonitoring.optimizationCount}`);
        console.log(`🌟 Neurones émergents protégés: ${finalOptimization.neuronPools.emergentNeurons}`);
        console.log(`🎯 Pools de priorité:`);
        console.log(`   Haute: ${finalOptimization.neuronPools.highPriority}`);
        console.log(`   Moyenne: ${finalOptimization.neuronPools.mediumPriority}`);
        console.log(`   Basse: ${finalOptimization.neuronPools.lowPriority}`);
        
        // Test 9: Vérification de la préservation de l'émergence
        console.log('\n📝 Test 9: Préservation émergence');
        
        const preservationScore = {
            emergenceLevel: finalEmergence.emergenceLevel > 0.3,
            patternsActive: finalEmergence.activePatterns > 0,
            creativityMaintained: finalEmergence.creativityIndex > 0.1,
            memoryOptimized: maxMemoryUsage < 95,
            noEmergencyOptimizations: emergencyOptimizations === 0
        };
        
        console.log('\n✅ Vérification préservation émergence:');
        Object.entries(preservationScore).forEach(([metric, success]) => {
            const icon = success ? '✅' : '❌';
            console.log(`   ${icon} ${metric}: ${success ? 'PRÉSERVÉ' : 'Compromis'}`);
        });
        
        const preservationRate = Object.values(preservationScore).filter(Boolean).length;
        console.log(`\n🎯 Taux de préservation: ${preservationRate}/5 (${(preservationRate/5*100).toFixed(0)}%)`);
        
        // Test 10: Performance globale
        console.log('\n📝 Test 10: Performance globale');
        const brainStats = brain.getCompleteMetrics();
        
        console.log('\n📊 === PERFORMANCE GLOBALE ===');
        console.log(`🧠 Neurones en veille: ${brainStats.state.standby.toLocaleString()}`);
        console.log(`⚡ Neurones actifs: ${brainStats.state.active.toLocaleString()}`);
        console.log(`🔗 Synapses totales: ${brainStats.state.totalSynapses.toLocaleString()}`);
        console.log(`💭 Pensées générées: ${brainStats.metrics.thoughtsGenerated.toLocaleString()}`);
        console.log(`📚 Souvenirs: ${brain.brainMemory.memories.size}`);
        console.log(`🎓 Apprentissages: ${brain.brainMemory.learnings.size}`);
        console.log(`⚡ Boost Kyber: ${brainStats.metrics.kyberBoost.toFixed(1)}x`);
        
        // Évaluation finale
        const finalScore = {
            emergence: finalEmergence.emergenceLevel > 0.5 ? 2 : finalEmergence.emergenceLevel > 0.2 ? 1 : 0,
            optimization: maxMemoryUsage < 85 ? 2 : maxMemoryUsage < 95 ? 1 : 0,
            preservation: preservationRate >= 4 ? 2 : preservationRate >= 2 ? 1 : 0,
            performance: brainStats.metrics.thoughtsGenerated > 1000 ? 2 : brainStats.metrics.thoughtsGenerated > 100 ? 1 : 0
        };
        
        const totalScore = Object.values(finalScore).reduce((sum, score) => sum + score, 0);
        
        console.log('\n🏆 === ÉVALUATION FINALE ===');
        console.log(`🌟 Émergence cognitive: ${finalScore.emergence}/2`);
        console.log(`🧠 Optimisation mémoire: ${finalScore.optimization}/2`);
        console.log(`🛡️ Préservation: ${finalScore.preservation}/2`);
        console.log(`⚡ Performance: ${finalScore.performance}/2`);
        console.log(`\n🎯 Score total: ${totalScore}/8 (${(totalScore/8*100).toFixed(0)}%)`);
        
        if (totalScore >= 7) {
            console.log('🎉 EXCELLENT - Émergence cognitive optimisée parfaitement !');
        } else if (totalScore >= 5) {
            console.log('✅ BIEN - Émergence cognitive optimisée avec succès !');
        } else if (totalScore >= 3) {
            console.log('⚠️ MOYEN - Optimisation partielle, émergence préservée');
        } else {
            console.log('❌ INSUFFISANT - Optimisation nécessaire');
        }
        
        console.log('\n✅ Tests d\'émergence cognitive optimisée terminés !');
        console.log('🧠 Le cerveau continue d\'évoluer de manière optimisée...');
        
        // Laisser tourner encore un peu pour voir l'évolution continue
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        console.log('\n🛑 Fin du test émergence optimisée');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests d\'émergence optimisée:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testOptimizedEmergence().catch(console.error);
