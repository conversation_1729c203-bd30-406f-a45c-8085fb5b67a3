/**
 * 🧪 TEST GARDE-FOU CORRIGÉ
 * Test pour vérifier que le garde-fou en boucle infinie est corrigé
 */

const PureBrainSystem = require('./pure-brain-system');

async function testGardeFouCorrige() {
    console.log('🧪 === TEST GARDE-FOU CORRIGÉ ===\n');
    
    try {
        // Initialiser le cerveau
        console.log('🚀 Initialisation du cerveau avec garde-fou corrigé...');
        const brain = new PureBrainSystem();
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 1: Vérifier l'état initial
        console.log('\n📝 Test 1: État initial du système');
        const initialMetrics = brain.memoryOptimization.getOptimizationMetrics();
        console.log(`📊 Mémoire initiale: ${initialMetrics.memoryMonitoring.currentUsage.toFixed(1)}%`);
        console.log(`🚨 Optimisations d'urgence initiales: ${initialMetrics.memoryMonitoring.emergencyActivations}`);
        console.log(`🔄 Optimisations vides consécutives: ${initialMetrics.memoryMonitoring.consecutiveEmptyOptimizations || 0}`);
        
        // Test 2: Forcer une situation de mémoire critique
        console.log('\n📝 Test 2: Simulation mémoire critique');
        
        // Activer beaucoup de neurones pour déclencher l'optimisation
        console.log('🧠 Activation massive pour déclencher optimisations...');
        await brain.activateNeurons(0.3, 10000, 'test_garde_fou_1');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await brain.activateNeurons(0.4, 8000, 'test_garde_fou_2');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await brain.activateNeurons(0.5, 6000, 'test_garde_fou_3');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test 3: Surveiller les optimisations d'urgence
        console.log('\n📝 Test 3: Surveillance optimisations d\'urgence (15 secondes)');
        
        const startTime = Date.now();
        let previousEmergencyCount = 0;
        let emergencyStoppedCount = 0;
        let maxConsecutiveEmpty = 0;
        
        while (Date.now() - startTime < 15000) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const currentMetrics = brain.memoryOptimization.getOptimizationMetrics();
            const currentEmergencyCount = currentMetrics.memoryMonitoring.emergencyActivations;
            const consecutiveEmpty = currentMetrics.memoryMonitoring.consecutiveEmptyOptimizations || 0;
            
            maxConsecutiveEmpty = Math.max(maxConsecutiveEmpty, consecutiveEmpty);
            
            if (currentEmergencyCount > previousEmergencyCount) {
                console.log(`   🚨 Optimisation d'urgence ${currentEmergencyCount} - Mémoire: ${currentMetrics.memoryMonitoring.currentUsage.toFixed(1)}% - Vides: ${consecutiveEmpty}`);
                previousEmergencyCount = currentEmergencyCount;
            }
            
            // Vérifier si les optimisations se sont arrêtées
            if (consecutiveEmpty >= 3) {
                emergencyStoppedCount++;
                console.log(`   🛑 Garde-fou activé ! Optimisations arrêtées après ${consecutiveEmpty} tentatives vides`);
                break;
            }
        }
        
        // Test 4: Vérifier l'efficacité du garde-fou
        console.log('\n📝 Test 4: Vérification efficacité garde-fou');
        const finalMetrics = brain.memoryOptimization.getOptimizationMetrics();
        
        console.log('\n🔍 === RÉSULTATS GARDE-FOU ===');
        console.log(`🚨 Optimisations d'urgence totales: ${finalMetrics.memoryMonitoring.emergencyActivations}`);
        console.log(`🔄 Maximum optimisations vides consécutives: ${maxConsecutiveEmpty}`);
        console.log(`🛑 Garde-fou activé: ${emergencyStoppedCount > 0 ? 'OUI' : 'NON'}`);
        console.log(`📊 Mémoire finale: ${finalMetrics.memoryMonitoring.currentUsage.toFixed(1)}%`);
        console.log(`⚡ Neurones actifs: ${brain.brainState.activeNeurons.size.toLocaleString()}`);
        
        // Test 5: Test de récupération après garde-fou
        console.log('\n📝 Test 5: Récupération après garde-fou');
        
        // Attendre un peu puis tenter une nouvelle activation
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log('🧠 Test activation après garde-fou...');
        const recoveryActivation = await brain.activateNeurons(0.1, 5000, 'test_recovery');
        
        if (recoveryActivation) {
            console.log('✅ Récupération réussie - Le système fonctionne après garde-fou');
        } else {
            console.log('❌ Récupération échouée - Le système reste bloqué');
        }
        
        // Test 6: Vérifier l'émergence après garde-fou
        console.log('\n📝 Test 6: Émergence après garde-fou');
        const emergenceMetrics = brain.getEmergenceMetrics();
        
        console.log(`🌟 Niveau d'émergence: ${(emergenceMetrics.emergenceLevel * 100).toFixed(1)}%`);
        console.log(`🎨 Index créativité: ${(emergenceMetrics.creativityIndex * 100).toFixed(1)}%`);
        console.log(`🔍 Patterns actifs: ${emergenceMetrics.activePatterns}`);
        
        // Évaluation finale
        console.log('\n🏆 === ÉVALUATION GARDE-FOU ===');
        
        const gardeFouScore = {
            arretOptimisations: emergencyStoppedCount > 0 ? 2 : 0,
            limiteBoucleInfinie: maxConsecutiveEmpty <= 3 ? 2 : 0,
            preserveEmergence: emergenceMetrics.emergenceLevel > 0.5 ? 2 : 1,
            recuperationSysteme: recoveryActivation ? 2 : 0
        };
        
        const totalScore = Object.values(gardeFouScore).reduce((sum, score) => sum + score, 0);
        
        console.log(`🛑 Arrêt optimisations: ${gardeFouScore.arretOptimisations}/2`);
        console.log(`🔄 Limite boucle infinie: ${gardeFouScore.limiteBoucleInfinie}/2`);
        console.log(`🌟 Préservation émergence: ${gardeFouScore.preserveEmergence}/2`);
        console.log(`🔧 Récupération système: ${gardeFouScore.recuperationSysteme}/2`);
        console.log(`\n🎯 Score total: ${totalScore}/8 (${(totalScore/8*100).toFixed(0)}%)`);
        
        if (totalScore >= 7) {
            console.log('🎉 EXCELLENT - Garde-fou parfaitement fonctionnel !');
        } else if (totalScore >= 5) {
            console.log('✅ BIEN - Garde-fou efficace !');
        } else if (totalScore >= 3) {
            console.log('⚠️ MOYEN - Garde-fou partiellement fonctionnel');
        } else {
            console.log('❌ INSUFFISANT - Garde-fou défaillant');
        }
        
        // Test 7: Recommandations
        console.log('\n📝 Test 7: Recommandations');
        
        if (emergencyStoppedCount === 0) {
            console.log('⚠️ Recommandation: Le garde-fou ne s\'est pas activé - Vérifier les seuils');
        } else {
            console.log('✅ Recommandation: Garde-fou fonctionne correctement');
        }
        
        if (maxConsecutiveEmpty > 3) {
            console.log('⚠️ Recommandation: Trop d\'optimisations vides - Ajuster la logique');
        } else {
            console.log('✅ Recommandation: Nombre d\'optimisations vides acceptable');
        }
        
        if (finalMetrics.memoryMonitoring.emergencyActivations > 50) {
            console.log('⚠️ Recommandation: Trop d\'optimisations d\'urgence - Ajuster les seuils');
        } else {
            console.log('✅ Recommandation: Nombre d\'optimisations d\'urgence raisonnable');
        }
        
        console.log('\n✅ Tests garde-fou terminés !');
        console.log('🧠 Le cerveau continue de fonctionner avec garde-fou corrigé...');
        
        // Laisser tourner encore un peu pour vérifier la stabilité
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        console.log('\n🛑 Fin du test garde-fou corrigé');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests garde-fou:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testGardeFouCorrige().catch(console.error);
