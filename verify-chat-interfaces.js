/**
 * Script de vérification complète des interfaces de chat LOUNA AI
 */

const fs = require('fs');
const path = require('path');

console.log('💬 === VÉRIFICATION COMPLÈTE INTERFACES DE CHAT ===');

// Interfaces de chat à vérifier
const chatInterfaces = [
    'chat-agents.html',
    'chat-cognitif-complet.html',
    'chat.html'
];

const appsDir = './applications-originales';
let totalIssues = 0;

chatInterfaces.forEach(chatFile => {
    console.log(`\n🔍 === ANALYSE DE ${chatFile.toUpperCase()} ===`);
    
    try {
        const filePath = path.join(appsDir, chatFile);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ ${chatFile}: Fichier non trouvé`);
            totalIssues++;
            return;
        }
        
        const content = fs.readFileSync(filePath, 'utf8');
        let fileIssues = 0;
        
        // 1. Vérifier la navigation de retour
        if (content.includes('../interface-originale-complete.html')) {
            console.log(`✅ ${chatFile}: Navigation retour OK`);
        } else {
            console.log(`❌ ${chatFile}: Navigation retour manquante`);
            fileIssues++;
        }
        
        // 2. Vérifier les API endpoints
        const apiPatterns = [
            { name: 'API Chat', pattern: /\/api\/chat/g },
            { name: 'API Cognitive', pattern: /\/api\/cognitive/g },
            { name: 'API DeepSeek', pattern: /\/api\/deepseek/g },
            { name: 'Fetch API', pattern: /fetch\(/g }
        ];
        
        console.log(`   📡 APIs détectées dans ${chatFile}:`);
        apiPatterns.forEach(api => {
            const matches = (content.match(api.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${api.name}: ${matches} appels`);
            } else {
                console.log(`   ⚠️ ${api.name}: Aucun appel`);
            }
        });
        
        // 3. Vérifier les fonctions de chat
        const chatFunctions = [
            { name: 'sendMessage', pattern: /function sendMessage/g },
            { name: 'addMessage', pattern: /function addMessage/g },
            { name: 'addUserMessage', pattern: /function addUserMessage/g },
            { name: 'addAgentMessage', pattern: /function addAgentMessage/g }
        ];
        
        console.log(`   🔧 Fonctions chat dans ${chatFile}:`);
        chatFunctions.forEach(func => {
            const matches = (content.match(func.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${func.name}: Trouvée`);
            } else {
                console.log(`   ❌ ${func.name}: Manquante`);
                fileIssues++;
            }
        });
        
        // 4. Vérifier les éléments UI
        const uiElements = [
            { name: 'Input message', pattern: /id=["'].*[Ii]nput["']/g },
            { name: 'Send button', pattern: /onclick=["'].*[Ss]end/g },
            { name: 'Messages container', pattern: /id=["'].*[Mm]essages["']/g }
        ];
        
        console.log(`   🎨 Éléments UI dans ${chatFile}:`);
        uiElements.forEach(element => {
            const matches = (content.match(element.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${element.name}: ${matches} trouvé(s)`);
            } else {
                console.log(`   ❌ ${element.name}: Manquant`);
                fileIssues++;
            }
        });
        
        // 5. Vérifier la connexion Internet
        const internetFeatures = [
            { name: 'Web Search', pattern: /search|google|bing/gi },
            { name: 'External APIs', pattern: /https?:\/\/[^"'\s]+/g },
            { name: 'CORS Headers', pattern: /cors|cross-origin/gi },
            { name: 'Network Error Handling', pattern: /network.*error|connection.*error/gi }
        ];
        
        console.log(`   🌐 Fonctionnalités Internet dans ${chatFile}:`);
        internetFeatures.forEach(feature => {
            const matches = (content.match(feature.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${feature.name}: ${matches} références`);
            } else {
                console.log(`   ⚠️ ${feature.name}: Non détecté`);
            }
        });
        
        // 6. Vérifier les agents IA
        const aiAgents = [
            { name: 'DeepSeek', pattern: /deepseek/gi },
            { name: 'Agent Local LOUNA', pattern: /agent local/gi },
            { name: 'GPT', pattern: /gpt|openai/gi },
            { name: 'Louna Agent', pattern: /louna.*agent/gi }
        ];
        
        console.log(`   🤖 Agents IA dans ${chatFile}:`);
        aiAgents.forEach(agent => {
            const matches = (content.match(agent.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${agent.name}: ${matches} références`);
            } else {
                console.log(`   ⚠️ ${agent.name}: Non détecté`);
            }
        });
        
        // 7. Vérifier les QI
        if (content.includes('QI 185') || content.includes('QI: 185')) {
            console.log(`   ✅ ${chatFile}: QI correct (185)`);
        } else if (content.includes('QI 225') || content.includes('QI: 225')) {
            console.log(`   ❌ ${chatFile}: QI incorrect (225)`);
            fileIssues++;
        } else {
            console.log(`   ⚠️ ${chatFile}: QI non spécifié`);
        }
        
        // 8. Vérifier les liens cassés
        const brokenLinkPatterns = [
            /href=["']\/[^"']*["']/g,
            /src=["']\/[^"']*["']/g,
            /action=["']\/[^"']*["']/g
        ];
        
        let brokenLinks = 0;
        brokenLinkPatterns.forEach(pattern => {
            const matches = (content.match(pattern) || []).length;
            brokenLinks += matches;
        });
        
        if (brokenLinks > 0) {
            console.log(`   ❌ ${chatFile}: ${brokenLinks} liens potentiellement cassés`);
            fileIssues++;
        } else {
            console.log(`   ✅ ${chatFile}: Aucun lien cassé détecté`);
        }
        
        // Score du fichier
        const maxChecks = 8;
        const fileScore = Math.round(((maxChecks - fileIssues) / maxChecks) * 100);
        console.log(`   📊 Score ${chatFile}: ${fileScore}%`);
        
        totalIssues += fileIssues;
        
    } catch (error) {
        console.error(`❌ Erreur analyse ${chatFile}:`, error.message);
        totalIssues++;
    }
});

// Vérifier les scripts JavaScript de chat
console.log('\n🔧 === SCRIPTS JAVASCRIPT CHAT ===');
const jsDir = path.join(appsDir, 'js');
const chatScripts = [
    'chat-cognitif-advanced.js',
    'chat-complet.js',
    'chat-fixes.js',
    'chat-hub-central.js',
    'chat-hub-master.js',
    'chat-integration-patch.js',
    'chat-multimedia-integration.js',
    'chat-ultra-complet.js'
];

let scriptsFound = 0;
chatScripts.forEach(script => {
    const scriptPath = path.join(jsDir, script);
    if (fs.existsSync(scriptPath)) {
        console.log(`✅ ${script}: Trouvé`);
        scriptsFound++;
    } else {
        console.log(`❌ ${script}: Manquant`);
        totalIssues++;
    }
});

console.log(`📊 Scripts chat: ${scriptsFound}/${chatScripts.length}`);

// Score final
console.log('\n🎯 === SCORE FINAL INTERFACES CHAT ===');
const totalChecks = 30; // Estimation du nombre total de vérifications
const chatScore = Math.round(((totalChecks - totalIssues) / totalChecks) * 100);

console.log(`📊 Score global chat: ${chatScore}%`);
console.log(`❌ Total problèmes détectés: ${totalIssues}`);

if (chatScore >= 90) {
    console.log('🎉 EXCELLENT ! Interfaces de chat parfaitement fonctionnelles !');
} else if (chatScore >= 75) {
    console.log('👍 BIEN ! Interfaces de chat fonctionnelles avec quelques améliorations possibles');
} else if (chatScore >= 50) {
    console.log('⚠️ MOYEN ! Interfaces de chat partiellement fonctionnelles');
} else {
    console.log('❌ PROBLÉMATIQUE ! Interfaces de chat nécessitent des corrections importantes');
}

console.log('\n📋 === INSTRUCTIONS POUR TESTER ===');
console.log('1. Ouvrez interface-originale-complete.html');
console.log('2. Cliquez sur "💬 Chat" pour tester chat-agents.html');
console.log('3. Testez l\'envoi de messages');
console.log('4. Vérifiez la connexion Internet (recherche web)');
console.log('5. Testez les autres interfaces de chat');

console.log('\n💬 === VÉRIFICATION CHAT TERMINÉE ===');
