/**
 * <PERSON>ript pour corriger toutes les navigations d'un coup
 */

const fs = require('fs');

// <PERSON><PERSON> le fichier
let content = fs.readFileSync('interface-originale-complete.html', 'utf8');

// Remplacer tous les onclick="openApp('/xxx.html')" par onclick="window.open('applications-originales/xxx.html', '_blank')"
content = content.replace(/onclick="openApp\('\/([^']+)'\)"/g, (match, filename) => {
    // Ajouter .html si pas présent
    if (!filename.endsWith('.html')) {
        filename += '.html';
    }
    return `onclick="window.open('applications-originales/${filename}', '_blank')"`;
});

// Sauvegarder
fs.writeFileSync('interface-originale-complete.html', content);

console.log('✅ Toutes les navigations ont été corrigées !');
