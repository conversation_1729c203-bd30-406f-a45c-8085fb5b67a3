/**
 * Script pour corriger TOUS les liens internes dans TOUTES les applications
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 === CORRECTION COMPLÈTE DE TOUS LES LIENS ===');

// Lister tous les fichiers HTML dans applications-originales
const appsDir = './applications-originales';
const files = fs.readdirSync(appsDir).filter(file => file.endsWith('.html'));

console.log(`📁 Trouvé ${files.length} applications à corriger`);

let totalCorrections = 0;
let filesModified = 0;
let errors = 0;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Sauvegarder le contenu original
        const originalContent = content;
        let fileCorrections = 0;
        
        // Corrections complètes pour tous les types de liens
        const corrections = [
            // 1. Liens vers l'accueil
            {
                name: 'Accueil href="/"',
                pattern: /href=["']\/["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // 2. Liens vers applications avec /
            {
                name: 'Applications avec /',
                pattern: /href=["']\/([^"']+\.html)["']/g,
                replacement: 'href="$1"'
            },
            // 3. JavaScript window.location vers /
            {
                name: 'JavaScript window.location /',
                pattern: /window\.location\.href\s*=\s*["']\/["']/g,
                replacement: 'window.location.href = "../interface-originale-complete.html"'
            },
            // 4. JavaScript location.href vers /
            {
                name: 'JavaScript location.href /',
                pattern: /location\.href\s*=\s*["']\/["']/g,
                replacement: 'location.href = "../interface-originale-complete.html"'
            },
            // 5. Liens vers ../
            {
                name: 'Liens ../',
                pattern: /href=["']\.\.\/["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // 6. Liens vers index.html
            {
                name: 'Liens index.html',
                pattern: /href=["']index\.html["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // 7. Liens vers ../index.html
            {
                name: 'Liens ../index.html',
                pattern: /href=["']\.\.\/index\.html["']/g,
                replacement: 'href="../interface-originale-complete.html"'
            },
            // 8. Actions JavaScript vers /
            {
                name: 'Actions JavaScript /',
                pattern: /onclick=["']window\.open\(["']\/["']/g,
                replacement: 'onclick="window.open("../interface-originale-complete.html"'
            },
            // 9. Formulaires action vers /
            {
                name: 'Formulaires action /',
                pattern: /action=["']\/["']/g,
                replacement: 'action="../interface-originale-complete.html"'
            },
            // 10. Liens avec paramètres vers /
            {
                name: 'Liens avec paramètres /',
                pattern: /href=["']\/\?([^"']*)["']/g,
                replacement: 'href="../interface-originale-complete.html?$1"'
            }
        ];
        
        // Appliquer toutes les corrections
        corrections.forEach(correction => {
            const beforeCount = (content.match(correction.pattern) || []).length;
            content = content.replace(correction.pattern, correction.replacement);
            const afterCount = (content.match(correction.pattern) || []).length;
            
            if (beforeCount > 0) {
                console.log(`   ${file}: ${beforeCount} corrections (${correction.name})`);
                fileCorrections += beforeCount;
            }
        });
        
        // Vérifier si des changements ont été faits
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ ${file} - ${fileCorrections} liens corrigés`);
            filesModified++;
            totalCorrections += fileCorrections;
        } else {
            console.log(`ℹ️ ${file} - Aucune correction nécessaire`);
        }
        
    } catch (error) {
        console.error(`❌ Erreur avec ${file}:`, error.message);
        errors++;
    }
});

console.log('\n📊 === RÉSULTATS FINAUX ===');
console.log(`✅ Fichiers modifiés: ${filesModified}`);
console.log(`🔧 Total corrections: ${totalCorrections}`);
console.log(`ℹ️ Fichiers sans changement: ${files.length - filesModified - errors}`);
console.log(`❌ Erreurs: ${errors}`);

// Vérification finale complète
console.log('\n🔍 === VÉRIFICATION FINALE COMPLÈTE ===');
let remainingIssues = 0;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Chercher tous les patterns problématiques
        const problemPatterns = [
            { name: 'href="/"', pattern: /href=["']\/["']/g },
            { name: 'href="/xxx.html"', pattern: /href=["']\/[^"']*\.html["']/g },
            { name: 'window.location="/"', pattern: /window\.location\.href\s*=\s*["']\/["']/g },
            { name: 'location.href="/"', pattern: /location\.href\s*=\s*["']\/["']/g },
            { name: 'action="/"', pattern: /action=["']\/["']/g }
        ];
        
        let fileIssues = 0;
        problemPatterns.forEach(problem => {
            const matches = content.match(problem.pattern);
            if (matches) {
                console.log(`⚠️ ${file}: ${matches.length} ${problem.name} restants`);
                fileIssues += matches.length;
            }
        });
        
        remainingIssues += fileIssues;
        
    } catch (error) {
        console.error(`❌ Erreur vérification ${file}:`, error.message);
    }
});

console.log('\n🎯 === RÉSULTAT FINAL ===');
if (remainingIssues === 0) {
    console.log('🎉 PARFAIT ! Tous les liens sont maintenant corrects !');
    console.log('🏠 Navigation complète fonctionnelle dans toutes les applications !');
    console.log('✅ Vous pouvez maintenant revenir à l\'accueil depuis n\'importe quelle application !');
} else {
    console.log(`⚠️ Il reste ${remainingIssues} liens problématiques à corriger manuellement`);
    console.log('🔧 Relancez le script ou corrigez manuellement les liens restants');
}

console.log('\n🧠 === CORRECTION NAVIGATION TERMINÉE ===');
