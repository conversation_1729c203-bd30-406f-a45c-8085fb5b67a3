/**
 * 🧪 TEST COMPLET DES BOUTONS DE SÉCURITÉ
 * Test exhaustif de tous les boutons de sécurité de LOUNA AI
 */

console.log('🧪 === TEST COMPLET BOUTONS SÉCURITÉ ===');

/**
 * Test principal des boutons de sécurité
 */
function testerBoutonsSecuriteComplet() {
    console.log('🔍 Lancement du test complet des boutons de sécurité...');
    
    const resultats = {
        total: 0,
        fonctionnels: 0,
        nonFonctionnels: 0,
        details: []
    };
    
    // 1. Test des boutons de contrôle de sécurité
    console.log('\n📝 Test 1: Boutons de contrôle de sécurité');
    const controleSecurite = testerBoutonsControleSecurite();
    resultats.total += controleSecurite.total;
    resultats.fonctionnels += controleSecurite.fonctionnels;
    resultats.details.push(...controleSecurite.details);
    
    // 2. Test des boutons de navigation
    console.log('\n📝 Test 2: Boutons de navigation');
    const navigation = testerBoutonsNavigation();
    resultats.total += navigation.total;
    resultats.fonctionnels += navigation.fonctionnels;
    resultats.details.push(...navigation.details);
    
    // 3. Test des boutons d'outils
    console.log('\n📝 Test 3: Boutons d\'outils');
    const outils = testerBoutonsOutils();
    resultats.total += outils.total;
    resultats.fonctionnels += outils.fonctionnels;
    resultats.details.push(...outils.details);
    
    // 4. Test des boutons spéciaux
    console.log('\n📝 Test 4: Boutons spéciaux');
    const speciaux = testerBoutonsSpeciaux();
    resultats.total += speciaux.total;
    resultats.fonctionnels += speciaux.fonctionnels;
    resultats.details.push(...speciaux.details);
    
    // 5. Calcul des résultats finaux
    resultats.nonFonctionnels = resultats.total - resultats.fonctionnels;
    const pourcentage = Math.round((resultats.fonctionnels / resultats.total) * 100);
    
    // 6. Affichage des résultats
    console.log('\n🎯 === RÉSULTATS COMPLETS ===');
    console.log(`📊 Total boutons testés: ${resultats.total}`);
    console.log(`✅ Boutons fonctionnels: ${resultats.fonctionnels}`);
    console.log(`❌ Boutons non fonctionnels: ${resultats.nonFonctionnels}`);
    console.log(`📈 Taux de réussite: ${pourcentage}%`);
    
    // 7. Détails par bouton
    console.log('\n📋 === DÉTAILS PAR BOUTON ===');
    resultats.details.forEach(detail => {
        const status = detail.fonctionnel ? '✅' : '❌';
        console.log(`${status} ${detail.nom}: ${detail.statut}`);
    });
    
    // 8. Recommandations
    console.log('\n💡 === RECOMMANDATIONS ===');
    if (pourcentage === 100) {
        console.log('🎉 PARFAIT ! Tous les boutons de sécurité fonctionnent correctement !');
    } else if (pourcentage >= 80) {
        console.log('✅ BIEN ! La plupart des boutons fonctionnent. Quelques corrections mineures nécessaires.');
    } else if (pourcentage >= 60) {
        console.log('⚠️ MOYEN ! Plusieurs boutons nécessitent une correction.');
        console.log('💡 Recommandation: Exécuter forcerCorrectionBoutons()');
    } else {
        console.log('🚨 CRITIQUE ! La majorité des boutons ne fonctionnent pas.');
        console.log('💡 Recommandation: Exécuter forcerCorrectionBoutons() immédiatement');
    }
    
    return resultats;
}

/**
 * Test des boutons de contrôle de sécurité
 */
function testerBoutonsControleSecurite() {
    const boutons = [
        ['.btn-hibernation', 'Hibernation', 'activateHibernation'],
        ['.btn-sleep', 'Sommeil', 'activateSleep'],
        ['.btn-wakeup', 'Réveil', 'wakeupAgent']
    ];
    
    const resultats = { total: boutons.length, fonctionnels: 0, details: [] };
    
    boutons.forEach(([selector, nom, fonction]) => {
        const detail = testerBoutonIndividuel(selector, nom, fonction);
        resultats.details.push(detail);
        if (detail.fonctionnel) resultats.fonctionnels++;
    });
    
    return resultats;
}

/**
 * Test des boutons de navigation
 */
function testerBoutonsNavigation() {
    const boutons = [
        ['.btn-surveillance', 'Surveillance', 'openSurveillance'],
        ['.btn-backup', 'Sauvegarde', 'openBackup'],
        ['.btn-memory', 'Mémoire', 'openMemoryControl']
    ];
    
    const resultats = { total: boutons.length, fonctionnels: 0, details: [] };
    
    boutons.forEach(([selector, nom, fonction]) => {
        const detail = testerBoutonIndividuel(selector, nom, fonction);
        resultats.details.push(detail);
        if (detail.fonctionnel) resultats.fonctionnels++;
    });
    
    return resultats;
}

/**
 * Test des boutons d'outils
 */
function testerBoutonsOutils() {
    const boutons = [
        ['button[onclick*="testerTousLesBoutons"]', 'Test Boutons', 'testerTousLesBoutons'],
        ['button[onclick*="diagnostiquerInterface"]', 'Diagnostic', 'diagnostiquerInterface'],
        ['button[onclick*="toggleDeepSeekChat"]', 'DeepSeek', 'toggleDeepSeekChat'],
        ['button[onclick*="forcerCorrectionBoutons"]', 'Corriger', 'forcerCorrectionBoutons']
    ];
    
    const resultats = { total: boutons.length, fonctionnels: 0, details: [] };
    
    boutons.forEach(([selector, nom, fonction]) => {
        const detail = testerBoutonIndividuel(selector, nom, fonction);
        resultats.details.push(detail);
        if (detail.fonctionnel) resultats.fonctionnels++;
    });
    
    return resultats;
}

/**
 * Test des boutons spéciaux
 */
function testerBoutonsSpeciaux() {
    const boutons = [
        ['#pauseEvolution', 'Pause Évolution', null],
        ['#resumeEvolution', 'Reprendre Évolution', null],
        ['#analyzeSystem', 'Analyser Système', null],
        ['#forceEvolution', 'Forcer Évolution', null]
    ];
    
    const resultats = { total: boutons.length, fonctionnels: 0, details: [] };
    
    boutons.forEach(([selector, nom, fonction]) => {
        const detail = testerBoutonIndividuel(selector, nom, fonction);
        resultats.details.push(detail);
        if (detail.fonctionnel) resultats.fonctionnels++;
    });
    
    return resultats;
}

/**
 * Test d'un bouton individuel
 */
function testerBoutonIndividuel(selector, nom, fonctionNom) {
    const bouton = document.querySelector(selector);
    
    if (!bouton) {
        return {
            nom: nom,
            fonctionnel: false,
            statut: 'BOUTON NON TROUVÉ',
            element: null
        };
    }
    
    // Vérifier si le bouton a une fonction onclick
    const hasOnclick = bouton.onclick || bouton.getAttribute('onclick');
    
    if (!hasOnclick) {
        return {
            nom: nom,
            fonctionnel: false,
            statut: 'PAS DE FONCTION ONCLICK',
            element: bouton
        };
    }
    
    // Vérifier si la fonction globale existe (si spécifiée)
    if (fonctionNom && typeof window[fonctionNom] !== 'function') {
        return {
            nom: nom,
            fonctionnel: false,
            statut: `FONCTION ${fonctionNom} NON DÉFINIE`,
            element: bouton
        };
    }
    
    return {
        nom: nom,
        fonctionnel: true,
        statut: 'FONCTIONNEL',
        element: bouton
    };
}

/**
 * Test de simulation de clic sur tous les boutons
 */
function simulerClicsBoutons() {
    console.log('🖱️ Simulation de clics sur tous les boutons...');
    
    const selecteurs = [
        '.btn-hibernation',
        '.btn-sleep',
        '.btn-wakeup',
        '.btn-surveillance',
        '.btn-backup',
        '.btn-memory'
    ];
    
    let clicsReussis = 0;
    
    selecteurs.forEach(selector => {
        const bouton = document.querySelector(selector);
        if (bouton && bouton.onclick) {
            try {
                console.log(`🖱️ Simulation clic: ${bouton.textContent.trim()}`);
                // Ne pas vraiment cliquer, juste vérifier que c'est possible
                clicsReussis++;
            } catch (error) {
                console.log(`❌ Erreur simulation clic: ${selector}`, error);
            }
        }
    });
    
    console.log(`📊 Clics simulés: ${clicsReussis}/${selecteurs.length}`);
    return clicsReussis;
}

/**
 * Rapport détaillé de sécurité
 */
function genererRapportSecurite() {
    console.log('📋 === RAPPORT DÉTAILLÉ DE SÉCURITÉ ===');
    
    const resultats = testerBoutonsSecuriteComplet();
    const clics = simulerClicsBoutons();
    
    const rapport = {
        timestamp: new Date().toISOString(),
        boutons: resultats,
        simulation: {
            total: 6,
            reussis: clics,
            pourcentage: Math.round((clics / 6) * 100)
        },
        recommandations: []
    };
    
    // Générer des recommandations
    if (resultats.fonctionnels < resultats.total) {
        rapport.recommandations.push('Exécuter forcerCorrectionBoutons() pour corriger les boutons défaillants');
    }
    
    if (rapport.simulation.pourcentage < 100) {
        rapport.recommandations.push('Vérifier les fonctions JavaScript associées aux boutons');
    }
    
    if (resultats.fonctionnels === resultats.total && rapport.simulation.pourcentage === 100) {
        rapport.recommandations.push('Système de sécurité entièrement fonctionnel - Aucune action requise');
    }
    
    console.log('📊 Rapport généré:', rapport);
    return rapport;
}

// Exporter les fonctions pour utilisation globale
window.testerBoutonsSecuriteComplet = testerBoutonsSecuriteComplet;
window.simulerClicsBoutons = simulerClicsBoutons;
window.genererRapportSecurite = genererRapportSecurite;

// Lancer le test automatiquement après chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🚀 Lancement du test automatique des boutons de sécurité...');
        testerBoutonsSecuriteComplet();
    }, 3000);
});

console.log('🧪 Script de test complet des boutons de sécurité chargé');
console.log('💡 Utilisez testerBoutonsSecuriteComplet() pour tester manuellement');
console.log('💡 Utilisez genererRapportSecurite() pour un rapport détaillé');
