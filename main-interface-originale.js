/**
 * 🧠 LOUNA AI ULTRA-AUTONOME - INTERFACE ORIGINALE COMPLÈTE
 * Avec votre vraie interface + modifications DeepSeek + Mémoire Thermique
 */

const { app, BrowserWindow } = require('electron');
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const fs = require('fs');
const path = require('path');

// Configuration
const PORT = 52797;
const expressApp = express();
const server = http.createServer(expressApp);
const io = socketIo(server);
let mainWindow;

// Charger les modules de ce matin
let DeepSeekConnector;
let ThermalMemory;

try {
    DeepSeekConnector = require('./modules/deepseek-direct-connector');
    console.log('✅ Module DeepSeek chargé');
} catch (error) {
    console.log('⚠️ Module DeepSeek non trouvé, utilisation du fallback');
}

try {
    ThermalMemory = require('./thermal-memory-system');
    console.log('✅ Module Mémoire Thermique chargé');
} catch (error) {
    console.log('⚠️ Module Mémoire Thermique non trouvé, utilisation du fallback');
}

// Initialiser la mémoire thermique avec vos vraies données
let thermalMemory;
let deepSeekConnector;
let systemMetrics = {
    neurones: 1064012,
    temperature: 37.2,
    memoire: 7448045,
    pensees: 0,
    energie: 85.4,
    formations: 14,
    connexionsDeepSeek: 0,
    mobiusActif: true
};

// Charger les vraies données de fusion
async function chargerDonneesReelles() {
    try {
        // Charger les données de fusion thermique
        if (fs.existsSync('./data/memory/thermal_fusion_expansion.json')) {
            const thermalData = JSON.parse(fs.readFileSync('./data/memory/thermal_fusion_expansion.json', 'utf8'));
            systemMetrics.neurones = thermalData.memoryState?.neurogenesis || systemMetrics.neurones;
            systemMetrics.memoire = thermalData.memoryState?.memory?.totalEntries || systemMetrics.memoire;
            systemMetrics.formations = thermalData.formationDirecte?.formationsInjectees || systemMetrics.formations;
            console.log('✅ Données thermiques chargées');
        }

        // Initialiser la mémoire thermique
        if (ThermalMemory) {
            thermalMemory = new ThermalMemory({
                dataPath: './data/memory',
                zonesPath: './MEMOIRE-REELLE/zones-thermiques',
                temperature: systemMetrics.temperature
            });
            await thermalMemory.init();
            console.log('✅ Mémoire thermique initialisée');
        }

        // Initialiser DeepSeek R1 8B
        if (DeepSeekConnector && thermalMemory) {
            deepSeekConnector = new DeepSeekConnector(thermalMemory, {
                model: 'deepseek-r1:8b',
                temperature: 0.7,
                maxTokens: 2048,
                ollamaUrl: 'http://localhost:11434'
            });
            console.log('✅ DeepSeek R1 8B connecté à la mémoire thermique');
        }

    } catch (error) {
        console.error('❌ Erreur chargement données:', error.message);
    }
}

// Middleware Express
expressApp.use(express.json());
expressApp.use(express.static('./'));
expressApp.use('/applications', express.static('./applications-originales'));

// Route principale - Votre interface originale
expressApp.get('/', (req, res) => {
    try {
        // Lire votre interface originale
        const interfaceOriginale = fs.readFileSync('./interface-originale-complete.html', 'utf8');
        
        // Injecter les modifications DeepSeek et mémoire thermique
        const interfaceModifiee = interfaceOriginale.replace(
            '</body>',
            `
            <!-- MODIFICATIONS DEEPSEEK + MÉMOIRE THERMIQUE -->
            <div id="deepseek-integration" style="position: fixed; top: 20px; right: 20px; background: rgba(0,0,0,0.8); border: 2px solid #00ff88; border-radius: 10px; padding: 1rem; backdrop-filter: blur(10px); z-index: 9999;">
                <div style="display: flex; align-items: center; gap: 0.5rem; color: #00ff88; font-weight: bold;">
                    <div style="width: 10px; height: 10px; border-radius: 50%; background: #00ff88; animation: pulse 2s infinite;"></div>
                    <span>DeepSeek R1 8B</span>
                </div>
                <div style="font-size: 0.8rem; color: #cccccc;">
                    Connexions: <span id="deepseekConnections">${systemMetrics.connexionsDeepSeek}</span>
                </div>
                <div style="font-size: 0.8rem; color: #cccccc;">
                    Température: <span id="thermalTemp">${systemMetrics.temperature}°C</span>
                </div>
            </div>

            <script>
                // Intégration DeepSeek dans votre interface originale
                console.log('🧠 LOUNA AI avec DeepSeek R1 8B et Mémoire Thermique');
                console.log('✅ Interface originale + modifications de ce matin');
                
                // API pour DeepSeek
                async function envoyerMessageDeepSeek(message, context = {}) {
                    try {
                        const response = await fetch('/api/deepseek/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                message: message,
                                context: {
                                    temperature: ${systemMetrics.temperature},
                                    neurones: ${systemMetrics.neurones},
                                    ...context
                                }
                            })
                        });
                        
                        const data = await response.json();
                        
                        if (data.success) {
                            // Mettre à jour le compteur de connexions
                            document.getElementById('deepseekConnections').textContent = 
                                parseInt(document.getElementById('deepseekConnections').textContent) + 1;
                            
                            return data;
                        } else {
                            console.error('Erreur DeepSeek:', data.error);
                            return { success: false, error: data.error };
                        }
                        
                    } catch (error) {
                        console.error('Erreur connexion DeepSeek:', error);
                        return { success: false, error: error.message };
                    }
                }
                
                // Mise à jour des métriques en temps réel
                setInterval(async () => {
                    try {
                        const response = await fetch('/api/metrics');
                        const data = await response.json();
                        
                        if (data.success) {
                            document.getElementById('thermalTemp').textContent = data.temperature + '°C';
                            
                            // Mettre à jour les métriques dans l'interface originale si les éléments existent
                            const neuroneElement = document.querySelector('[data-metric="neurones"]');
                            if (neuroneElement) {
                                neuroneElement.textContent = data.neurones.toLocaleString();
                            }
                            
                            const memoireElement = document.querySelector('[data-metric="memoire"]');
                            if (memoireElement) {
                                memoireElement.textContent = data.memoire.toLocaleString();
                            }
                            
                            const tempElement = document.querySelector('[data-metric="temperature"]');
                            if (tempElement) {
                                tempElement.textContent = data.temperature + '°C';
                            }
                        }
                    } catch (error) {
                        console.log('Mise à jour métriques en mode hors ligne');
                    }
                }, 3000);
                
                // Exposer la fonction DeepSeek globalement pour votre interface
                window.lounaDeepSeek = envoyerMessageDeepSeek;
                
                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.5; }
                }
            </script>
            </body>`
        );
        
        res.send(interfaceModifiee);
        
    } catch (error) {
        console.error('Erreur lecture interface:', error);
        res.status(500).send('Erreur chargement interface');
    }
});

// API pour DeepSeek
expressApp.post('/api/deepseek/chat', async (req, res) => {
    try {
        const { message, context } = req.body;
        
        if (deepSeekConnector) {
            const response = await deepSeekConnector.chat(message, context);
            systemMetrics.connexionsDeepSeek++;
            
            res.json({
                success: true,
                response: response.content,
                model: 'deepseek-r1:8b',
                thermalIntegration: response.thermalIntegration,
                mobiusIntegration: response.mobiusIntegration,
                tokensUsed: response.tokensUsed,
                responseTime: response.responseTime
            });
        } else {
            res.json({
                success: false,
                error: 'DeepSeek non disponible'
            });
        }
    } catch (error) {
        res.json({
            success: false,
            error: error.message
        });
    }
});

// API métriques avec données réelles
expressApp.get('/api/metrics', (req, res) => {
    res.json({
        success: true,
        ...systemMetrics,
        timestamp: new Date().toISOString(),
        thermalMemoryActive: !!thermalMemory,
        deepSeekActive: !!deepSeekConnector,
        mobiusThoughts: thermalMemory?.thoughtQueue?.length || 0,
        thermalZones: thermalMemory?.getActiveZones?.() || []
    });
});

// Créer la fenêtre Electron
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        },
        title: '🧠 LOUNA AI Ultra-Autonome - Interface Originale + DeepSeek R1 8B',
        show: false
    });
    
    mainWindow.loadURL(`http://localhost:${PORT}`);
    
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('🧠 LOUNA AI Ultra-Autonome avec interface originale affiché !');
    });
}

// Démarrage de l'application
app.whenReady().then(async () => {
    await chargerDonneesReelles();
    
    server.listen(PORT, () => {
        console.log(`🧠 === LOUNA AI INTERFACE ORIGINALE + MODIFICATIONS ===`);
        console.log(`🌐 Interface: http://localhost:${PORT}`);
        console.log(`🧠 Neurones: ${systemMetrics.neurones.toLocaleString()}`);
        console.log(`🌡️ Température: ${systemMetrics.temperature}°C`);
        console.log(`💾 Mémoire: ${systemMetrics.memoire.toLocaleString()} entrées`);
        console.log(`🎓 Formations: ${systemMetrics.formations}`);
        console.log(`🤖 DeepSeek R1 8B: ${deepSeekConnector ? 'CONNECTÉ' : 'STANDBY'}`);
        console.log(`💾 Mémoire Thermique: ${thermalMemory ? 'ACTIVE' : 'STANDBY'}`);
        console.log(`🔄 Système Möbius: ${systemMetrics.mobiusActif ? 'ACTIF' : 'INACTIF'}`);
        console.log(`✅ VOTRE INTERFACE ORIGINALE AVEC TOUTES LES MODIFICATIONS !`);
    });
    
    createWindow();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
