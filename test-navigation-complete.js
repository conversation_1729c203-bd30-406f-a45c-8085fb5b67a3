/**
 * Script de test complet de la navigation LOUNA AI
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 === TEST COMPLET DE NAVIGATION LOUNA AI ===');

// Test 1: Vérifier l'interface principale
console.log('\n1️⃣ === TEST INTERFACE PRINCIPALE ===');
try {
    const mainInterface = fs.readFileSync('./interface-originale-complete.html', 'utf8');
    
    // Compter les liens vers applications
    const appLinks = (mainInterface.match(/window\.open\('applications-originales\//g) || []).length;
    console.log(`✅ Interface principale: ${appLinks} liens vers applications détectés`);
    
    // Vérifier DeepSeek
    if (mainInterface.includes('DeepSeek R1 8B')) {
        console.log('✅ DeepSeek R1 8B intégré');
    } else {
        console.log('❌ DeepSeek R1 8B manquant');
    }
    
    // Vérifier QI
    if (mainInterface.includes('QI: 185')) {
        console.log('✅ QI Jean-Luc correct (185)');
    } else {
        console.log('❌ QI incorrect');
    }
    
} catch (error) {
    console.log('❌ Erreur lecture interface principale:', error.message);
}

// Test 2: Vérifier toutes les applications
console.log('\n2️⃣ === TEST APPLICATIONS ===');
const appsDir = './applications-originales';
const files = fs.readdirSync(appsDir).filter(file => file.endsWith('.html'));

let appsWithHomeLink = 0;
let appsWithoutHomeLink = 0;
let totalApps = files.length;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Vérifier lien retour accueil
        if (content.includes('../interface-originale-complete.html')) {
            appsWithHomeLink++;
        } else {
            appsWithoutHomeLink++;
            console.log(`⚠️ ${file}: Pas de lien retour accueil`);
        }
        
    } catch (error) {
        console.log(`❌ Erreur lecture ${file}:`, error.message);
    }
});

console.log(`✅ Applications avec lien retour: ${appsWithHomeLink}/${totalApps}`);
console.log(`⚠️ Applications sans lien retour: ${appsWithoutHomeLink}/${totalApps}`);

// Test 3: Vérifier les liens internes
console.log('\n3️⃣ === TEST LIENS INTERNES ===');
let brokenLinks = 0;
let goodLinks = 0;

files.forEach(file => {
    try {
        const filePath = path.join(appsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Chercher les liens problématiques
        const badPatterns = [
            /href=["']\/[^"']*["']/g,
            /window\.location\.href\s*=\s*["']\/["']/g,
            /location\.href\s*=\s*["']\/["']/g
        ];
        
        let fileBrokenLinks = 0;
        badPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                fileBrokenLinks += matches.length;
            }
        });
        
        if (fileBrokenLinks > 0) {
            brokenLinks += fileBrokenLinks;
            console.log(`⚠️ ${file}: ${fileBrokenLinks} liens cassés`);
        } else {
            goodLinks++;
        }
        
    } catch (error) {
        console.log(`❌ Erreur vérification ${file}:`, error.message);
    }
});

console.log(`✅ Applications avec liens corrects: ${goodLinks}/${totalApps}`);
console.log(`❌ Total liens cassés: ${brokenLinks}`);

// Test 4: Applications clés
console.log('\n4️⃣ === TEST APPLICATIONS CLÉS ===');
const keyApps = [
    'chat-agents.html',
    'generation-center.html',
    'brain-dashboard-live.html',
    'kyber-dashboard.html',
    'phone-camera-system.html',
    'thermal-memory-dashboard.html'
];

keyApps.forEach(app => {
    const filePath = path.join(appsDir, app);
    if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.includes('../interface-originale-complete.html')) {
            console.log(`✅ ${app}: Navigation OK`);
        } else {
            console.log(`❌ ${app}: Navigation cassée`);
        }
    } else {
        console.log(`❌ ${app}: Fichier manquant`);
    }
});

// Test 5: Score final
console.log('\n🎯 === SCORE FINAL ===');
const navigationScore = Math.round((appsWithHomeLink / totalApps) * 100);
const linksScore = Math.round(((totalApps - (brokenLinks > 0 ? 1 : 0)) / totalApps) * 100);
const overallScore = Math.round((navigationScore + linksScore) / 2);

console.log(`📊 Score navigation: ${navigationScore}% (${appsWithHomeLink}/${totalApps} apps)`);
console.log(`🔗 Score liens: ${linksScore}% (${brokenLinks} liens cassés)`);
console.log(`🏆 Score global: ${overallScore}%`);

if (overallScore >= 95) {
    console.log('🎉 EXCELLENT ! Navigation parfaitement fonctionnelle !');
} else if (overallScore >= 80) {
    console.log('👍 BIEN ! Navigation fonctionnelle avec quelques améliorations possibles');
} else if (overallScore >= 60) {
    console.log('⚠️ MOYEN ! Navigation partiellement fonctionnelle');
} else {
    console.log('❌ PROBLÉMATIQUE ! Navigation nécessite des corrections importantes');
}

// Instructions finales
console.log('\n📋 === INSTRUCTIONS POUR TESTER ===');
console.log('1. Ouvrez interface-originale-complete.html');
console.log('2. Cliquez sur n\'importe quelle application');
console.log('3. Dans l\'application, cliquez sur "Accueil" pour revenir');
console.log('4. Testez plusieurs applications pour vérifier');

console.log('\n🧠 === TEST NAVIGATION TERMINÉ ===');
