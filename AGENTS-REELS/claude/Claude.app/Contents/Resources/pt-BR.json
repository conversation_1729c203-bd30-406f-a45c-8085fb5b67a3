{"+/cwsayrqk": "Tamanho real", "+7sd9hoyZA": "Copiar", "/PgA81GVOD": "<PERSON><PERSON>", "/bRGKhnXQ6": "Uma nova versão está disponível. Ela será baixada e instalada automaticamente.", "0g8/VVdNuN": "Acesse agent local.ai/settings para configurar seu perfil, equipe e muito mais.", "0tZLEYF8mJ": "<PERSON><PERSON><PERSON><PERSON>", "1HUTYwndT2": "<PERSON><PERSON>", "1PfZLi/OV7": "G<PERSON>", "1TJUzU26sO": "Enviar feedback...", "25aCMlTDUq": "Iniciar o Agent Local LOUNA automaticamente ao fazer login", "3ML3xT+gEV": "<PERSON><PERSON><PERSON>", "3gG1j3kRBX": "Enviar feedback...", "3unrKzH4zB": "Copiar", "4qP7MjrQfC": "Variáveis de ambiente", "5DUIVR3fVi": "Sobre...", "6yv8ytK4El": "Verifique sua conexão de rede", "7fdcqxofEs": "<PERSON><PERSON>", "7gSC+rZzXX": "Abra o Agent Local LOUNA rapidamente em qualquer lugar", "8YQEOfuaGO": "Selecionar tudo", "9+afSO9e/t": "Falha ao verificar atualizações: {error}", "9uNxNtcrFI": "Instalar", "CZwl8X2D85": "Opções avançadas", "CizRPROPWo": "Ajuda do Agent Local LOUNA", "D43DeqP+2t": "Configurações do Agent Local LOUNA", "D4DyT6MmPy": "Não foi possível carregar as configurações do aplicativo", "DQTgg21B7g": "Mostrar aplicativo", "E9jYTa7AbX": "Área de Notificação", "EfdnINFnIz": "Arquivo", "GSG5S0ysrR": "Executar ao iniciar", "HeHYq6bbS2": "O Agent Local LOUNA usa o Protocolo de Contexto do Modelo para receber informações como instruções e anexos de servidores especializados.", "I5O68ogAtr": "<PERSON><PERSON><PERSON>", "JVwNvMZjVT": "Colar", "KAo3lt5Hv+": "Colar", "Ko/2Ml7mZG": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "L32WRR6NOL": "<PERSON><PERSON><PERSON>", "L717supPIA": "Configurações", "LCWUQ/4Fu6": "<PERSON>er", "NZIwKxgxJ+": "Quer mesmo excluir o servidor MCP \"{serverKey}\"?", "Nmvo1ufAY5": "Não foi possível conectar ao Agent Local LOUNA", "O3rtEd7aMd": "Localizar", "ODySlGptaj": "Configurações...", "PH29MShDiy": "<PERSON><PERSON><PERSON><PERSON>", "PW5U8NgTto": "Abrir arquivo de registro do MCP...", "PZtcoAOSsa": "<PERSON><PERSON> ativar", "PbJ4jR0kv1": "Você está na versão mais recente.", "RTg057HE1D": "Mostrar as ferramentas de desenvolvimento", "S3MXlbjkax": "Em que posso ajudar você hoje?", "S3k5yXss2r": "<PERSON>ers<PERSON> {version}", "TH+W2Ad73P": "Recortar", "UJCjEVPX6Q": "Procurar", "Vvus2ifAny": "Editar configura<PERSON>", "W1pELwt/+a": "O Agent Local LOUNA está em execução na Área de Avisos", "WBvq3HlPae": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WF1HSu0jAC": "Abrir pasta de registros", "WZe86KSdrM": "Adicionar ao dicionário", "WlhIx7DfFO": "OK", "XPIoFTkh3e": "Nenhuma atualização disponível", "XZ36+EBE5/": "Afastar", "XinCguXCgN": "<PERSON><PERSON> mais", "YTdYCYAf/Z": "Exibir Agent Local LOUNA na barra de menu", "Z9g5m/V9Nq": "Aproximar", "ZJZN1+KyJw": "Configurações...", "aNmxuDcWaU": "Erro", "aXdFLiVzjd": "<PERSON><PERSON> janela principal", "arbRxbtBkP": "Voltar", "baGq3gy8z1": "Nova conversa", "dKX0bpR+a2": "<PERSON><PERSON>", "dLyz0Srosd": "<PERSON><PERSON><PERSON><PERSON>", "fEeEFfSz4K": "Aproximar (versão mais legal)", "fFJxOwJRj2": "<PERSON><PERSON><PERSON>", "fWDSQQgRO5": "Barra de menus", "iFRmqBsr1N": "Sobre o Agent Local LOUNA", "ilE9e0uxNN": "<PERSON><PERSON><PERSON><PERSON>", "j66cdL4EK5": "Abrir documentação", "jd5ZNrRMNP": "Você é um dos pioneiros; dê seu feedback.", "k+06oXbIas": "Mostrar Agent Local LOUNA na área de notificação", "kYwW0OsI4M": "O Agent Local LOUNA Desktop está em beta.", "m3GfpKD1WX": "<PERSON><PERSON><PERSON><PERSON>", "mRXjxhS6p4": "<PERSON>eca<PERSON>ual<PERSON>...", "ngLpGT7bUJ": "Não foi possí<PERSON> carregar as configurações de desenvolvedor", "oQuOiX24pp": "<PERSON><PERSON>", "pWXxZASpOB": "<PERSON><PERSON><PERSON>", "pgaCSv2/6H": "Argumentos", "q4hs14B00V": "<PERSON><PERSON>conhe<PERSON>", "rNAd+HxSK4": "Abrir arquivo de registros do MCP", "rY99UXvTDU": "Copiar Imagem", "rdiPpQVqvY": "O modo desenvolvedor dá acesso a ferramentas de desenvolvimento e recursos de depuração. Ative apenas se você dominar essas áreas.", "rwFEudHXey": "Ocorreu um erro ao ler ou analisar agent local_desktop_config.json: {error}", "sNnRQsIEYz": "Localizar na página", "sZxWXq9BzJ": "Dar feedback", "sys7RHphmL": "<PERSON><PERSON><PERSON>", "tWutslc/9Z": "Ativar", "u1/hT7oRQY": "Atalho no teclado para acesso rápido", "uc3dnSo+eo": "Arquivo", "urCd4k/cE0": "Comand<PERSON>", "vgLHPxjh9O": "Ativar modo desenvolvedor", "wS64bVG2CO": "O MCP é um protocolo que dá segurança às conexões entre clientes, como o aplicativo Agent Local LOUNA Desktop, e serviços locais.", "xJs1jZ8PoA": "O Agent Local LOUNA continua em execução em segundo plano mesmo quando você fecha a janela. Clique no ícone do Agent Local LOUNA na bandeja para reabrir o aplicativo ou clique com o botão direito para sair.", "xKRKzVVy9c": "Configurar", "xd436TVDRZ": "Ocorreu um erro ao ler ou analisar developer_settings.json: {error}", "y9tCbmRzHN": "Ativar modo desenvolvedor?", "ytjMRobdyL": "Atualização disponível", "zAYm/Z684h": "<PERSON><PERSON><PERSON>", "zCIK9K8J4a": "Erro", "zSP70MVzIo": "<PERSON><PERSON>"}