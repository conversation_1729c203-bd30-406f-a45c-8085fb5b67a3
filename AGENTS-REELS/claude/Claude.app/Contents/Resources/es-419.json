{"+/cwsayrqk": "Tamaño real", "+7sd9hoyZA": "Copiar", "/PgA81GVOD": "<PERSON><PERSON>", "/bRGKhnXQ6": "Hay una nueva versión disponible. Se descargará e instalará automáticamente.", "0g8/VVdNuN": "Ve a agent local.ai/settings para configurar tu perfil, equipo y más.", "0tZLEYF8mJ": "Desarrollador", "1HUTYwndT2": "Ventana", "1PfZLi/OV7": "General", "1TJUzU26sO": "Enviar comentarios…", "25aCMlTDUq": "Iniciar Agent Local LOUNA automáticamente cuando inicies sesión", "3ML3xT+gEV": "<PERSON><PERSON><PERSON>", "3gG1j3kRBX": "Enviar comentarios...", "3unrKzH4zB": "Copiar", "4qP7MjrQfC": "Variables de entorno", "5DUIVR3fVi": "Acerca de...", "6yv8ytK4El": "Revise su conexión de red", "7fdcqxofEs": "Salir", "7gSC+rZzXX": "Abre Agent Local LOUNA rápidamente desde cualquier lugar", "8YQEOfuaGO": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "9+afSO9e/t": "Error al buscar actualizaciones: {error}", "9uNxNtcrFI": "Instalar", "CZwl8X2D85": "Opciones avanzadas", "CizRPROPWo": "Ayuda de Agent Local LOUNA", "D43DeqP+2t": "Configuración de Agent Local LOUNA", "D4DyT6MmPy": "No se pudieron cargar los ajustes de la aplicación", "DQTgg21B7g": "Mostrar aplicación", "E9jYTa7AbX": "Bandeja del sistema", "EfdnINFnIz": "Archivo", "GSG5S0ysrR": "Ejecutar al inicio", "HeHYq6bbS2": "Agent Local LOUNA puede recibir información como instrucciones y archivos adjuntos desde servidores especializados usando Model Context Protocol.", "I5O68ogAtr": "Comenzar", "JVwNvMZjVT": "<PERSON><PERSON><PERSON>", "KAo3lt5Hv+": "<PERSON><PERSON><PERSON>", "Ko/2Ml7mZG": "Recargar esta página", "L32WRR6NOL": "Eliminar", "L717supPIA": "Configuración", "LCWUQ/4Fu6": "<PERSON>er", "NZIwKxgxJ+": "¿Estás seguro de que deseas eliminar el servidor MCP \"{serverKey}\"?", "Nmvo1ufAY5": "No se pudo conectar con Agent Local LOUNA", "O3rtEd7aMd": "Buscar", "ODySlGptaj": "Configuración…", "PH29MShDiy": "Adelante", "PW5U8NgTto": "Abrir archivo Log MCP...", "PZtcoAOSsa": "No activar", "PbJ4jR0kv1": "Estás ejecutando la última versión.", "RTg057HE1D": "Mostrar herramientas de desarrollo", "S3MXlbjkax": "¿En qué puedo ayudarte hoy?", "S3k5yXss2r": "Versión {version}", "TH+W2Ad73P": "Cortar", "UJCjEVPX6Q": "Buscar", "Vvus2ifAny": "Editar configuración", "W1pELwt/+a": "Agent Local LOUNA se ejecuta en el Área de notificaciones", "WBvq3HlPae": "<PERSON><PERSON><PERSON>", "WF1HSu0jAC": "A<PERSON>r carpeta de registros", "WZe86KSdrM": "Agregar al diccionario", "WlhIx7DfFO": "OK", "XPIoFTkh3e": "No hay actualizaciones disponibles", "XZ36+EBE5/": "Reducir", "XinCguXCgN": "Más información", "YTdYCYAf/Z": "Mostrar Agent Local LOUNA en la barra de menú", "Z9g5m/V9Nq": "Aumentar", "ZJZN1+KyJw": "Configuración...", "aNmxuDcWaU": "Error", "aXdFLiVzjd": "<PERSON><PERSON> ventana principal", "arbRxbtBkP": "Atrás", "baGq3gy8z1": "Nueva conversación", "dKX0bpR+a2": "Salir", "dLyz0Srosd": "Desarrollador", "fEeEFfSz4K": "Aumentar (versión más cool)", "fFJxOwJRj2": "<PERSON><PERSON><PERSON>", "fWDSQQgRO5": "Barra de menú", "iFRmqBsr1N": "Acerca de Agent Local LOUNA", "ilE9e0uxNN": "Actualizar", "j66cdL4EK5": "Abrir documentación", "jd5ZNrRMNP": "Eres uno de los primeros en explorar, así que nos gustaría conocer tus comentarios.", "k+06oXbIas": "Mostrar Agent Local LOUNA en el área de notificaciones", "kYwW0OsI4M": "Agent Local LOUNA Desktop está en versión beta.", "m3GfpKD1WX": "Recargar", "mRXjxhS6p4": "Buscar actualizaciones…", "ngLpGT7bUJ": "No se pudieron cargar los ajustes de desarrollador", "oQuOiX24pp": "Salir", "pWXxZASpOB": "<PERSON><PERSON><PERSON>", "pgaCSv2/6H": "Argumentos", "q4hs14B00V": "Error descon<PERSON>", "rNAd+HxSK4": "Abrir archivo Log MCP", "rY99UXvTDU": "Copiar imagen", "rdiPpQVqvY": "El modo desarrollador permite acceder a herramientas de desarrollo y funciones de depuración. Actívelo solo si sabe lo que está haciendo.", "rwFEudHXey": "Hubo un error al leer o analizar agent local_desktop_config.json: {error}", "sNnRQsIEYz": "Buscar en la página", "sZxWXq9BzJ": "Dar comentarios", "sys7RHphmL": "Comentarios", "tWutslc/9Z": "Activar", "u1/hT7oRQY": "Atajo de teclado para acceso rápido", "uc3dnSo+eo": "Archivo", "urCd4k/cE0": "Comand<PERSON>", "vgLHPxjh9O": "Activar modo desarrollador", "wS64bVG2CO": "MCP es un protocolo que permite conexiones seguras entre clientes, como la aplicación Agent Local LOUNA de Escritorio, y servicios locales.", "xJs1jZ8PoA": "Agent Local LOUNA se ejecuta en segundo plano incluso cuando cierras la ventana. Haz clic en el ícono de Agent Local LOUNA en la bandeja del sistema para reabrir la aplicación o haz clic derecho para salir.", "xKRKzVVy9c": "Configurar", "xd436TVDRZ": "Hubo un error al leer o analizar developer_settings.json: {error}", "y9tCbmRzHN": "¿Activar modo desarrollador?", "ytjMRobdyL": "Actualización disponible", "zAYm/Z684h": "<PERSON><PERSON><PERSON>", "zCIK9K8J4a": "Error", "zSP70MVzIo": "<PERSON><PERSON><PERSON>"}