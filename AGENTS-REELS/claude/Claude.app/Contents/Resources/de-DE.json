{"+/cwsayrqk": "Originalgröße", "+7sd9hoyZA": "<PERSON><PERSON><PERSON>", "/PgA81GVOD": "<PERSON><PERSON><PERSON>", "/bRGKhnXQ6": "Eine neue Version ist verfügbar. Sie wird automatisch heruntergeladen und installiert.", "0g8/VVdNuN": "Gehen Sie zu „agent local.ai/settings“, um Ihr Profil, Team und weitere Einstellungen zu konfigurieren.", "0tZLEYF8mJ": "<PERSON><PERSON><PERSON><PERSON>", "1HUTYwndT2": "<PERSON><PERSON>", "1PfZLi/OV7": "Allgemein", "1TJUzU26sO": "Feedback übermitteln …", "25aCMlTDUq": "Agent Local LOUNA automatisch beim Anmelden starten", "3ML3xT+gEV": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3gG1j3kRBX": "Feedback übermitteln …", "3unrKzH4zB": "<PERSON><PERSON><PERSON>", "4qP7MjrQfC": "Umgebungsvariablen", "5DUIVR3fVi": "Über...", "6yv8ytK4El": "Überprüfen Sie Ihre Netzwerkverbindung", "7fdcqxofEs": "<PERSON>den", "7gSC+rZzXX": "Agent Local LOUNA von überall schnell öffnen", "8YQEOfuaGO": "Alles auswählen", "9+afSO9e/t": "Fehler beim Suchen nach Aktualisierungen: {error}", "9uNxNtcrFI": "Installieren", "CZwl8X2D85": "Erweiterte Optionen", "CizRPROPWo": "Agent Local LOUNA-<PERSON><PERSON>e", "D43DeqP+2t": "Agent Local LOUNA-Einstellungen", "D4DyT6MmPy": "App-Einstellungen konnten nicht geladen werden", "DQTgg21B7g": "App anzeigen", "E9jYTa7AbX": "Benachrichtigungsbereich", "EfdnINFnIz": "<PERSON><PERSON>", "GSG5S0ysrR": "Autostart", "HeHYq6bbS2": "Agent Local LOUNA kann Informationen wie Eingabeaufforderungen und Anhänge von spezialisierten Servern über das Model Context Protocol empfangen.", "I5O68ogAtr": "<PERSON><PERSON><PERSON>", "JVwNvMZjVT": "Einfügen", "KAo3lt5Hv+": "Einfügen", "Ko/2Ml7mZG": "Seite neu laden", "L32WRR6NOL": "Löschen", "L717supPIA": "Einstellungen", "LCWUQ/4Fu6": "<PERSON><PERSON><PERSON>", "NZIwKxgxJ+": "Möchten Sie den MCP-Server „{serverKey}“ wirklich entfernen?", "Nmvo1ufAY5": "Verbindung zu Agent Local LOUNA konnte nicht hergestellt werden", "O3rtEd7aMd": "<PERSON><PERSON>", "ODySlGptaj": "Einstellungen …", "PH29MShDiy": "Vorwärts", "PW5U8NgTto": "MCP-Protokolldatei öffnen ...", "PZtcoAOSsa": "Nicht aktivieren", "PbJ4jR0kv1": "Sie verwenden bereits die neueste Version.", "RTg057HE1D": "Entwicklertools anzeigen", "S3MXlbjkax": "Womit kann ich Ihnen heute helfen?", "S3k5yXss2r": "Version {version}", "TH+W2Ad73P": "Ausschneiden", "UJCjEVPX6Q": "Nachschlagen", "Vvus2ifAny": "Konfiguration bearbeiten", "W1pELwt/+a": "Agent Local LOUNA läuft im Benachrichtigungsbereich", "WBvq3HlPae": "Tastenkombination festlegen", "WF1HSu0jAC": "Protokollordner öffnen", "WZe86KSdrM": "Zum Wörterbuch hinzufügen", "WlhIx7DfFO": "OK", "XPIoFTkh3e": "Keine Aktualisierung verfügbar", "XZ36+EBE5/": "Verkleinern", "XinCguXCgN": "<PERSON><PERSON> er<PERSON>", "YTdYCYAf/Z": "Agent Local LOUNA in der Menüleiste anzeigen", "Z9g5m/V9Nq": "Vergrößern", "ZJZN1+KyJw": "Einstellungen ...", "aNmxuDcWaU": "<PERSON><PERSON>", "aXdFLiVzjd": "Hauptfenster anzeigen", "arbRxbtBkP": "Zurück", "baGq3gy8z1": "Neue Konversation", "dKX0bpR+a2": "<PERSON>den", "dLyz0Srosd": "<PERSON><PERSON><PERSON><PERSON>", "fEeEFfSz4K": "Vergr<PERSON>ßern (alternative Version)", "fFJxOwJRj2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fWDSQQgRO5": "Menüleiste", "iFRmqBsr1N": "Über Agent Local LOUNA", "ilE9e0uxNN": "Aktualisieren", "j66cdL4EK5": "Dokumentation öffnen", "jd5ZNrRMNP": "Sie gehören zu den ersten Benutzern. Teilen Sie uns Ihr Feedback mit.", "k+06oXbIas": "Agent Local LOUNA im Benachrichtigungsbereich anzeigen", "kYwW0OsI4M": "Agent Local LOUNA Desktop befindet sich in der Beta-Phase.", "m3GfpKD1WX": "Neu laden", "mRXjxhS6p4": "Nach Aktualisierungen suchen …", "ngLpGT7bUJ": "Entwicklereinstellungen konnten nicht geladen werden", "oQuOiX24pp": "<PERSON>den", "pWXxZASpOB": "<PERSON><PERSON><PERSON>", "pgaCSv2/6H": "Argumente", "q4hs14B00V": "Unbekannter Fehler", "rNAd+HxSK4": "MCP-Protokolldatei öffnen", "rY99UXvTDU": "Bild kopieren", "rdiPpQVqvY": "Der Entwicklermodus ermöglicht den Zugriff auf Entwicklertools und Debugging-Funktionen. Aktivieren Si<PERSON> diesen nur, wenn Sie Erfahrung damit haben.", "rwFEudHXey": "<PERSON><PERSON> beim Lesen oder Verarbeiten von agent local_desktop_config.json: {error}", "sNnRQsIEYz": "<PERSON>f Seite suchen", "sZxWXq9BzJ": "Feedback geben", "sys7RHphmL": "<PERSON><PERSON><PERSON>", "tWutslc/9Z": "Aktivieren", "u1/hT7oRQY": "Tastenkombination für Schnellzugriff", "uc3dnSo+eo": "<PERSON><PERSON>", "urCd4k/cE0": "<PERSON><PERSON><PERSON>", "vgLHPxjh9O": "Entwicklermodus aktivieren", "wS64bVG2CO": "MCP ist ein Protokoll, das sichere Verbindungen zwischen Clients, wie der Agent Local LOUNA Desktop App, und lokalen Diensten ermöglicht.", "xJs1jZ8PoA": "Agent Local LOUNA läuft im Hintergrund, auch wenn Sie das Fenster schließen. Klicken Sie auf das Agent Local LOUNA-Symbol in der Taskleiste, um die App erneut zu öffnen, oder klicken Sie das Symbol mit der rechten Maustaste an, um die App zu beenden.", "xKRKzVVy9c": "Konfigurieren", "xd436TVDRZ": "<PERSON><PERSON> beim Lesen oder Verarbeiten von developer_settings.json: {error}", "y9tCbmRzHN": "Entwicklermodus aktivieren?", "ytjMRobdyL": "Aktualisierung verfügbar", "zAYm/Z684h": "<PERSON><PERSON><PERSON>", "zCIK9K8J4a": "<PERSON><PERSON>", "zSP70MVzIo": "Tastenkombination löschen"}