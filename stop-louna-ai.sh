#!/bin/bash

# 🛑 ARRÊT LOUNA AI
# Script d'arrêt pour LOUNA AI
# Version: 2.0.0 - Juin 2025

# Configuration
LOUNA_DIR="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"
PID_FILE="$LOUNA_DIR/louna.pid"
LOG_FILE="$LOUNA_DIR/louna-launch.log"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
    echo "$(date): $message" >> "$LOG_FILE" 2>/dev/null
}

# Arrêter le serveur LOUNA AI
stop_louna_server() {
    print_message $BLUE "🛑 Arrêt du serveur LOUNA AI..."
    
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "🔄 Arrêt du processus $pid..."
            kill $pid
            sleep 3
            
            if ps -p $pid > /dev/null 2>&1; then
                print_message $YELLOW "🔨 Forçage de l'arrêt..."
                kill -9 $pid
                sleep 1
            fi
            
            if ! ps -p $pid > /dev/null 2>&1; then
                print_message $GREEN "✅ Serveur arrêté avec succès"
            else
                print_message $RED "❌ Impossible d'arrêter le serveur"
            fi
        else
            print_message $YELLOW "⚠️ Processus déjà arrêté"
        fi
        rm -f "$PID_FILE"
    else
        print_message $YELLOW "⚠️ Fichier PID non trouvé"
    fi
}

# Libérer le port 3000
free_port() {
    print_message $BLUE "🔌 Libération du port 3000..."
    
    if lsof -ti:3000 > /dev/null 2>&1; then
        print_message $YELLOW "🔄 Port 3000 occupé, libération..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 1
        
        if ! lsof -ti:3000 > /dev/null 2>&1; then
            print_message $GREEN "✅ Port 3000 libéré"
        else
            print_message $RED "❌ Port 3000 toujours occupé"
        fi
    else
        print_message $GREEN "✅ Port 3000 déjà libre"
    fi
}

# Fonction principale
main() {
    print_message $BLUE "🛑 === ARRÊT LOUNA AI ==="
    print_message $BLUE "📅 $(date)"
    
    stop_louna_server
    free_port
    
    print_message $GREEN "🎯 LOUNA AI arrêté complètement"
    print_message $BLUE "📋 Logs disponibles dans: $LOG_FILE"
}

main "$@"
