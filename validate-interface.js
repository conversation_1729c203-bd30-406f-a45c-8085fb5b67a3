/**
 * Script de validation complète de l'interface LOUNA AI
 */

const fs = require('fs');

console.log('🔍 === VALIDATION COMPLÈTE DE L\'INTERFACE LOUNA AI ===');

// Lire le fichier
const content = fs.readFileSync('interface-originale-complete.html', 'utf8');

// Tests de validation
let errors = [];
let warnings = [];
let success = [];

// 1. Vérifier la structure HTML de base
if (content.includes('<!DOCTYPE html>')) {
    success.push('✅ DOCTYPE HTML5 présent');
} else {
    errors.push('❌ DOCTYPE HTML5 manquant');
}

if (content.includes('<html lang="fr">')) {
    success.push('✅ Langue française définie');
} else {
    warnings.push('⚠️ Langue française non définie');
}

// 2. Vérifier les balises fermantes
const openDivs = (content.match(/<div/g) || []).length;
const closeDivs = (content.match(/<\/div>/g) || []).length;

if (openDivs === closeDivs) {
    success.push(`✅ Balises DIV équilibrées (${openDivs}/${closeDivs})`);
} else {
    errors.push(`❌ Balises DIV déséquilibrées (${openDivs} ouvertes, ${closeDivs} fermées)`);
}

// 3. Vérifier les navigations
const windowOpenCount = (content.match(/window\.open\(/g) || []).length;
const oldOpenAppCount = (content.match(/openApp\(/g) || []).length;

if (windowOpenCount > 40) {
    success.push(`✅ Navigation corrigée (${windowOpenCount} liens directs)`);
} else {
    warnings.push(`⚠️ Peu de liens de navigation (${windowOpenCount})`);
}

if (oldOpenAppCount === 0) {
    success.push('✅ Ancienne fonction openApp supprimée');
} else {
    warnings.push(`⚠️ Ancienne fonction openApp encore présente (${oldOpenAppCount})`);
}

// 4. Vérifier le QI
if (content.includes('QI: 185')) {
    success.push('✅ QI Jean-Luc correct (185)');
} else if (content.includes('QI: 235')) {
    errors.push('❌ QI incorrect (235 au lieu de 185)');
} else {
    warnings.push('⚠️ QI non trouvé');
}

// 5. Vérifier DeepSeek
if (content.includes('DeepSeek R1 8B')) {
    success.push('✅ DeepSeek R1 8B intégré');
} else {
    errors.push('❌ DeepSeek R1 8B manquant');
}

// 6. Vérifier la mémoire thermique
if (content.includes('37.2°C')) {
    success.push('✅ Température thermique correcte');
} else {
    warnings.push('⚠️ Température thermique non trouvée');
}

if (content.includes('1,064,012')) {
    success.push('✅ Nombre de neurones correct');
} else {
    warnings.push('⚠️ Nombre de neurones non trouvé');
}

// 7. Vérifier les animations CSS
const animations = ['pulse', 'glow', 'pulseGlow', 'pulseGreen', 'bounce', 'rotate', 'shine'];
let animationsFound = 0;

animations.forEach(anim => {
    if (content.includes(`@keyframes ${anim}`)) {
        animationsFound++;
    }
});

if (animationsFound >= 6) {
    success.push(`✅ Animations CSS présentes (${animationsFound}/${animations.length})`);
} else {
    warnings.push(`⚠️ Animations CSS manquantes (${animationsFound}/${animations.length})`);
}

// 8. Vérifier les sections principales
const sections = ['Démarrage Rapide', 'Applications Principales', 'Applications Avancées', 'Applications Système'];
let sectionsFound = 0;

sections.forEach(section => {
    if (content.includes(section)) {
        sectionsFound++;
    }
});

if (sectionsFound === sections.length) {
    success.push(`✅ Toutes les sections présentes (${sectionsFound}/${sections.length})`);
} else {
    warnings.push(`⚠️ Sections manquantes (${sectionsFound}/${sections.length})`);
}

// 9. Vérifier les contrôles de sécurité
if (content.includes('security-controls')) {
    success.push('✅ Contrôles de sécurité présents');
} else {
    errors.push('❌ Contrôles de sécurité manquants');
}

// 10. Vérifier la structure JavaScript
if (content.includes('systemMetrics')) {
    success.push('✅ Métriques système définies');
} else {
    warnings.push('⚠️ Métriques système non trouvées');
}

// Afficher les résultats
console.log('\n🎉 === RÉSULTATS DE VALIDATION ===\n');

if (success.length > 0) {
    console.log('✅ SUCCÈS:');
    success.forEach(msg => console.log(`   ${msg}`));
    console.log('');
}

if (warnings.length > 0) {
    console.log('⚠️ AVERTISSEMENTS:');
    warnings.forEach(msg => console.log(`   ${msg}`));
    console.log('');
}

if (errors.length > 0) {
    console.log('❌ ERREURS:');
    errors.forEach(msg => console.log(`   ${msg}`));
    console.log('');
}

// Score final
const totalTests = success.length + warnings.length + errors.length;
const score = Math.round((success.length / totalTests) * 100);

console.log(`📊 SCORE FINAL: ${score}% (${success.length}/${totalTests} tests réussis)`);

if (score >= 90) {
    console.log('🎉 EXCELLENT ! Interface parfaitement fonctionnelle !');
} else if (score >= 75) {
    console.log('👍 BIEN ! Interface fonctionnelle avec quelques améliorations possibles');
} else if (score >= 50) {
    console.log('⚠️ MOYEN ! Interface partiellement fonctionnelle, corrections nécessaires');
} else {
    console.log('❌ PROBLÉMATIQUE ! Interface nécessite des corrections importantes');
}

console.log('\n🧠 === VALIDATION LOUNA AI TERMINÉE ===');
