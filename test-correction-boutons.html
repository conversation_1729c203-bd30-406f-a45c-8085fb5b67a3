<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Correction Boutons Sécurité - LOUNA AI</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }
        
        .test-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .test-button.success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .test-button.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .test-button.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .results {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .security-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .security-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .security-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .security-btn i {
            font-size: 18px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> Test Correction Boutons Sécurité</h1>
            <p>Vérification et correction des boutons de sécurité LOUNA AI</p>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-play"></i> Tests Automatiques</h3>
            <button class="test-button" onclick="lancerTestComplet()">
                <i class="fas fa-rocket"></i> Test Complet
            </button>
            <button class="test-button success" onclick="forcerCorrection()">
                <i class="fas fa-wrench"></i> Forcer Correction
            </button>
            <button class="test-button warning" onclick="genererRapport()">
                <i class="fas fa-file-alt"></i> Générer Rapport
            </button>
            <button class="test-button danger" onclick="viderConsole()">
                <i class="fas fa-trash"></i> Vider Console
            </button>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> Boutons de Sécurité à Tester</h3>
            <div class="security-controls">
                <button class="security-btn btn-hibernation" onclick="testHibernation()">
                    <i class="fas fa-snowflake"></i>
                    <span>Hibernation</span>
                </button>
                <button class="security-btn btn-sleep" onclick="testSleep()">
                    <i class="fas fa-moon"></i>
                    <span>Sommeil</span>
                </button>
                <button class="security-btn btn-wakeup" onclick="testWakeup()">
                    <i class="fas fa-sun"></i>
                    <span>Réveil</span>
                </button>
                <button class="security-btn btn-surveillance" onclick="testSurveillance()">
                    <i class="fas fa-shield-alt"></i>
                    <span>Surveillance</span>
                </button>
                <button class="security-btn btn-backup" onclick="testBackup()">
                    <i class="fas fa-save"></i>
                    <span>Sauvegarde</span>
                </button>
                <button class="security-btn btn-memory" onclick="testMemory()">
                    <i class="fas fa-brain"></i>
                    <span>Mémoire</span>
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> Résultats des Tests</h3>
            <div class="results" id="results">
                <div>🔍 Console de test prête...</div>
                <div>💡 Cliquez sur "Test Complet" pour commencer</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> Statut des Boutons</h3>
            <div id="status-grid">
                <!-- Le statut sera généré dynamiquement -->
            </div>
        </div>
    </div>
    
    <script src="correction-boutons-securite.js"></script>
    <script src="test-boutons-securite-complet.js"></script>
    
    <script>
        // Variables globales
        let testResults = [];
        
        // Rediriger console.log vers notre interface
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const resultsDiv = document.getElementById('results');
            const message = args.join(' ');
            const div = document.createElement('div');
            div.textContent = message;
            
            // Colorer selon le type de message
            if (message.includes('✅')) {
                div.style.color = '#27ae60';
            } else if (message.includes('❌')) {
                div.style.color = '#e74c3c';
            } else if (message.includes('⚠️')) {
                div.style.color = '#f39c12';
            } else if (message.includes('🎉')) {
                div.style.color = '#9b59b6';
                div.style.fontWeight = 'bold';
            }
            
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        };
        
        // Fonctions de test
        function lancerTestComplet() {
            console.log('🚀 === LANCEMENT TEST COMPLET ===');
            
            if (typeof testerBoutonsSecuriteComplet === 'function') {
                const resultats = testerBoutonsSecuriteComplet();
                testResults = resultats;
                mettreAJourStatut(resultats);
            } else {
                console.log('❌ Fonction testerBoutonsSecuriteComplet non disponible');
            }
        }
        
        function forcerCorrection() {
            console.log('🔧 === CORRECTION FORCÉE ===');
            
            if (typeof forcerCorrectionBoutons === 'function') {
                forcerCorrectionBoutons();
                setTimeout(() => {
                    lancerTestComplet();
                }, 1000);
            } else {
                console.log('❌ Fonction forcerCorrectionBoutons non disponible');
            }
        }
        
        function genererRapport() {
            console.log('📋 === GÉNÉRATION RAPPORT ===');
            
            if (typeof genererRapportSecurite === 'function') {
                const rapport = genererRapportSecurite();
                console.log('📊 Rapport généré avec succès');
            } else {
                console.log('❌ Fonction genererRapportSecurite non disponible');
            }
        }
        
        function viderConsole() {
            document.getElementById('results').innerHTML = '<div>🔍 Console vidée...</div>';
        }
        
        // Fonctions de test individuelles
        function testHibernation() {
            console.log('❄️ Test bouton Hibernation...');
            if (typeof activateHibernation === 'function') {
                console.log('✅ Fonction activateHibernation disponible');
            } else {
                console.log('❌ Fonction activateHibernation non disponible');
            }
        }
        
        function testSleep() {
            console.log('😴 Test bouton Sommeil...');
            if (typeof activateSleep === 'function') {
                console.log('✅ Fonction activateSleep disponible');
            } else {
                console.log('❌ Fonction activateSleep non disponible');
            }
        }
        
        function testWakeup() {
            console.log('☀️ Test bouton Réveil...');
            if (typeof wakeupAgent === 'function') {
                console.log('✅ Fonction wakeupAgent disponible');
            } else {
                console.log('❌ Fonction wakeupAgent non disponible');
            }
        }
        
        function testSurveillance() {
            console.log('🛡️ Test bouton Surveillance...');
            if (typeof openSurveillance === 'function') {
                console.log('✅ Fonction openSurveillance disponible');
            } else {
                console.log('❌ Fonction openSurveillance non disponible');
            }
        }
        
        function testBackup() {
            console.log('💾 Test bouton Sauvegarde...');
            if (typeof openBackup === 'function') {
                console.log('✅ Fonction openBackup disponible');
            } else {
                console.log('❌ Fonction openBackup non disponible');
            }
        }
        
        function testMemory() {
            console.log('🧠 Test bouton Mémoire...');
            if (typeof openMemoryControl === 'function') {
                console.log('✅ Fonction openMemoryControl disponible');
            } else {
                console.log('❌ Fonction openMemoryControl non disponible');
            }
        }
        
        // Mettre à jour le statut visuel
        function mettreAJourStatut(resultats) {
            const statusGrid = document.getElementById('status-grid');
            statusGrid.innerHTML = '';
            
            if (resultats && resultats.details) {
                resultats.details.forEach(detail => {
                    const div = document.createElement('div');
                    div.style.display = 'flex';
                    div.style.alignItems = 'center';
                    div.style.padding = '10px';
                    div.style.margin = '5px 0';
                    div.style.background = detail.fonctionnel ? '#d5f4e6' : '#ffeaa7';
                    div.style.borderRadius = '5px';
                    
                    const indicator = document.createElement('span');
                    indicator.className = `status-indicator ${detail.fonctionnel ? 'status-ok' : 'status-error'}`;
                    
                    const text = document.createElement('span');
                    text.textContent = `${detail.nom}: ${detail.statut}`;
                    
                    div.appendChild(indicator);
                    div.appendChild(text);
                    statusGrid.appendChild(div);
                });
            }
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Interface de test chargée');
            console.log('💡 Prêt pour les tests de sécurité');
            
            // Test automatique après 2 secondes
            setTimeout(() => {
                console.log('🚀 Lancement du test automatique...');
                lancerTestComplet();
            }, 2000);
        });
    </script>
</body>
</html>
