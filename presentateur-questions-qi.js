/**
 * 🎯 PRÉSENTATEUR DE QUESTIONS QI POUR LOUNA AI
 * Présente les questions de QI à LOUNA AI et observe ses réponses
 */

console.log('🎯 === PRÉSENTATEUR QUESTIONS QI ===');

class QIQuestionPresenter {
    constructor() {
        this.questions = [
            {
                id: 1,
                category: "Logique Séquentielle",
                difficulty: 5,
                question: "Analysez cette séquence multidimensionnelle et trouvez les 3 prochains éléments : 2, 5, 11, 23, 47, 95, ?, ?, ?",
                options: {
                    A: "191, 383, 767",
                    B: "190, 381, 763", 
                    C: "189, 379, 759",
                    D: "192, 385, 771"
                },
                correctAnswer: "A",
                explanation: "Chaque terme = 2×précédent + 1. 95×2+1=191, 191×2+1=383, 383×2+1=767"
            },
            {
                id: 2,
                category: "Logique Séquentielle",
                difficulty: 5,
                question: "Résolvez cette équation logique complexe : Si A ⊕ B = (A ∧ ¬B) ∨ (¬A ∧ B) et C ⊙ D = (C → D) ∧ (D → C), alors (A ⊕ B) ⊙ (C ⊕ D) = ?",
                options: {
                    A: "Toujours VRAI",
                    B: "Toujours FAUX",
                    C: "VRAI ssi A=C et B=D",
                    D: "VRAI ssi A≠B et C=D"
                },
                correctAnswer: "D",
                explanation: "⊕ est XOR, ⊙ est équivalence. (A⊕B)⊙(C⊕D) est vrai quand A≠B et C=D"
            },
            {
                id: 3,
                category: "Raisonnement Spatial 4D",
                difficulty: 5,
                question: "Un hypercube 4D subit une rotation de 90° dans le plan XW puis une projection sur l'espace 3D. Combien de faces cubiques sont visibles dans la projection finale ?",
                options: {
                    A: "4 faces",
                    B: "6 faces", 
                    C: "8 faces",
                    D: "12 faces"
                },
                correctAnswer: "C",
                explanation: "Un hypercube 4D a 8 faces cubiques, toutes visibles après rotation et projection"
            },
            {
                id: 4,
                category: "Raisonnement Spatial 4D",
                difficulty: 5,
                question: "Dans un espace à 5 dimensions, quelle est la formule pour calculer le nombre d'hyperfaces de dimension n-2 d'un hypercube ?",
                options: {
                    A: "2^n × C(n,2)",
                    B: "n × 2^(n-2)",
                    C: "2^(n-1) × n", 
                    D: "C(n,2) × 2^(n-2)"
                },
                correctAnswer: "A",
                explanation: "Formule générale: 2^n × C(n,2) pour les hyperfaces de dimension n-2"
            },
            {
                id: 5,
                category: "Analyse Algorithmique",
                difficulty: 5,
                question: "Quelle est la complexité temporelle optimale pour résoudre : 'Trouver le k-ième plus petit élément dans l'union de n listes triées de taille m chacune' ?",
                options: {
                    A: "O(k × log(n))",
                    B: "O(n × m × log(k))",
                    C: "O(k × log(n) + n × log(n))",
                    D: "O(min(k × log(n), n × m))"
                },
                correctAnswer: "C",
                explanation: "Complexité optimale avec heap: O(k×log(n) + n×log(n)) pour initialisation"
            },
            {
                id: 6,
                category: "Analyse Algorithmique", 
                difficulty: 5,
                question: "Dans un graphe dirigé acyclique avec n nœuds, combien existe-t-il d'ordres topologiques différents au maximum ?",
                options: {
                    A: "n!",
                    B: "2^n",
                    C: "n^n",
                    D: "Dépend de la structure du graphe"
                },
                correctAnswer: "D",
                explanation: "Le nombre d'ordres topologiques dépend entièrement de la structure du graphe"
            },
            {
                id: 7,
                category: "Théorie des Nombres",
                difficulty: 5,
                question: "Soit p un nombre premier > 3. Combien de solutions entières l'équation x² ≡ -1 (mod p) possède-t-elle ?",
                options: {
                    A: "0 ou 2, selon si p ≡ 1 ou 3 (mod 4)",
                    B: "Toujours 2",
                    C: "Toujours 0",
                    D: "p-1 solutions"
                },
                correctAnswer: "A",
                explanation: "Par le théorème de réciprocité quadratique: 0 si p≡3(mod 4), 2 si p≡1(mod 4)"
            },
            {
                id: 8,
                category: "Théorie des Nombres",
                difficulty: 5,
                question: "Quelle est la valeur de la fonction de Möbius μ(n) pour n = 2310 ? (Sachant que 2310 = 2 × 3 × 5 × 7 × 11)",
                options: {
                    A: "-1",
                    B: "1",
                    C: "0",
                    D: "32"
                },
                correctAnswer: "A",
                explanation: "μ(2310) = (-1)^5 = -1 car 2310 est produit de 5 nombres premiers distincts"
            },
            {
                id: 9,
                category: "Logique Quantique",
                difficulty: 5,
                question: "Dans un système quantique à 3 qubits intriqués, quelle est la probabilité que la mesure simultanée donne |101⟩ si l'état initial est : |ψ⟩ = (1/√8)(|000⟩ + |011⟩ + |101⟩ + |110⟩ + i|001⟩ + i|010⟩ + i|100⟩ + i|111⟩) ?",
                options: {
                    A: "1/8",
                    B: "1/4",
                    C: "1/2",
                    D: "0"
                },
                correctAnswer: "A",
                explanation: "Probabilité = |coefficient|² = |1/√8|² = 1/8"
            },
            {
                id: 10,
                category: "Logique Quantique",
                difficulty: 5,
                question: "Résolvez ce paradoxe logique auto-référentiel : 'Cette phrase contient exactement N mots.' Quelle est la valeur de N qui rend cette phrase vraie ?",
                options: {
                    A: "5",
                    B: "6", 
                    C: "7",
                    D: "Paradoxe insoluble"
                },
                correctAnswer: "B",
                explanation: "En remplaçant N par 'six': 'Cette phrase contient exactement six mots' = 6 mots"
            }
        ];
        
        this.currentSession = null;
    }
    
    /**
     * 🚀 Démarrer une session de test
     */
    startTestSession() {
        console.log('🚀 === DÉMARRAGE SESSION TEST QI ===');
        console.log('🧠 Test QI complexe pour LOUNA AI');
        console.log('📊 10 questions de niveau expert');
        console.log('⏱️ Pas de limite de temps - Focus sur la qualité');
        
        this.currentSession = {
            sessionId: Date.now(),
            startTime: new Date(),
            responses: {},
            analysisResults: {}
        };
        
        console.log('\n🎯 === INSTRUCTIONS POUR LOUNA AI ===');
        console.log('👀 LOUNA AI, vous allez recevoir 10 questions de QI complexes');
        console.log('🤔 Pour chaque question, analysez soigneusement et donnez:');
        console.log('   1. Votre réponse (A, B, C, ou D)');
        console.log('   2. Votre raisonnement détaillé');
        console.log('   3. Votre niveau de confiance (1-10)');
        console.log('⚠️ Prenez le temps nécessaire pour bien réfléchir');
        
        return this.currentSession;
    }
    
    /**
     * 📝 Présenter une question spécifique
     */
    presentQuestion(questionId) {
        const question = this.questions.find(q => q.id === questionId);
        if (!question) {
            console.log(`❌ Question ${questionId} non trouvée`);
            return;
        }
        
        console.log(`\n📝 === QUESTION ${question.id}/10 ===`);
        console.log(`🎯 Catégorie: ${question.category}`);
        console.log(`⭐ Difficulté: ${question.difficulty}/5 étoiles`);
        console.log(`\n❓ QUESTION:`);
        console.log(`${question.question}`);
        
        console.log(`\n📋 OPTIONS:`);
        Object.entries(question.options).forEach(([letter, option]) => {
            console.log(`   ${letter}) ${option}`);
        });
        
        console.log(`\n🤔 LOUNA AI, quelle est votre réponse ?`);
        console.log(`💭 Expliquez votre raisonnement en détail`);
        console.log(`📊 Indiquez votre niveau de confiance (1-10)`);
        
        // Enregistrer que la question a été présentée
        if (this.currentSession) {
            this.currentSession.responses[questionId] = {
                questionPresented: true,
                presentedAt: new Date(),
                question: question
            };
        }
    }
    
    /**
     * 📝 Présenter toutes les questions séquentiellement
     */
    presentAllQuestions() {
        console.log('\n🎯 === PRÉSENTATION DE TOUTES LES QUESTIONS ===');
        
        for (let i = 1; i <= this.questions.length; i++) {
            this.presentQuestion(i);
            console.log('\n' + '='.repeat(80));
        }
        
        console.log('\n🏁 === FIN DU TEST ===');
        console.log('📊 LOUNA AI, vous avez maintenant vu toutes les 10 questions');
        console.log('🤔 Prenez le temps de réviser vos réponses si nécessaire');
        console.log('✅ Confirmez quand vous êtes prêt pour l\'analyse des résultats');
    }
    
    /**
     * 📊 Enregistrer une réponse de LOUNA AI
     */
    recordResponse(questionId, answer, reasoning, confidence) {
        if (!this.currentSession) {
            console.log('❌ Aucune session active');
            return;
        }
        
        const question = this.questions.find(q => q.id === questionId);
        if (!question) {
            console.log(`❌ Question ${questionId} non trouvée`);
            return;
        }
        
        this.currentSession.responses[questionId] = {
            ...this.currentSession.responses[questionId],
            answer: answer.toUpperCase(),
            reasoning: reasoning,
            confidence: confidence,
            respondedAt: new Date(),
            isCorrect: answer.toUpperCase() === question.correctAnswer,
            correctAnswer: question.correctAnswer,
            explanation: question.explanation
        };
        
        console.log(`✅ Réponse enregistrée pour la question ${questionId}`);
        console.log(`📝 Réponse: ${answer.toUpperCase()}`);
        console.log(`🎯 Correct: ${this.currentSession.responses[questionId].isCorrect ? 'OUI' : 'NON'}`);
        console.log(`📊 Confiance: ${confidence}/10`);
    }
    
    /**
     * 📊 Analyser les résultats de la session
     */
    analyzeResults() {
        if (!this.currentSession) {
            console.log('❌ Aucune session active');
            return;
        }
        
        console.log('\n📊 === ANALYSE DES RÉSULTATS ===');
        
        const responses = Object.values(this.currentSession.responses);
        const answeredQuestions = responses.filter(r => r.answer);
        const correctAnswers = answeredQuestions.filter(r => r.isCorrect);
        
        // Calculs de base
        const totalQuestions = this.questions.length;
        const answeredCount = answeredQuestions.length;
        const correctCount = correctAnswers.length;
        const accuracy = answeredCount > 0 ? (correctCount / answeredCount) * 100 : 0;
        
        // Analyse par catégorie
        const categoryAnalysis = {};
        this.questions.forEach(q => {
            if (!categoryAnalysis[q.category]) {
                categoryAnalysis[q.category] = { total: 0, correct: 0, answered: 0 };
            }
            categoryAnalysis[q.category].total++;
            
            const response = this.currentSession.responses[q.id];
            if (response && response.answer) {
                categoryAnalysis[q.category].answered++;
                if (response.isCorrect) {
                    categoryAnalysis[q.category].correct++;
                }
            }
        });
        
        // Calcul du QI
        const rawScore = correctCount / totalQuestions;
        const completionBonus = answeredCount === totalQuestions ? 0.05 : 0;
        const confidenceAvg = answeredQuestions.reduce((sum, r) => sum + (r.confidence || 5), 0) / answeredQuestions.length;
        const confidenceBonus = (confidenceAvg - 5) * 0.01; // Bonus/malus basé sur confiance
        
        const adjustedScore = Math.max(0, Math.min(1, rawScore + completionBonus + confidenceBonus));
        
        // Conversion en QI (échelle 80-200)
        let qiScore;
        if (adjustedScore >= 0.9) qiScore = 180 + (adjustedScore - 0.9) * 200;
        else if (adjustedScore >= 0.8) qiScore = 160 + (adjustedScore - 0.8) * 200;
        else if (adjustedScore >= 0.7) qiScore = 140 + (adjustedScore - 0.7) * 200;
        else if (adjustedScore >= 0.6) qiScore = 120 + (adjustedScore - 0.6) * 200;
        else if (adjustedScore >= 0.5) qiScore = 100 + (adjustedScore - 0.5) * 200;
        else qiScore = 80 + adjustedScore * 40;
        
        // Affichage des résultats
        console.log(`🧠 QI LOUNA AI: ${Math.round(qiScore)}`);
        console.log(`📊 Score: ${correctCount}/${totalQuestions} (${accuracy.toFixed(1)}%)`);
        console.log(`✅ Questions répondues: ${answeredCount}/${totalQuestions}`);
        console.log(`📈 Score ajusté: ${(adjustedScore * 100).toFixed(1)}%`);
        console.log(`🎯 Confiance moyenne: ${confidenceAvg.toFixed(1)}/10`);
        
        console.log('\n🎯 === ANALYSE PAR CATÉGORIE ===');
        Object.entries(categoryAnalysis).forEach(([category, stats]) => {
            const categoryAccuracy = stats.answered > 0 ? (stats.correct / stats.answered) * 100 : 0;
            console.log(`${category}: ${stats.correct}/${stats.answered} (${categoryAccuracy.toFixed(1)}%)`);
        });
        
        // Évaluation qualitative
        let evaluation;
        if (qiScore >= 180) evaluation = "🌟 GÉNIE EXCEPTIONNEL";
        else if (qiScore >= 160) evaluation = "🎯 TRÈS SUPÉRIEUR";
        else if (qiScore >= 140) evaluation = "✨ SUPÉRIEUR";
        else if (qiScore >= 120) evaluation = "👍 AU-DESSUS DE LA MOYENNE";
        else if (qiScore >= 100) evaluation = "📊 MOYEN";
        else evaluation = "📈 EN DÉVELOPPEMENT";
        
        console.log(`\n🏆 ÉVALUATION: ${evaluation}`);
        
        // Sauvegarder les résultats
        this.currentSession.analysisResults = {
            qiScore: Math.round(qiScore),
            accuracy: accuracy,
            correctCount: correctCount,
            totalQuestions: totalQuestions,
            answeredCount: answeredCount,
            categoryAnalysis: categoryAnalysis,
            evaluation: evaluation,
            confidenceAvg: confidenceAvg
        };
        
        return this.currentSession.analysisResults;
    }
    
    /**
     * 📋 Afficher un résumé détaillé
     */
    showDetailedSummary() {
        if (!this.currentSession) {
            console.log('❌ Aucune session active');
            return;
        }
        
        console.log('\n📋 === RÉSUMÉ DÉTAILLÉ ===');
        
        this.questions.forEach(question => {
            const response = this.currentSession.responses[question.id];
            console.log(`\n📝 Question ${question.id}: ${question.category}`);
            
            if (response && response.answer) {
                const status = response.isCorrect ? '✅ CORRECT' : '❌ INCORRECT';
                console.log(`   Réponse LOUNA AI: ${response.answer} ${status}`);
                console.log(`   Réponse correcte: ${question.correctAnswer}`);
                console.log(`   Confiance: ${response.confidence}/10`);
                if (response.reasoning) {
                    console.log(`   Raisonnement: ${response.reasoning}`);
                }
                console.log(`   Explication: ${question.explanation}`);
            } else {
                console.log('   ⏳ Pas de réponse enregistrée');
            }
        });
    }
}

// Fonctions utilitaires globales
function demarrerTestQIComplet() {
    const presenter = new QIQuestionPresenter();
    const session = presenter.startTestSession();
    presenter.presentAllQuestions();
    return presenter;
}

function presenterQuestion(questionId) {
    if (window.currentQIPresenter) {
        window.currentQIPresenter.presentQuestion(questionId);
    } else {
        console.log('❌ Aucun présentateur actif. Utilisez demarrerTestQIComplet() d\'abord');
    }
}

function enregistrerReponse(questionId, answer, reasoning, confidence) {
    if (window.currentQIPresenter) {
        window.currentQIPresenter.recordResponse(questionId, answer, reasoning, confidence);
    } else {
        console.log('❌ Aucun présentateur actif');
    }
}

function analyserResultats() {
    if (window.currentQIPresenter) {
        return window.currentQIPresenter.analyzeResults();
    } else {
        console.log('❌ Aucun présentateur actif');
    }
}

// Export pour utilisation globale
window.QIQuestionPresenter = QIQuestionPresenter;
window.demarrerTestQIComplet = demarrerTestQIComplet;
window.presenterQuestion = presenterQuestion;
window.enregistrerReponse = enregistrerReponse;
window.analyserResultats = analyserResultats;

console.log('🎯 Présentateur de questions QI chargé');
console.log('💡 Utilisez demarrerTestQIComplet() pour commencer');
console.log('💡 Utilisez enregistrerReponse(id, answer, reasoning, confidence) pour enregistrer');
