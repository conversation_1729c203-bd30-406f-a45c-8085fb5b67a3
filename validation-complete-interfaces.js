/**
 * 🔍 VALIDATION COMPLÈTE DES INTERFACES LOUNA AI
 * Vérifie que toutes les interfaces récupèrent bien leurs données
 * Version: 1.0.0 - Juin 2025
 */

const fs = require('fs');
const path = require('path');

class InterfaceValidator {
    constructor() {
        this.results = {
            interfaces: {},
            summary: {
                total: 0,
                withRealData: 0,
                withSimulatedData: 0,
                withoutData: 0,
                missingFiles: 0
            }
        };
        
        this.interfacesToCheck = [
            // Interfaces principales
            { file: 'interface-originale-complete.html', name: 'Interface Principale', priority: 'HIGH' },
            { file: 'applications-originales/main-dashboard.html', name: '<PERSON>au de Bord Principal', priority: 'HIGH' },
            { file: 'applications-originales/control-dashboard.html', name: 'Tableau de Contrôle', priority: 'HIGH' },
            { file: 'applications-originales/futuristic-interface.html', name: 'Interface Mémoire Thermique', priority: 'HIGH' },
            
            // Interfaces spécialisées
            { file: 'applications-originales/thermal-memory-dashboard.html', name: 'Dashboard Mémoire Thermique', priority: 'MEDIUM' },
            { file: 'applications-originales/brain-monitoring-complete.html', name: 'Monitoring Cérébral', priority: 'MEDIUM' },
            { file: 'applications-originales/brain-dashboard-live.html', name: 'Dashboard Cérébral Live', priority: 'MEDIUM' },
            { file: 'applications-originales/comparative-analysis.html', name: 'Analyse Comparative', priority: 'MEDIUM' },
            
            // Interfaces de contrôle
            { file: 'applications-originales/memory-control.html', name: 'Contrôle Mémoire', priority: 'MEDIUM' },
            { file: 'applications-originales/kyber-dashboard.html', name: 'Dashboard Kyber', priority: 'LOW' },
            { file: 'applications-originales/advanced-settings.html', name: 'Paramètres Avancés', priority: 'LOW' }
        ];
    }

    /**
     * 🚀 Lance la validation complète
     */
    async validateAll() {
        console.log('🔍 === VALIDATION COMPLÈTE DES INTERFACES ===\n');
        
        for (const interfaceInfo of this.interfacesToCheck) {
            await this.validateInterface(interfaceInfo);
        }
        
        this.generateReport();
        return this.results;
    }

    /**
     * 🔍 Valide une interface spécifique
     */
    async validateInterface(interfaceInfo) {
        const { file, name, priority } = interfaceInfo;
        
        console.log(`📄 Validation: ${name} (${priority})`);
        
        if (!fs.existsSync(file)) {
            this.results.interfaces[file] = {
                name,
                priority,
                status: 'MISSING',
                dataSource: 'NONE',
                apis: [],
                issues: ['Fichier manquant'],
                score: 0
            };
            this.results.summary.missingFiles++;
            console.log(`   ❌ FICHIER MANQUANT: ${file}\n`);
            return;
        }

        const content = fs.readFileSync(file, 'utf8');
        const analysis = this.analyzeInterface(content);
        
        this.results.interfaces[file] = {
            name,
            priority,
            ...analysis
        };
        
        // Mettre à jour le résumé
        this.results.summary.total++;
        if (analysis.status === 'REAL_DATA') {
            this.results.summary.withRealData++;
        } else if (analysis.status === 'SIMULATED_DATA') {
            this.results.summary.withSimulatedData++;
        } else {
            this.results.summary.withoutData++;
        }
        
        console.log(`   ${this.getStatusIcon(analysis.status)} ${analysis.status} (Score: ${analysis.score}/10)`);
        if (analysis.issues.length > 0) {
            analysis.issues.forEach(issue => console.log(`     ⚠️ ${issue}`));
        }
        console.log('');
    }

    /**
     * 🔍 Analyse le contenu d'une interface
     */
    analyzeInterface(content) {
        const analysis = {
            status: 'NO_DATA',
            dataSource: 'NONE',
            apis: [],
            issues: [],
            score: 0,
            features: {
                hasRealAPI: false,
                hasFallback: false,
                hasErrorHandling: false,
                hasRealTimeUpdate: false,
                usesThermalAPI: false
            }
        };

        // Rechercher les APIs réelles
        const realAPIs = [
            '/api/neural-kyber/status',
            '/api/thermal-memory/stats',
            '/api/brain/status',
            '/api/system/metrics',
            '/api/control/',
            '/api/thermal/',
            '/api/neurons/'
        ];

        realAPIs.forEach(api => {
            if (content.includes(api)) {
                analysis.apis.push(api);
                analysis.features.hasRealAPI = true;
            }
        });

        // Vérifier l'utilisation de thermalDataAPI
        if (content.includes('thermalDataAPI') || content.includes('thermal-data-api.js')) {
            analysis.features.usesThermalAPI = true;
            analysis.dataSource = 'THERMAL_API';
        }

        // Vérifier les appels fetch
        const fetchMatches = content.match(/fetch\(['"`]([^'"`]+)['"`]\)/g);
        if (fetchMatches) {
            fetchMatches.forEach(match => {
                const url = match.match(/['"`]([^'"`]+)['"`]/)[1];
                if (!analysis.apis.includes(url)) {
                    analysis.apis.push(url);
                }
            });
        }

        // Vérifier la gestion d'erreur
        if (content.includes('catch') && content.includes('error')) {
            analysis.features.hasErrorHandling = true;
        }

        // Vérifier le fallback
        if (content.includes('fallback') || content.includes('simulation') || content.includes('simulé')) {
            analysis.features.hasFallback = true;
        }

        // Vérifier les mises à jour temps réel
        if (content.includes('setInterval') || content.includes('setTimeout')) {
            analysis.features.hasRealTimeUpdate = true;
        }

        // Déterminer le statut
        if (analysis.features.hasRealAPI || analysis.features.usesThermalAPI) {
            if (analysis.features.hasFallback) {
                analysis.status = 'REAL_DATA';
                analysis.dataSource = 'REAL_WITH_FALLBACK';
            } else {
                analysis.status = 'REAL_DATA';
                analysis.dataSource = 'REAL_ONLY';
            }
        } else if (analysis.features.hasFallback || content.includes('Math.random')) {
            analysis.status = 'SIMULATED_DATA';
            analysis.dataSource = 'SIMULATED';
        } else {
            analysis.status = 'NO_DATA';
            analysis.dataSource = 'NONE';
        }

        // Calculer le score
        analysis.score = this.calculateScore(analysis);

        // Identifier les problèmes
        analysis.issues = this.identifyIssues(analysis, content);

        return analysis;
    }

    /**
     * 📊 Calcule le score de qualité
     */
    calculateScore(analysis) {
        let score = 0;
        
        if (analysis.features.hasRealAPI) score += 3;
        if (analysis.features.usesThermalAPI) score += 2;
        if (analysis.features.hasErrorHandling) score += 2;
        if (analysis.features.hasFallback) score += 2;
        if (analysis.features.hasRealTimeUpdate) score += 1;
        
        return Math.min(score, 10);
    }

    /**
     * ⚠️ Identifie les problèmes
     */
    identifyIssues(analysis, content) {
        const issues = [];
        
        if (!analysis.features.hasRealAPI && !analysis.features.usesThermalAPI) {
            issues.push('Aucune API réelle détectée');
        }
        
        if (!analysis.features.hasErrorHandling) {
            issues.push('Gestion d\'erreur manquante');
        }
        
        if (!analysis.features.hasFallback && analysis.features.hasRealAPI) {
            issues.push('Pas de fallback en cas d\'erreur API');
        }
        
        if (content.includes('Math.random') && analysis.status === 'REAL_DATA') {
            issues.push('Mélange de données réelles et simulées');
        }
        
        if (!analysis.features.hasRealTimeUpdate) {
            issues.push('Pas de mise à jour temps réel');
        }
        
        return issues;
    }

    /**
     * 📊 Génère le rapport final
     */
    generateReport() {
        console.log('📊 === RAPPORT DE VALIDATION ===\n');
        
        // Résumé global
        console.log('🎯 RÉSUMÉ GLOBAL:');
        console.log(`   Total interfaces: ${this.results.summary.total}`);
        console.log(`   ✅ Avec vraies données: ${this.results.summary.withRealData}`);
        console.log(`   ⚠️ Avec données simulées: ${this.results.summary.withSimulatedData}`);
        console.log(`   ❌ Sans données: ${this.results.summary.withoutData}`);
        console.log(`   📁 Fichiers manquants: ${this.results.summary.missingFiles}\n`);
        
        // Interfaces par priorité
        ['HIGH', 'MEDIUM', 'LOW'].forEach(priority => {
            const interfaces = Object.values(this.results.interfaces)
                .filter(i => i.priority === priority);
            
            if (interfaces.length > 0) {
                console.log(`🔥 PRIORITÉ ${priority}:`);
                interfaces.forEach(interfaceItem => {
                    console.log(`   ${this.getStatusIcon(interfaceItem.status)} ${interfaceItem.name} (${interfaceItem.score}/10)`);
                });
                console.log('');
            }
        });
        
        // Recommandations
        console.log('💡 RECOMMANDATIONS:');
        const recommendations = this.generateRecommendations();
        recommendations.forEach(rec => console.log(`   ${rec}`));
    }

    /**
     * 💡 Génère les recommandations
     */
    generateRecommendations() {
        const recommendations = [];
        
        if (this.results.summary.withoutData > 0) {
            recommendations.push('🔧 Connecter les interfaces sans données aux APIs existantes');
        }
        
        if (this.results.summary.missingFiles > 0) {
            recommendations.push('📁 Créer les fichiers manquants');
        }
        
        const lowScoreInterfaces = Object.values(this.results.interfaces)
            .filter(i => i.score < 7);
        
        if (lowScoreInterfaces.length > 0) {
            recommendations.push('⚡ Améliorer les interfaces avec score < 7');
        }
        
        recommendations.push('🔄 Utiliser thermal-data-api.js pour centraliser les données');
        recommendations.push('🛡️ Ajouter gestion d\'erreur et fallback partout');
        
        return recommendations;
    }

    /**
     * 🎨 Obtient l'icône de statut
     */
    getStatusIcon(status) {
        switch (status) {
            case 'REAL_DATA': return '✅';
            case 'SIMULATED_DATA': return '⚠️';
            case 'NO_DATA': return '❌';
            case 'MISSING': return '📁';
            default: return '❓';
        }
    }
}

// Exécution si appelé directement
if (require.main === module) {
    const validator = new InterfaceValidator();
    validator.validateAll().then(() => {
        console.log('🎯 Validation terminée !');
    });
}

module.exports = InterfaceValidator;
