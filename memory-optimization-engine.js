/**
 * 🧠⚡ MOTEUR D'OPTIMISATION MÉMOIRE
 * Optimise l'utilisation mémoire tout en préservant l'émergence cognitive
 * Gestion intelligente des neurones actifs/veille/hibernation
 */

const EventEmitter = require('events');

class MemoryOptimizationEngine extends EventEmitter {
    constructor(brain) {
        super();
        
        this.brain = brain;
        
        // 🧠 CONFIGURATION OPTIMISATION
        this.optimizationConfig = {
            maxMemoryUsage: 85,              // 85% mémoire max
            emergencyMemoryThreshold: 95,    // 95% = urgence
            optimalActiveNeurons: 500000,    // 500k neurones actifs optimal
            maxActiveNeurons: 2000000,       // 2M neurones actifs max
            compressionThreshold: 70,        // Compression si > 70% mémoire
            hibernationThreshold: 80,        // Hibernation si > 80% mémoire
            emergencyHibernationRatio: 0.5   // 50% hibernation d'urgence
        };
        
        // 📊 SURVEILLANCE MÉMOIRE
        this.memoryMonitoring = {
            currentUsage: 0,
            peakUsage: 0,
            lastOptimization: Date.now(),
            optimizationCount: 0,
            emergencyActivations: 0,
            memoryHistory: []
        };
        
        // 🎯 STRATÉGIES D'OPTIMISATION
        this.optimizationStrategies = {
            smartCompression: new SmartCompressionStrategy(),
            adaptiveHibernation: new AdaptiveHibernationStrategy(),
            emergentPreservation: new EmergentPreservationStrategy(),
            memoryRecycling: new MemoryRecyclingStrategy()
        };
        
        // 🧠 POOLS DE NEURONES OPTIMISÉS
        this.neuronPools = {
            highPriority: new Set(),      // Neurones critiques (émergence)
            mediumPriority: new Set(),    // Neurones importants
            lowPriority: new Set(),       // Neurones recyclables
            emergentNeurons: new Set()    // Neurones avec patterns émergents
        };
        
        this.initializeOptimization();
    }

    /**
     * 🚀 Initialise l'optimisation mémoire
     */
    initializeOptimization() {
        console.log('🧠⚡ Initialisation optimisation mémoire...');
        
        // Démarrer la surveillance continue
        this.startMemoryMonitoring();
        
        // Démarrer l'optimisation adaptative
        this.startAdaptiveOptimization();
        
        // Classifier les neurones par priorité
        this.classifyNeuronsByPriority();
        
        console.log('🧠⚡ Optimisation mémoire active');
    }

    /**
     * 📊 Démarre la surveillance mémoire
     */
    startMemoryMonitoring() {
        // Surveillance rapide toutes les 100ms
        setInterval(() => {
            this.updateMemoryUsage();
        }, 100);
        
        // Optimisation préventive toutes les 500ms
        setInterval(() => {
            this.preventiveOptimization();
        }, 500);
        
        // Optimisation profonde toutes les 5 secondes
        setInterval(() => {
            this.deepOptimization();
        }, 5000);
        
        console.log('📊 Surveillance mémoire démarrée');
    }

    /**
     * 🔄 Démarre l'optimisation adaptative
     */
    startAdaptiveOptimization() {
        // Adaptation dynamique toutes les 2 secondes
        setInterval(() => {
            this.adaptiveMemoryManagement();
        }, 2000);
        
        // Recyclage mémoire toutes les 10 secondes
        setInterval(() => {
            this.recycleMemory();
        }, 10000);
        
        console.log('🔄 Optimisation adaptative démarrée');
    }

    /**
     * 📊 Met à jour l'utilisation mémoire
     */
    updateMemoryUsage() {
        const memUsage = process.memoryUsage();
        const totalMemory = memUsage.heapTotal;
        const usedMemory = memUsage.heapUsed;
        
        this.memoryMonitoring.currentUsage = (usedMemory / totalMemory) * 100;
        this.memoryMonitoring.peakUsage = Math.max(this.memoryMonitoring.peakUsage, this.memoryMonitoring.currentUsage);
        
        // Ajouter à l'historique
        this.memoryMonitoring.memoryHistory.push({
            timestamp: Date.now(),
            usage: this.memoryMonitoring.currentUsage,
            activeNeurons: this.brain.brainState.activeNeurons.size,
            standbyNeurons: this.brain.brainState.standbyNeurons.size
        });
        
        // Limiter l'historique
        if (this.memoryMonitoring.memoryHistory.length > 100) {
            this.memoryMonitoring.memoryHistory.shift();
        }
        
        // Déclenchement d'urgence si nécessaire
        if (this.memoryMonitoring.currentUsage > this.optimizationConfig.emergencyMemoryThreshold) {
            this.emergencyMemoryOptimization();
        }
    }

    /**
     * 🚨 Optimisation mémoire d'urgence
     */
    emergencyMemoryOptimization() {
        console.log('🚨 OPTIMISATION MÉMOIRE D\'URGENCE !');
        
        this.memoryMonitoring.emergencyActivations++;
        
        // 1. Hibernation massive des neurones actifs
        this.massiveHibernation();
        
        // 2. Compression d'urgence
        this.emergencyCompression();
        
        // 3. Préservation des patterns émergents
        this.preserveEmergentPatterns();
        
        // 4. Nettoyage agressif
        this.aggressiveCleanup();
        
        console.log(`🚨 Optimisation d'urgence terminée - Mémoire: ${this.memoryMonitoring.currentUsage.toFixed(1)}%`);
        this.emit('emergencyOptimization', this.memoryMonitoring.currentUsage);
    }

    /**
     * 💤 Hibernation massive des neurones actifs
     */
    massiveHibernation() {
        const activeNeurons = Array.from(this.brain.brainState.activeNeurons.values());
        const neuronsToHibernate = Math.floor(activeNeurons.length * this.optimizationConfig.emergencyHibernationRatio);
        
        // Trier par priorité (garder les plus importants)
        const sortedNeurons = activeNeurons.sort((a, b) => {
            const priorityA = this.getNeuronPriority(a);
            const priorityB = this.getNeuronPriority(b);
            return priorityA - priorityB; // Hiberner les moins prioritaires
        });
        
        let hibernated = 0;
        for (let i = 0; i < neuronsToHibernate && i < sortedNeurons.length; i++) {
            const neuron = sortedNeurons[i];
            
            // Déplacer vers hibernation
            neuron.state = 'hibernating';
            neuron.energy = 0.1;
            this.brain.brainState.hibernatingNeurons.set(neuron.id, neuron);
            this.brain.brainState.activeNeurons.delete(neuron.id);
            hibernated++;
        }
        
        console.log(`💤 ${hibernated.toLocaleString()} neurones hibernés d'urgence`);
    }

    /**
     * 🗜️ Compression d'urgence
     */
    emergencyCompression() {
        // Compresser les données des neurones en veille
        let compressed = 0;
        
        this.brain.brainState.standbyNeurons.forEach(neuron => {
            if (neuron.synapses && neuron.synapses.size > 50) {
                // Réduire les connexions synaptiques
                const synapsesToKeep = Math.floor(neuron.synapses.size * 0.7); // Garder 70%
                const synapseArray = Array.from(neuron.synapses);
                neuron.synapses.clear();
                
                // Garder les connexions les plus importantes
                for (let i = 0; i < synapsesToKeep; i++) {
                    neuron.synapses.add(synapseArray[i]);
                }
                compressed++;
            }
        });
        
        console.log(`🗜️ ${compressed.toLocaleString()} neurones compressés d'urgence`);
    }

    /**
     * 🌟 Préserve les patterns émergents
     */
    preserveEmergentPatterns() {
        if (!this.brain.cognitiveEmergence) return;
        
        const emergentPatterns = this.brain.cognitiveEmergence.getActiveEmergentPatterns();
        
        emergentPatterns.forEach(pattern => {
            // Identifier les neurones impliqués dans ce pattern
            const involvedNeurons = this.identifyPatternNeurons(pattern);
            
            // Marquer comme haute priorité
            involvedNeurons.forEach(neuronId => {
                this.neuronPools.emergentNeurons.add(neuronId);
                this.neuronPools.highPriority.add(neuronId);
            });
        });
        
        console.log(`🌟 ${this.neuronPools.emergentNeurons.size} neurones émergents préservés`);
    }

    /**
     * 🧹 Nettoyage agressif
     */
    aggressiveCleanup() {
        // Nettoyer l'historique mémoire
        this.memoryMonitoring.memoryHistory = this.memoryMonitoring.memoryHistory.slice(-20);
        
        // Nettoyer les patterns émergents anciens
        if (this.brain.cognitiveEmergence) {
            this.brain.cognitiveEmergence.optimizeEmergentPatterns();
        }
        
        // Forcer le garbage collection si possible
        if (global.gc) {
            global.gc();
        }
        
        console.log('🧹 Nettoyage agressif terminé');
    }

    /**
     * 🔍 Optimisation préventive
     */
    preventiveOptimization() {
        if (this.memoryMonitoring.currentUsage > this.optimizationConfig.compressionThreshold) {
            this.smartCompression();
        }
        
        if (this.memoryMonitoring.currentUsage > this.optimizationConfig.hibernationThreshold) {
            this.adaptiveHibernation();
        }
    }

    /**
     * 🗜️ Compression intelligente
     */
    smartCompression() {
        const strategy = this.optimizationStrategies.smartCompression;
        const compressionResult = strategy.compress(this.brain, this.neuronPools);
        
        if (compressionResult.compressed > 0) {
            console.log(`🗜️ Compression intelligente: ${compressionResult.compressed} éléments compressés`);
        }
    }

    /**
     * 💤 Hibernation adaptative
     */
    adaptiveHibernation() {
        const strategy = this.optimizationStrategies.adaptiveHibernation;
        const hibernationResult = strategy.hibernate(this.brain, this.neuronPools);
        
        if (hibernationResult.hibernated > 0) {
            console.log(`💤 Hibernation adaptative: ${hibernationResult.hibernated} neurones hibernés`);
        }
    }

    /**
     * 🔄 Gestion mémoire adaptative
     */
    adaptiveMemoryManagement() {
        const activeNeurons = this.brain.brainState.activeNeurons.size;
        const targetNeurons = this.calculateOptimalActiveNeurons();
        
        if (activeNeurons > targetNeurons * 1.2) {
            // Trop de neurones actifs
            this.reduceActiveNeurons(activeNeurons - targetNeurons);
        } else if (activeNeurons < targetNeurons * 0.8 && this.memoryMonitoring.currentUsage < 60) {
            // Pas assez de neurones actifs et mémoire disponible
            this.increaseActiveNeurons(targetNeurons - activeNeurons);
        }
    }

    /**
     * 📊 Calcule le nombre optimal de neurones actifs
     */
    calculateOptimalActiveNeurons() {
        const memoryFactor = Math.max(0.3, 1.0 - (this.memoryMonitoring.currentUsage / 100));
        const emergenceFactor = this.brain.cognitiveEmergence ? 
            this.brain.cognitiveEmergence.getEmergenceMetrics().emergenceLevel : 0.5;
        
        return Math.floor(this.optimizationConfig.optimalActiveNeurons * memoryFactor * (1 + emergenceFactor));
    }

    /**
     * ⬇️ Réduit le nombre de neurones actifs
     */
    reduceActiveNeurons(reduction) {
        const activeNeurons = Array.from(this.brain.brainState.activeNeurons.values());
        const neuronsToReduce = Math.min(reduction, activeNeurons.length);
        
        // Trier par priorité
        const sortedNeurons = activeNeurons.sort((a, b) => 
            this.getNeuronPriority(a) - this.getNeuronPriority(b)
        );
        
        let reduced = 0;
        for (let i = 0; i < neuronsToReduce; i++) {
            const neuron = sortedNeurons[i];
            
            // Déplacer vers veille ou hibernation selon la priorité
            if (this.getNeuronPriority(neuron) > 2) {
                neuron.state = 'standby';
                neuron.energy = 0.3;
                this.brain.brainState.standbyNeurons.set(neuron.id, neuron);
            } else {
                neuron.state = 'hibernating';
                neuron.energy = 0.1;
                this.brain.brainState.hibernatingNeurons.set(neuron.id, neuron);
            }
            
            this.brain.brainState.activeNeurons.delete(neuron.id);
            reduced++;
        }
        
        if (reduced > 0) {
            console.log(`⬇️ ${reduced.toLocaleString()} neurones désactivés pour optimisation`);
        }
    }

    /**
     * ⬆️ Augmente le nombre de neurones actifs
     */
    increaseActiveNeurons(increase) {
        const standbyNeurons = Array.from(this.brain.brainState.standbyNeurons.values());
        const neuronsToActivate = Math.min(increase, standbyNeurons.length);
        
        // Trier par priorité (activer les plus prioritaires)
        const sortedNeurons = standbyNeurons.sort((a, b) => 
            this.getNeuronPriority(b) - this.getNeuronPriority(a)
        );
        
        let activated = 0;
        for (let i = 0; i < neuronsToActivate; i++) {
            const neuron = sortedNeurons[i];
            
            neuron.state = 'active';
            neuron.energy = 1.0;
            this.brain.brainState.activeNeurons.set(neuron.id, neuron);
            this.brain.brainState.standbyNeurons.delete(neuron.id);
            activated++;
        }
        
        if (activated > 0) {
            console.log(`⬆️ ${activated.toLocaleString()} neurones activés pour optimisation`);
        }
    }

    /**
     * 🎯 Obtient la priorité d'un neurone
     */
    getNeuronPriority(neuron) {
        let priority = 1; // Priorité de base
        
        // Priorité haute pour neurones émergents
        if (this.neuronPools.emergentNeurons.has(neuron.id)) {
            priority += 5;
        }
        
        // Priorité selon l'activité
        if (neuron.activationCount > 10) {
            priority += 2;
        }
        
        // Priorité selon l'énergie
        if (neuron.energy > 0.8) {
            priority += 1;
        }
        
        // Priorité selon les connexions
        if (neuron.synapses && neuron.synapses.size > 30) {
            priority += 1;
        }
        
        // Priorité selon la mémoire stockée
        if (neuron.memoryLoad && neuron.memoryLoad > 50) {
            priority += 2;
        }
        
        return priority;
    }

    /**
     * 🔍 Identifie les neurones d'un pattern
     */
    identifyPatternNeurons(pattern) {
        const neurons = new Set();
        
        // Analyser le pattern pour identifier les neurones impliqués
        if (pattern.pattern && pattern.pattern.standbyActivity) {
            pattern.pattern.standbyActivity.forEach(activity => {
                neurons.add(activity.id);
            });
        }
        
        if (pattern.pattern && pattern.pattern.activeActivity) {
            pattern.pattern.activeActivity.forEach(activity => {
                neurons.add(activity.id);
            });
        }
        
        return Array.from(neurons);
    }

    /**
     * 🔄 Recycle la mémoire
     */
    recycleMemory() {
        const strategy = this.optimizationStrategies.memoryRecycling;
        const recyclingResult = strategy.recycle(this.brain, this.memoryMonitoring);
        
        if (recyclingResult.recycled > 0) {
            console.log(`🔄 Recyclage mémoire: ${recyclingResult.recycled} éléments recyclés`);
        }
    }

    /**
     * 🎯 Classifie les neurones par priorité
     */
    classifyNeuronsByPriority() {
        this.brain.brainState.standbyNeurons.forEach(neuron => {
            const priority = this.getNeuronPriority(neuron);
            
            if (priority >= 7) {
                this.neuronPools.highPriority.add(neuron.id);
            } else if (priority >= 4) {
                this.neuronPools.mediumPriority.add(neuron.id);
            } else {
                this.neuronPools.lowPriority.add(neuron.id);
            }
        });
        
        console.log(`🎯 Classification: ${this.neuronPools.highPriority.size} haute, ${this.neuronPools.mediumPriority.size} moyenne, ${this.neuronPools.lowPriority.size} basse priorité`);
    }

    /**
     * 🔧 Optimisation profonde
     */
    deepOptimization() {
        this.memoryMonitoring.optimizationCount++;
        this.memoryMonitoring.lastOptimization = Date.now();
        
        // Reclassifier les neurones
        this.classifyNeuronsByPriority();
        
        // Optimiser selon l'émergence
        if (this.brain.cognitiveEmergence) {
            const emergenceMetrics = this.brain.cognitiveEmergence.getEmergenceMetrics();
            
            if (emergenceMetrics.emergenceLevel > 0.8) {
                // Haute émergence = plus de neurones actifs
                this.optimizationConfig.optimalActiveNeurons = Math.min(
                    this.optimizationConfig.maxActiveNeurons,
                    this.optimizationConfig.optimalActiveNeurons * 1.1
                );
            } else if (emergenceMetrics.emergenceLevel < 0.3) {
                // Faible émergence = moins de neurones actifs
                this.optimizationConfig.optimalActiveNeurons = Math.max(
                    100000,
                    this.optimizationConfig.optimalActiveNeurons * 0.9
                );
            }
        }
    }

    /**
     * 📊 Obtient les métriques d'optimisation
     */
    getOptimizationMetrics() {
        return {
            memoryMonitoring: this.memoryMonitoring,
            optimizationConfig: this.optimizationConfig,
            neuronPools: {
                highPriority: this.neuronPools.highPriority.size,
                mediumPriority: this.neuronPools.mediumPriority.size,
                lowPriority: this.neuronPools.lowPriority.size,
                emergentNeurons: this.neuronPools.emergentNeurons.size
            },
            currentOptimalNeurons: this.calculateOptimalActiveNeurons()
        };
    }
}

// Stratégies d'optimisation
class SmartCompressionStrategy {
    compress(brain, neuronPools) {
        let compressed = 0;
        
        // Compresser les neurones de basse priorité
        neuronPools.lowPriority.forEach(neuronId => {
            const neuron = brain.brainState.standbyNeurons.get(neuronId);
            if (neuron && neuron.synapses && neuron.synapses.size > 20) {
                const newSize = Math.floor(neuron.synapses.size * 0.8);
                const synapseArray = Array.from(neuron.synapses);
                neuron.synapses.clear();
                
                for (let i = 0; i < newSize; i++) {
                    neuron.synapses.add(synapseArray[i]);
                }
                compressed++;
            }
        });
        
        return { compressed };
    }
}

class AdaptiveHibernationStrategy {
    hibernate(brain, neuronPools) {
        let hibernated = 0;
        
        // Hiberner les neurones de basse priorité actifs
        brain.brainState.activeNeurons.forEach(neuron => {
            if (neuronPools.lowPriority.has(neuron.id) && Math.random() < 0.3) {
                neuron.state = 'hibernating';
                neuron.energy = 0.1;
                brain.brainState.hibernatingNeurons.set(neuron.id, neuron);
                brain.brainState.activeNeurons.delete(neuron.id);
                hibernated++;
            }
        });
        
        return { hibernated };
    }
}

class EmergentPreservationStrategy {
    preserve(brain, emergentPatterns) {
        // Préserver les neurones impliqués dans l'émergence
        // Implémentation spécialisée pour préserver l'émergence
    }
}

class MemoryRecyclingStrategy {
    recycle(brain, memoryMonitoring) {
        let recycled = 0;
        
        // Recycler les souvenirs anciens peu importants
        brain.brainMemory.memories.forEach((memory, id) => {
            if (memory.importance < 0.3 && Date.now() - memory.lastAccess > 60000) {
                brain.brainMemory.memories.delete(id);
                recycled++;
            }
        });
        
        return { recycled };
    }
}

module.exports = MemoryOptimizationEngine;
