/**
 * 🧪 TEST MÉMOIRE DU CERVEAU
 * Test du stockage et récupération des souvenirs et apprentissages
 */

const PureBrainSystem = require('./pure-brain-system');

async function testBrainMemory() {
    console.log('🧪 === TEST MÉMOIRE DU CERVEAU ===\n');
    
    try {
        // Initialiser le cerveau
        console.log('🚀 Initialisation du cerveau avec stockage mémoire...');
        const brain = new PureBrainSystem();
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test 1: Stockage de souvenirs
        console.log('\n📝 Test 1: Stockage de souvenirs');
        const memory1 = brain.remember("J'ai appris à utiliser les neurones", 'episodic', 0.8);
        const memory2 = brain.remember("Les synapses transmettent l'information", 'semantic', 0.9);
        const memory3 = brain.remember("Première activation réussie", 'emotional', 0.7);
        
        console.log(`✅ 3 souvenirs stockés`);
        
        // Test 2: Apprentissage de compétences
        console.log('\n📝 Test 2: Apprentissage de compétences');
        brain.learn('pattern_recognition', 0.3);
        brain.learn('memory_management', 0.4);
        brain.learn('neural_optimization', 0.2);
        
        console.log(`✅ 3 compétences apprises`);
        
        // Attendre un peu pour l'activité de fond
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 3: Recherche de souvenirs
        console.log('\n📝 Test 3: Recherche de souvenirs');
        const searchResults = brain.recall('neurones');
        console.log(`🔍 Recherche "neurones": ${searchResults.length} résultats`);
        searchResults.forEach(memory => {
            console.log(`   - "${memory.content}" (${memory.type}, importance: ${(memory.importance * 100).toFixed(0)}%)`);
        });
        
        // Test 4: Rappel de souvenirs spécifiques
        console.log('\n📝 Test 4: Rappel de souvenirs spécifiques');
        const recalled = brain.recallMemory(memory1);
        if (recalled) {
            console.log(`🧠 Souvenir rappelé: "${recalled.content}"`);
            console.log(`   Accès: ${recalled.accessCount}, Importance: ${(recalled.importance * 100).toFixed(0)}%`);
        }
        
        // Test 5: Pratique de compétences
        console.log('\n📝 Test 5: Pratique de compétences');
        brain.learn('pattern_recognition', 0.2); // Améliorer
        brain.learn('pattern_recognition', 0.3); // Encore
        brain.learn('pattern_recognition', 0.4); // Maîtrise !
        
        // Test 6: Statistiques mémoire
        console.log('\n📝 Test 6: Statistiques mémoire');
        const stats = brain.getMemoryStats();
        console.log('\n📊 === STATISTIQUES MÉMOIRE ===');
        console.log(`📚 Souvenirs totaux: ${stats.memories.total}`);
        console.log(`   Types:`, stats.memories.byType);
        console.log(`   Accès moyen: ${stats.memories.averageAccess.toFixed(1)}`);
        console.log(`🎓 Apprentissages: ${stats.learnings.total}`);
        console.log(`   Maîtrisés: ${stats.learnings.mastered}`);
        console.log(`   Niveau moyen: ${(stats.learnings.averageLevel * 100).toFixed(0)}%`);
        console.log(`🔗 Associations: ${stats.associations}`);
        console.log(`💾 Charge mémoire moyenne: ${stats.neuronMemoryLoad.average.toFixed(1)}%`);
        
        // Test 7: Compétences acquises
        console.log('\n📝 Test 7: Compétences acquises');
        const skills = brain.getSkills();
        console.log('\n🎓 === COMPÉTENCES ===');
        skills.forEach(skill => {
            const masteryIcon = skill.mastery ? '🏆' : '📈';
            console.log(`${masteryIcon} ${skill.skill}: ${(skill.level * 100).toFixed(0)}% (pratiqué ${skill.practiceCount} fois)`);
        });
        
        // Test 8: Souvenirs par type
        console.log('\n📝 Test 8: Souvenirs par type');
        const episodicMemories = brain.getMemoriesByType('episodic');
        const semanticMemories = brain.getMemoriesByType('semantic');
        
        console.log(`\n📖 Souvenirs épisodiques (${episodicMemories.length}):`);
        episodicMemories.forEach(memory => {
            console.log(`   - "${memory.content}" (${(memory.importance * 100).toFixed(0)}%)`);
        });
        
        console.log(`\n🧠 Souvenirs sémantiques (${semanticMemories.length}):`);
        semanticMemories.forEach(memory => {
            console.log(`   - "${memory.content}" (${(memory.importance * 100).toFixed(0)}%)`);
        });
        
        // Test 9: Laisser le cerveau travailler en arrière-plan
        console.log('\n📝 Test 9: Activité de fond (15 secondes)');
        console.log('🧠 Le cerveau travaille en arrière-plan...');
        
        await new Promise(resolve => setTimeout(resolve, 15000));
        
        // Statistiques finales
        const finalStats = brain.getMemoryStats();
        console.log('\n📊 === STATISTIQUES FINALES ===');
        console.log(`📚 Souvenirs: ${finalStats.memories.total} (accès moyen: ${finalStats.memories.averageAccess.toFixed(1)})`);
        console.log(`🎓 Apprentissages: ${finalStats.learnings.total} (niveau moyen: ${(finalStats.learnings.averageLevel * 100).toFixed(0)}%)`);
        console.log(`🔗 Associations créées: ${finalStats.associations}`);
        console.log(`💾 Neurones avec mémoire: ${finalStats.neuronMemoryLoad.neuronsWithMemory.toLocaleString()}`);
        
        // Test 10: Associations mémoire
        console.log('\n📝 Test 10: Associations mémoire');
        const associations = brain.getMemoryAssociations(memory1);
        if (associations.length > 0) {
            console.log(`🔗 Associations pour "${brain.brainMemory.memories.get(memory1).content}":`);
            associations.forEach(assoc => {
                console.log(`   → "${assoc.content}" (${assoc.type})`);
            });
        } else {
            console.log('🔗 Aucune association trouvée (normal pour un test court)');
        }
        
        console.log('\n✅ Tests mémoire terminés avec succès !');
        console.log('🧠 Le cerveau continue de consolider les souvenirs en arrière-plan...');
        
        // Laisser tourner encore un peu pour voir la consolidation
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        console.log('\n🛑 Fin du test mémoire');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests mémoire:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testBrainMemory().catch(console.error);
