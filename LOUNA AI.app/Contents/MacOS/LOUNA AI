#!/bin/bash

# 🔥 LOUNA AI ULTRA-AUTONOME - LANCEUR DESKTOP
# Application macOS pour lancement depuis le bureau
# Version: 2.0.0 - Juin 2025

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOUNA_DIR="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"

# Fonction d'affichage de notification
show_notification() {
    local title="$1"
    local message="$2"
    local sound="${3:-Glass}"
    
    osascript -e "display notification \"$message\" with title \"$title\" sound name \"$sound\""
}

# Fonction d'affichage de dialogue
show_dialog() {
    local title="$1"
    local message="$2"
    local buttons="${3:-OK}"
    
    osascript -e "display dialog \"$message\" with title \"$title\" buttons {\"$buttons\"} default button 1 with icon note"
}

# Vérifier les prérequis
check_prerequisites() {
    # Vérifier que le répertoire LOUNA AI existe
    if [ ! -d "$LOUNA_DIR" ]; then
        show_dialog "❌ Erreur LOUNA AI" "Répertoire LOUNA AI non trouvé:\n$LOUNA_DIR\n\nVérifiez que le disque Seagate est connecté."
        exit 1
    fi
    
    # Vérifier que le script de lancement existe
    if [ ! -f "$LOUNA_DIR/launch-louna-ai.sh" ]; then
        show_dialog "❌ Erreur LOUNA AI" "Script de lancement non trouvé.\n\nRestauration nécessaire."
        exit 1
    fi
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        show_dialog "❌ Erreur LOUNA AI" "Node.js n'est pas installé.\n\nInstallez Node.js depuis nodejs.org"
        exit 1
    fi
}

# Fonction de lancement principal
launch_louna() {
    show_notification "🚀 LOUNA AI" "Démarrage en cours..." "Hero"
    
    # Changer vers le répertoire LOUNA AI
    cd "$LOUNA_DIR"
    
    # Lancer le script principal
    if ./launch-louna-ai.sh; then
        show_notification "✅ LOUNA AI" "Système démarré avec succès!" "Glass"
        
        # Afficher les informations
        show_dialog "🔥 LOUNA AI Ultra-Autonome" "✅ Système démarré avec succès!\n\n🌐 Interface principale ouverte dans le navigateur\n📊 Dashboard disponible\n🧠 86 milliards de neurones actifs\n🔥 Données 100% réelles\n\n🛑 Pour arrêter: Double-cliquez sur 'Arrêter LOUNA AI'" "Parfait"
    else
        show_notification "❌ LOUNA AI" "Erreur de démarrage" "Basso"
        show_dialog "❌ Erreur LOUNA AI" "Échec du démarrage du système.\n\nConsultez les logs pour plus d'informations:\n$LOUNA_DIR/louna-launch.log"
        exit 1
    fi
}

# Fonction principale
main() {
    # Afficher le splash screen
    show_notification "🔥 LOUNA AI" "Initialisation..." "Ping"
    
    # Vérifier les prérequis
    check_prerequisites
    
    # Lancer LOUNA AI
    launch_louna
}

# Gestion des erreurs
set -e
trap 'show_notification "❌ LOUNA AI" "Erreur inattendue" "Basso"' ERR

# Exécution
main "$@"
