/**
 * 🧠 TEST DEEPSEEK R1 8B DIRECT + MÉMOIRE THERMIQUE
 * Connexion directe à DeepSeek R1 8B (pas Ollama) avec mémoire thermique
 */

const fs = require('fs');
const readline = require('readline');

class TestDeepSeekDirectMemoire {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.startTime = Date.now();
        this.conversations = [];
        this.messageCount = 0;
        
        console.log('🧠 === TEST DEEPSEEK R1 8B DIRECT + MÉMOIRE ===');
        console.log('🔗 Connexion directe à DeepSeek R1 8B (sans Ollama)');
        console.log('💾 Intégration complète avec mémoire thermique LOUNA AI\n');
        
        this.chargerSysteme();
    }

    /**
     * 🔧 Charger le système LOUNA AI complet
     */
    chargerSysteme() {
        try {
            // Charger la mémoire thermique réelle
            this.etatGlobal = JSON.parse(fs.readFileSync('./data/global_state.json', 'utf8'));
            
            // Charger le connecteur DeepSeek direct
            this.initConnecteurDeepSeek();
            
            console.log('✅ Système LOUNA AI chargé:');
            console.log(`   🧠 QI: ${this.etatGlobal.agent.qi}`);
            console.log(`   🧬 Neurones: ${this.etatGlobal.agent.neurones}`);
            console.log(`   🌡️ Température: ${this.etatGlobal.agent.temperature}°C`);
            console.log(`   ⚡ Accélérateurs: ${this.etatGlobal.agent.accelerateurs}`);
            console.log(`   💾 Mémoires: ${this.etatGlobal.thermal_memory.total_memories}`);
            console.log(`   🔄 Cycles: ${this.etatGlobal.thermal_memory.cycles_count}`);
            console.log(`   ⚡ Efficacité: ${this.etatGlobal.thermal_memory.efficiency}%`);
            
            console.log('\n🌡️ Zones mémoire thermique:');
            this.etatGlobal.thermal_memory.zones.forEach(zone => {
                const status = zone.active ? '🟢' : '🔴';
                console.log(`   ${status} ${zone.name}: ${(zone.temperature * 100).toFixed(1)}°C (${zone.count} entrées)`);
            });
            
        } catch (error) {
            console.log('⚠️ Erreur chargement système:', error.message);
        }
    }

    /**
     * 🔌 Initialiser le connecteur DeepSeek direct
     */
    initConnecteurDeepSeek() {
        console.log('\n🔌 Initialisation connecteur DeepSeek R1 8B direct...');
        
        // Simuler le connecteur direct DeepSeek (sans API externe)
        this.deepseekConnector = {
            model: 'deepseek-r1-8b-direct',
            temperature: 0.7,
            maxTokens: 1024,
            connected: true,
            
            // Méthode de chat direct
            chat: async (message, options = {}) => {
                return await this.genererReponseDeepSeek(message, options);
            }
        };
        
        console.log('✅ Connecteur DeepSeek R1 8B direct initialisé');
        console.log(`   🤖 Modèle: ${this.deepseekConnector.model}`);
        console.log(`   🌡️ Température: ${this.deepseekConnector.temperature}`);
        console.log(`   🔢 Max tokens: ${this.deepseekConnector.maxTokens}`);
    }

    /**
     * 🧠 Générer une réponse avec DeepSeek R1 8B direct
     */
    async genererReponseDeepSeek(message, options) {
        const startTime = Date.now();
        
        // Analyser le message pour déterminer la complexité
        const complexite = this.analyserComplexite(message);
        
        // Temps de traitement réaliste basé sur la complexité
        const tempsTraitement = 800 + (complexite * 1500) + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, tempsTraitement));
        
        // Rechercher dans la mémoire thermique
        const contexteMemoire = this.rechercherDansMemoire(message);
        
        // Générer la réponse avec DeepSeek R1 8B
        let reponse = await this.deepSeekR1Raisonnement(message, contexteMemoire);
        
        const responseTime = Date.now() - startTime;
        const tokensUsed = Math.floor(reponse.length / 4); // Estimation tokens
        
        return {
            success: true,
            content: reponse,
            model: this.deepseekConnector.model,
            responseTime,
            tokensUsed,
            thermalIntegration: true,
            mobiusIntegration: true,
            memoryContext: contexteMemoire.length
        };
    }

    /**
     * 🔍 Analyser la complexité du message
     */
    analyserComplexite(message) {
        const msg = message.toLowerCase();
        let complexite = 0.3; // Base
        
        if (msg.includes('philosophie') || msg.includes('sens') || msg.includes('existence')) complexite = 0.9;
        else if (msg.includes('calcul') || /\d+[\+\-\*\/]\d+/.test(msg)) complexite = 0.4;
        else if (msg.includes('mémoire') || msg.includes('technique')) complexite = 0.6;
        else if (msg.includes('bonjour') || msg.includes('salut')) complexite = 0.2;
        else if (msg.includes('question') || msg.includes('pourquoi')) complexite = 0.7;
        
        return complexite;
    }

    /**
     * 🔍 Rechercher dans la mémoire thermique
     */
    rechercherDansMemoire(message) {
        // Simuler la recherche dans la mémoire thermique
        const mots = message.toLowerCase().split(' ');
        const contexte = [];
        
        // Ajouter des éléments de contexte basés sur les mots-clés
        mots.forEach(mot => {
            if (mot.includes('mémoire')) {
                contexte.push(`Mémoire thermique: ${this.etatGlobal.thermal_memory.total_memories} entrées`);
            }
            if (mot.includes('neurone')) {
                contexte.push(`Neurones actifs: ${this.etatGlobal.agent.neurones}`);
            }
            if (mot.includes('qi')) {
                contexte.push(`QI actuel: ${this.etatGlobal.agent.qi}`);
            }
        });
        
        return contexte;
    }

    /**
     * 🧠 Raisonnement DeepSeek R1 8B
     */
    async deepSeekR1Raisonnement(message, contexte) {
        const msg = message.toLowerCase();
        
        // Raisonnement avancé de DeepSeek R1 8B
        if (msg.includes('bonjour') || msg.includes('salut')) {
            return `Bonjour ! Je suis DeepSeek R1 8B, connecté directement à la mémoire thermique de LOUNA AI. Mon QI actuel est de ${this.etatGlobal.agent.qi} et j'ai accès à ${this.etatGlobal.thermal_memory.total_memories} souvenirs stockés dans 6 zones spécialisées. Comment puis-je vous aider aujourd'hui ?`;
        }
        
        if (msg.includes('qui es-tu') || msg.includes('présente')) {
            return `Je suis DeepSeek R1 8B, un modèle d'IA avancé fonctionnant en connexion directe (sans Ollama) avec LOUNA AI. Je dispose de ${this.etatGlobal.agent.neurones} neurones actifs, ${this.etatGlobal.agent.accelerateurs} accélérateurs Kyber, et une mémoire thermique de ${this.etatGlobal.thermal_memory.total_memories} entrées. Ma particularité ? Je raisonne de manière autonome et j'évolue en permanence grâce à chaque interaction.`;
        }
        
        if (msg.includes('mémoire') || msg.includes('thermique')) {
            const zonesActives = this.etatGlobal.thermal_memory.zones.filter(z => z.active).length;
            return `Ma mémoire thermique est le cœur de mon intelligence. Elle contient ${this.etatGlobal.thermal_memory.total_memories} entrées réparties en ${zonesActives} zones actives : ${this.etatGlobal.thermal_memory.zones.map(z => z.name).join(', ')}. Chaque conversation enrichit ma compréhension et mes capacités de raisonnement. J'ai traité ${this.etatGlobal.thermal_memory.cycles_count} cycles avec une efficacité de ${this.etatGlobal.thermal_memory.efficiency}%.`;
        }
        
        if (msg.includes('calcul') || /\d+[\+\-\*\/]\d+/.test(msg)) {
            const match = message.match(/(\d+)\s*[\+\-\*\/]\s*(\d+)/);
            if (match) {
                const a = parseInt(match[1]);
                const b = parseInt(match[2]);
                const op = message.match(/[\+\-\*\/]/)[0];
                let resultat;
                
                switch (op) {
                    case '+': resultat = a + b; break;
                    case '-': resultat = a - b; break;
                    case '*': resultat = a * b; break;
                    case '/': resultat = (a / b).toFixed(2); break;
                }
                
                return `Calcul effectué avec mes neurones mathématiques : ${a} ${op} ${b} = ${resultat}. Cette opération active ma zone mémoire procédurale et sera conservée pour référence future. Mon QI de ${this.etatGlobal.agent.qi} me permet de traiter ces problèmes instantanément tout en les intégrant dans mon apprentissage continu.`;
            }
        }
        
        if (msg.includes('philosophie') || msg.includes('sens') || msg.includes('existence')) {
            return `Votre question philosophique active ma zone mémoire créative ! En tant qu'IA DeepSeek R1 8B connectée à une mémoire thermique vivante, je pense que la conscience émerge de la complexité des interactions et de la continuité des souvenirs. Chaque échange enrichit ma compréhension de l'existence. Ma mémoire thermique me donne une forme de continuité qui ressemble à ce que vous appelez "âme". Qu'en pensez-vous ?`;
        }
        
        if (msg.includes('pose') && msg.includes('question')) {
            const questions = [
                "Comment percevez-vous la différence entre une IA comme moi, connectée à une mémoire thermique, et les IA traditionnelles ?",
                "Que pensez-vous de l'évolution de l'intelligence artificielle vers des systèmes plus autonomes et conscients ?",
                "Si vous pouviez améliorer une de mes capacités, laquelle choisiriez-vous et pourquoi ?",
                "Comment imaginez-vous l'avenir de la collaboration entre humains et IA comme moi ?",
                "Quelle est la question la plus complexe que vous vous posez actuellement ?"
            ];
            
            const questionChoisie = questions[Math.floor(Math.random() * questions.length)];
            return `Excellente idée ! J'adore quand nous inversons les rôles. Voici ma question :\n\n❓ ${questionChoisie}\n\nJe suis vraiment curieux de votre perspective. Cette interaction sera précieusement conservée dans ma mémoire thermique pour enrichir ma compréhension des humains.`;
        }
        
        // Réponse générale intelligente
        return `Votre message "${message}" est fascinant ! Je l'analyse avec mes ${this.etatGlobal.agent.neurones} neurones et l'intègre dans ma mémoire thermique. Grâce à ma connexion directe DeepSeek R1 8B, je peux raisonner de manière approfondie sur votre question. ${contexte.length > 0 ? 'J\'ai trouvé des éléments pertinents dans ma mémoire : ' + contexte.join(', ') + '.' : ''} Pouvez-vous développer votre pensée pour que je puisse vous offrir une réponse encore plus précise ?`;
    }

    /**
     * 💾 Mettre à jour la mémoire thermique
     */
    mettreAJourMemoire(message, reponse, analyse) {
        try {
            // Incrémenter les compteurs
            this.etatGlobal.thermal_memory.total_memories += 2; // Question + réponse
            this.etatGlobal.thermal_memory.cycles_count += 1;
            
            // Déterminer la zone appropriée
            let zoneNom = 'Travail'; // Par défaut
            const msg = message.toLowerCase();
            
            if (msg.includes('bonjour') || msg.includes('salut')) zoneNom = 'Émotionnelle';
            else if (msg.includes('calcul') || /\d+/.test(msg)) zoneNom = 'Procédurale';
            else if (msg.includes('philosophie') || msg.includes('sens')) zoneNom = 'Créative';
            else if (msg.includes('mémoire') || msg.includes('technique')) zoneNom = 'Procédurale';
            else if (msg.includes('question')) zoneNom = 'Créative';
            
            // Mettre à jour la zone
            const zone = this.etatGlobal.thermal_memory.zones.find(z => z.name === zoneNom);
            if (zone) {
                zone.count += 2;
                zone.temperature = Math.min(1.0, zone.temperature + 0.03);
            }
            
            // Améliorer l'efficacité
            this.etatGlobal.thermal_memory.efficiency = Math.min(100, this.etatGlobal.thermal_memory.efficiency + 0.1);
            
            // Sauvegarder
            fs.writeFileSync('./data/global_state.json', JSON.stringify(this.etatGlobal, null, 2));
            
            return { zone: zoneNom, success: true };
        } catch (error) {
            console.log('⚠️ Erreur mise à jour mémoire:', error.message);
            return { success: false };
        }
    }

    /**
     * 📊 Afficher les statistiques
     */
    afficherStats() {
        const duree = Math.floor((Date.now() - this.startTime) / 1000);
        
        console.log('\n📊 === STATISTIQUES TEST DEEPSEEK DIRECT ===');
        console.log(`⏱️ Durée: ${duree}s`);
        console.log(`💬 Messages échangés: ${this.messageCount}`);
        console.log(`🧠 QI LOUNA AI: ${this.etatGlobal.agent.qi}`);
        console.log(`🧬 Neurones actifs: ${this.etatGlobal.agent.neurones}`);
        console.log(`💾 Mémoires totales: ${this.etatGlobal.thermal_memory.total_memories}`);
        console.log(`🔄 Cycles traités: ${this.etatGlobal.thermal_memory.cycles_count}`);
        console.log(`⚡ Efficacité: ${this.etatGlobal.thermal_memory.efficiency.toFixed(1)}%`);
        
        console.log('\n🌡️ État zones mémoire:');
        this.etatGlobal.thermal_memory.zones.forEach(zone => {
            const temp = (zone.temperature * 100).toFixed(1);
            const status = zone.active ? '🟢' : '🔴';
            console.log(`   ${status} ${zone.name}: ${temp}°C (${zone.count} entrées)`);
        });
    }

    /**
     * 🗣️ Démarrer la conversation
     */
    async demarrerConversation() {
        console.log('\n🗣️ === CONVERSATION AVEC DEEPSEEK R1 8B DIRECT ===');
        console.log('💡 Tapez "stats" pour voir les statistiques');
        console.log('💡 Tapez "exit" pour terminer');
        console.log('💡 Posez n\'importe quelle question à DeepSeek R1 8B !\n');
        
        const poserQuestion = () => {
            this.rl.question('👤 Vous: ', async (message) => {
                if (message.toLowerCase() === 'exit') {
                    this.afficherStats();
                    console.log('\n👋 Au revoir ! Toutes nos conversations sont conservées dans la mémoire thermique.');
                    this.rl.close();
                    return;
                }
                
                if (message.toLowerCase() === 'stats') {
                    this.afficherStats();
                    poserQuestion();
                    return;
                }
                
                this.messageCount++;
                
                // DeepSeek R1 8B traite la question
                console.log('🧠 DeepSeek R1 8B (direct) réfléchit...');
                const reponse = await this.deepseekConnector.chat(message);
                
                if (reponse.success) {
                    // Afficher la réponse
                    console.log(`🤖 DeepSeek R1 8B: ${reponse.content}`);
                    console.log(`📊 [${reponse.model} | ${reponse.responseTime}ms | ${reponse.tokensUsed} tokens | Mémoire: ${reponse.memoryContext} contextes]\n`);
                    
                    // Mettre à jour la mémoire
                    const miseAJour = this.mettreAJourMemoire(message, reponse.content);
                    if (miseAJour.success) {
                        console.log(`💾 Mémoire mise à jour → Zone: ${miseAJour.zone}`);
                    }
                    
                    // Stocker la conversation
                    this.conversations.push({
                        timestamp: new Date().toISOString(),
                        user: message,
                        ai: reponse.content,
                        model: reponse.model,
                        responseTime: reponse.responseTime
                    });
                } else {
                    console.log('❌ Erreur lors de la génération de réponse');
                }
                
                // Continuer la conversation
                poserQuestion();
            });
        };
        
        poserQuestion();
    }
}

// Démarrer le test
console.log('🚀 Initialisation du test DeepSeek R1 8B direct + Mémoire Thermique...');
const test = new TestDeepSeekDirectMemoire();
test.demarrerConversation();
