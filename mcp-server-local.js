/**
 * Serveur MCP Local Simplifié pour LOUNA AI
 * Fonctionne sans dépendances externes
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { exec } = require('child_process');

class LocalMCPServer {
    constructor(port = 3002) {
        this.port = port;
        this.config = {
            allowInternet: true,
            allowDesktop: true,
            allowSystemCommands: true,
            debug: true
        };
        
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });
        
        console.log('🔧 Serveur MCP Local initialisé');
    }

    handleRequest(req, res) {
        // Headers CORS
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        
        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        const url = new URL(req.url, `http://localhost:${this.port}`);
        const pathname = url.pathname;

        this.log(`${req.method} ${pathname}`);

        try {
            if (pathname === '/mcp/status') {
                this.handleStatus(req, res);
            } else if (pathname === '/mcp/internet/test') {
                this.handleInternetTest(req, res);
            } else if (pathname === '/mcp/desktop/list') {
                this.handleDesktopList(req, res);
            } else if (pathname === '/mcp/system/execute') {
                this.handleSystemExecute(req, res);
            } else if (pathname === '/mcp/config') {
                this.handleConfig(req, res);
            } else {
                this.sendError(res, 404, 'Endpoint non trouvé');
            }
        } catch (error) {
            this.log(`Erreur: ${error.message}`);
            this.sendError(res, 500, error.message);
        }
    }

    handleStatus(req, res) {
        const status = {
            status: 'ok',
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            capabilities: {
                internet: this.config.allowInternet,
                desktop: this.config.allowDesktop,
                systemCommands: this.config.allowSystemCommands
            },
            system: {
                platform: os.platform(),
                arch: os.arch(),
                nodeVersion: process.version,
                uptime: process.uptime()
            }
        };
        
        this.sendJSON(res, status);
    }

    handleInternetTest(req, res) {
        if (!this.config.allowInternet) {
            this.sendError(res, 403, 'Accès Internet désactivé');
            return;
        }

        // Test simple de connectivité
        const testUrls = [
            'https://www.google.com',
            'https://httpbin.org/get',
            'https://api.github.com'
        ];

        const randomUrl = testUrls[Math.floor(Math.random() * testUrls.length)];
        
        // Simuler un test de connectivité
        setTimeout(() => {
            const result = {
                success: true,
                message: 'Connectivité Internet vérifiée',
                testUrl: randomUrl,
                timestamp: new Date().toISOString(),
                latency: Math.floor(50 + Math.random() * 100) + 'ms'
            };
            
            this.sendJSON(res, result);
        }, 500);
    }

    handleDesktopList(req, res) {
        if (!this.config.allowDesktop) {
            this.sendError(res, 403, 'Accès Bureau désactivé');
            return;
        }

        try {
            const desktopPath = path.join(os.homedir(), 'Desktop');
            const documentsPath = path.join(os.homedir(), 'Documents');
            
            let files = [];
            
            // Lister le bureau si accessible
            if (fs.existsSync(desktopPath)) {
                const desktopFiles = fs.readdirSync(desktopPath).slice(0, 10); // Limiter à 10 fichiers
                files = files.concat(desktopFiles.map(file => ({
                    name: file,
                    path: path.join(desktopPath, file),
                    location: 'Desktop',
                    isDirectory: fs.statSync(path.join(desktopPath, file)).isDirectory()
                })));
            }
            
            // Lister quelques documents si accessible
            if (fs.existsSync(documentsPath)) {
                const docFiles = fs.readdirSync(documentsPath).slice(0, 5); // Limiter à 5 fichiers
                files = files.concat(docFiles.map(file => ({
                    name: file,
                    path: path.join(documentsPath, file),
                    location: 'Documents',
                    isDirectory: fs.statSync(path.join(documentsPath, file)).isDirectory()
                })));
            }

            const result = {
                success: true,
                files: files,
                totalCount: files.length,
                timestamp: new Date().toISOString()
            };
            
            this.sendJSON(res, result);
        } catch (error) {
            this.sendError(res, 500, `Erreur accès bureau: ${error.message}`);
        }
    }

    handleSystemExecute(req, res) {
        if (!this.config.allowSystemCommands) {
            this.sendError(res, 403, 'Commandes système désactivées');
            return;
        }

        if (req.method !== 'POST') {
            this.sendError(res, 405, 'Méthode non autorisée');
            return;
        }

        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const command = data.command;

                if (!command) {
                    this.sendError(res, 400, 'Commande manquante');
                    return;
                }

                // Commandes sécurisées autorisées
                const safeCommands = [
                    'echo',
                    'date',
                    'whoami',
                    'pwd',
                    'ls -la',
                    'node --version',
                    'npm --version'
                ];

                const isCommandSafe = safeCommands.some(safe => 
                    command.startsWith(safe) || command === safe
                );

                if (!isCommandSafe) {
                    this.sendError(res, 403, 'Commande non autorisée pour des raisons de sécurité');
                    return;
                }

                exec(command, { timeout: 5000 }, (error, stdout, stderr) => {
                    const result = {
                        success: !error,
                        command: command,
                        stdout: stdout || '',
                        stderr: stderr || '',
                        timestamp: new Date().toISOString()
                    };

                    if (error) {
                        result.error = error.message;
                    }

                    this.sendJSON(res, result);
                });

            } catch (error) {
                this.sendError(res, 400, `Erreur parsing JSON: ${error.message}`);
            }
        });
    }

    handleConfig(req, res) {
        if (req.method === 'GET') {
            this.sendJSON(res, this.config);
        } else if (req.method === 'POST') {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });

            req.on('end', () => {
                try {
                    const newConfig = JSON.parse(body);
                    
                    // Mettre à jour la configuration
                    Object.assign(this.config, newConfig);
                    
                    this.log(`Configuration mise à jour: ${JSON.stringify(this.config)}`);
                    
                    this.sendJSON(res, {
                        success: true,
                        config: this.config,
                        message: 'Configuration mise à jour'
                    });
                } catch (error) {
                    this.sendError(res, 400, `Erreur parsing JSON: ${error.message}`);
                }
            });
        } else {
            this.sendError(res, 405, 'Méthode non autorisée');
        }
    }

    sendJSON(res, data) {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(200);
        res.end(JSON.stringify(data, null, 2));
    }

    sendError(res, code, message) {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(code);
        res.end(JSON.stringify({
            success: false,
            error: message,
            code: code,
            timestamp: new Date().toISOString()
        }));
    }

    log(message) {
        if (this.config.debug) {
            console.log(`[MCP] ${new Date().toLocaleTimeString()} - ${message}`);
        }
    }

    start() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.port, () => {
                console.log(`🚀 Serveur MCP Local démarré sur le port ${this.port}`);
                console.log(`📡 Accès Internet: ${this.config.allowInternet ? 'Activé' : 'Désactivé'}`);
                console.log(`📁 Accès Bureau: ${this.config.allowDesktop ? 'Activé' : 'Désactivé'}`);
                console.log(`⚡ Commandes Système: ${this.config.allowSystemCommands ? 'Activé' : 'Désactivé'}`);
                console.log(`🌐 Test de statut: http://localhost:${this.port}/mcp/status`);
                resolve();
            }).on('error', (err) => {
                console.error('❌ Erreur démarrage serveur MCP:', err.message);
                reject(err);
            });
        });
    }

    stop() {
        return new Promise((resolve) => {
            this.server.close(() => {
                console.log('🔴 Serveur MCP Local arrêté');
                resolve();
            });
        });
    }
}

// Démarrer le serveur si ce fichier est exécuté directement
if (require.main === module) {
    const server = new LocalMCPServer();
    
    server.start()
        .then(() => {
            console.log('✅ Serveur MCP Local prêt !');
            console.log('🎯 Testez depuis l\'interface LOUNA AI: Mode MCP');
            console.log('💡 Appuyez sur Ctrl+C pour arrêter');
        })
        .catch(error => {
            console.error('❌ Erreur:', error.message);
            process.exit(1);
        });

    // Gestion de l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du serveur...');
        server.stop().then(() => {
            process.exit(0);
        });
    });
}

module.exports = LocalMCPServer;
