<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - Interface Principale</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00ff88;
            --secondary-color: #0066cc;
            --dark-bg: #0a0a0a;
            --card-bg: rgba(0, 0, 0, 0.8);
            --gradient-bg: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-bg);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .hero-section {
            background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), 
                        radial-gradient(circle at 50% 50%, rgba(0,255,136,0.1) 0%, transparent 70%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
        }

        .hero-content {
            text-align: center;
            z-index: 2;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-color), #00ccff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(0,255,136,0.5);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: #cccccc;
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .stat-card {
            background: var(--card-bg);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0,255,136,0.1), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,255,136,0.3);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            color: #cccccc;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .action-buttons {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 3rem;
        }

        .action-btn {
            background: linear-gradient(45deg, var(--primary-color), #00cc6a);
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            color: black;
            font-weight: bold;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 200px;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,255,136,0.4);
            color: black;
        }

        .action-btn.secondary {
            background: linear-gradient(45deg, var(--secondary-color), #0088ff);
            color: white;
        }

        .action-btn.secondary:hover {
            color: white;
        }

        .floating-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: float 6s infinite linear;
            opacity: 0.7;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.7;
            }
            90% {
                opacity: 0.7;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .system-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-bg);
            border: 1px solid var(--primary-color);
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--primary-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .features-section {
            padding: 5rem 0;
            background: rgba(0,0,0,0.3);
        }

        .feature-card {
            background: var(--card-bg);
            border: 1px solid rgba(0,255,136,0.3);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Système de statut -->
    <div class="system-status">
        <div class="status-indicator">
            <div class="status-dot"></div>
            <small>Système Actif</small>
        </div>
        <div class="status-indicator">
            <div class="status-dot"></div>
            <small>Mémoire T7 Sync</small>
        </div>
    </div>

    <!-- Section Hero -->
    <section class="hero-section">
        <div class="floating-particles" id="particles"></div>
        
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">🧠 LOUNA AI</h1>
                <h2 class="hero-subtitle">Ultra-Autonome • Mémoire Thermique • Intelligence Avancée</h2>
                
                <!-- Statistiques en temps réel -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="neuronCount">1,064,012</div>
                        <div class="stat-label">🧠 Neurones Actifs</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="memoryEntries">7,448,045</div>
                        <div class="stat-label">💾 Entrées Mémoire</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="temperature">37.2°C</div>
                        <div class="stat-label">🌡️ Température</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="formations">14</div>
                        <div class="stat-label">🎓 Formations</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="energy">85.4%</div>
                        <div class="stat-label">⚡ Énergie</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">ACTIF</div>
                        <div class="stat-label">🔄 Système Möbius</div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="action-buttons">
                    <a href="/luna/chat" class="action-btn">
                        <i class="bi bi-chat-dots"></i>
                        Interface Chat
                    </a>
                    <a href="/luna/thermal" class="action-btn secondary">
                        <i class="bi bi-thermometer-half"></i>
                        Mémoire Thermique
                    </a>
                    <a href="/luna/brain" class="action-btn">
                        <i class="bi bi-cpu"></i>
                        Cerveau 3D
                    </a>
                    <a href="/luna/training" class="action-btn secondary">
                        <i class="bi bi-mortarboard"></i>
                        Formation
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Fonctionnalités -->
    <section class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-brain"></i>
                        </div>
                        <h4>Intelligence Avancée</h4>
                        <p>Système neuronal avec 1,064,012 neurones actifs et mémoire thermique intégrée.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-infinity"></i>
                        </div>
                        <h4>Système Möbius</h4>
                        <p>Pensées continues en bande de Möbius pour une réflexion perpétuelle et créative.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <h4>Sécurité Totale</h4>
                        <p>Sauvegarde automatique sur T7, éthique garantie et fidélité au créateur.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Créer les particules flottantes
        function createParticles() {
            const container = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                container.appendChild(particle);
            }
        }

        // Mettre à jour les métriques en temps réel
        function updateMetrics() {
            // Simulation de variations légères
            const baseTemp = 37.2;
            const baseEnergy = 85.4;
            
            document.getElementById('temperature').textContent = 
                (baseTemp + (Math.random() - 0.5) * 0.6).toFixed(1) + '°C';
            
            document.getElementById('energy').textContent = 
                (baseEnergy + (Math.random() - 0.5) * 5).toFixed(1) + '%';
        }

        // Charger les vraies données depuis l'API
        async function loadRealData() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neuronCount').textContent = 
                        data.neurones.toLocaleString();
                    document.getElementById('memoryEntries').textContent = 
                        data.memoire.toLocaleString();
                    document.getElementById('formations').textContent = 
                        data.formations || 14;
                }
            } catch (error) {
                console.log('Utilisation des données par défaut');
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            loadRealData();
            
            // Mise à jour périodique
            setInterval(updateMetrics, 3000);
            setInterval(loadRealData, 10000);
        });

        console.log('🧠 LOUNA AI Ultra-Autonome - Interface d\'accueil chargée');
        console.log('💾 Mémoire thermique synchronisée avec T7');
        console.log('✅ Toutes les fonctionnalités opérationnelles');
    </script>
</body>
</html>
