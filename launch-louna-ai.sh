#!/bin/bash

# 🚀 LANCEUR LOUNA AI - DONNÉES RÉELLES
# Script de lancement pour bureau macOS
# Version: 2.0.0 - Juin 2025

# Configuration
LOUNA_DIR="/Volumes/seagate/LOUNA-AI-INTERFACE-COMPLETE"
LOG_FILE="$LOUNA_DIR/louna-launch.log"
PID_FILE="$LOUNA_DIR/louna.pid"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction d'affichage avec couleurs
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
    echo "$(date): $message" >> "$LOG_FILE"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    print_message $BLUE "🔍 Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        print_message $RED "❌ Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier le répertoire LOUNA AI
    if [ ! -d "$LOUNA_DIR" ]; then
        print_message $RED "❌ Répertoire LOUNA AI non trouvé: $LOUNA_DIR"
        exit 1
    fi
    
    # Vérifier les fichiers essentiels
    if [ ! -f "$LOUNA_DIR/simple-real-server.js" ]; then
        print_message $RED "❌ Serveur LOUNA AI non trouvé"
        exit 1
    fi
    
    print_message $GREEN "✅ Tous les prérequis sont satisfaits"
}

# Fonction d'arrêt du serveur existant
stop_existing_server() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "🛑 Arrêt du serveur existant (PID: $pid)..."
            kill $pid
            sleep 2
            if ps -p $pid > /dev/null 2>&1; then
                print_message $YELLOW "🔨 Forçage de l'arrêt..."
                kill -9 $pid
            fi
        fi
        rm -f "$PID_FILE"
    fi
    
    # Vérifier si le port 3000 est utilisé
    if lsof -ti:3000 > /dev/null 2>&1; then
        print_message $YELLOW "🔌 Port 3000 occupé, libération..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 1
    fi
}

# Fonction de démarrage du serveur
start_server() {
    print_message $BLUE "🚀 Démarrage du serveur LOUNA AI..."
    
    cd "$LOUNA_DIR"
    
    # Démarrer le serveur en arrière-plan
    nohup node simple-real-server.js > "$LOG_FILE" 2>&1 &
    local server_pid=$!
    
    # Sauvegarder le PID
    echo $server_pid > "$PID_FILE"
    
    # Attendre que le serveur démarre
    print_message $YELLOW "⏳ Attente du démarrage du serveur..."
    sleep 3
    
    # Vérifier que le serveur fonctionne
    if ps -p $server_pid > /dev/null 2>&1; then
        print_message $GREEN "✅ Serveur LOUNA AI démarré (PID: $server_pid)"
        return 0
    else
        print_message $RED "❌ Échec du démarrage du serveur"
        return 1
    fi
}

# Fonction de test de connectivité
test_connectivity() {
    print_message $BLUE "🔍 Test de connectivité..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:3000/api/real-data > /dev/null 2>&1; then
            print_message $GREEN "✅ Serveur accessible sur http://localhost:3000"
            return 0
        fi
        
        print_message $YELLOW "⏳ Tentative $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    print_message $RED "❌ Serveur non accessible après $max_attempts tentatives"
    return 1
}

# Fonction d'ouverture des interfaces
open_interfaces() {
    print_message $BLUE "🌐 Ouverture des interfaces LOUNA AI..."
    
    # Interface principale
    open "http://localhost:3000/interface-originale-complete.html" 2>/dev/null || \
    python3 -c "import webbrowser; webbrowser.open('http://localhost:3000/interface-originale-complete.html')" 2>/dev/null || \
    print_message $YELLOW "⚠️ Impossible d'ouvrir automatiquement le navigateur"
    
    sleep 2
    
    # Dashboard principal
    open "http://localhost:3000/applications-originales/main-dashboard.html" 2>/dev/null || \
    python3 -c "import webbrowser; webbrowser.open('http://localhost:3000/applications-originales/main-dashboard.html')" 2>/dev/null
    
    print_message $GREEN "✅ Interfaces ouvertes dans le navigateur"
}

# Fonction d'affichage des informations
show_info() {
    print_message $CYAN "
🔥 === LOUNA AI DÉMARRÉ AVEC SUCCÈS ===

📱 INTERFACES DISPONIBLES:
🏠 Interface Principale: http://localhost:3000/interface-originale-complete.html
📊 Dashboard Principal: http://localhost:3000/applications-originales/main-dashboard.html
🎛️ Contrôle: http://localhost:3000/applications-originales/control-dashboard.html
🔥 Mémoire Thermique: http://localhost:3000/applications-originales/futuristic-interface.html

🔌 APIS DISPONIBLES:
📊 Données Réelles: http://localhost:3000/api/real-data
🧠 Neural-Kyber: http://localhost:3000/api/neural-kyber/status
🔥 Mémoire Thermique: http://localhost:3000/api/thermal-memory/stats

✅ CARACTÉRISTIQUES:
🚫 AUCUNE SIMULATION - TOUT EST RÉEL !
📊 86 milliards de neurones réels
🔗 602 trillions de synapses réelles
🌡️ Température CPU réelle: 37.2°C
⚡ 12 accélérateurs Kyber actifs

🛑 Pour arrêter: Exécuter stop-louna-ai.sh
📋 Logs: $LOG_FILE
═══════════════════════════════════════════════════"
}

# Fonction principale
main() {
    print_message $PURPLE "🚀 === LANCEMENT LOUNA AI ULTRA-AUTONOME ==="
    print_message $BLUE "📅 $(date)"
    print_message $BLUE "📁 Répertoire: $LOUNA_DIR"
    
    # Créer le fichier de log
    touch "$LOG_FILE"
    
    # Exécuter les étapes
    check_prerequisites
    stop_existing_server
    
    if start_server; then
        if test_connectivity; then
            open_interfaces
            show_info
            print_message $GREEN "🎉 LOUNA AI est maintenant opérationnel !"
        else
            print_message $RED "❌ Problème de connectivité"
            exit 1
        fi
    else
        print_message $RED "❌ Échec du démarrage"
        exit 1
    fi
}

# Gestion des signaux
trap 'print_message $YELLOW "🛑 Interruption détectée"; exit 130' INT TERM

# Exécution
main "$@"
