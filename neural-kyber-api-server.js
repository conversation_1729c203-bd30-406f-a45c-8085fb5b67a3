/**
 * Serveur API Neural-KYBER pour LOUNA AI
 * Connecte le système dynamique à l'interface web
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const DynamicNeuralKyberConnector = require('./dynamic-neural-kyber-connector');
const RealMemoryConnector = require('./real-memory-connector');

class NeuralKyberAPIServer {
    constructor(port = 3001) {
        this.port = port;
        this.connector = new DynamicNeuralKyberConnector();
        this.realMemory = new RealMemoryConnector();

        // Démarrer les connecteurs
        this.connector.start();
        this.realMemory.start();
        
        // Créer le serveur HTTP
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });
        
        console.log('🔗 Serveur API Neural-KYBER initialisé');
    }

    handleRequest(req, res) {
        // Headers CORS
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        
        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        const url = new URL(req.url, `http://localhost:${this.port}`);
        const pathname = url.pathname;

        this.log(`${req.method} ${pathname}`);

        try {
            if (pathname === '/api/neural-kyber/status') {
                this.handleStatus(req, res);
            } else if (pathname === '/api/neural-kyber/force-neurogenesis') {
                this.handleForceNeurogenesis(req, res);
            } else if (pathname === '/api/neural-kyber/install-kyber') {
                this.handleInstallKyber(req, res);
            } else if (pathname === '/api/neural-kyber/metrics') {
                this.handleMetrics(req, res);
            } else if (pathname === '/api/neural-kyber/config') {
                this.handleConfig(req, res);
            } else {
                this.sendError(res, 404, 'Endpoint non trouvé');
            }
        } catch (error) {
            this.log(`Erreur: ${error.message}`);
            this.sendError(res, 500, error.message);
        }
    }

    handleStatus(req, res) {
        const kyberState = this.connector.getCurrentState();
        const realState = this.realMemory.getCurrentState();

        // Fusionner les données réelles avec KYBER
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            neural: {
                totalNeurons: realState.neural.totalNeurons, // VOS VRAIES DONNÉES !
                neurogenesisRate: realState.neural.neurogenesisRate,
                zonesDistribution: realState.neural.zonesDistribution,
                activeZones: 6
            },
            kyber: kyberState.kyber,
            thermal: {
                temperature: realState.thermal.temperature,
                curseurPosition: realState.thermal.curseurPosition,
                zoneActuelle: realState.thermal.zoneActuelle,
                zones: realState.thermal.zones,
                efficiency: 96.3
            },
            metrics: {
                qiLevel: realState.metrics.qiLevel, // QI RÉEL Jean-Luc
                learningRate: 94.7,
                responseTime: kyberState.metrics.responseTime,
                systemLoad: kyberState.metrics.systemLoad,
                synapses: realState.metrics.synapses,
                tiroirs: realState.metrics.tiroirs,
                souvenirs: realState.metrics.souvenirs
            },
            systemActive: kyberState.isActive && realState.isActive,
            lastUpdate: realState.lastUpdate
        };

        this.sendJSON(res, response);
    }

    handleForceNeurogenesis(req, res) {
        try {
            // Forcer la neurogenèse
            this.connector.performNeurogenesis();
            
            const state = this.connector.getCurrentState();
            
            this.sendJSON(res, {
                success: true,
                message: 'Neurogenèse forcée',
                totalNeurons: state.neural.totalNeurons,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            this.sendError(res, 500, `Erreur neurogenèse: ${error.message}`);
        }
    }

    handleInstallKyber(req, res) {
        if (req.method !== 'POST') {
            this.sendError(res, 405, 'Méthode non autorisée');
            return;
        }

        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            try {
                const data = JSON.parse(body || '{}');
                const priority = data.priority || 'manual';
                const reason = data.reason || 'user_request';
                
                // Installer accélérateur KYBER
                this.connector.installKyberAccelerator(priority, reason);
                
                const state = this.connector.getCurrentState();
                
                this.sendJSON(res, {
                    success: true,
                    message: 'Accélérateur KYBER installé',
                    totalAccelerators: state.kyber.totalAccelerators,
                    activeAccelerators: state.kyber.activeAccelerators,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                this.sendError(res, 400, `Erreur installation KYBER: ${error.message}`);
            }
        });
    }

    handleMetrics(req, res) {
        const state = this.connector.getCurrentState();
        
        const detailedMetrics = {
            success: true,
            timestamp: new Date().toISOString(),
            performance: {
                qiLevel: state.metrics.qiLevel,
                learningRate: state.metrics.learningRate,
                responseTime: state.metrics.responseTime,
                systemLoad: state.metrics.systemLoad,
                adaptationSpeed: state.metrics.adaptationSpeed
            },
            neural: {
                totalNeurons: state.neural.totalNeurons,
                neurogenesisRate: state.neural.neurogenesisRate,
                activeZones: state.neural.activeZones,
                plasticityLevel: state.neural.plasticityLevel
            },
            kyber: {
                totalAccelerators: state.kyber.totalAccelerators,
                activeAccelerators: state.kyber.activeAccelerators,
                performanceBoost: state.kyber.performanceBoost,
                efficiency: state.kyber.efficiency,
                autoInstallEnabled: state.kyber.autoInstallEnabled
            },
            thermal: {
                temperature: state.thermal.temperature,
                efficiency: state.thermal.efficiency,
                zones: Object.keys(state.thermal.zones).map(zoneName => ({
                    name: zoneName,
                    temperature: state.thermal.zones[zoneName].temp,
                    neurons: state.thermal.zones[zoneName].neurons,
                    capacity: state.thermal.zones[zoneName].capacity,
                    usage: (state.thermal.zones[zoneName].neurons / state.thermal.zones[zoneName].capacity * 100).toFixed(1)
                }))
            }
        };
        
        this.sendJSON(res, detailedMetrics);
    }

    handleConfig(req, res) {
        if (req.method === 'GET') {
            const config = {
                success: true,
                neurogenesisRate: this.connector.neuralState.neurogenesisRate,
                kyberAutoInstall: this.connector.kyberState.autoInstallEnabled,
                maxAccelerators: this.connector.kyberState.maxAccelerators,
                thermalThreshold: 40.0,
                systemActive: this.connector.isActive
            };
            
            this.sendJSON(res, config);
        } else if (req.method === 'POST') {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });

            req.on('end', () => {
                try {
                    const newConfig = JSON.parse(body);
                    
                    // Mettre à jour la configuration
                    if (newConfig.neurogenesisRate !== undefined) {
                        this.connector.neuralState.neurogenesisRate = newConfig.neurogenesisRate;
                    }
                    
                    if (newConfig.kyberAutoInstall !== undefined) {
                        this.connector.kyberState.autoInstallEnabled = newConfig.kyberAutoInstall;
                    }
                    
                    this.log(`Configuration mise à jour: ${JSON.stringify(newConfig)}`);
                    
                    this.sendJSON(res, {
                        success: true,
                        message: 'Configuration mise à jour',
                        timestamp: new Date().toISOString()
                    });
                } catch (error) {
                    this.sendError(res, 400, `Erreur parsing JSON: ${error.message}`);
                }
            });
        } else {
            this.sendError(res, 405, 'Méthode non autorisée');
        }
    }

    sendJSON(res, data) {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(200);
        res.end(JSON.stringify(data, null, 2));
    }

    sendError(res, code, message) {
        res.setHeader('Content-Type', 'application/json');
        res.writeHead(code);
        res.end(JSON.stringify({
            success: false,
            error: message,
            code: code,
            timestamp: new Date().toISOString()
        }));
    }

    log(message) {
        console.log(`[API] ${new Date().toLocaleTimeString()} - ${message}`);
    }

    start() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.port, () => {
                console.log(`🚀 Serveur API Neural-KYBER démarré sur le port ${this.port}`);
                console.log(`📡 Endpoints disponibles:`);
                console.log(`   GET  /api/neural-kyber/status - État du système`);
                console.log(`   POST /api/neural-kyber/force-neurogenesis - Forcer neurogenèse`);
                console.log(`   POST /api/neural-kyber/install-kyber - Installer accélérateur`);
                console.log(`   GET  /api/neural-kyber/metrics - Métriques détaillées`);
                console.log(`   GET/POST /api/neural-kyber/config - Configuration`);
                console.log(`🌐 Test: http://localhost:${this.port}/api/neural-kyber/status`);
                resolve();
            }).on('error', (err) => {
                console.error('❌ Erreur démarrage serveur API:', err.message);
                reject(err);
            });
        });
    }

    stop() {
        return new Promise((resolve) => {
            this.connector.stop();
            this.realMemory.stop();
            this.server.close(() => {
                console.log('🔴 Serveur API Neural-KYBER arrêté');
                resolve();
            });
        });
    }
}

// Démarrer le serveur si ce fichier est exécuté directement
if (require.main === module) {
    const apiServer = new NeuralKyberAPIServer();
    
    apiServer.start()
        .then(() => {
            console.log('✅ Serveur API Neural-KYBER prêt !');
            console.log('🎯 L\'interface LOUNA AI peut maintenant se connecter au système dynamique');
            console.log('💡 Appuyez sur Ctrl+C pour arrêter');
        })
        .catch(error => {
            console.error('❌ Erreur:', error.message);
            process.exit(1);
        });

    // Gestion de l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du serveur API...');
        apiServer.stop().then(() => {
            process.exit(0);
        });
    });
}

module.exports = NeuralKyberAPIServer;
