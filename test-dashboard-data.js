/**
 * 🧪 TEST VALIDATION DONNÉES TABLEAU DE BORD
 * Vérifie que toutes les données affichées sont correctes
 */

const http = require('http');

// Configuration
const BASE_URL = 'http://localhost:3000';
const EXPECTED_VALUES = {
    qi: 185,
    temperature: 37.2,
    neurones: 86000000000,
    neuronsActifs: 14125,
    synapses: 602000000000000,
    synapsesActives: 523740000000000,
    accelerateurs: 12,
    formations: 2
};

/**
 * 🔍 Faire une requête HTTP
 */
function makeRequest(url) {
    return new Promise((resolve, reject) => {
        http.get(url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (error) {
                    reject(new Error(`Erreur parsing JSON: ${error.message}`));
                }
            });
        }).on('error', reject);
    });
}

/**
 * 🧪 Tester l'API principale
 */
async function testMainAPI() {
    console.log('\n🔍 === TEST API PRINCIPALE ===');
    
    try {
        const response = await makeRequest(`${BASE_URL}/api/real-data?format=json`);
        
        if (!response.success) {
            throw new Error('API retourne success: false');
        }
        
        const data = response.data;
        console.log('✅ API répond correctement');
        
        // Vérifier QI
        if (data.qi !== EXPECTED_VALUES.qi) {
            console.log(`❌ QI incorrect: ${data.qi} (attendu: ${EXPECTED_VALUES.qi})`);
        } else {
            console.log(`✅ QI correct: ${data.qi}`);
        }
        
        // Vérifier température
        if (data.temperature !== EXPECTED_VALUES.temperature) {
            console.log(`❌ Température incorrecte: ${data.temperature} (attendu: ${EXPECTED_VALUES.temperature})`);
        } else {
            console.log(`✅ Température correcte: ${data.temperature}°C`);
        }
        
        // Vérifier neurones
        if (data.neurones.total !== EXPECTED_VALUES.neurones) {
            console.log(`❌ Neurones total incorrect: ${data.neurones.total} (attendu: ${EXPECTED_VALUES.neurones})`);
        } else {
            console.log(`✅ Neurones total correct: ${data.neurones.total.toLocaleString()}`);
        }
        
        // Vérifier neurones actifs
        if (data.neurones.actifs !== EXPECTED_VALUES.neuronsActifs) {
            console.log(`❌ Neurones actifs incorrect: ${data.neurones.actifs} (attendu: ${EXPECTED_VALUES.neuronsActifs})`);
        } else {
            console.log(`✅ Neurones actifs correct: ${data.neurones.actifs.toLocaleString()}`);
        }
        
        // Vérifier accélérateurs
        if (data.accelerateurs.actifs !== EXPECTED_VALUES.accelerateurs) {
            console.log(`❌ Accélérateurs incorrect: ${data.accelerateurs.actifs} (attendu: ${EXPECTED_VALUES.accelerateurs})`);
        } else {
            console.log(`✅ Accélérateurs correct: ${data.accelerateurs.actifs}`);
        }
        
        return true;
        
    } catch (error) {
        console.log(`❌ Erreur API principale: ${error.message}`);
        return false;
    }
}

/**
 * 🧪 Tester l'API Neural-Kyber
 */
async function testNeuralKyberAPI() {
    console.log('\n🔍 === TEST API NEURAL-KYBER ===');
    
    try {
        const response = await makeRequest(`${BASE_URL}/api/neural-kyber/status`);
        
        if (!response.success) {
            throw new Error('API Neural-Kyber retourne success: false');
        }
        
        const metrics = response.metrics;
        console.log('✅ API Neural-Kyber répond correctement');
        
        // Vérifier QI
        if (metrics.qiLevel !== EXPECTED_VALUES.qi) {
            console.log(`❌ QI Neural-Kyber incorrect: ${metrics.qiLevel} (attendu: ${EXPECTED_VALUES.qi})`);
        } else {
            console.log(`✅ QI Neural-Kyber correct: ${metrics.qiLevel}`);
        }
        
        // Vérifier neurones
        if (metrics.neuronCount !== EXPECTED_VALUES.neurones) {
            console.log(`❌ Neurones Neural-Kyber incorrect: ${metrics.neuronCount} (attendu: ${EXPECTED_VALUES.neurones})`);
        } else {
            console.log(`✅ Neurones Neural-Kyber correct: ${metrics.neuronCount.toLocaleString()}`);
        }
        
        return true;
        
    } catch (error) {
        console.log(`❌ Erreur API Neural-Kyber: ${error.message}`);
        return false;
    }
}

/**
 * 🧪 Tester l'API Mémoire Thermique
 */
async function testThermalMemoryAPI() {
    console.log('\n🔍 === TEST API MÉMOIRE THERMIQUE ===');
    
    try {
        const response = await makeRequest(`${BASE_URL}/api/thermal-memory/stats`);
        
        if (!response.success) {
            throw new Error('API Mémoire Thermique retourne success: false');
        }
        
        const stats = response.stats;
        console.log('✅ API Mémoire Thermique répond correctement');
        
        // Vérifier formations
        if (stats.totalMemories !== EXPECTED_VALUES.formations) {
            console.log(`❌ Formations incorrectes: ${stats.totalMemories} (attendu: ${EXPECTED_VALUES.formations})`);
        } else {
            console.log(`✅ Formations correctes: ${stats.totalMemories}`);
        }
        
        // Vérifier température
        if (stats.temperature !== EXPECTED_VALUES.temperature) {
            console.log(`❌ Température thermique incorrecte: ${stats.temperature} (attendu: ${EXPECTED_VALUES.temperature})`);
        } else {
            console.log(`✅ Température thermique correcte: ${stats.temperature}°C`);
        }
        
        return true;
        
    } catch (error) {
        console.log(`❌ Erreur API Mémoire Thermique: ${error.message}`);
        return false;
    }
}

/**
 * 🧪 Tester l'API Cerveau
 */
async function testBrainAPI() {
    console.log('\n🔍 === TEST API CERVEAU ===');
    
    try {
        const response = await makeRequest(`${BASE_URL}/api/brain/status`);
        
        if (!response.success) {
            throw new Error('API Cerveau retourne success: false');
        }
        
        const brain = response.brain;
        console.log('✅ API Cerveau répond correctement');
        
        // Vérifier QI
        if (brain.qi !== EXPECTED_VALUES.qi) {
            console.log(`❌ QI Cerveau incorrect: ${brain.qi} (attendu: ${EXPECTED_VALUES.qi})`);
        } else {
            console.log(`✅ QI Cerveau correct: ${brain.qi}`);
        }
        
        return true;
        
    } catch (error) {
        console.log(`❌ Erreur API Cerveau: ${error.message}`);
        return false;
    }
}

/**
 * 🚀 Exécuter tous les tests
 */
async function runAllTests() {
    console.log('🧪 === VALIDATION DONNÉES TABLEAU DE BORD ===');
    console.log(`🎯 URL de base: ${BASE_URL}`);
    console.log(`📊 Valeurs attendues:`, EXPECTED_VALUES);
    
    const results = [];
    
    // Tester toutes les APIs
    results.push(await testMainAPI());
    results.push(await testNeuralKyberAPI());
    results.push(await testThermalMemoryAPI());
    results.push(await testBrainAPI());
    
    // Résumé
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log('\n📊 === RÉSUMÉ DES TESTS ===');
    console.log(`✅ Tests réussis: ${passed}/${total}`);
    
    if (passed === total) {
        console.log('🎉 TOUS LES TESTS SONT PASSÉS !');
        console.log('✅ Le tableau de bord affiche les bonnes données');
    } else {
        console.log('❌ Certains tests ont échoué');
        console.log('⚠️ Vérifiez les données du tableau de bord');
    }
    
    console.log('\n🔗 URLs à tester manuellement:');
    console.log(`📊 Dashboard: ${BASE_URL}/applications-originales/main-dashboard.html`);
    console.log(`🔥 API Données: ${BASE_URL}/api/real-data`);
    console.log(`📈 API JSON: ${BASE_URL}/api/real-data?format=json`);
}

// Exécuter les tests
runAllTests().catch(error => {
    console.error('❌ Erreur lors des tests:', error);
    process.exit(1);
});
