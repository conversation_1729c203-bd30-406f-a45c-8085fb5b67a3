// Diagnostic complet de l'interface LOUNA AI
console.log('🔍 === DIAGNOSTIC COMPLET DE L\'INTERFACE ===');

class DiagnosticInterface {
    constructor() {
        this.problemes = [];
        this.corrections = [];
        this.statistiques = {
            boutonsTotal: 0,
            boutonsFonctionnels: 0,
            liensTotal: 0,
            liensFonctionnels: 0,
            scriptsTotal: 0,
            scriptsFonctionnels: 0
        };
    }

    // Diagnostic des boutons
    diagnostiquerBoutons() {
        console.log('🔍 Diagnostic des boutons...');
        
        // Boutons avec onclick
        const boutonsAvecOnclick = document.querySelectorAll('button[onclick]');
        boutonsAvecOnclick.forEach(bouton => {
            this.statistiques.boutonsTotal++;
            const onclick = bouton.getAttribute('onclick');
            try {
                // Vérifier si la fonction existe
                const nomFonction = onclick.match(/(\w+)\(/)?.[1];
                if (nomFonction && typeof window[nomFonction] === 'function') {
                    this.statistiques.boutonsFonctionnels++;
                } else {
                    this.problemes.push({
                        type: 'bouton',
                        element: bouton,
                        probleme: `Fonction ${nomFonction} introuvable`,
                        solution: `Définir la fonction ${nomFonction}`
                    });
                }
            } catch (error) {
                this.problemes.push({
                    type: 'bouton',
                    element: bouton,
                    probleme: `Erreur onclick: ${error.message}`,
                    solution: 'Corriger la syntaxe onclick'
                });
            }
        });

        // Boutons sans onclick
        const boutonsSansOnclick = document.querySelectorAll('button:not([onclick])');
        boutonsSansOnclick.forEach(bouton => {
            if (!bouton.onclick) {
                this.statistiques.boutonsTotal++;
                this.problemes.push({
                    type: 'bouton',
                    element: bouton,
                    probleme: 'Bouton sans fonction',
                    solution: 'Ajouter une fonction onclick'
                });
            }
        });
    }

    // Diagnostic des liens
    diagnostiquerLiens() {
        console.log('🔍 Diagnostic des liens...');
        
        const liens = document.querySelectorAll('a');
        liens.forEach(lien => {
            this.statistiques.liensTotal++;
            const href = lien.getAttribute('href');
            
            if (!href || href === '#' || href === '') {
                this.problemes.push({
                    type: 'lien',
                    element: lien,
                    probleme: 'Lien vide ou invalide',
                    solution: 'Définir un href valide'
                });
            } else {
                this.statistiques.liensFonctionnels++;
            }
        });
    }

    // Diagnostic des scripts
    diagnostiquerScripts() {
        console.log('🔍 Diagnostic des scripts...');
        
        const scripts = document.querySelectorAll('script[src]');
        scripts.forEach(script => {
            this.statistiques.scriptsTotal++;
            const src = script.getAttribute('src');
            
            // Vérifier si le script est chargé (approximatif)
            if (script.readyState === 'complete' || script.readyState === 'loaded') {
                this.statistiques.scriptsFonctionnels++;
            } else {
                this.problemes.push({
                    type: 'script',
                    element: script,
                    probleme: `Script non chargé: ${src}`,
                    solution: 'Vérifier le chemin du script'
                });
            }
        });
    }

    // Diagnostic des fonctions critiques
    diagnostiquerFonctionsCritiques() {
        console.log('🔍 Diagnostic des fonctions critiques...');
        
        const fonctionsCritiques = [
            'activateHibernation',
            'activateSleep',
            'wakeupAgent',
            'openSurveillance',
            'openBackup',
            'openMemoryControl',
            'fixAllInterfaces',
            'pauserEvolution',
            'reprendreEvolution',
            'analyserSysteme',
            'forcerEvolutionVisible',
            'ouvrirVerification',
            'envoyerMessageDeepSeek',
            'retourAccueil'
        ];

        fonctionsCritiques.forEach(nomFonction => {
            if (typeof window[nomFonction] !== 'function') {
                this.problemes.push({
                    type: 'fonction',
                    element: null,
                    probleme: `Fonction critique manquante: ${nomFonction}`,
                    solution: `Définir la fonction ${nomFonction}`
                });
            }
        });
    }

    // Corriger automatiquement les problèmes
    corrigerProblemes() {
        console.log('🔧 Correction automatique des problèmes...');
        
        this.problemes.forEach(probleme => {
            switch (probleme.type) {
                case 'bouton':
                    if (probleme.probleme.includes('sans fonction')) {
                        probleme.element.onclick = () => {
                            console.log('⚠️ Bouton temporairement désactivé:', probleme.element.textContent);
                        };
                        this.corrections.push(`Bouton "${probleme.element.textContent}" corrigé`);
                    }
                    break;
                    
                case 'lien':
                    if (probleme.probleme.includes('vide ou invalide')) {
                        probleme.element.href = 'javascript:void(0)';
                        probleme.element.onclick = () => {
                            console.log('⚠️ Lien temporairement désactivé:', probleme.element.textContent);
                        };
                        this.corrections.push(`Lien "${probleme.element.textContent}" corrigé`);
                    }
                    break;
            }
        });
    }

    // Générer le rapport
    genererRapport() {
        console.log('📊 === RAPPORT DE DIAGNOSTIC ===');
        console.log(`🔘 Boutons: ${this.statistiques.boutonsFonctionnels}/${this.statistiques.boutonsTotal} fonctionnels`);
        console.log(`🔗 Liens: ${this.statistiques.liensFonctionnels}/${this.statistiques.liensTotal} fonctionnels`);
        console.log(`📜 Scripts: ${this.statistiques.scriptsFonctionnels}/${this.statistiques.scriptsTotal} chargés`);
        console.log(`❌ Problèmes détectés: ${this.problemes.length}`);
        console.log(`✅ Corrections appliquées: ${this.corrections.length}`);
        
        if (this.problemes.length > 0) {
            console.log('🚨 === PROBLÈMES DÉTECTÉS ===');
            this.problemes.forEach((probleme, index) => {
                console.log(`${index + 1}. ${probleme.type.toUpperCase()}: ${probleme.probleme}`);
                console.log(`   Solution: ${probleme.solution}`);
            });
        }
        
        if (this.corrections.length > 0) {
            console.log('🔧 === CORRECTIONS APPLIQUÉES ===');
            this.corrections.forEach((correction, index) => {
                console.log(`${index + 1}. ${correction}`);
            });
        }
        
        const tauxReussite = Math.round(
            ((this.statistiques.boutonsFonctionnels + this.statistiques.liensFonctionnels) /
            (this.statistiques.boutonsTotal + this.statistiques.liensTotal)) * 100
        );
        
        console.log(`🎯 Taux de réussite global: ${tauxReussite}%`);
        
        if (tauxReussite >= 95) {
            console.log('🎉 INTERFACE EXCELLENTE !');
        } else if (tauxReussite >= 80) {
            console.log('✅ Interface fonctionnelle');
        } else {
            console.log('⚠️ Interface nécessite des améliorations');
        }
        
        return {
            statistiques: this.statistiques,
            problemes: this.problemes,
            corrections: this.corrections,
            tauxReussite: tauxReussite
        };
    }

    // Lancer le diagnostic complet
    lancerDiagnostic() {
        console.log('🚀 Lancement du diagnostic complet...');
        
        this.diagnostiquerBoutons();
        this.diagnostiquerLiens();
        this.diagnostiquerScripts();
        this.diagnostiquerFonctionsCritiques();
        this.corrigerProblemes();
        
        return this.genererRapport();
    }
}

// Fonction globale pour lancer le diagnostic
function diagnostiquerInterface() {
    const diagnostic = new DiagnosticInterface();
    return diagnostic.lancerDiagnostic();
}

// Exporter pour utilisation globale
window.diagnostiquerInterface = diagnostiquerInterface;
window.DiagnosticInterface = DiagnosticInterface;

// Lancer automatiquement après chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🔍 Lancement du diagnostic automatique...');
        diagnostiquerInterface();
    }, 3000);
});

console.log('📝 Diagnostic interface chargé. Utilisez diagnostiquerInterface() pour lancer manuellement.');
