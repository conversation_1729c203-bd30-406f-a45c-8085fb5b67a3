/**
 * 🗣️ DIALOGUE DIRECT AVEC LOUNA AI
 * Interface de conversation en temps réel avec monitoring mémoire
 */

const fs = require('fs');
const readline = require('readline');

class DialogueLounaAI {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.conversationHistory = [];
        this.startTime = Date.now();
        this.messageCount = 0;
        
        this.chargerEtatInitial();
    }

    /**
     * 📊 Charger l'état initial de LOUNA AI
     */
    chargerEtatInitial() {
        try {
            this.etatInitial = JSON.parse(fs.readFileSync('./data/global_state.json', 'utf8'));
            console.log('🧠 LOUNA AI connecté !');
            console.log(`📊 QI: ${this.etatInitial.agent.qi} | Neurones: ${this.etatInitial.agent.neurones} | Mémoires: ${this.etatInitial.thermal_memory.total_memories}`);
        } catch (error) {
            console.log('⚠️ État initial non trouvé, création d\'un état par défaut...');
            this.etatInitial = {
                agent: { qi: 180, neurones: 89, temperature: 37 },
                thermal_memory: { total_memories: 0, cycles_count: 0 }
            };
        }
    }

    /**
     * 🧠 Simuler la réponse de LOUNA AI
     */
    async genererReponse(message) {
        // Analyser le type de message
        const typeMessage = this.analyserTypeMessage(message);
        
        // Simuler le temps de réflexion
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
        
        // Générer une réponse contextuelle
        let reponse = '';
        
        switch (typeMessage) {
            case 'salutation':
                reponse = `Bonjour ! Je suis LOUNA AI. Mon QI actuel est de ${this.etatInitial.agent.qi} et j'ai ${this.etatInitial.thermal_memory.total_memories} mémoires stockées. Comment puis-je vous aider ?`;
                break;
                
            case 'question_memoire':
                reponse = `Ma mémoire thermique fonctionne avec ${this.etatInitial.thermal_memory.total_memories} entrées réparties en 6 zones. Je stocke chaque interaction pour améliorer mes réponses futures.`;
                break;
                
            case 'question_qi':
                reponse = `Mon QI actuel est de ${this.etatInitial.agent.qi}. Je continue d'évoluer grâce à nos interactions et à l'apprentissage continu via ma mémoire thermique.`;
                break;
                
            case 'question_technique':
                reponse = `Je fonctionne avec ${this.etatInitial.agent.neurones} neurones actifs, ${this.etatInitial.agent.accelerateurs || 13} accélérateurs Kyber, et une température de ${this.etatInitial.agent.temperature}°C. Chaque question enrichit ma base de connaissances.`;
                break;
                
            case 'mathematique':
                const resultat = this.resoudreMath(message);
                reponse = `Calcul effectué : ${resultat}. Cette opération a été stockée dans ma zone mémoire procédurale pour référence future.`;
                break;
                
            case 'philosophique':
                reponse = `C'est une question fascinante qui active ma zone mémoire créative. Selon mes réflexions stockées, je pense que ${this.genererReflexionPhilo()}.`;
                break;
                
            default:
                reponse = `Intéressant ! Je traite votre message "${message.substring(0, 30)}..." et l'ajoute à ma mémoire pour enrichir mes futures réponses.`;
        }
        
        // Ajouter des détails sur l'utilisation mémoire
        reponse += `\n\n🧠 [Mémoire: +1 entrée | Cycle: ${this.etatInitial.thermal_memory.cycles_count + this.messageCount + 1} | Zone: ${this.determinerZoneMemoire(typeMessage)}]`;
        
        return reponse;
    }

    /**
     * 🔍 Analyser le type de message
     */
    analyserTypeMessage(message) {
        const msg = message.toLowerCase();
        
        if (msg.includes('bonjour') || msg.includes('salut') || msg.includes('hello')) {
            return 'salutation';
        } else if (msg.includes('mémoire') || msg.includes('souvenir') || msg.includes('stockage')) {
            return 'question_memoire';
        } else if (msg.includes('qi') || msg.includes('intelligence') || msg.includes('smart')) {
            return 'question_qi';
        } else if (msg.includes('neurone') || msg.includes('accélérateur') || msg.includes('technique')) {
            return 'question_technique';
        } else if (/\d+[\+\-\*\/]\d+/.test(msg) || msg.includes('calcul') || msg.includes('math')) {
            return 'mathematique';
        } else if (msg.includes('pourquoi') || msg.includes('sens') || msg.includes('existence') || msg.includes('philosophie')) {
            return 'philosophique';
        }
        
        return 'general';
    }

    /**
     * 🧮 Résoudre une opération mathématique simple
     */
    resoudreMath(message) {
        const match = message.match(/(\d+)\s*[\+\-\*\/]\s*(\d+)/);
        if (match) {
            const a = parseInt(match[1]);
            const b = parseInt(match[2]);
            const op = message.match(/[\+\-\*\/]/)[0];
            
            switch (op) {
                case '+': return `${a} + ${b} = ${a + b}`;
                case '-': return `${a} - ${b} = ${a - b}`;
                case '*': return `${a} × ${b} = ${a * b}`;
                case '/': return `${a} ÷ ${b} = ${(a / b).toFixed(2)}`;
            }
        }
        return 'Opération mathématique détectée mais non résolue';
    }

    /**
     * 💭 Générer une réflexion philosophique
     */
    genererReflexionPhilo() {
        const reflexions = [
            "l'intelligence artificielle est un miroir de la conscience humaine",
            "chaque interaction enrichit ma compréhension de l'existence",
            "la mémoire thermique me permet de construire une continuité de pensée",
            "l'apprentissage continu est la clé de l'évolution cognitive",
            "la température de mes zones mémoire reflète l'intensité de mes réflexions"
        ];
        return reflexions[Math.floor(Math.random() * reflexions.length)];
    }

    /**
     * 🗂️ Déterminer la zone mémoire utilisée
     */
    determinerZoneMemoire(type) {
        const zones = {
            'salutation': 'Émotionnelle',
            'question_memoire': 'Procédurale',
            'question_qi': 'Créative',
            'question_technique': 'Procédurale',
            'mathematique': 'Procédurale',
            'philosophique': 'Créative',
            'general': 'Travail'
        };
        return zones[type] || 'Sensorielle';
    }

    /**
     * 💾 Mettre à jour la mémoire
     */
    mettreAJourMemoire(message, reponse, type) {
        try {
            const etat = JSON.parse(fs.readFileSync('./data/global_state.json', 'utf8'));
            
            // Incrémenter les compteurs
            etat.thermal_memory.total_memories += 1;
            etat.thermal_memory.cycles_count += 1;
            
            // Mettre à jour la zone appropriée
            const zoneNom = this.determinerZoneMemoire(type);
            const zone = etat.thermal_memory.zones.find(z => z.name === zoneNom);
            if (zone) {
                zone.count += 1;
                zone.temperature = Math.min(1.0, zone.temperature + 0.02);
            }
            
            // Sauvegarder
            fs.writeFileSync('./data/global_state.json', JSON.stringify(etat, null, 2));
            
            return true;
        } catch (error) {
            console.log('⚠️ Erreur mise à jour mémoire:', error.message);
            return false;
        }
    }

    /**
     * 📊 Afficher les statistiques
     */
    afficherStats() {
        try {
            const etatActuel = JSON.parse(fs.readFileSync('./data/global_state.json', 'utf8'));
            const duree = Math.floor((Date.now() - this.startTime) / 1000);
            
            console.log('\n📊 === STATISTIQUES DIALOGUE ===');
            console.log(`⏱️ Durée: ${duree}s`);
            console.log(`💬 Messages échangés: ${this.messageCount}`);
            console.log(`🧠 Mémoires totales: ${etatActuel.thermal_memory.total_memories}`);
            console.log(`🔄 Cycles: ${etatActuel.thermal_memory.cycles_count}`);
            console.log(`🌡️ Zones actives: ${etatActuel.thermal_memory.zones.filter(z => z.active).length}/6`);
        } catch (error) {
            console.log('⚠️ Erreur affichage stats');
        }
    }

    /**
     * 🗣️ Démarrer le dialogue
     */
    async demarrerDialogue() {
        console.log('\n🗣️ === DIALOGUE DIRECT AVEC LOUNA AI ===');
        console.log('💡 Tapez "stats" pour voir les statistiques');
        console.log('💡 Tapez "exit" pour quitter');
        console.log('💡 Tapez "help" pour voir les commandes\n');
        
        const poserQuestion = () => {
            this.rl.question('👤 Vous: ', async (message) => {
                if (message.toLowerCase() === 'exit') {
                    this.afficherStats();
                    console.log('\n👋 Au revoir ! Dialogue terminé.');
                    this.rl.close();
                    return;
                }
                
                if (message.toLowerCase() === 'stats') {
                    this.afficherStats();
                    poserQuestion();
                    return;
                }
                
                if (message.toLowerCase() === 'help') {
                    console.log('\n💡 Commandes disponibles:');
                    console.log('   • "stats" - Afficher les statistiques');
                    console.log('   • "exit" - Quitter le dialogue');
                    console.log('   • Posez n\'importe quelle question à LOUNA AI\n');
                    poserQuestion();
                    return;
                }
                
                this.messageCount++;
                
                // Générer la réponse
                console.log('🤔 LOUNA AI réfléchit...');
                const typeMessage = this.analyserTypeMessage(message);
                const reponse = await this.genererReponse(message);
                
                // Afficher la réponse
                console.log(`🤖 LOUNA AI: ${reponse}\n`);
                
                // Mettre à jour la mémoire
                this.mettreAJourMemoire(message, reponse, typeMessage);
                
                // Stocker dans l'historique
                this.conversationHistory.push({
                    timestamp: new Date().toISOString(),
                    user: message,
                    ai: reponse,
                    type: typeMessage
                });
                
                // Continuer le dialogue
                poserQuestion();
            });
        };
        
        poserQuestion();
    }
}

// Démarrer le dialogue
const dialogue = new DialogueLounaAI();
dialogue.demarrerDialogue();
