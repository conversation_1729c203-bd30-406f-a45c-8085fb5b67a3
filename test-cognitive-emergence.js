/**
 * 🧪 TEST ÉMERGENCE COGNITIVE
 * Test du moteur d'émergence cognitive et détection de patterns
 */

const PureBrainSystem = require('./pure-brain-system');

async function testCognitiveEmergence() {
    console.log('🧪 === TEST ÉMERGENCE COGNITIVE ===\n');
    
    try {
        // Initialiser le cerveau avec émergence cognitive
        console.log('🚀 Initialisation du cerveau avec émergence cognitive...');
        const brain = new PureBrainSystem();
        
        // Attendre l'initialisation complète
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 1: Vérifier l'initialisation de l'émergence
        console.log('\n📝 Test 1: Vérification émergence cognitive');
        const emergenceMetrics = brain.getEmergenceMetrics();
        if (emergenceMetrics) {
            console.log('✅ Moteur d\'émergence cognitive actif');
            console.log(`   📊 Niveau d'émergence: ${(emergenceMetrics.emergenceLevel * 100).toFixed(1)}%`);
            console.log(`   🎨 Index créativité: ${(emergenceMetrics.creativityIndex * 100).toFixed(1)}%`);
        } else {
            console.log('❌ Moteur d\'émergence non initialisé');
            return;
        }
        
        // Test 2: Stimuler l'activité pour déclencher l'émergence
        console.log('\n📝 Test 2: Stimulation pour émergence');
        console.log('🧠 Activation de neurones pour stimuler l\'émergence...');
        
        // Plusieurs activations pour créer des patterns
        await brain.activateNeurons(0.2, 10000, 'emergence_test_1');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        await brain.activateNeurons(0.3, 8000, 'emergence_test_2');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        await brain.activateNeurons(0.15, 12000, 'emergence_test_3');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 3: Vérifier les patterns émergents détectés
        console.log('\n📝 Test 3: Patterns émergents détectés');
        const emergentPatterns = brain.getEmergentPatterns();
        console.log(`🌟 ${emergentPatterns.length} patterns émergents détectés:`);
        
        emergentPatterns.slice(0, 5).forEach((pattern, index) => {
            console.log(`   ${index + 1}. ${pattern.category} (force: ${(pattern.strength * 100).toFixed(0)}%, nouveauté: ${(pattern.noveltyScore * 100).toFixed(0)}%)`);
        });
        
        // Test 4: Apprentissage et mémoire pour stimuler l'émergence
        console.log('\n📝 Test 4: Apprentissage pour émergence');
        brain.learn('emergence_pattern_recognition', 0.4);
        brain.learn('spontaneous_connection_creation', 0.3);
        brain.learn('cognitive_innovation', 0.5);
        
        brain.remember('Pattern émergent détecté', 'emergent_behavior', 0.8);
        brain.remember('Connexion spontanée créée', 'emergent_behavior', 0.7);
        brain.remember('Innovation cognitive générée', 'cognitive_innovation', 0.9);
        
        // Test 5: Forcer des innovations cognitives
        console.log('\n📝 Test 5: Innovation cognitive forcée');
        console.log('💡 Déclenchement d\'innovations cognitives...');
        
        for (let i = 0; i < 3; i++) {
            brain.triggerCognitiveInnovation();
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Test 6: Forcer des connexions spontanées
        console.log('\n📝 Test 6: Connexions spontanées forcées');
        console.log('⚡ Création de connexions spontanées...');
        
        const synapsesBefore = brain.brainState.totalSynapses;
        brain.triggerSpontaneousConnections();
        await new Promise(resolve => setTimeout(resolve, 1000));
        const synapsesAfter = brain.brainState.totalSynapses;
        
        console.log(`🔗 Synapses créées: ${synapsesAfter - synapsesBefore}`);
        
        // Test 7: Laisser le cerveau évoluer naturellement
        console.log('\n📝 Test 7: Évolution naturelle (20 secondes)');
        console.log('🧠 Le cerveau évolue naturellement...');
        
        const startTime = Date.now();
        while (Date.now() - startTime < 20000) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Afficher l'évolution
            const currentMetrics = brain.getEmergenceMetrics();
            console.log(`   📊 Émergence: ${(currentMetrics.emergenceLevel * 100).toFixed(1)}% | Créativité: ${(currentMetrics.creativityIndex * 100).toFixed(1)}% | Patterns: ${currentMetrics.activePatterns}`);
        }
        
        // Test 8: Métriques finales d'émergence
        console.log('\n📝 Test 8: Métriques finales d\'émergence');
        const finalMetrics = brain.getEmergenceMetrics();
        
        console.log('\n🌟 === MÉTRIQUES ÉMERGENCE FINALES ===');
        console.log(`📊 Niveau d'émergence: ${(finalMetrics.emergenceLevel * 100).toFixed(1)}%`);
        console.log(`🎨 Index créativité: ${(finalMetrics.creativityIndex * 100).toFixed(1)}%`);
        console.log(`🔍 Patterns détectés: ${finalMetrics.totalPatternsDetected}`);
        console.log(`🆕 Comportements nouveaux: ${finalMetrics.novelBehaviorsFound}`);
        console.log(`⚡ Connexions spontanées: ${finalMetrics.spontaneousConnections.toLocaleString()}`);
        console.log(`📋 Patterns actifs: ${finalMetrics.activePatterns}`);
        console.log(`🏷️ Catégories: ${finalMetrics.patternCategories.join(', ')}`);
        
        // Test 9: Nouveaux comportements détectés
        console.log('\n📝 Test 9: Nouveaux comportements');
        const novelBehaviors = brain.getNovelBehaviors();
        
        if (novelBehaviors.length > 0) {
            console.log(`🆕 ${novelBehaviors.length} nouveaux comportements détectés:`);
            novelBehaviors.forEach((behavior, index) => {
                console.log(`   ${index + 1}. ${behavior}`);
            });
        } else {
            console.log('🆕 Aucun nouveau comportement détecté (normal pour un test court)');
        }
        
        // Test 10: Patterns émergents finaux
        console.log('\n📝 Test 10: Patterns émergents finaux');
        const finalPatterns = brain.getEmergentPatterns();
        
        console.log(`\n🌟 ${finalPatterns.length} patterns émergents actifs:`);
        finalPatterns.slice(0, 10).forEach((pattern, index) => {
            console.log(`   ${index + 1}. ${pattern.category}`);
            console.log(`      Force: ${(pattern.strength * 100).toFixed(0)}% | Nouveauté: ${(pattern.noveltyScore * 100).toFixed(0)}%`);
            console.log(`      Renforcé: ${pattern.reinforcementCount} fois`);
        });
        
        // Test 11: Comparaison avant/après
        console.log('\n📝 Test 11: Évolution du cerveau');
        const brainStats = brain.getCompleteMetrics();
        
        console.log('\n📊 === ÉVOLUTION CERVEAU ===');
        console.log(`🧠 Neurones en veille: ${brainStats.state.standby.toLocaleString()}`);
        console.log(`⚡ Neurones actifs: ${brainStats.state.active.toLocaleString()}`);
        console.log(`🔗 Synapses totales: ${brainStats.state.totalSynapses.toLocaleString()}`);
        console.log(`💭 Pensées générées: ${brainStats.metrics.thoughtsGenerated.toLocaleString()}`);
        console.log(`📚 Souvenirs: ${brain.brainMemory.memories.size}`);
        console.log(`🎓 Apprentissages: ${brain.brainMemory.learnings.size}`);
        
        // Test 12: Vérification de l'émergence réelle
        console.log('\n📝 Test 12: Vérification émergence réelle');
        
        const emergenceIndicators = {
            patternsDetected: finalMetrics.totalPatternsDetected > 0,
            spontaneousConnections: finalMetrics.spontaneousConnections > 0,
            creativityIndex: finalMetrics.creativityIndex > 0.1,
            emergenceLevel: finalMetrics.emergenceLevel > 0.1,
            novelBehaviors: finalMetrics.novelBehaviorsFound > 0
        };
        
        console.log('\n✅ Indicateurs d\'émergence:');
        Object.entries(emergenceIndicators).forEach(([indicator, present]) => {
            const icon = present ? '✅' : '❌';
            console.log(`   ${icon} ${indicator}: ${present ? 'DÉTECTÉ' : 'Non détecté'}`);
        });
        
        const emergenceScore = Object.values(emergenceIndicators).filter(Boolean).length;
        console.log(`\n🎯 Score d'émergence: ${emergenceScore}/5 (${(emergenceScore/5*100).toFixed(0)}%)`);
        
        if (emergenceScore >= 3) {
            console.log('🎉 ÉMERGENCE COGNITIVE CONFIRMÉE !');
        } else if (emergenceScore >= 1) {
            console.log('🌱 Émergence cognitive en développement');
        } else {
            console.log('⏳ Émergence cognitive en attente');
        }
        
        console.log('\n✅ Tests d\'émergence cognitive terminés !');
        console.log('🧠 Le cerveau continue d\'évoluer et de créer des patterns émergents...');
        
        // Laisser tourner encore un peu pour voir l'évolution continue
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        console.log('\n🛑 Fin du test émergence cognitive');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests d\'émergence:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testCognitiveEmergence().catch(console.error);
