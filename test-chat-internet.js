/**
 * Script de test réel de connexion Internet pour les interfaces de chat
 */

const fs = require('fs');
const path = require('path');

console.log('🌐 === TEST CONNEXION INTERNET INTERFACES CHAT ===');

// Interfaces de chat à tester
const chatInterfaces = [
    'chat-agents.html',
    'chat-cognitif-complet.html',
    'chat.html'
];

const appsDir = './applications-originales';

chatInterfaces.forEach(chatFile => {
    console.log(`\n🔍 === ANALYSE CONNEXION INTERNET ${chatFile.toUpperCase()} ===`);
    
    try {
        const filePath = path.join(appsDir, chatFile);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 1. Vérifier les endpoints API
        console.log(`   📡 Endpoints API dans ${chatFile}:`);
        
        const apiEndpoints = [
            { name: 'API Chat Local', pattern: /\/api\/chat/g },
            { name: 'API Cognitive', pattern: /\/api\/cognitive/g },
            { name: 'API DeepSeek', pattern: /\/api\/deepseek/g },
            { name: 'API Global', pattern: /\/api\/global/g },
            { name: 'API Search', pattern: /\/api\/search/g }
        ];
        
        apiEndpoints.forEach(api => {
            const matches = (content.match(api.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${api.name}: ${matches} appels détectés`);
            } else {
                console.log(`   ⚠️ ${api.name}: Aucun appel`);
            }
        });
        
        // 2. Vérifier les URLs externes
        console.log(`   🌍 URLs externes dans ${chatFile}:`);
        
        const externalUrls = content.match(/https?:\/\/[^\s"'<>]+/g) || [];
        if (externalUrls.length > 0) {
            console.log(`   ✅ ${externalUrls.length} URLs externes trouvées:`);
            externalUrls.forEach(url => {
                console.log(`     - ${url}`);
            });
        } else {
            console.log(`   ⚠️ Aucune URL externe détectée`);
        }
        
        // 3. Vérifier les fonctions de recherche web
        console.log(`   🔍 Fonctions de recherche dans ${chatFile}:`);
        
        const searchFunctions = [
            { name: 'Web Search', pattern: /function.*search|search.*function/gi },
            { name: 'Google Search', pattern: /google.*search|search.*google/gi },
            { name: 'Internet Query', pattern: /internet.*query|query.*internet/gi },
            { name: 'Online Search', pattern: /online.*search|search.*online/gi }
        ];
        
        searchFunctions.forEach(func => {
            const matches = (content.match(func.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${func.name}: ${matches} références`);
            } else {
                console.log(`   ⚠️ ${func.name}: Non détecté`);
            }
        });
        
        // 4. Vérifier les gestions d'erreur réseau
        console.log(`   🚨 Gestion erreurs réseau dans ${chatFile}:`);
        
        const errorHandling = [
            { name: 'Network Error', pattern: /network.*error|error.*network/gi },
            { name: 'Connection Error', pattern: /connection.*error|error.*connection/gi },
            { name: 'Fetch Error', pattern: /fetch.*error|error.*fetch/gi },
            { name: 'Timeout Error', pattern: /timeout.*error|error.*timeout/gi },
            { name: 'CORS Error', pattern: /cors.*error|error.*cors/gi }
        ];
        
        errorHandling.forEach(error => {
            const matches = (content.match(error.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${error.name}: ${matches} gestions`);
            } else {
                console.log(`   ⚠️ ${error.name}: Non géré`);
            }
        });
        
        // 5. Vérifier les headers CORS
        console.log(`   🔒 Configuration CORS dans ${chatFile}:`);
        
        const corsConfig = [
            { name: 'CORS Headers', pattern: /cors|cross-origin/gi },
            { name: 'Access-Control', pattern: /access-control/gi },
            { name: 'Origin Headers', pattern: /origin.*header|header.*origin/gi }
        ];
        
        corsConfig.forEach(cors => {
            const matches = (content.match(cors.pattern) || []).length;
            if (matches > 0) {
                console.log(`   ✅ ${cors.name}: ${matches} configurations`);
            } else {
                console.log(`   ⚠️ ${cors.name}: Non configuré`);
            }
        });
        
        // 6. Vérifier les fonctions fetch
        console.log(`   📤 Appels réseau dans ${chatFile}:`);
        
        const fetchCalls = content.match(/fetch\s*\(/g) || [];
        const ajaxCalls = content.match(/\$\.ajax|\$\.get|\$\.post/g) || [];
        const xmlHttpCalls = content.match(/XMLHttpRequest|xhr/gi) || [];
        
        console.log(`   ✅ Fetch API: ${fetchCalls.length} appels`);
        console.log(`   ✅ jQuery AJAX: ${ajaxCalls.length} appels`);
        console.log(`   ✅ XMLHttpRequest: ${xmlHttpCalls.length} appels`);
        
        // 7. Score de connectivité Internet
        let internetScore = 0;
        const maxScore = 10;
        
        // Points pour les APIs
        if (apiEndpoints.some(api => (content.match(api.pattern) || []).length > 0)) internetScore += 2;
        
        // Points pour les URLs externes
        if (externalUrls.length > 0) internetScore += 2;
        
        // Points pour les fonctions de recherche
        if (searchFunctions.some(func => (content.match(func.pattern) || []).length > 0)) internetScore += 2;
        
        // Points pour la gestion d'erreur
        if (errorHandling.some(error => (content.match(error.pattern) || []).length > 0)) internetScore += 1;
        
        // Points pour CORS
        if (corsConfig.some(cors => (content.match(cors.pattern) || []).length > 0)) internetScore += 1;
        
        // Points pour les appels réseau
        if (fetchCalls.length > 0 || ajaxCalls.length > 0 || xmlHttpCalls.length > 0) internetScore += 2;
        
        const internetPercentage = Math.round((internetScore / maxScore) * 100);
        console.log(`   📊 Score connectivité Internet: ${internetPercentage}% (${internetScore}/${maxScore})`);
        
        if (internetPercentage >= 80) {
            console.log(`   🎉 ${chatFile}: Excellente connectivité Internet !`);
        } else if (internetPercentage >= 60) {
            console.log(`   👍 ${chatFile}: Bonne connectivité Internet`);
        } else if (internetPercentage >= 40) {
            console.log(`   ⚠️ ${chatFile}: Connectivité Internet limitée`);
        } else {
            console.log(`   ❌ ${chatFile}: Connectivité Internet insuffisante`);
        }
        
    } catch (error) {
        console.error(`❌ Erreur analyse ${chatFile}:`, error.message);
    }
});

// Test final - Recommandations
console.log('\n🎯 === RECOMMANDATIONS POUR AMÉLIORER LA CONNECTIVITÉ ===');
console.log('1. Vérifiez que les endpoints API sont accessibles');
console.log('2. Testez les fonctions de recherche web');
console.log('3. Configurez les headers CORS si nécessaire');
console.log('4. Ajoutez la gestion d\'erreurs réseau');
console.log('5. Testez avec une connexion Internet réelle');

console.log('\n📋 === INSTRUCTIONS POUR TESTER ===');
console.log('1. Ouvrez une interface de chat');
console.log('2. Tapez "recherche sur internet: météo Paris"');
console.log('3. Vérifiez si la recherche fonctionne');
console.log('4. Testez avec différents types de requêtes');
console.log('5. Vérifiez les logs de la console pour les erreurs');

console.log('\n🌐 === TEST CONNEXION INTERNET TERMINÉ ===');
