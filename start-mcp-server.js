/**
 * <PERSON>ript pour démarrer le serveur MCP (Model Context Protocol)
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🚀 === DÉMARRAGE SERVEUR MCP ===');

// Chemins possibles pour le serveur MCP
const mcpPaths = [
    './mcp-server-local.js', // Serveur local simplifié (priorité)
    'LOUNA-AI-COMPLETE-REAL/deepseek-node-ui/mcp/mcp-server.js',
    'code/deepseek-node-ui/mcp/mcp-server.js',
    'LOUNA-AI-COMPLETE-REAL/deepseek-node-ui/server-luna.js',
    'code/deepseek-node-ui/server-luna.js'
];

// Trouver le serveur MCP
let mcpServerPath = null;
for (const mcpPath of mcpPaths) {
    if (fs.existsSync(mcpPath)) {
        mcpServerPath = mcpPath;
        console.log(`✅ Serveur MCP trouvé: ${mcpPath}`);
        break;
    }
}

if (!mcpServerPath) {
    console.log('❌ Aucun serveur MCP trouvé dans les chemins suivants:');
    mcpPaths.forEach(p => console.log(`   - ${p}`));
    console.log('\n📋 === INSTRUCTIONS MANUELLES ===');
    console.log('1. Naviguez vers le dossier deepseek-node-ui:');
    console.log('   cd LOUNA-AI-COMPLETE-REAL/deepseek-node-ui');
    console.log('2. Installez les dépendances:');
    console.log('   npm install');
    console.log('3. Démarrez le serveur MCP:');
    console.log('   node mcp/mcp-server.js');
    console.log('   OU');
    console.log('   node server-luna.js (démarre tout le système)');
    process.exit(1);
}

// Vérifier si Node.js est disponible
function checkNodeJS() {
    return new Promise((resolve) => {
        const nodeCheck = spawn('node', ['--version']);
        nodeCheck.on('close', (code) => {
            resolve(code === 0);
        });
        nodeCheck.on('error', () => {
            resolve(false);
        });
    });
}

// Démarrer le serveur MCP
async function startMCPServer() {
    console.log('\n🔍 Vérification de Node.js...');
    
    const nodeAvailable = await checkNodeJS();
    if (!nodeAvailable) {
        console.log('❌ Node.js non trouvé. Veuillez installer Node.js pour continuer.');
        return;
    }
    
    console.log('✅ Node.js disponible');
    
    // Déterminer le répertoire de travail
    const workingDir = path.dirname(mcpServerPath);
    console.log(`📁 Répertoire de travail: ${workingDir}`);
    
    // Vérifier package.json
    const packageJsonPath = path.join(workingDir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
        console.log('✅ package.json trouvé');
        
        // Vérifier node_modules
        const nodeModulesPath = path.join(workingDir, 'node_modules');
        if (!fs.existsSync(nodeModulesPath)) {
            console.log('⚠️ node_modules manquant - Installation des dépendances requise');
            console.log('\n📋 === INSTALLATION REQUISE ===');
            console.log(`1. cd ${workingDir}`);
            console.log('2. npm install');
            console.log('3. Relancez ce script');
            return;
        }
    }
    
    console.log('\n🚀 Démarrage du serveur MCP...');
    
    // Démarrer le serveur
    const serverProcess = spawn('node', [path.basename(mcpServerPath)], {
        cwd: workingDir,
        stdio: 'inherit'
    });
    
    serverProcess.on('spawn', () => {
        console.log('✅ Serveur MCP démarré avec succès !');
        console.log('🌐 Interface MCP disponible sur: http://localhost:3002');
        console.log('📱 Testez depuis l\'interface LOUNA AI: Mode MCP');
        console.log('\n💡 Appuyez sur Ctrl+C pour arrêter le serveur');
    });
    
    serverProcess.on('error', (error) => {
        console.error('❌ Erreur lors du démarrage:', error.message);
    });
    
    serverProcess.on('close', (code) => {
        console.log(`\n🔴 Serveur MCP arrêté (code: ${code})`);
    });
    
    // Gestion de l'arrêt propre
    process.on('SIGINT', () => {
        console.log('\n🛑 Arrêt du serveur MCP...');
        serverProcess.kill('SIGINT');
        process.exit(0);
    });
}

// Fonction pour tester la connexion MCP
function testMCPConnection() {
    const http = require('http');
    
    return new Promise((resolve) => {
        const req = http.get('http://localhost:3002/mcp/status', (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ Serveur MCP déjà actif:');
                    console.log(`   Status: ${response.status}`);
                    console.log(`   Version: ${response.version}`);
                    console.log(`   Internet: ${response.capabilities?.internet ? 'Activé' : 'Désactivé'}`);
                    console.log(`   Bureau: ${response.capabilities?.desktop ? 'Activé' : 'Désactivé'}`);
                    resolve(true);
                } catch (error) {
                    resolve(false);
                }
            });
        });
        
        req.on('error', () => {
            resolve(false);
        });
        
        req.setTimeout(3000, () => {
            req.destroy();
            resolve(false);
        });
    });
}

// Exécution principale
(async () => {
    // Vérifier si le serveur est déjà actif
    console.log('\n🔌 Test de connexion au serveur MCP...');
    const isAlreadyRunning = await testMCPConnection();
    
    if (isAlreadyRunning) {
        console.log('\n🎉 Le serveur MCP est déjà actif et fonctionnel !');
        console.log('📱 Vous pouvez utiliser l\'interface MCP depuis LOUNA AI');
        console.log('🌐 URL directe: http://localhost:3002/mcp/status');
    } else {
        console.log('⚠️ Serveur MCP non actif - Démarrage...');
        await startMCPServer();
    }
})();

// Afficher les informations d'aide
console.log('\n📋 === INFORMATIONS MCP ===');
console.log('🔧 Mode MCP (Model Context Protocol) permet à LOUNA AI de:');
console.log('   • Accéder à Internet en temps réel');
console.log('   • Contrôler le bureau utilisateur');
console.log('   • Exécuter des commandes système');
console.log('   • Gérer les fichiers et dossiers');
console.log('\n🎯 Une fois démarré, utilisez le bouton "Mode MCP" dans LOUNA AI');
console.log('🔒 Toutes les opérations sont sécurisées et contrôlées');
