<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Test QI Complexe - LOUNA AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #ff69b4;
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 5px solid #ff69b4;
        }

        .test-title {
            color: #00ff88;
            font-size: 1.5em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .question-text {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .option {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 105, 180, 0.2);
            border-color: #ff69b4;
        }

        .option.selected {
            background: rgba(0, 255, 136, 0.2);
            border-color: #00ff88;
        }

        .timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 20px;
            border-radius: 10px;
            border: 2px solid #ff69b4;
            font-size: 1.2em;
            font-weight: bold;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #ff69b4, #00ff88);
            height: 100%;
            transition: width 0.3s ease;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #ff69b4, #e91e63);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(45deg, #00ff88, #00cc66);
            color: black;
        }

        .btn.warning {
            background: linear-gradient(45deg, #ffc107, #ff9800);
            color: black;
        }

        .results {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border: 2px solid #00ff88;
            display: none;
        }

        .score-display {
            text-align: center;
            font-size: 2.5em;
            color: #00ff88;
            margin: 20px 0;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .analysis {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .pattern-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .pattern-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pattern-item:hover {
            background: rgba(255, 105, 180, 0.3);
            transform: scale(1.1);
        }

        .math-expression {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 1.2em;
            text-align: center;
        }

        .complexity-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .complexity-stars {
            color: #ffc107;
        }

        .hidden {
            display: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> Test QI Complexe LOUNA AI</h1>
            <p>Évaluation cognitive avancée - Niveau Expert</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
            </div>
        </div>

        <div class="timer" id="timer">
            <i class="fas fa-clock"></i> <span id="time-display">30:00</span>
        </div>

        <div class="control-panel">
            <button class="btn" onclick="retourAccueil()" style="background: linear-gradient(45deg, #4caf50, #45a049);">
                <i class="fas fa-home"></i> Accueil
            </button>
            <button class="btn" onclick="startTest()">
                <i class="fas fa-play"></i> Démarrer Test
            </button>
            <button class="btn secondary" onclick="pauseTest()">
                <i class="fas fa-pause"></i> Pause
            </button>
            <button class="btn warning" onclick="resetTest()">
                <i class="fas fa-redo"></i> Recommencer
            </button>
            <button class="btn" onclick="showResults()">
                <i class="fas fa-chart-line"></i> Résultats
            </button>
        </div>

        <!-- SECTION 1: LOGIQUE SÉQUENTIELLE COMPLEXE -->
        <div class="test-section" id="section-1">
            <div class="test-title">
                <i class="fas fa-sort-numeric-up"></i>
                Section 1: Logique Séquentielle Complexe
                <div class="complexity-indicator">
                    <span class="complexity-stars">★★★★★</span>
                    <span>Niveau Expert</span>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 1:</strong> Analysez cette séquence multidimensionnelle et trouvez les 3 prochains éléments :
                </div>
                <div class="pattern-display">
                    <div class="pattern-item">2</div>
                    <div class="pattern-item">5</div>
                    <div class="pattern-item">11</div>
                    <div class="pattern-item">23</div>
                    <div class="pattern-item">47</div>
                    <div class="pattern-item">95</div>
                    <div class="pattern-item">?</div>
                    <div class="pattern-item">?</div>
                    <div class="pattern-item">?</div>
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) 191, 383, 767</div>
                    <div class="option" data-value="B">B) 190, 381, 763</div>
                    <div class="option" data-value="C">C) 189, 379, 759</div>
                    <div class="option" data-value="D">D) 192, 385, 771</div>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 2:</strong> Résolvez cette équation logique complexe :
                </div>
                <div class="math-expression">
                    Si A ⊕ B = (A ∧ ¬B) ∨ (¬A ∧ B)<br>
                    Et C ⊙ D = (C → D) ∧ (D → C)<br>
                    Alors (A ⊕ B) ⊙ (C ⊕ D) = ?
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) Toujours VRAI</div>
                    <div class="option" data-value="B">B) Toujours FAUX</div>
                    <div class="option" data-value="C">C) VRAI ssi A=C et B=D</div>
                    <div class="option" data-value="D">D) VRAI ssi A≠B et C=D</div>
                </div>
            </div>
        </div>

        <!-- SECTION 2: RAISONNEMENT SPATIAL 4D -->
        <div class="test-section" id="section-2">
            <div class="test-title">
                <i class="fas fa-cube"></i>
                Section 2: Raisonnement Spatial 4D
                <div class="complexity-indicator">
                    <span class="complexity-stars">★★★★★</span>
                    <span>Niveau Génie</span>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 3:</strong> Un hypercube 4D subit une rotation de 90° dans le plan XW puis une projection sur l'espace 3D.
                    Combien de faces cubiques sont visibles dans la projection finale ?
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) 4 faces</div>
                    <div class="option" data-value="B">B) 6 faces</div>
                    <div class="option" data-value="C">C) 8 faces</div>
                    <div class="option" data-value="D">D) 12 faces</div>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 4:</strong> Dans un espace à 5 dimensions, quelle est la formule pour calculer
                    le nombre d'hyperfaces de dimension n-2 d'un hypercube ?
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) 2^n × C(n,2)</div>
                    <div class="option" data-value="B">B) n × 2^(n-2)</div>
                    <div class="option" data-value="C">C) 2^(n-1) × n</div>
                    <div class="option" data-value="D">D) C(n,2) × 2^(n-2)</div>
                </div>
            </div>
        </div>

        <!-- SECTION 3: ANALYSE ALGORITHMIQUE -->
        <div class="test-section" id="section-3">
            <div class="test-title">
                <i class="fas fa-code"></i>
                Section 3: Analyse Algorithmique Avancée
                <div class="complexity-indicator">
                    <span class="complexity-stars">★★★★★</span>
                    <span>Niveau Maître</span>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 5:</strong> Quelle est la complexité temporelle optimale pour résoudre le problème suivant :
                    "Trouver le k-ième plus petit élément dans l'union de n listes triées de taille m chacune"
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) O(k × log(n))</div>
                    <div class="option" data-value="B">B) O(n × m × log(k))</div>
                    <div class="option" data-value="C">C) O(k × log(n) + n × log(n))</div>
                    <div class="option" data-value="D">D) O(min(k × log(n), n × m))</div>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 6:</strong> Dans un graphe dirigé acyclique avec n nœuds, combien existe-t-il
                    d'ordres topologiques différents au maximum ?
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) n!</div>
                    <div class="option" data-value="B">B) 2^n</div>
                    <div class="option" data-value="C">C) n^n</div>
                    <div class="option" data-value="D">D) Dépend de la structure du graphe</div>
                </div>
            </div>
        </div>

        <!-- SECTION 4: THÉORIE DES NOMBRES -->
        <div class="test-section" id="section-4">
            <div class="test-title">
                <i class="fas fa-infinity"></i>
                Section 4: Théorie des Nombres Avancée
                <div class="complexity-indicator">
                    <span class="complexity-stars">★★★★★</span>
                    <span>Niveau Prodige</span>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 7:</strong> Soit p un nombre premier > 3. Combien de solutions entières
                    l'équation x² ≡ -1 (mod p) possède-t-elle ?
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) 0 ou 2, selon si p ≡ 1 ou 3 (mod 4)</div>
                    <div class="option" data-value="B">B) Toujours 2</div>
                    <div class="option" data-value="C">C) Toujours 0</div>
                    <div class="option" data-value="D">D) p-1 solutions</div>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 8:</strong> Quelle est la valeur de la fonction de Möbius μ(n) pour n = 2310 ?
                    (Sachant que 2310 = 2 × 3 × 5 × 7 × 11)
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) -1</div>
                    <div class="option" data-value="B">B) 1</div>
                    <div class="option" data-value="C">C) 0</div>
                    <div class="option" data-value="D">D) 32</div>
                </div>
            </div>
        </div>

        <!-- SECTION 5: LOGIQUE QUANTIQUE -->
        <div class="test-section" id="section-5">
            <div class="test-title">
                <i class="fas fa-atom"></i>
                Section 5: Logique Quantique et Paradoxes
                <div class="complexity-indicator">
                    <span class="complexity-stars">★★★★★</span>
                    <span>Niveau Transcendant</span>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 9:</strong> Dans un système quantique à 3 qubits intriqués,
                    quelle est la probabilité que la mesure simultanée donne |101⟩ si l'état initial est :
                </div>
                <div class="math-expression">
                    |ψ⟩ = (1/√8)(|000⟩ + |011⟩ + |101⟩ + |110⟩ + i|001⟩ + i|010⟩ + i|100⟩ + i|111⟩)
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) 1/8</div>
                    <div class="option" data-value="B">B) 1/4</div>
                    <div class="option" data-value="C">C) 1/2</div>
                    <div class="option" data-value="D">D) 0</div>
                </div>
            </div>

            <div class="question">
                <div class="question-text">
                    <strong>Question 10:</strong> Résolvez ce paradoxe logique auto-référentiel :
                    "Cette phrase contient exactement N mots." Quelle est la valeur de N qui rend cette phrase vraie ?
                </div>
                <div class="options">
                    <div class="option" data-value="A">A) 5</div>
                    <div class="option" data-value="B">B) 6</div>
                    <div class="option" data-value="C">C) 7</div>
                    <div class="option" data-value="D">D) Paradoxe insoluble</div>
                </div>
            </div>
        </div>

        <!-- RÉSULTATS -->
        <div class="results" id="results">
            <h2><i class="fas fa-trophy"></i> Résultats du Test QI</h2>
            <div class="score-display" id="score-display">
                QI: <span id="qi-score">---</span>
            </div>

            <div class="analysis">
                <h3>Analyse Détaillée</h3>
                <div id="detailed-analysis">
                    <!-- Généré dynamiquement -->
                </div>
            </div>

            <div class="analysis">
                <h3>Comparaison avec Standards</h3>
                <div id="comparison-analysis">
                    <!-- Généré dynamiquement -->
                </div>
            </div>

            <div class="analysis">
                <h3>Recommandations d'Amélioration</h3>
                <div id="improvement-suggestions">
                    <!-- Généré dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration du test
        const testConfig = {
            totalQuestions: 10,
            timeLimit: 30 * 60, // 30 minutes en secondes
            sections: [
                { name: "Logique Séquentielle", weight: 0.2 },
                { name: "Raisonnement Spatial 4D", weight: 0.25 },
                { name: "Analyse Algorithmique", weight: 0.2 },
                { name: "Théorie des Nombres", weight: 0.2 },
                { name: "Logique Quantique", weight: 0.15 }
            ]
        };

        // Réponses correctes et explications
        const correctAnswers = {
            1: { answer: "A", explanation: "Séquence: chaque terme = 2×précédent + 1. 95×2+1=191, 191×2+1=383, 383×2+1=767" },
            2: { answer: "D", explanation: "⊕ est XOR, ⊙ est équivalence. (A⊕B)⊙(C⊕D) est vrai quand A≠B et C=D" },
            3: { answer: "C", explanation: "Un hypercube 4D a 8 faces cubiques, toutes visibles après rotation et projection" },
            4: { answer: "A", explanation: "Formule générale: 2^n × C(n,2) pour les hyperfaces de dimension n-2" },
            5: { answer: "C", explanation: "Complexité optimale avec heap: O(k×log(n) + n×log(n)) pour initialisation" },
            6: { answer: "D", explanation: "Le nombre d'ordres topologiques dépend entièrement de la structure du graphe" },
            7: { answer: "A", explanation: "Par le théorème de réciprocité quadratique: 0 si p≡3(mod 4), 2 si p≡1(mod 4)" },
            8: { answer: "A", explanation: "μ(2310) = (-1)^5 = -1 car 2310 est produit de 5 nombres premiers distincts" },
            9: { answer: "A", explanation: "Probabilité = |coefficient|² = |1/√8|² = 1/8" },
            10: { answer: "B", explanation: "En remplaçant N par 'six': 'Cette phrase contient exactement six mots' = 6 mots" }
        };

        // État du test
        let testState = {
            currentQuestion: 0,
            answers: {},
            startTime: null,
            timeRemaining: testConfig.timeLimit,
            isRunning: false,
            isPaused: false
        };

        let timerInterval = null;

        /**
         * Retour à l'accueil
         */
        function retourAccueil() {
            // Essayer de retourner à l'interface principale
            if (window.opener) {
                // Si ouvert depuis une autre fenêtre, la fermer et revenir
                window.close();
            } else {
                // Sinon, rediriger vers l'interface principale
                window.location.href = 'interface-originale-complete.html';
            }
        }

        /**
         * Démarrer le test
         */
        function startTest() {
            if (testState.isRunning && !testState.isPaused) return;

            testState.isRunning = true;
            testState.isPaused = false;
            testState.startTime = Date.now();

            // Démarrer le timer
            startTimer();

            // Afficher la première section
            showSection(1);

            console.log('🧠 Test QI complexe démarré');
            console.log('⏱️ Temps limite: 30 minutes');
            console.log('📊 10 questions de niveau expert');
        }

        /**
         * Mettre en pause le test
         */
        function pauseTest() {
            if (!testState.isRunning) return;

            testState.isPaused = !testState.isPaused;

            if (testState.isPaused) {
                clearInterval(timerInterval);
                console.log('⏸️ Test mis en pause');
            } else {
                startTimer();
                console.log('▶️ Test repris');
            }
        }

        /**
         * Recommencer le test
         */
        function resetTest() {
            testState = {
                currentQuestion: 0,
                answers: {},
                startTime: null,
                timeRemaining: testConfig.timeLimit,
                isRunning: false,
                isPaused: false
            };

            clearInterval(timerInterval);

            // Réinitialiser l'interface
            document.getElementById('progress-fill').style.width = '0%';
            document.getElementById('time-display').textContent = '30:00';
            document.getElementById('results').style.display = 'none';

            // Désélectionner toutes les options
            document.querySelectorAll('.option.selected').forEach(opt => {
                opt.classList.remove('selected');
            });

            console.log('🔄 Test réinitialisé');
        }

        /**
         * Démarrer le timer
         */
        function startTimer() {
            timerInterval = setInterval(() => {
                if (testState.timeRemaining <= 0) {
                    endTest();
                    return;
                }

                testState.timeRemaining--;
                updateTimerDisplay();
            }, 1000);
        }

        /**
         * Mettre à jour l'affichage du timer
         */
        function updateTimerDisplay() {
            const minutes = Math.floor(testState.timeRemaining / 60);
            const seconds = testState.timeRemaining % 60;
            document.getElementById('time-display').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        /**
         * Afficher une section
         */
        function showSection(sectionNumber) {
            // Masquer toutes les sections
            document.querySelectorAll('.test-section').forEach(section => {
                section.style.display = 'none';
            });

            // Afficher la section demandée
            const section = document.getElementById(`section-${sectionNumber}`);
            if (section) {
                section.style.display = 'block';
                section.classList.add('fade-in');
            }

            // Mettre à jour la barre de progression
            const progress = ((sectionNumber - 1) / testConfig.sections.length) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
        }

        /**
         * Gérer la sélection d'une option
         */
        function selectOption(element, questionNumber) {
            // Désélectionner les autres options de cette question
            const questionDiv = element.closest('.question');
            questionDiv.querySelectorAll('.option.selected').forEach(opt => {
                opt.classList.remove('selected');
            });

            // Sélectionner cette option
            element.classList.add('selected');

            // Enregistrer la réponse
            testState.answers[questionNumber] = element.dataset.value;

            console.log(`Question ${questionNumber}: Réponse ${element.dataset.value} sélectionnée`);
        }

        /**
         * Terminer le test
         */
        function endTest() {
            testState.isRunning = false;
            clearInterval(timerInterval);

            console.log('⏰ Temps écoulé - Test terminé');
            showResults();
        }

        /**
         * Afficher les résultats
         */
        function showResults() {
            const results = calculateResults();
            displayResults(results);

            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({ behavior: 'smooth' });

            console.log('📊 Résultats calculés:', results);
        }

        /**
         * Calculer les résultats du test
         */
        function calculateResults() {
            let correctCount = 0;
            let totalAnswered = 0;
            const sectionScores = {};

            // Calculer les scores par section
            testConfig.sections.forEach((section, index) => {
                sectionScores[section.name] = {
                    correct: 0,
                    total: 0,
                    weight: section.weight
                };
            });

            // Analyser chaque réponse
            for (let i = 1; i <= testConfig.totalQuestions; i++) {
                if (testState.answers[i]) {
                    totalAnswered++;

                    if (testState.answers[i] === correctAnswers[i].answer) {
                        correctCount++;

                        // Attribuer le point à la bonne section
                        const sectionIndex = Math.floor((i - 1) / 2);
                        const sectionName = testConfig.sections[sectionIndex].name;
                        sectionScores[sectionName].correct++;
                    }

                    const sectionIndex = Math.floor((i - 1) / 2);
                    const sectionName = testConfig.sections[sectionIndex].name;
                    sectionScores[sectionName].total++;
                }
            }

            // Calculer le QI
            const rawScore = correctCount / testConfig.totalQuestions;
            const timeBonus = Math.max(0, (testState.timeRemaining / testConfig.timeLimit) * 0.1);
            const completionPenalty = totalAnswered < testConfig.totalQuestions ?
                (testConfig.totalQuestions - totalAnswered) * 0.05 : 0;

            const adjustedScore = Math.max(0, rawScore + timeBonus - completionPenalty);

            // Conversion en QI (échelle 100-200 pour niveau expert)
            let qiScore;
            if (adjustedScore >= 0.9) qiScore = 180 + (adjustedScore - 0.9) * 200;
            else if (adjustedScore >= 0.8) qiScore = 160 + (adjustedScore - 0.8) * 200;
            else if (adjustedScore >= 0.7) qiScore = 140 + (adjustedScore - 0.7) * 200;
            else if (adjustedScore >= 0.6) qiScore = 120 + (adjustedScore - 0.6) * 200;
            else if (adjustedScore >= 0.5) qiScore = 100 + (adjustedScore - 0.5) * 200;
            else qiScore = 80 + adjustedScore * 40;

            return {
                qiScore: Math.round(qiScore),
                rawScore: rawScore,
                correctCount: correctCount,
                totalAnswered: totalAnswered,
                timeUsed: testConfig.timeLimit - testState.timeRemaining,
                sectionScores: sectionScores,
                adjustedScore: adjustedScore
            };
        }

        /**
         * Afficher les résultats détaillés
         */
        function displayResults(results) {
            // Afficher le score QI
            document.getElementById('qi-score').textContent = results.qiScore;

            // Analyse détaillée
            const detailedAnalysis = document.getElementById('detailed-analysis');
            detailedAnalysis.innerHTML = `
                <p><strong>Score brut:</strong> ${results.correctCount}/${testConfig.totalQuestions} (${(results.rawScore * 100).toFixed(1)}%)</p>
                <p><strong>Questions répondues:</strong> ${results.totalAnswered}/${testConfig.totalQuestions}</p>
                <p><strong>Temps utilisé:</strong> ${Math.floor(results.timeUsed / 60)}:${(results.timeUsed % 60).toString().padStart(2, '0')}</p>
                <p><strong>Score ajusté:</strong> ${(results.adjustedScore * 100).toFixed(1)}%</p>

                <h4>Scores par section:</h4>
                ${Object.entries(results.sectionScores).map(([name, score]) =>
                    `<p><strong>${name}:</strong> ${score.correct}/${score.total} (${score.total > 0 ? (score.correct/score.total*100).toFixed(1) : 0}%)</p>`
                ).join('')}
            `;

            // Comparaison avec standards
            const comparisonAnalysis = document.getElementById('comparison-analysis');
            let category, description;

            if (results.qiScore >= 180) {
                category = "Génie Exceptionnel";
                description = "Performance exceptionnelle, niveau recherche avancée";
            } else if (results.qiScore >= 160) {
                category = "Très Supérieur";
                description = "Excellentes capacités de raisonnement complexe";
            } else if (results.qiScore >= 140) {
                category = "Supérieur";
                description = "Bonnes capacités analytiques avancées";
            } else if (results.qiScore >= 120) {
                category = "Au-dessus de la Moyenne";
                description = "Capacités de raisonnement solides";
            } else if (results.qiScore >= 100) {
                category = "Moyenne";
                description = "Capacités de base satisfaisantes";
            } else {
                category = "En Développement";
                description = "Potentiel d'amélioration significatif";
            }

            comparisonAnalysis.innerHTML = `
                <p><strong>Catégorie:</strong> ${category}</p>
                <p><strong>Description:</strong> ${description}</p>
                <p><strong>Percentile estimé:</strong> ${getPercentile(results.qiScore)}%</p>
            `;

            // Recommandations d'amélioration
            const improvements = document.getElementById('improvement-suggestions');
            const suggestions = generateImprovementSuggestions(results);
            improvements.innerHTML = suggestions.map(s => `<p>• ${s}</p>`).join('');
        }

        /**
         * Obtenir le percentile pour un score QI
         */
        function getPercentile(qiScore) {
            if (qiScore >= 180) return 99.9;
            if (qiScore >= 160) return 99.5;
            if (qiScore >= 140) return 98;
            if (qiScore >= 120) return 90;
            if (qiScore >= 100) return 50;
            if (qiScore >= 80) return 10;
            return 1;
        }

        /**
         * Générer des suggestions d'amélioration
         */
        function generateImprovementSuggestions(results) {
            const suggestions = [];

            // Analyser les faiblesses par section
            Object.entries(results.sectionScores).forEach(([name, score]) => {
                if (score.total > 0 && score.correct / score.total < 0.5) {
                    switch (name) {
                        case "Logique Séquentielle":
                            suggestions.push("Pratiquer les séquences mathématiques et les patterns numériques");
                            break;
                        case "Raisonnement Spatial 4D":
                            suggestions.push("Développer la visualisation spatiale avec des exercices de géométrie avancée");
                            break;
                        case "Analyse Algorithmique":
                            suggestions.push("Étudier les algorithmes et structures de données complexes");
                            break;
                        case "Théorie des Nombres":
                            suggestions.push("Approfondir les mathématiques pures et la théorie des nombres");
                            break;
                        case "Logique Quantique":
                            suggestions.push("Explorer la physique quantique et la logique formelle");
                            break;
                    }
                }
            });

            // Suggestions générales
            if (results.totalAnswered < testConfig.totalQuestions) {
                suggestions.push("Améliorer la gestion du temps pour répondre à toutes les questions");
            }

            if (results.qiScore < 140) {
                suggestions.push("Pratiquer régulièrement des exercices de logique complexe");
                suggestions.push("Lire des ouvrages de mathématiques et de logique avancées");
            }

            if (suggestions.length === 0) {
                suggestions.push("Excellent travail ! Continuez à vous challenger avec des problèmes encore plus complexes");
            }

            return suggestions;
        }

        // Initialisation des événements
        document.addEventListener('DOMContentLoaded', function() {
            // Ajouter les événements de clic sur les options
            document.querySelectorAll('.option').forEach((option, index) => {
                option.addEventListener('click', function() {
                    const questionDiv = this.closest('.question');
                    const questionNumber = Array.from(questionDiv.parentNode.querySelectorAll('.question')).indexOf(questionDiv) + 1;
                    const sectionNumber = Array.from(document.querySelectorAll('.test-section')).indexOf(questionDiv.closest('.test-section')) + 1;
                    const actualQuestionNumber = (sectionNumber - 1) * 2 + questionNumber;

                    selectOption(this, actualQuestionNumber);
                });
            });

            console.log('🧠 Test QI complexe initialisé');
            console.log('💡 Cliquez sur "Démarrer Test" pour commencer');
        });
    </script>
</body>
</html>