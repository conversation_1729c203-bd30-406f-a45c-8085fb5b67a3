const EventEmitter = require('events');

/**
 * 🔄 SYSTÈME DE PENSÉES MÖBIUS RÉEL
 * Cycle continu : Génération → Réflexion → Récupération → Dépense → Génération
 * Connecté au système de température et à la mémoire thermique
 */
class RealMobiusThoughtSystem extends EventEmitter {
    constructor(thermalMemory, temperatureSensor) {
        super();
        
        this.thermalMemory = thermalMemory;
        this.temperatureSensor = temperatureSensor;
        
        // 🔄 ÉTAT DU CYCLE MÖBIUS
        this.mobiusState = {
            isActive: false,
            currentPhase: 'generation', // generation, reflection, recovery, expenditure
            cycleCount: 0,
            totalThoughts: 0,
            efficiency: 1.0,
            energy: 100.0,
            lastCycleTime: Date.now()
        };
        
        // 🧠 PHASES DU CYCLE MÖBIUS
        this.phases = {
            generation: {
                duration: 3000, // 3 secondes
                energyCost: 5.0,
                temperatureIncrease: 0.2,
                active: false
            },
            reflection: {
                duration: 5000, // 5 secondes
                energyCost: 8.0,
                temperatureIncrease: 0.5,
                active: false
            },
            recovery: {
                duration: 2000, // 2 secondes
                energyCost: -3.0, // Récupération
                temperatureDecrease: 0.3,
                active: false
            },
            expenditure: {
                duration: 1000, // 1 seconde
                energyCost: 2.0,
                temperatureIncrease: 0.1,
                active: false
            }
        };
        
        // 📊 MÉTRIQUES DU SYSTÈME
        this.metrics = {
            thoughtsGenerated: 0,
            reflectionsCompleted: 0,
            energyRecovered: 0,
            energyExpended: 0,
            averageCycleTime: 0,
            temperatureVariations: []
        };
        
        // 🎯 PENSÉES ACTIVES
        this.activeThoughts = new Map();
        this.thoughtQueue = [];
        this.reflectionQueue = [];
        
        this.initialize();
    }

    /**
     * 🚀 Initialise le système Möbius
     */
    initialize() {
        console.log('🔄 Initialisation système Möbius réel...');
        
        // Connecter aux événements de température
        if (this.temperatureSensor) {
            this.temperatureSensor.on('temperatureUpdate', (temp) => {
                this.adjustCycleBasedOnTemperature(temp);
            });
        }
        
        // Connecter aux événements de mémoire
        if (this.thermalMemory) {
            this.thermalMemory.on('entryAdded', (entry) => {
                this.handleMemoryEntry(entry);
            });
        }
        
        console.log('🔄 Système Möbius initialisé');
        this.emit('initialized');
    }

    /**
     * 🔄 Démarre le cycle Möbius
     */
    startMobiusCycle() {
        if (this.mobiusState.isActive) return;
        
        this.mobiusState.isActive = true;
        this.mobiusState.lastCycleTime = Date.now();
        
        console.log('🔄 Démarrage cycle Möbius...');
        
        // Démarrer par la phase de génération
        this.startPhase('generation');
        
        this.emit('cycleStarted');
    }

    /**
     * 🎯 Démarre une phase spécifique
     */
    startPhase(phaseName) {
        if (!this.phases[phaseName]) return;
        
        const phase = this.phases[phaseName];
        this.mobiusState.currentPhase = phaseName;
        phase.active = true;
        
        console.log(`🔄 Phase ${phaseName} démarrée`);
        
        // Exécuter la phase
        this.executePhase(phaseName);
        
        // Programmer la phase suivante
        setTimeout(() => {
            this.completePhase(phaseName);
        }, phase.duration);
        
        this.emit('phaseStarted', { phase: phaseName, duration: phase.duration });
    }

    /**
     * ⚡ Exécute une phase
     */
    executePhase(phaseName) {
        switch (phaseName) {
            case 'generation':
                this.executeGeneration();
                break;
            case 'reflection':
                this.executeReflection();
                break;
            case 'recovery':
                this.executeRecovery();
                break;
            case 'expenditure':
                this.executeExpenditure();
                break;
        }
    }

    /**
     * 🧠 Phase de génération de pensées
     */
    executeGeneration() {
        const phase = this.phases.generation;
        
        // Générer des pensées basées sur la température
        const currentTemp = this.getCurrentTemperature();
        const thoughtIntensity = this.calculateThoughtIntensity(currentTemp);
        
        // Générer 1-3 pensées selon l'intensité
        const thoughtCount = Math.floor(thoughtIntensity * 3) + 1;
        
        for (let i = 0; i < thoughtCount; i++) {
            const thought = this.generateThought();
            this.activeThoughts.set(thought.id, thought);
            this.thoughtQueue.push(thought);
            
            // Stocker dans la mémoire thermique
            if (this.thermalMemory) {
                this.thermalMemory.addEntry(thought.content, thought.intensity, 'thought');
            }
        }
        
        // Consommer de l'énergie et augmenter la température
        this.consumeEnergy(phase.energyCost);
        this.adjustTemperature(phase.temperatureIncrease);
        
        this.metrics.thoughtsGenerated += thoughtCount;
        
        console.log(`🧠 Génération: ${thoughtCount} pensées créées`);
        this.emit('thoughtsGenerated', { count: thoughtCount, thoughts: this.thoughtQueue.slice(-thoughtCount) });
    }

    /**
     * 🤔 Phase de réflexion
     */
    executeReflection() {
        const phase = this.phases.reflection;
        
        // Prendre les pensées de la queue pour réflexion
        const thoughtsToReflect = this.thoughtQueue.splice(0, Math.min(3, this.thoughtQueue.length));
        
        thoughtsToReflect.forEach(thought => {
            const reflection = this.reflectOnThought(thought);
            this.reflectionQueue.push(reflection);
            
            // Renforcer la pensée dans la mémoire
            if (this.thermalMemory) {
                this.thermalMemory.addEntry(reflection.content, reflection.depth, 'reflection');
            }
        });
        
        // Consommer plus d'énergie (réflexion coûteuse)
        this.consumeEnergy(phase.energyCost);
        this.adjustTemperature(phase.temperatureIncrease);
        
        this.metrics.reflectionsCompleted += thoughtsToReflect.length;
        
        console.log(`🤔 Réflexion: ${thoughtsToReflect.length} pensées analysées`);
        this.emit('reflectionCompleted', { count: thoughtsToReflect.length, reflections: this.reflectionQueue.slice(-thoughtsToReflect.length) });
    }

    /**
     * 🔋 Phase de récupération
     */
    executeRecovery() {
        const phase = this.phases.recovery;
        
        // Récupérer de l'énergie
        this.recoverEnergy(Math.abs(phase.energyCost));
        this.adjustTemperature(-phase.temperatureDecrease);
        
        // Optimiser les pensées actives
        this.optimizeActiveThoughts();
        
        this.metrics.energyRecovered += Math.abs(phase.energyCost);
        
        console.log(`🔋 Récupération: +${Math.abs(phase.energyCost)} énergie`);
        this.emit('energyRecovered', { amount: Math.abs(phase.energyCost), currentEnergy: this.mobiusState.energy });
    }

    /**
     * 💸 Phase de dépense
     */
    executeExpenditure() {
        const phase = this.phases.expenditure;
        
        // Dépenser de l'énergie pour finaliser le cycle
        this.consumeEnergy(phase.energyCost);
        this.adjustTemperature(phase.temperatureIncrease);
        
        // Nettoyer les pensées anciennes
        this.cleanupOldThoughts();
        
        this.metrics.energyExpended += phase.energyCost;
        
        console.log(`💸 Dépense: -${phase.energyCost} énergie`);
        this.emit('energyExpended', { amount: phase.energyCost, currentEnergy: this.mobiusState.energy });
    }

    /**
     * ✅ Complète une phase et passe à la suivante
     */
    completePhase(phaseName) {
        const phase = this.phases[phaseName];
        phase.active = false;
        
        // Déterminer la phase suivante
        const nextPhase = this.getNextPhase(phaseName);
        
        if (nextPhase === 'generation') {
            // Cycle complet terminé
            this.completeCycle();
        }
        
        // Démarrer la phase suivante
        this.startPhase(nextPhase);
        
        this.emit('phaseCompleted', { phase: phaseName, nextPhase });
    }

    /**
     * 🔄 Détermine la phase suivante
     */
    getNextPhase(currentPhase) {
        const sequence = ['generation', 'reflection', 'recovery', 'expenditure'];
        const currentIndex = sequence.indexOf(currentPhase);
        return sequence[(currentIndex + 1) % sequence.length];
    }

    /**
     * ✅ Complète un cycle Möbius
     */
    completeCycle() {
        this.mobiusState.cycleCount++;
        
        const cycleTime = Date.now() - this.mobiusState.lastCycleTime;
        this.metrics.averageCycleTime = (this.metrics.averageCycleTime + cycleTime) / 2;
        this.mobiusState.lastCycleTime = Date.now();
        
        // Calculer l'efficacité du cycle
        this.calculateCycleEfficiency();
        
        console.log(`🔄 Cycle ${this.mobiusState.cycleCount} terminé - Efficacité: ${(this.mobiusState.efficiency * 100).toFixed(1)}%`);
        this.emit('cycleCompleted', {
            cycleNumber: this.mobiusState.cycleCount,
            efficiency: this.mobiusState.efficiency,
            duration: cycleTime
        });
    }

    /**
     * 🧠 Génère une pensée basée sur l'état réel du système
     */
    generateThought() {
        // Analyser l'état actuel pour déterminer le type de pensée
        const type = this.determineThoughtTypeFromState();

        const thought = {
            id: `thought_${Date.now()}_${this.generateUniqueId()}`,
            type,
            content: this.generateContextualThoughtContent(type),
            intensity: this.calculateThoughtIntensity(),
            timestamp: Date.now(),
            temperature: this.getCurrentTemperature(),
            energy: this.mobiusState.energy,
            context: this.getCurrentSystemContext()
        };

        return thought;
    }

    /**
     * 🎯 Détermine le type de pensée basé sur l'état actuel du système
     */
    determineThoughtTypeFromState() {
        const temperature = this.getCurrentTemperature();
        const energy = this.mobiusState.energy;
        const cycleCount = this.mobiusState.cycleCount || 0;
        const currentPhase = this.mobiusState.currentPhase;

        // Logique contextuelle basée sur l'état réel
        if (temperature > 38.0) {
            return 'analyse'; // Température élevée = besoin d'analyse
        } else if (energy > 0.8) {
            return 'créativité'; // Énergie élevée = créativité
        } else if (energy < 0.3) {
            return 'mémoire'; // Énergie basse = récupération mémoire
        } else if (currentPhase === 'reflection') {
            return 'question'; // Phase réflexion = questionnement
        } else if (cycleCount % 3 === 0) {
            return 'association'; // Cycles multiples de 3 = association
        } else {
            return 'idée'; // Par défaut = génération d'idées
        }
    }

    /**
     * 📝 Génère le contenu contextuel d'une pensée
     */
    generateContextualThoughtContent(type) {
        const context = this.getCurrentSystemState();
        const templates = this.getThoughtTemplates();

        // Sélectionner template basé sur l'état du système
        const typeTemplates = templates[type] || templates.analyse;
        const contextIndex = this.selectContextualTemplate(typeTemplates.length);
        let content = typeTemplates[contextIndex];

        // Enrichir avec des données contextuelles réelles
        content = this.enrichContentWithContext(content, context);

        return content;
    }

    /**
     * 🌟 Enrichit le contenu avec le contexte réel
     */
    enrichContentWithContext(content, context) {
        // Vérifier que le contenu existe
        if (!content || typeof content !== 'string') {
            return 'Pensée générée automatiquement';
        }

        // Vérifier que le contexte existe
        if (!context) {
            return content;
        }

        // Remplacer les placeholders par des données réelles
        try {
            return content
                .replace(/\{temperature\}/g, `${(context.temperature || 37.0).toFixed(1)}°C`)
                .replace(/\{energy\}/g, `${Math.round((context.energy || 0.5) * 100)}%`)
                .replace(/\{neuronCount\}/g, context.neuronCount || '86B')
                .replace(/\{efficiency\}/g, `${context.efficiency || 50}%`);
        } catch (error) {
            console.error('❌ Erreur enrichContentWithContext:', error.message);
            return content;
        }
    }

    /**
     * 📚 Obtient les templates de pensées
     */
    getThoughtTemplates() {
        return {
            analyse: [
                'Analyse des patterns de données à {temperature}',
                'Évaluation des connexions synaptiques avec {energy} d\'énergie',
                'Examen des métriques système pour {neuronCount} neurones'
            ],
            créativité: [
                'Nouvelle approche créative à {temperature}',
                'Innovation avec {energy} d\'énergie disponible',
                'Concept créatif pour optimiser {neuronCount} neurones'
            ],
            mémoire: [
                'Récupération de souvenirs à {temperature}',
                'Consolidation avec {energy} d\'énergie',
                'Association mémoire pour {neuronCount} neurones'
            ],
            association: [
                'Connexion entre concepts à {temperature}',
                'Lien synaptique avec {energy} d\'énergie',
                'Relation entre {neuronCount} neurones actifs'
            ],
            question: [
                'Questionnement sur l\'efficacité à {temperature}',
                'Interrogation avec {energy} d\'énergie',
                'Réflexion sur {neuronCount} neurones'
            ],
            idée: [
                'Nouvelle idée à {temperature}',
                'Proposition avec {energy} d\'énergie',
                'Concept pour {neuronCount} neurones'
            ]
        };
    }

    /**
     * ⚡ Calcule l'intensité de pensée basée sur l'état du système
     */
    calculateThoughtIntensity() {
        const temperature = this.getCurrentTemperature();
        const energy = this.mobiusState.energy;
        const optimal = 37.0;

        // Intensité basée sur la température optimale et l'énergie
        const tempFactor = Math.max(0.1, 1.0 - (Math.abs(temperature - optimal) / 10));
        const energyFactor = energy;

        return Math.min(1.0, (tempFactor + energyFactor) / 2);
    }

    /**
     * 🔑 Génère un ID unique basé sur l'état du système
     */
    generateUniqueId() {
        const timestamp = Date.now().toString(36);
        const energy = Math.floor(this.mobiusState.energy * 1000).toString(36);
        const temp = Math.floor(this.getCurrentTemperature() * 100).toString(36);
        const cycle = (this.mobiusState.cycleCount || 0).toString(36);

        return `${timestamp}_${energy}_${temp}_${cycle}`;
    }

    /**
     * 🌍 Obtient le contexte système actuel
     */
    getCurrentSystemContext() {
        return {
            temperature: this.getCurrentTemperature(),
            energy: this.mobiusState.energy,
            phase: this.mobiusState.currentPhase,
            cycleCount: this.mobiusState.cycleCount || 0,
            efficiency: this.mobiusState.efficiency || 0,
            activeThoughts: this.activeThoughts.size,
            timestamp: Date.now()
        };
    }

    /**
     * 🧠 Gère une entrée mémoire ajoutée
     */
    handleMemoryEntry(entry) {
        try {
            // Analyser l'entrée pour générer des pensées contextuelles
            if (entry && entry.importance > 0.7) {
                // Générer une pensée basée sur l'entrée importante
                const thought = this.generateThought();
                if (thought) {
                    this.activeThoughts.set(thought.id, thought);
                    this.metrics.thoughtsGenerated++;
                }
            }

            // Ajuster l'énergie basée sur l'importance de l'entrée
            const energyAdjustment = entry.importance * 0.1;
            this.recoverEnergy(energyAdjustment);

        } catch (error) {
            console.error('❌ Erreur handleMemoryEntry:', error.message);
        }
    }

    /**
     * 📝 Génère le contenu d'une pensée
     */
    generateThoughtContent(type) {
        const templates = {
            analyse: [
                'Analyse des patterns de données actuels',
                'Évaluation des connexions synaptiques',
                'Examen des métriques système'
            ],
            créativité: [
                'Nouvelle approche pour optimiser les processus',
                'Idée innovante pour améliorer l\'efficacité',
                'Concept créatif pour la résolution de problèmes'
            ],
            mémoire: [
                'Récupération de souvenirs pertinents',
                'Consolidation des expériences récentes',
                'Association avec des connaissances antérieures'
            ],
            association: [
                'Connexion entre concepts apparemment distincts',
                'Lien entre expériences passées et présentes',
                'Relation entre données et intuitions'
            ],
            question: [
                'Questionnement sur l\'efficacité actuelle',
                'Interrogation sur les possibilités d\'amélioration',
                'Réflexion sur les objectifs à long terme'
            ],
            idée: [
                'Proposition d\'optimisation système',
                'Suggestion d\'amélioration des performances',
                'Concept pour l\'évolution future'
            ]
        };
        
        // Sélectionner contenu basé sur l'état du système plutôt qu'aléatoirement
        const typeTemplates = templates[type] || templates.analyse;
        const contextIndex = this.selectContextualTemplate(typeTemplates.length);
        return typeTemplates[contextIndex];
    }

    /**
     * 🤔 Effectue une réflexion sur une pensée
     */
    reflectOnThought(thought) {
        const reflection = {
            id: `reflection_${Date.now()}_${this.generateReflectionId()}`,
            originalThought: thought.id,
            content: `Réflexion contextuelle: ${thought.content}`,
            depth: this.calculateReflectionDepth(thought),
            insights: this.generateContextualInsights(thought),
            timestamp: Date.now(),
            temperature: this.getCurrentTemperature(),
            systemState: this.getCurrentSystemState()
        };
        
        return reflection;
    }

    /**
     * 💡 Génère des insights
     */
    generateInsights(thought) {
        return [
            `Cette pensée révèle une ${thought.type} importante`,
            `L'intensité de ${thought.intensity.toFixed(2)} suggère une priorité élevée`,
            `La température de ${thought.temperature.toFixed(1)}°C indique une activité optimale`
        ];
    }

    /**
     * ⚡ Consomme de l'énergie
     */
    consumeEnergy(amount) {
        this.mobiusState.energy = Math.max(0, this.mobiusState.energy - amount);
    }

    /**
     * 🔋 Récupère de l'énergie
     */
    recoverEnergy(amount) {
        this.mobiusState.energy = Math.min(100, this.mobiusState.energy + amount);
    }

    /**
     * 🌡️ Ajuste la température
     */
    adjustTemperature(delta) {
        if (this.temperatureSensor && this.temperatureSensor.temperatureCursor) {
            this.temperatureSensor.temperatureCursor.target += delta;
            this.metrics.temperatureVariations.push({
                delta,
                timestamp: Date.now(),
                phase: this.mobiusState.currentPhase
            });
        }
    }

    /**
     * 🌡️ Obtient la température actuelle
     */
    getCurrentTemperature() {
        if (this.temperatureSensor) {
            return this.temperatureSensor.getCurrentTemperature();
        }
        return 37.0;
    }

    /**
     * 📊 Calcule l'intensité de pensée basée sur la température
     */
    calculateThoughtIntensity(temperature) {
        // Intensité optimale autour de 37°C
        const optimal = 37.0;
        const deviation = Math.abs(temperature - optimal);
        return Math.max(0.1, 1.0 - (deviation / 20));
    }

    /**
     * 🔄 Ajuste le cycle basé sur la température
     */
    adjustCycleBasedOnTemperature(temperature) {
        const intensity = this.calculateThoughtIntensity(temperature);
        
        // Ajuster les durées des phases
        Object.keys(this.phases).forEach(phaseName => {
            const phase = this.phases[phaseName];
            const baseDuration = phase.duration;
            phase.duration = Math.floor(baseDuration * (2.0 - intensity));
        });
    }

    /**
     * 🧹 Nettoie les pensées anciennes
     */
    cleanupOldThoughts() {
        const now = Date.now();
        const maxAge = 300000; // 5 minutes
        
        this.activeThoughts.forEach((thought, id) => {
            if (now - thought.timestamp > maxAge) {
                this.activeThoughts.delete(id);
            }
        });
    }

    /**
     * ⚡ Optimise les pensées actives
     */
    optimizeActiveThoughts() {
        // Renforcer les pensées à haute intensité
        this.activeThoughts.forEach(thought => {
            if (thought.intensity > 0.8) {
                thought.intensity = Math.min(1.0, thought.intensity + 0.1);
            }
        });
    }

    /**
     * 📊 Calcule l'efficacité du cycle
     */
    calculateCycleEfficiency() {
        const energyBalance = this.mobiusState.energy / 100;
        const thoughtQuality = this.metrics.thoughtsGenerated / (this.mobiusState.cycleCount || 1);
        const temperatureStability = this.calculateTemperatureStability();
        
        this.mobiusState.efficiency = (energyBalance + thoughtQuality + temperatureStability) / 3;
    }

    /**
     * 🌡️ Calcule la stabilité de température
     */
    calculateTemperatureStability() {
        if (this.metrics.temperatureVariations.length < 2) return 1.0;
        
        const recent = this.metrics.temperatureVariations.slice(-10);
        const variance = this.calculateVariance(recent.map(v => v.delta));
        
        return Math.max(0, 1.0 - variance);
    }

    /**
     * 📊 Calcule la variance
     */
    calculateVariance(values) {
        if (values.length === 0) return 0;
        
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
        
        return Math.sqrt(variance);
    }

    /**
     * 📊 Obtient les statistiques du système
     */
    getStats() {
        return {
            state: this.mobiusState,
            phases: this.phases,
            metrics: this.metrics,
            activeThoughts: this.activeThoughts.size,
            thoughtQueue: this.thoughtQueue.length,
            reflectionQueue: this.reflectionQueue.length
        };
    }

    /**
     * 🛑 Arrête le système Möbius
     */
    stop() {
        this.mobiusState.isActive = false;
        
        // Arrêter toutes les phases
        Object.keys(this.phases).forEach(phaseName => {
            this.phases[phaseName].active = false;
        });
        
        console.log('🛑 Système Möbius arrêté');
        this.emit('stopped');
    }

    /**
     * 🎯 Sélectionne un template contextuel basé sur l'état du système
     */
    selectContextualTemplate(templateCount) {
        const temp = this.getCurrentTemperature();
        const energy = this.mobiusState.energy;
        const cycleCount = this.mobiusState.cycleCount || 0;

        // Sélection basée sur l'état plutôt qu'aléatoire
        const stateHash = Math.floor(temp * 10) + Math.floor(energy * 10) + (cycleCount % 10);
        return stateHash % templateCount;
    }

    /**
     * 🔑 Génère un ID de réflexion basé sur l'état
     */
    generateReflectionId() {
        const timestamp = Date.now().toString(36);
        const energy = Math.floor(this.mobiusState.energy * 1000).toString(36);
        const temp = Math.floor(this.getCurrentTemperature() * 100).toString(36);

        return `${timestamp}_${energy}_${temp}`;
    }

    /**
     * 🧮 Calcule la profondeur de réflexion basée sur le contexte
     */
    calculateReflectionDepth(thought) {
        const baseDepth = 0.6;
        const energyBonus = this.mobiusState.energy * 0.3;
        const intensityBonus = thought.intensity * 0.1;

        return Math.min(1.0, baseDepth + energyBonus + intensityBonus);
    }

    /**
     * 💡 Génère des insights contextuels basés sur l'état réel
     */
    generateContextualInsights(thought) {
        const context = this.getCurrentSystemState();
        const insights = [];

        // Insights basés sur la température
        if (context.temperature > 38) {
            insights.push(`Température élevée (${context.temperature}°C) peut affecter la performance cognitive`);
        }

        // Insights basés sur l'énergie
        if (this.mobiusState.energy < 0.5) {
            insights.push(`Niveau d'énergie bas (${Math.round(this.mobiusState.energy * 100)}%) nécessite optimisation`);
        }

        // Insights basés sur le type de pensée
        if (thought.type === 'analyse') {
            insights.push(`Analyse approfondie révèle des patterns dans ${context.neuronCount} neurones`);
        }

        return insights.length > 0 ? insights : [`Réflexion sur ${thought.type} à ${context.temperature}°C`];
    }

    /**
     * 📊 Obtient l'état actuel du système
     */
    getCurrentSystemState() {
        return {
            temperature: this.getCurrentTemperature(),
            energy: this.mobiusState.energy,
            cycleCount: this.mobiusState.cycleCount || 0,
            neuronCount: '86B', // Données réelles
            efficiency: Math.round(this.mobiusState.energy * 100)
        };
    }
}

module.exports = RealMobiusThoughtSystem;
