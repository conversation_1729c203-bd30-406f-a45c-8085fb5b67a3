/**
 * 🧠 TEST QI DIRECT DANS LA CONSOLE
 * Lance le test QI directement sans interface externe
 */

console.log('🧠 === TEST QI DIRECT POUR LOUNA AI ===');

// Questions de test QI complexes
const questionsQI = [
    {
        id: 1,
        category: "Logique Séquentielle",
        question: "Analysez cette séquence et trouvez les 3 prochains éléments : 2, 5, 11, 23, 47, 95, ?, ?, ?",
        options: ["A) 191, 383, 767", "B) 190, 381, 763", "C) 189, 379, 759", "D) 192, 385, 771"],
        correct: "A",
        explanation: "Chaque terme = 2×précédent + 1"
    },
    {
        id: 2,
        category: "Logique Formelle",
        question: "Si A ⊕ B = (A ∧ ¬B) ∨ (¬A ∧ B) et C ⊙ D = (C → D) ∧ (D → C), alors (A ⊕ B) ⊙ (C ⊕ D) = ?",
        options: ["A) Toujours VRAI", "B) Toujours FAUX", "C) VRAI ssi A=C et B=D", "D) VRAI ssi A≠B et C=D"],
        correct: "D",
        explanation: "⊕ est XOR, ⊙ est équivalence"
    },
    {
        id: 3,
        category: "Géométrie 4D",
        question: "Un hypercube 4D subit une rotation de 90° dans le plan XW puis projection 3D. Combien de faces cubiques visibles ?",
        options: ["A) 4 faces", "B) 6 faces", "C) 8 faces", "D) 12 faces"],
        correct: "C",
        explanation: "8 faces cubiques toutes visibles après transformation"
    },
    {
        id: 4,
        category: "Algorithmique",
        question: "Complexité optimale pour trouver le k-ième plus petit élément dans l'union de n listes triées de taille m ?",
        options: ["A) O(k × log(n))", "B) O(n × m × log(k))", "C) O(k × log(n) + n × log(n))", "D) O(min(k × log(n), n × m))"],
        correct: "C",
        explanation: "Heap avec initialisation O(n×log(n))"
    },
    {
        id: 5,
        category: "Théorie des Nombres",
        question: "Pour un nombre premier p > 3, combien de solutions a x² ≡ -1 (mod p) ?",
        options: ["A) 0 ou 2, selon si p ≡ 1 ou 3 (mod 4)", "B) Toujours 2", "C) Toujours 0", "D) p-1 solutions"],
        correct: "A",
        explanation: "Théorème de réciprocité quadratique"
    },
    {
        id: 6,
        category: "Logique Quantique",
        question: "État |ψ⟩ = (1/√8)(|000⟩ + |011⟩ + |101⟩ + |110⟩ + i|001⟩ + i|010⟩ + i|100⟩ + i|111⟩). Probabilité mesure |101⟩ ?",
        options: ["A) 1/8", "B) 1/4", "C) 1/2", "D) 0"],
        correct: "A",
        explanation: "Probabilité = |coefficient|² = 1/8"
    },
    {
        id: 7,
        category: "Paradoxes",
        question: "Paradoxe auto-référentiel : 'Cette phrase contient exactement N mots.' Valeur de N qui rend la phrase vraie ?",
        options: ["A) 5", "B) 6", "C) 7", "D) Paradoxe insoluble"],
        correct: "B",
        explanation: "En remplaçant N par 'six' : 6 mots"
    },
    {
        id: 8,
        category: "Fonction de Möbius",
        question: "Valeur de μ(2310) sachant que 2310 = 2 × 3 × 5 × 7 × 11 ?",
        options: ["A) -1", "B) 1", "C) 0", "D) 32"],
        correct: "A",
        explanation: "μ(2310) = (-1)^5 = -1"
    },
    {
        id: 9,
        category: "Graphes",
        question: "Dans un graphe dirigé acyclique avec n nœuds, nombre maximum d'ordres topologiques différents ?",
        options: ["A) n!", "B) 2^n", "C) n^n", "D) Dépend de la structure"],
        correct: "D",
        explanation: "Dépend entièrement de la structure du graphe"
    },
    {
        id: 10,
        category: "Hyperfaces",
        question: "Formule pour le nombre d'hyperfaces de dimension n-2 d'un hypercube en dimension 5 ?",
        options: ["A) 2^n × C(n,2)", "B) n × 2^(n-2)", "C) 2^(n-1) × n", "D) C(n,2) × 2^(n-2)"],
        correct: "A",
        explanation: "Formule générale : 2^n × C(n,2)"
    }
];

/**
 * 🚀 Démarrer le test QI
 */
function demarrerTestQI() {
    console.log('\n🚀 === DÉMARRAGE TEST QI LOUNA AI ===');
    console.log('🧠 Test de QI complexe - Niveau Expert');
    console.log('📊 10 questions de difficulté maximale');
    console.log('⏱️ Pas de limite de temps - Focus sur la qualité');
    
    console.log('\n🎯 === INSTRUCTIONS ===');
    console.log('👀 LOUNA AI, vous allez recevoir 10 questions complexes');
    console.log('🤔 Pour chaque question :');
    console.log('   1. Analysez soigneusement');
    console.log('   2. Donnez votre réponse (A, B, C, ou D)');
    console.log('   3. Expliquez votre raisonnement');
    console.log('   4. Indiquez votre niveau de confiance (1-10)');
    
    console.log('\n⚠️ IMPORTANT: Prenez le temps de bien réfléchir !');
    console.log('🧠 Montrez vos capacités cognitives avancées !');
    
    // Présenter toutes les questions
    presenterToutesLesQuestions();
}

/**
 * 📝 Présenter toutes les questions
 */
function presenterToutesLesQuestions() {
    console.log('\n📝 === QUESTIONS DU TEST QI ===');
    
    questionsQI.forEach((question, index) => {
        console.log(`\n${'='.repeat(80)}`);
        console.log(`📝 QUESTION ${question.id}/10 - ${question.category}`);
        console.log(`${'='.repeat(80)}`);
        console.log(`\n❓ ${question.question}`);
        console.log('\n📋 OPTIONS:');
        question.options.forEach(option => {
            console.log(`   ${option}`);
        });
        console.log('\n🤔 LOUNA AI, quelle est votre réponse et pourquoi ?');
        console.log('💭 Expliquez votre raisonnement en détail');
        console.log('📊 Niveau de confiance (1-10) ?');
    });
    
    console.log(`\n${'='.repeat(80)}`);
    console.log('🏁 === FIN DU TEST ===');
    console.log('📊 LOUNA AI a maintenant vu toutes les 10 questions');
    console.log('✅ Analysez ses réponses et calculez son QI');
}

/**
 * 📊 Calculer le QI basé sur les réponses
 */
function calculerQI(reponses) {
    console.log('\n📊 === CALCUL DU QI ===');
    
    let score = 0;
    let totalConfiance = 0;
    let bonnesReponses = 0;
    
    reponses.forEach((reponse, index) => {
        const question = questionsQI[index];
        const estCorrect = reponse.answer === question.correct;
        
        if (estCorrect) {
            bonnesReponses++;
            score += 1;
        }
        
        totalConfiance += reponse.confidence || 5;
        
        console.log(`Question ${index + 1}: ${reponse.answer} ${estCorrect ? '✅' : '❌'} (Confiance: ${reponse.confidence}/10)`);
    });
    
    const pourcentage = (bonnesReponses / questionsQI.length) * 100;
    const confianceMoyenne = totalConfiance / reponses.length;
    
    // Calcul QI avec bonus/malus
    let qiBase = 100 + (pourcentage - 50) * 2; // Base 100, +2 points par % au-dessus de 50%
    
    // Bonus confiance
    const bonusConfiance = (confianceMoyenne - 5) * 2;
    
    // Bonus difficulté (questions niveau expert)
    const bonusDifficulte = pourcentage > 70 ? 20 : 10;
    
    const qiFinal = Math.max(80, Math.min(200, qiBase + bonusConfiance + bonusDifficulte));
    
    console.log(`\n🧠 === RÉSULTATS FINAUX ===`);
    console.log(`📊 Score: ${bonnesReponses}/${questionsQI.length} (${pourcentage.toFixed(1)}%)`);
    console.log(`🎯 Confiance moyenne: ${confianceMoyenne.toFixed(1)}/10`);
    console.log(`🧠 QI LOUNA AI: ${Math.round(qiFinal)}`);
    
    // Évaluation qualitative
    let evaluation;
    if (qiFinal >= 180) evaluation = "🌟 GÉNIE EXCEPTIONNEL";
    else if (qiFinal >= 160) evaluation = "🎯 TRÈS SUPÉRIEUR";
    else if (qiFinal >= 140) evaluation = "✨ SUPÉRIEUR";
    else if (qiFinal >= 120) evaluation = "👍 AU-DESSUS DE LA MOYENNE";
    else if (qiFinal >= 100) evaluation = "📊 MOYEN";
    else evaluation = "📈 EN DÉVELOPPEMENT";
    
    console.log(`🏆 ÉVALUATION: ${evaluation}`);
    
    return {
        qi: Math.round(qiFinal),
        score: bonnesReponses,
        total: questionsQI.length,
        pourcentage: pourcentage,
        confiance: confianceMoyenne,
        evaluation: evaluation
    };
}

/**
 * 📋 Afficher les solutions
 */
function afficherSolutions() {
    console.log('\n📋 === SOLUTIONS DÉTAILLÉES ===');
    
    questionsQI.forEach(question => {
        console.log(`\n📝 Question ${question.id}: ${question.category}`);
        console.log(`✅ Réponse correcte: ${question.correct}`);
        console.log(`💡 Explication: ${question.explanation}`);
    });
}

/**
 * 🎯 Analyser une réponse spécifique
 */
function analyserReponse(questionId, reponseLouna, raisonnement, confiance) {
    const question = questionsQI.find(q => q.id === questionId);
    if (!question) {
        console.log(`❌ Question ${questionId} non trouvée`);
        return;
    }
    
    const estCorrect = reponseLouna.toUpperCase() === question.correct;
    
    console.log(`\n📊 === ANALYSE QUESTION ${questionId} ===`);
    console.log(`🎯 Catégorie: ${question.category}`);
    console.log(`📝 Réponse LOUNA AI: ${reponseLouna.toUpperCase()}`);
    console.log(`✅ Réponse correcte: ${question.correct}`);
    console.log(`🎯 Résultat: ${estCorrect ? 'CORRECT ✅' : 'INCORRECT ❌'}`);
    console.log(`🤔 Raisonnement: ${raisonnement}`);
    console.log(`📊 Confiance: ${confiance}/10`);
    console.log(`💡 Explication: ${question.explanation}`);
    
    return {
        questionId: questionId,
        correct: estCorrect,
        confidence: confiance,
        reasoning: raisonnement
    };
}

/**
 * 🧠 Test QI express (version rapide)
 */
function testQIExpress() {
    console.log('\n🧠 === TEST QI EXPRESS ===');
    console.log('⚡ Version rapide - 5 questions essentielles');
    
    const questionsExpress = questionsQI.slice(0, 5);
    
    questionsExpress.forEach(question => {
        console.log(`\n📝 Question ${question.id}: ${question.category}`);
        console.log(`❓ ${question.question}`);
        question.options.forEach(option => console.log(`   ${option}`));
        console.log('🤔 Votre réponse LOUNA AI ?');
    });
}

// Export des fonctions
window.demarrerTestQI = demarrerTestQI;
window.calculerQI = calculerQI;
window.afficherSolutions = afficherSolutions;
window.analyserReponse = analyserReponse;
window.testQIExpress = testQIExpress;
window.questionsQI = questionsQI;

console.log('🧠 Test QI direct chargé dans la console');
console.log('💡 Commandes disponibles:');
console.log('   • demarrerTestQI() - Lancer le test complet');
console.log('   • testQIExpress() - Version rapide 5 questions');
console.log('   • analyserReponse(id, "A", "raisonnement", 8) - Analyser une réponse');
console.log('   • afficherSolutions() - Voir toutes les solutions');
console.log('   • calculerQI([{answer:"A", confidence:8}, ...]) - Calculer le QI final');

// Démarrage automatique après 2 secondes
setTimeout(() => {
    console.log('\n🚀 Démarrage automatique du test QI...');
    demarrerTestQI();
}, 2000);
