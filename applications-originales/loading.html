<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Louna - Chargement...</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .loading-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
        }

        .logo {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ff69b4, #ff1493, #c2185b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        .subtitle {
            font-size: 1.5rem;
            color: #ff69b4;
            margin-bottom: 40px;
            opacity: 0.8;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 4px solid rgba(255, 105, 180, 0.2);
            border-top: 4px solid #ff69b4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 30px;
        }

        .loading-text {
            font-size: 1.2rem;
            color: #ffffff;
            margin-bottom: 20px;
            animation: fadeInOut 2s ease-in-out infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #ff1493);
            border-radius: 3px;
            animation: progress 3s ease-in-out infinite;
        }

        .status-text {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 20px;
        }

        .brain-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: brainPulse 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        @keyframes brainPulse {
            0%, 100% { transform: scale(1); filter: hue-rotate(0deg); }
            50% { transform: scale(1.1); filter: hue-rotate(180deg); }
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ff69b4;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
        }

        .system-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.5);
        }

        .version-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <!-- Bouton retour ajouté automatiquement -->
    <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
        <a href="../interface-originale-complete.html" style="
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255,105,180,0.3);
            transition: all 0.3s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
            🏠 Accueil
        </a>
    </div>
    <div class="particles" id="particles"></div>
    
    <div class="loading-container">
        <div class="brain-icon">🧠</div>
        <div class="logo">LOUNA</div>
        <div class="subtitle">Intelligence Artificielle Avancée</div>
        
        <div class="loading-spinner"></div>
        
        <div class="loading-text" id="loadingText">Initialisation du système...</div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="status-text" id="statusText">Démarrage des services...</div>
    </div>

    <div class="system-info">
        <div>🔥 Mémoire Thermique</div>
        <div>⚡ Accélérateurs KYBER</div>
        <div>🧠 Système Neural Avancé</div>
    </div>

    <div class="version-info">
        <div>Louna v2.0</div>
        <div>Build: Thermal Memory</div>
    </div>

    <script>
        // Messages de chargement dynamiques
        const loadingMessages = [
            "Initialisation du système...",
            "Chargement de la mémoire thermique...",
            "Activation des accélérateurs KYBER...",
            "Connexion au réseau neural...",
            "Préparation de l'interface...",
            "Synchronisation des données...",
            "Finalisation du démarrage..."
        ];

        const statusMessages = [
            "Démarrage des services...",
            "Configuration de la mémoire...",
            "Optimisation des performances...",
            "Chargement des modules...",
            "Vérification de l'intégrité...",
            "Établissement des connexions...",
            "Prêt à démarrer..."
        ];

        let messageIndex = 0;
        const loadingText = document.getElementById('loadingText');
        const statusText = document.getElementById('statusText');

        // Changer les messages de chargement
        setInterval(() => {
            messageIndex = (messageIndex + 1) % loadingMessages.length;
            loadingText.textContent = loadingMessages[messageIndex];
            statusText.textContent = statusMessages[messageIndex];
        }, 1000);

        // Créer des particules animées
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = 0 + '%';
                particle.style.animationDelay = 0 + 's';
                particle.style.animationDuration = (0 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Initialiser les particules
        createParticles();

        // Simuler la progression du chargement
        setTimeout(() => {
            loadingText.textContent = "Système prêt !";
            statusText.textContent = "Redirection vers l'interface principale...";
        }, 7000);
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
