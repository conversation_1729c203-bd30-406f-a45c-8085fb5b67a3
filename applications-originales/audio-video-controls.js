/**
 * Interface de contrôle audio/vidéo pour Louna
 * Gestion complète des fonctionnalités multimédia
 */

class AudioVideoInterface {
    constructor() {
        this.audioVideoSystem = null;
        this.recordedMedia = [];
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.waveformCanvas = null;
        this.waveformContext = null;
        this.audioAnalyser = null;
        this.dataArray = null;

        this.init();
    }

    async init() {
        try {
            console.log('🎵 Initialisation de l\'interface audio/vidéo...');

            // Importer le système audio/vidéo (simulation pour l'interface web)
            await this.initializeAudioVideoSystem();

            // Configurer les éléments de l'interface
            this.setupUI();

            // Configurer les gestionnaires d'événements
            this.setupEventHandlers();

            // Initialiser la visualisation
            this.initializeWaveform();

            // Vérifier les permissions
            this.checkPermissions();

            console.log('✅ Interface audio/vidéo initialisée');
            this.showNotification('Interface audio/vidéo prête', 'success');

        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation:', error);
            this.showNotification('Erreur d\'initialisation: ' + error.message, 'error');
        }
    }

    async initializeAudioVideoSystem() {
        // Simulation du système audio/vidéo pour l'interface web
        this.audioVideoSystem = {
            state: {
                isRecording: false,
                isPlaying: false,
                isMuted: false,
                volume: 1.0,
                devices: {
                    audioInputs: [],
                    audioOutputs: [],
                    videoInputs: []
                },
                currentDevices: {
                    audioInput: null,
                    audioOutput: null,
                    videoInput: null
                }
            },
            mediaStream: null,
            mediaRecorder: null,
            currentRecognition: null
        };

        // Énumérer les périphériques
        await this.enumerateDevices();

        // Charger les voix disponibles
        this.loadAvailableVoices();
    }

    async enumerateDevices() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();

            this.audioVideoSystem.state.devices.audioInputs = devices.filter(device => device.kind === 'audioinput');
            this.audioVideoSystem.state.devices.audioOutputs = devices.filter(device => device.kind === 'audiooutput');
            this.audioVideoSystem.state.devices.videoInputs = devices.filter(device => device.kind === 'videoinput');

            this.populateDeviceSelectors();
            this.updateDeviceCount();

        } catch (error) {
            console.error('❌ Erreur lors de l\'énumération des périphériques:', error);
        }
    }

    populateDeviceSelectors() {
        const audioInputSelect = document.getElementById('audioInputSelect');
        const audioOutputSelect = document.getElementById('audioOutputSelect');
        const videoInputSelect = document.getElementById('videoInputSelect');

        // Vider les sélecteurs
        audioInputSelect.innerHTML = '';
        audioOutputSelect.innerHTML = '';
        videoInputSelect.innerHTML = '';

        // Remplir les micros
        this.audioVideoSystem.state.devices.audioInputs.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.textContent = device.label || `Microphone ${device.deviceId.slice(0, 8)}`;
            audioInputSelect.appendChild(option);
        });

        // Remplir les haut-parleurs
        this.audioVideoSystem.state.devices.audioOutputs.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.textContent = device.label || `Haut-parleur ${device.deviceId.slice(0, 8)}`;
            audioOutputSelect.appendChild(option);
        });

        // Remplir les caméras
        this.audioVideoSystem.state.devices.videoInputs.forEach(device => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.textContent = device.label || `Caméra ${device.deviceId.slice(0, 8)}`;
            videoInputSelect.appendChild(option);
        });
    }

    loadAvailableVoices() {
        const voiceSelect = document.getElementById('voiceSelect');
        voiceSelect.innerHTML = '';

        const loadVoices = () => {
            const voices = speechSynthesis.getVoices();
            voices.forEach(voice => {
                const option = document.createElement('option');
                option.value = voice.voiceURI;
                option.textContent = `${voice.name} (${voice.lang})`;
                if (voice.default) option.selected = true;
                voiceSelect.appendChild(option);
            });
        };

        loadVoices();
        speechSynthesis.onvoiceschanged = loadVoices;
    }

    setupUI() {
        // Mettre à jour l'état initial
        this.updateSystemStatus('active', 'Système prêt');
        this.updateActiveMediaCount();

        // Configurer les sliders
        const speechRateSlider = document.getElementById('speechRate');
        const speechPitchSlider = document.getElementById('speechPitch');
        const volumeSlider = document.getElementById('volumeSlider');

        speechRateSlider.addEventListener('input', (e) => {
            document.getElementById('speechRateValue').textContent = e.target.value;
        });

        speechPitchSlider.addEventListener('input', (e) => {
            document.getElementById('speechPitchValue').textContent = e.target.value;
        });

        volumeSlider.addEventListener('input', (e) => {
            const volume = e.target.value / 100;
            this.setVolume(volume);
            document.getElementById('volumeValue').textContent = e.target.value + '%';
        });
    }

    setupEventHandlers() {
        // Enregistrement audio
        document.getElementById('startAudioRecording').addEventListener('click', () => {
            this.startAudioRecording();
        });

        // Enregistrement vidéo
        document.getElementById('startVideoRecording').addEventListener('click', () => {
            this.startVideoRecording();
        });

        // Arrêter l'enregistrement
        document.getElementById('stopRecording').addEventListener('click', () => {
            this.stopRecording();
        });

        // Aperçu vidéo
        document.getElementById('startVideoPreview').addEventListener('click', () => {
            this.startVideoPreview();
        });

        document.getElementById('stopVideoPreview').addEventListener('click', () => {
            this.stopVideoPreview();
        });

        // Synthèse vocale
        document.getElementById('speakButton').addEventListener('click', () => {
            this.speak();
        });

        document.getElementById('stopSpeechButton').addEventListener('click', () => {
            this.stopSpeech();
        });

        // Reconnaissance vocale
        document.getElementById('startRecognition').addEventListener('click', () => {
            this.startSpeechRecognition();
        });

        document.getElementById('stopRecognition').addEventListener('click', () => {
            this.stopSpeechRecognition();
        });

        // Contrôles de lecture
        document.getElementById('muteButton').addEventListener('click', () => {
            this.toggleMute();
        });

        document.getElementById('pauseAllButton').addEventListener('click', () => {
            this.pauseAllMedia();
        });

        document.getElementById('resumeAllButton').addEventListener('click', () => {
            this.resumeAllMedia();
        });

        document.getElementById('stopAllButton').addEventListener('click', () => {
            this.stopAllMedia();
        });

        // Changement de périphériques
        document.getElementById('audioInputSelect').addEventListener('change', (e) => {
            this.setAudioInputDevice(e.target.value);
        });

        document.getElementById('audioOutputSelect').addEventListener('change', (e) => {
            this.setAudioOutputDevice(e.target.value);
        });

        document.getElementById('videoInputSelect').addEventListener('change', (e) => {
            this.setVideoInputDevice(e.target.value);
        });
    }

    initializeWaveform() {
        this.waveformCanvas = document.getElementById('waveformCanvas');
        this.waveformContext = this.waveformCanvas.getContext('2d');

        // Ajuster la taille du canvas
        this.waveformCanvas.width = this.waveformCanvas.offsetWidth;
        this.waveformCanvas.height = this.waveformCanvas.offsetHeight;

        // Dessiner l'état initial
        this.drawWaveform([]);
    }

    drawWaveform(dataArray) {
        const canvas = this.waveformCanvas;
        const ctx = this.waveformContext;
        const width = canvas.width;
        const height = canvas.height;

        // Effacer le canvas
        ctx.fillStyle = '#000';
        ctx.fillRect(0, 0, width, height);

        if (!dataArray || dataArray.length === 0) {
            // Ligne plate si pas de données
            ctx.strokeStyle = '#c8a2c8';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, height / 2);
            ctx.lineTo(width, height / 2);
            ctx.stroke();
            return;
        }

        // Dessiner la forme d'onde
        ctx.strokeStyle = '#c8a2c8';
        ctx.lineWidth = 2;
        ctx.beginPath();

        const sliceWidth = width / dataArray.length;
        let x = 0;

        for (let i = 0; i < dataArray.length; i++) {
            const v = dataArray[i] / 128.0;
            const y = v * height / 2;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }

            x += sliceWidth;
        }

        ctx.stroke();
    }

    async checkPermissions() {
        try {
            const permissions = await Promise.all([
                navigator.permissions.query({ name: 'microphone' }),
                navigator.permissions.query({ name: 'camera' })
            ]);

            const micPermission = permissions[0];
            const cameraPermission = permissions[1];

            if (micPermission.state === 'denied' || cameraPermission.state === 'denied') {
                this.showPermissionRequest();
            }
        } catch (error) {
            console.log('Vérification des permissions non supportée');
        }
    }

    showPermissionRequest() {
        document.getElementById('permissionRequest').style.display = 'block';
    }

    async requestPermissions() {
        try {
            await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
            document.getElementById('permissionRequest').style.display = 'none';
            this.showNotification('Permissions accordées', 'success');
            await this.enumerateDevices();
        } catch (error) {
            this.showNotification('Permissions refusées: ' + error.message, 'error');
        }
    }

    async startAudioRecording() {
        try {
            const constraints = {
                audio: {
                    deviceId: this.audioVideoSystem.state.currentDevices.audioInput?.deviceId,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            };

            this.audioVideoSystem.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

            this.audioVideoSystem.mediaRecorder = new MediaRecorder(this.audioVideoSystem.mediaStream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.recordedChunks = [];

            this.audioVideoSystem.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.audioVideoSystem.mediaRecorder.onstop = () => {
                this.processRecordedAudio();
            };

            this.audioVideoSystem.mediaRecorder.start(100);
            this.audioVideoSystem.state.isRecording = true;

            this.startRecordingTimer();
            this.setupAudioAnalyser();

            document.getElementById('startAudioRecording').disabled = true;
            document.getElementById('startVideoRecording').disabled = true;
            document.getElementById('stopRecording').disabled = false;
            document.getElementById('startAudioRecording').classList.add('recording');

            this.showNotification('Enregistrement audio démarré', 'success');

        } catch (error) {
            console.error('❌ Erreur lors du démarrage de l\'enregistrement audio:', error);
            this.showNotification('Erreur: ' + error.message, 'error');
        }
    }

    async startVideoRecording() {
        try {
            const constraints = {
                audio: {
                    deviceId: this.audioVideoSystem.state.currentDevices.audioInput?.deviceId,
                    echoCancellation: true,
                    noiseSuppression: true
                },
                video: {
                    deviceId: this.audioVideoSystem.state.currentDevices.videoInput?.deviceId,
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    frameRate: { ideal: 30 }
                }
            };

            this.audioVideoSystem.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

            this.audioVideoSystem.mediaRecorder = new MediaRecorder(this.audioVideoSystem.mediaStream, {
                mimeType: 'video/webm;codecs=vp9,opus'
            });

            this.recordedChunks = [];

            this.audioVideoSystem.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.audioVideoSystem.mediaRecorder.onstop = () => {
                this.processRecordedVideo();
            };

            this.audioVideoSystem.mediaRecorder.start(100);
            this.audioVideoSystem.state.isRecording = true;

            this.startRecordingTimer();

            document.getElementById('startAudioRecording').disabled = true;
            document.getElementById('startVideoRecording').disabled = true;
            document.getElementById('stopRecording').disabled = false;
            document.getElementById('startVideoRecording').classList.add('recording');

            this.showNotification('Enregistrement vidéo démarré', 'success');

        } catch (error) {
            console.error('❌ Erreur lors du démarrage de l\'enregistrement vidéo:', error);
            this.showNotification('Erreur: ' + error.message, 'error');
        }
    }

    stopRecording() {
        if (this.audioVideoSystem.mediaRecorder && this.audioVideoSystem.state.isRecording) {
            this.audioVideoSystem.mediaRecorder.stop();
            this.audioVideoSystem.state.isRecording = false;

            if (this.audioVideoSystem.mediaStream) {
                this.audioVideoSystem.mediaStream.getTracks().forEach(track => track.stop());
                this.audioVideoSystem.mediaStream = null;
            }

            this.stopRecordingTimer();

            document.getElementById('startAudioRecording').disabled = false;
            document.getElementById('startVideoRecording').disabled = false;
            document.getElementById('stopRecording').disabled = true;
            document.getElementById('startAudioRecording').classList.remove('recording');
            document.getElementById('startVideoRecording').classList.remove('recording');

            this.showNotification('Enregistrement arrêté', 'success');
        }
    }

    processRecordedAudio() {
        const blob = new Blob(this.recordedChunks, { type: 'audio/webm' });
        const audioUrl = URL.createObjectURL(blob);

        const mediaItem = {
            id: Date.now(),
            type: 'audio',
            blob: blob,
            url: audioUrl,
            name: `Audio_${new Date().toLocaleString()}`,
            size: blob.size,
            duration: this.getRecordingDuration()
        };

        this.recordedMedia.push(mediaItem);
        this.updateMediaList();

        this.showNotification('Audio enregistré avec succès', 'success');
    }

    processRecordedVideo() {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const videoUrl = URL.createObjectURL(blob);

        const mediaItem = {
            id: Date.now(),
            type: 'video',
            blob: blob,
            url: videoUrl,
            name: `Vidéo_${new Date().toLocaleString()}`,
            size: blob.size,
            duration: this.getRecordingDuration()
        };

        this.recordedMedia.push(mediaItem);
        this.updateMediaList();

        this.showNotification('Vidéo enregistrée avec succès', 'success');
    }

    async startVideoPreview() {
        try {
            const constraints = {
                video: {
                    deviceId: this.audioVideoSystem.state.currentDevices.videoInput?.deviceId,
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            const videoPreview = document.getElementById('videoPreview');
            videoPreview.srcObject = stream;

            this.previewStream = stream;

            this.showNotification('Aperçu vidéo démarré', 'success');

        } catch (error) {
            console.error('❌ Erreur lors du démarrage de l\'aperçu vidéo:', error);
            this.showNotification('Erreur: ' + error.message, 'error');
        }
    }

    stopVideoPreview() {
        if (this.previewStream) {
            this.previewStream.getTracks().forEach(track => track.stop());
            this.previewStream = null;

            const videoPreview = document.getElementById('videoPreview');
            videoPreview.srcObject = null;

            this.showNotification('Aperçu vidéo arrêté', 'success');
        }
    }

    async speak() {
        try {
            const text = document.getElementById('speechText').value;
            if (!text.trim()) {
                this.showNotification('Veuillez entrer du texte à synthétiser', 'warning');
                return;
            }

            if (!('speechSynthesis' in window)) {
                throw new Error('Synthèse vocale non supportée');
            }

            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);

            const voiceSelect = document.getElementById('voiceSelect');
            const selectedVoice = speechSynthesis.getVoices().find(voice =>
                voice.voiceURI === voiceSelect.value
            );
            if (selectedVoice) {
                utterance.voice = selectedVoice;
            }

            utterance.rate = parseFloat(document.getElementById('speechRate').value);
            utterance.pitch = parseFloat(document.getElementById('speechPitch').value);
            utterance.volume = this.audioVideoSystem.state.volume;

            utterance.onstart = () => {
                this.showNotification('Synthèse vocale démarrée', 'info');
            };

            utterance.onend = () => {
                this.showNotification('Synthèse vocale terminée', 'success');
            };

            utterance.onerror = (error) => {
                this.showNotification('Erreur de synthèse: ' + error.error, 'error');
            };

            speechSynthesis.speak(utterance);

        } catch (error) {
            console.error('❌ Erreur lors de la synthèse vocale:', error);
            this.showNotification('Erreur: ' + error.message, 'error');
        }
    }

    stopSpeech() {
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();
            this.showNotification('Synthèse vocale arrêtée', 'info');
        }
    }

    async startSpeechRecognition() {
        try {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                throw new Error('Reconnaissance vocale non supportée');
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.audioVideoSystem.currentRecognition = new SpeechRecognition();

            this.audioVideoSystem.currentRecognition.lang = 'fr-FR';
            this.audioVideoSystem.currentRecognition.continuous = true;
            this.audioVideoSystem.currentRecognition.interimResults = true;

            const outputDiv = document.getElementById('recognitionOutput');

            this.audioVideoSystem.currentRecognition.onstart = () => {
                outputDiv.innerHTML = '<div style="color: #ffc107;">🎤 Écoute en cours...</div>';
                document.getElementById('startRecognition').disabled = true;
                document.getElementById('stopRecognition').disabled = false;
                this.showNotification('Reconnaissance vocale démarrée', 'info');
            };

            this.audioVideoSystem.currentRecognition.onresult = (event) => {
                let finalTranscript = '';
                let interimTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    const confidence = event.results[i][0].confidence;

                    if (event.results[i].isFinal) {
                        finalTranscript += transcript;
                        outputDiv.innerHTML += `<div style="color: #00ff00;">✅ Final: ${transcript} (${Math.round(confidence * 100)}%)</div>`;
                    } else {
                        interimTranscript += transcript;
                    }
                }

                if (interimTranscript) {
                    outputDiv.innerHTML = outputDiv.innerHTML.replace(
                        /<div style="color: #ffc107;">.*?<\/div>$/,
                        `<div style="color: #ffc107;">🎤 ${interimTranscript}</div>`
                    );
                }

                outputDiv.scrollTop = outputDiv.scrollHeight;
            };

            this.audioVideoSystem.currentRecognition.onerror = (error) => {
                outputDiv.innerHTML += `<div style="color: #ff0000;">❌ Erreur: ${error.error}</div>`;
                this.showNotification('Erreur de reconnaissance: ' + error.error, 'error');
                this.resetRecognitionButtons();
            };

            this.audioVideoSystem.currentRecognition.onend = () => {
                outputDiv.innerHTML += '<div style="color: #95a5a6;">⏹️ Reconnaissance terminée</div>';
                this.resetRecognitionButtons();
                this.showNotification('Reconnaissance vocale terminée', 'info');
            };

            this.audioVideoSystem.currentRecognition.start();

        } catch (error) {
            console.error('❌ Erreur lors de la reconnaissance vocale:', error);
            this.showNotification('Erreur: ' + error.message, 'error');
        }
    }

    stopSpeechRecognition() {
        if (this.audioVideoSystem.currentRecognition) {
            this.audioVideoSystem.currentRecognition.stop();
            this.audioVideoSystem.currentRecognition = null;
        }
    }

    resetRecognitionButtons() {
        document.getElementById('startRecognition').disabled = false;
        document.getElementById('stopRecognition').disabled = true;
    }

    setupAudioAnalyser() {
        if (this.audioVideoSystem.mediaStream) {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const source = audioContext.createMediaStreamSource(this.audioVideoSystem.mediaStream);
            this.audioAnalyser = audioContext.createAnalyser();

            this.audioAnalyser.fftSize = 256;
            const bufferLength = this.audioAnalyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);

            source.connect(this.audioAnalyser);

            this.animateWaveform();
        }
    }

    animateWaveform() {
        if (this.audioAnalyser && this.audioVideoSystem.state.isRecording) {
            this.audioAnalyser.getByteTimeDomainData(this.dataArray);
            this.drawWaveform(this.dataArray);
            requestAnimationFrame(() => this.animateWaveform());
        }
    }

    startRecordingTimer() {
        this.recordingStartTime = Date.now();
        this.recordingTimer = setInterval(() => {
            const elapsed = Date.now() - this.recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('recordingDuration').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
        document.getElementById('recordingDuration').textContent = '00:00';
    }

    getRecordingDuration() {
        if (this.recordingStartTime) {
            return Date.now() - this.recordingStartTime;
        }
        return 0;
    }

    setVolume(volume) {
        this.audioVideoSystem.state.volume = Math.max(0, Math.min(1, volume));
        this.showNotification(`Volume: ${Math.round(volume * 100)}%`, 'info');
    }

    toggleMute() {
        this.audioVideoSystem.state.isMuted = !this.audioVideoSystem.state.isMuted;
        const muteButton = document.getElementById('muteButton');

        if (this.audioVideoSystem.state.isMuted) {
            muteButton.textContent = '🔊 Activer son';
            speechSynthesis.cancel();
        } else {
            muteButton.textContent = '🔇 Muet';
        }

        this.showNotification(`Son ${this.audioVideoSystem.state.isMuted ? 'coupé' : 'activé'}`, 'info');
    }

    pauseAllMedia() {
        // Pause tous les éléments audio/vidéo de la page
        document.querySelectorAll('audio, video').forEach(element => {
            if (!element.paused) {
                element.pause();
            }
        });

        if ('speechSynthesis' in window) {
            speechSynthesis.pause();
        }

        this.showNotification('Tous les médias mis en pause', 'info');
    }

    resumeAllMedia() {
        // Reprend tous les éléments audio/vidéo de la page
        document.querySelectorAll('audio, video').forEach(element => {
            if (element.paused) {
                element.play().catch(console.error);
            }
        });

        if ('speechSynthesis' in window) {
            speechSynthesis.resume();
        }

        this.showNotification('Tous les médias repris', 'info');
    }

    stopAllMedia() {
        // Arrête tous les éléments audio/vidéo
        document.querySelectorAll('audio, video').forEach(element => {
            element.pause();
            element.currentTime = 0;
        });

        // Arrête la synthèse vocale
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();
        }

        // Arrête la reconnaissance vocale
        this.stopSpeechRecognition();

        // Arrête l'enregistrement
        this.stopRecording();

        // Arrête l'aperçu vidéo
        this.stopVideoPreview();

        this.showNotification('Tous les médias arrêtés', 'info');
    }

    setAudioInputDevice(deviceId) {
        const device = this.audioVideoSystem.state.devices.audioInputs.find(d => d.deviceId === deviceId);
        if (device) {
            this.audioVideoSystem.state.currentDevices.audioInput = device;
            this.showNotification(`Microphone: ${device.label}`, 'info');
        }
    }

    setAudioOutputDevice(deviceId) {
        const device = this.audioVideoSystem.state.devices.audioOutputs.find(d => d.deviceId === deviceId);
        if (device) {
            this.audioVideoSystem.state.currentDevices.audioOutput = device;
            this.showNotification(`Haut-parleur: ${device.label}`, 'info');
        }
    }

    setVideoInputDevice(deviceId) {
        const device = this.audioVideoSystem.state.devices.videoInputs.find(d => d.deviceId === deviceId);
        if (device) {
            this.audioVideoSystem.state.currentDevices.videoInput = device;
            this.showNotification(`Caméra: ${device.label}`, 'info');
        }
    }

    updateSystemStatus(status, text) {
        const indicator = document.getElementById('systemStatus');
        const statusText = document.getElementById('systemStatusText');

        indicator.className = `status-indicator ${status}`;
        statusText.textContent = text;
    }

    updateDeviceCount() {
        const total = this.audioVideoSystem.state.devices.audioInputs.length +
                     this.audioVideoSystem.state.devices.audioOutputs.length +
                     this.audioVideoSystem.state.devices.videoInputs.length;
        document.getElementById('deviceCount').textContent = total;
    }

    updateActiveMediaCount() {
        const activeAudio = document.querySelectorAll('audio:not([paused])').length;
        const activeVideo = document.querySelectorAll('video:not([paused])').length;
        const isRecording = this.audioVideoSystem.state.isRecording ? 1 : 0;

        document.getElementById('activeMediaCount').textContent = activeAudio + activeVideo + isRecording;
    }

    updateMediaList() {
        const mediaList = document.getElementById('mediaList');

        if (this.recordedMedia.length === 0) {
            mediaList.innerHTML = 'Aucun média enregistré';
            return;
        }

        mediaList.innerHTML = this.recordedMedia.map(media => `
            <div class="media-item">
                <div>
                    <strong>${media.name}</strong><br>
                    <small>${media.type.toUpperCase()} - ${this.formatFileSize(media.size)} - ${this.formatDuration(media.duration)}</small>
                </div>
                <div class="media-item-controls">
                    <button onclick="audioVideoInterface.playMedia('${media.id}')">▶️</button>
                    <button onclick="audioVideoInterface.downloadMedia('${media.id}')">💾</button>
                    <button onclick="audioVideoInterface.deleteMedia('${media.id}')">🗑️</button>
                </div>
            </div>
        `).join('');
    }

    playMedia(mediaId) {
        const media = this.recordedMedia.find(m => m.id == mediaId);
        if (!media) return;

        if (media.type === 'audio') {
            const audio = new Audio(media.url);
            audio.play();
        } else if (media.type === 'video') {
            // Ouvrir dans une nouvelle fenêtre ou utiliser un lecteur vidéo
            window.open(media.url, '_blank');
        }
    }

    downloadMedia(mediaId) {
        const media = this.recordedMedia.find(m => m.id == mediaId);
        if (!media) return;

        const a = document.createElement('a');
        a.href = media.url;
        a.download = `${media.name}.${media.type === 'audio' ? 'webm' : 'webm'}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        this.showNotification(`${media.name} téléchargé`, 'success');
    }

    deleteMedia(mediaId) {
        const mediaIndex = this.recordedMedia.findIndex(m => m.id == mediaId);
        if (mediaIndex === -1) return;

        const media = this.recordedMedia[mediaIndex];
        URL.revokeObjectURL(media.url);
        this.recordedMedia.splice(mediaIndex, 1);
        this.updateMediaList();

        this.showNotification(`${media.name} supprimé`, 'info');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification show ${type}`;

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// Initialiser l'interface au chargement de la page
let audioVideoInterface;

document.addEventListener('DOMContentLoaded', () => {
    audioVideoInterface = new AudioVideoInterface();
});

// Fonction globale pour les permissions
function requestPermissions() {
    if (audioVideoInterface) {
        audioVideoInterface.requestPermissions();
    }
}
