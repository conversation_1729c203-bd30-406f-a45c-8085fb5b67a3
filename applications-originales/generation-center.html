<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centre de Génération - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }

        .hero-section {
            margin-bottom: 60px;
        }

        .hero-title {
            font-size: 48px;
            font-weight: 700;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .hero-subtitle {
            font-size: 20px;
            color: #ccc;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .generation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .generation-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .generation-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(233, 30, 99, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .generation-card:hover {
            transform: translateY(-10px);
            border-color: #ff69b4;
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3);
        }

        .generation-card:hover::before {
            opacity: 1;
        }

        .card-content {
            position: relative;
            z-index: 1;
        }

        .card-icon {
            font-size: 64px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #ff69b4;
        }

        .card-description {
            font-size: 16px;
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-features {
            list-style: none;
            text-align: left;
            margin-bottom: 30px;
        }

        .card-features li {
            font-size: 14px;
            color: #aaa;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-features li i {
            color: #4caf50;
            width: 16px;
        }

        .card-button {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
        }

        .stats-title {
            font-size: 24px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #ff69b4;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 14px;
            color: #ccc;
        }

        .completed {
            opacity: 0.6;
            pointer-events: none;
        }

        .completed .card-button {
            background: linear-gradient(135deg, #666, #444);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 36px;
            }
            
            .hero-subtitle {
                font-size: 18px;
            }
            
            .generation-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .generation-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-magic"></i>
            Centre de Génération IA
        </h1>
        <div class="nav-buttons">
            <a href="chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="web-search.html" class="nav-btn">
                <i class="fas fa-search"></i>
                Recherche
            </a>
            <a href="face-recognition.html" class="nav-btn">
                <i class="fas fa-user-check"></i>
                Reconnaissance
            </a>
            <a href="brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="generation-monitor.html" class="nav-btn">
                <i class="fas fa-chart-line"></i>
                Moniteur
            </a>
            <a href="backup-manager.html" class="nav-btn">
                <i class="fas fa-save"></i>
                Sauvegardes
            </a>
            <a href="../interface-originale-complete.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Section héro -->
        <div class="hero-section">
            <h1 class="hero-title">Génération de Contenu IA</h1>
            <p class="hero-subtitle">
                Créez du contenu multimédia illimité avec l'intelligence artificielle avancée de Louna. 
                Images, vidéos, musique et plus encore, tout à portée de clic.
            </p>
        </div>

        <!-- Grille des types de génération -->
        <div class="generation-grid">
            <!-- Génération d'images -->
            <div class="generation-card" onclick="window.location.href='image-generator-simple.html'">
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <h3 class="card-title">Génération d'Images</h3>
                    <p class="card-description">
                        Créez des images uniques et personnalisées à partir de descriptions textuelles avec notre IA avancée.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> Styles artistiques variés</li>
                        <li><i class="fas fa-check"></i> Résolutions jusqu'à 1024x1024</li>
                        <li><i class="fas fa-check"></i> Galerie et sauvegarde</li>
                        <li><i class="fas fa-check"></i> Export haute qualité</li>
                    </ul>
                    <a href="image-generator-simple.html" class="card-button">
                        <i class="fas fa-magic"></i>
                        Créer des Images
                    </a>
                </div>
            </div>

            <!-- Génération de vidéos -->
            <div class="generation-card" onclick="window.location.href='video-generator.html'">
                <div class="badge">LTX</div>
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3 class="card-title">Génération Vidéo LTX</h3>
                    <p class="card-description">
                        Générez des vidéos cinématiques avec la technologie LTX de pointe pour des résultats époustouflants.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> Technologie LTX avancée</li>
                        <li><i class="fas fa-check"></i> Résolutions jusqu'à 4K</li>
                        <li><i class="fas fa-check"></i> Durées personnalisables</li>
                        <li><i class="fas fa-check"></i> Styles cinématiques</li>
                    </ul>
                    <a href="video-generator.html" class="card-button">
                        <i class="fas fa-video"></i>
                        Créer des Vidéos
                    </a>
                </div>
            </div>

            <!-- Génération de musique -->
            <div class="generation-card completed">
                <div class="badge">COMPLET</div>
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="card-title">Génération Musicale</h3>
                    <p class="card-description">
                        Composez de la musique originale dans tous les styles avec notre IA musicale avancée.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-clock"></i> Tous genres musicaux</li>
                        <li><i class="fas fa-clock"></i> Instruments virtuels</li>
                        <li><i class="fas fa-clock"></i> Export professionnel</li>
                        <li><i class="fas fa-clock"></i> Arrangements automatiques</li>
                    </ul>
                    <button class="card-button" onclick="window.location.href='music-generator.html'">
                        <i class="fas fa-clock"></i>
                        Créer de la Musique
                    </button>
                </div>
            </div>

            <!-- Génération de modèles 3D -->
            <div class="generation-card completed">
                <div class="badge">COMPLET</div>
                <div class="card-content">
                    <div class="card-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3 class="card-title">Modèles 3D</h3>
                    <p class="card-description">
                        Créez des modèles 3D détaillés et texturés à partir de descriptions ou d'images de référence.
                    </p>
                    <ul class="card-features">
                        <li><i class="fas fa-clock"></i> Modélisation automatique</li>
                        <li><i class="fas fa-clock"></i> Textures réalistes</li>
                        <li><i class="fas fa-clock"></i> Export multi-formats</li>
                        <li><i class="fas fa-clock"></i> Optimisation pour jeux</li>
                    </ul>
                    <button class="card-button" onclick="window.location.href='3d-generator.html'">
                        <i class="fas fa-cube"></i>
                        Créer des Modèles 3D
                    </button>
                </div>
            </div>
        </div>

        <!-- Section statistiques -->
        <!-- Panneau Mémoire Thermique RÉELLE -->
        <div class="thermal-memory-panel" id="thermal-panel" style="
            background: linear-gradient(135deg, rgba(0,20,40,0.95), rgba(0,40,80,0.95));
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 0 30px rgba(0,255,136,0.2);
            font-family: 'Courier New', monospace;
        ">
            <h3 style="color: #00ffff; text-align: center; margin-bottom: 15px; text-shadow: 0 0 10px #00ffff;">
                🧠 MÉMOIRE THERMIQUE RÉELLE 🧠
            </h3>

            <div class="thermal-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <!-- Statistiques Neuronales -->
                <div class="neural-stats" style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; border: 1px solid #00ff88;">
                    <h4 style="color: #ffff00; margin-bottom: 10px;">📊 Statistiques Neuronales</h4>
                    <div style="color: #00ff88; font-size: 14px; line-height: 1.6;">
                        <div>🧬 Neurones: <span id="neuron-count" style="color: #ffff00; font-weight: bold;">86,000,000,000</span></div>
                        <div>🔗 Synapses: <span id="synapse-count" style="color: #ff88ff; font-weight: bold;">602,000,000,000,000</span></div>
                        <div>⚡ Activité: <span id="neural-activity" style="color: #88ffff; font-weight: bold;">0%</span></div>
                        <div>📚 Formations: <span id="formation-count" style="color: #ffaa88; font-weight: bold;">14</span></div>
                    </div>
                </div>

                <!-- Données Thermiques -->
                <div class="thermal-stats" style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; border: 1px solid #ff8800;">
                    <h4 style="color: #ff8800; margin-bottom: 10px;">🌡️ Données Thermiques</h4>
                    <div style="color: #00ff88; font-size: 14px; line-height: 1.6;">
                        <div>🌡️ Température: <span id="system-temp" style="color: #ff8800; font-weight: bold;">37.2°C</span></div>
                        <div>📍 Curseur: <span id="cursor-position" style="color: #88ff88; font-weight: bold;">Zone 5</span></div>
                        <div>💻 CPU: <span id="cpu-temp" style="color: #ffff88; font-weight: bold;">50.4°C</span></div>
                        <div>🔄 Efficacité: <span id="thermal-efficiency" style="color: #88ffff; font-weight: bold;">85%</span></div>
                    </div>
                </div>
            </div>

            <!-- Zones Thermiques -->
            <div class="thermal-zones" style="margin-top: 15px; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; border: 1px solid #8888ff;">
                <h4 style="color: #8888ff; margin-bottom: 10px;">🏠 Zones Thermiques</h4>
                <div id="zones-display" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; font-size: 12px;">
                    <!-- Les zones seront ajoutées dynamiquement -->
                </div>
            </div>

            <!-- Contrôles -->
            <div class="thermal-controls" style="margin-top: 15px; text-align: center;">
                <button onclick="refreshThermalData()" style="
                    background: linear-gradient(45deg, #00ff88, #00aa55);
                    border: none;
                    color: black;
                    padding: 8px 16px;
                    border-radius: 5px;
                    font-weight: bold;
                    cursor: pointer;
                    margin: 0 5px;
                ">🔄 Actualiser</button>

                <button onclick="toggleThermalMonitoring()" style="
                    background: linear-gradient(45deg, #ff8800, #aa5500);
                    border: none;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 5px;
                    font-weight: bold;
                    cursor: pointer;
                    margin: 0 5px;
                ">⏸️ Pause/Resume</button>

                <span style="color: #888; font-size: 11px; margin-left: 10px;">
                    Dernière MAJ: <span id="last-update">--:--:--</span>
                </span>
            </div>
        </div>

        <div class="stats-section">
            <h2 class="stats-title">
                <i class="fas fa-chart-line"></i>
                Statistiques de Génération
            </h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalImages">0</div>
                    <div class="stat-label">Images Générées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalVideos">0</div>
                    <div class="stat-label">Vidéos Créées</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Qualité IA</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">∞</div>
                    <div class="stat-label">Créativité</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Centre de génération initialisé');
            loadStatistics();
        });

        function loadStatistics() {
            // Charger les statistiques depuis le localStorage
            const savedImages = localStorage.getItem('lounaGeneratedImages');
            const savedVideos = localStorage.getItem('lounaGeneratedVideos');
            
            let imageCount = 0;
            let videoCount = 0;
            
            if (savedImages) {
                try {
                    const images = JSON.parse(savedImages);
                    imageCount = images.length;
                } catch (e) {
                    console.warn('Erreur lecture images:', e);
                }
            }
            
            if (savedVideos) {
                try {
                    const videos = JSON.parse(savedVideos);
                    videoCount = videos.length;
                } catch (e) {
                    console.warn('Erreur lecture vidéos:', e);
                }
            }
            
            // Mettre à jour l'affichage avec animation
            animateNumber('totalImages', imageCount);
            animateNumber('totalVideos', videoCount);
        }

        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 2000; // 2 secondes
            const startTime = Date.now();

            function updateNumber() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Fonction d'easing pour une animation plus fluide
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.round(startValue + (targetValue - startValue) * easeOutQuart);

                element.textContent = currentValue;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }
            
            updateNumber();
        }

        // Ajouter des effets visuels aux cartes
        document.querySelectorAll('.generation-card:not(.completed)').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Intégration du système de génération avancé
        document.addEventListener('generationUpdate', function(event) {
            const generation = event.detail;
            console.log('🎨 Mise à jour génération:', generation);

            // Mettre à jour les statistiques en temps réel
            if (generation.status === 'completed') {
                loadStatistics();
                showGenerationSuccess(generation);
            }
        });

        function showGenerationSuccess(generation) {
            // Créer une notification de succès
            const notification = document.createElement('div');
            notification.className = 'notification success show';
            notification.innerHTML = `
                <i class="fas fa-check-circle"></i>
                ${generation.type.charAt(0).toUpperCase() + generation.type.slice(1)} généré avec succès !
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Améliorer les clics sur les cartes
        document.querySelectorAll('.generation-card:not(.completed)').forEach(card => {
            card.addEventListener('click', function() {
                const href = this.getAttribute('onclick');
                if (href) {
                    const url = href.match(/'([^']+)'/)[1];
                    if (url) {
                        // Ajouter un effet de chargement
                        this.style.opacity = '0.7';
                        this.style.transform = 'scale(0.98)';

                        setTimeout(() => {
                            window.location.href = url;
                        }, 200);
                    }
                }
            });
        });
    </script>

    <!-- Système de génération avancé -->
    <script src="js/advanced-generation-system.js"></script>

    <!-- Système de sauvegarde automatique -->
    <script src="js/generation-backup-system.js"></script>

    <!-- Vérificateur d'intégrité système -->
    <script src="js/system-integrity-checker.js"></script>

    <!-- Système Global QI -->
    <script src="js/global-qi-system.js"></script>

    <!-- API Données Thermiques RÉELLES -->
    <script src="js/thermal-data-api.js"></script>

    <!-- Monitoring Mémoire Thermique en Temps Réel -->
    <script>
        class ThermalMemoryMonitor {
            constructor() {
                this.isActive = false;
                this.updateInterval = 2000; // 2 secondes
                this.monitorTimer = null;
                this.init();
            }

            async init() {
                // Attendre que le système de génération soit prêt
                if (window.advancedGenerationSystem) {
                    this.startMonitoring();
                } else {
                    setTimeout(() => this.init(), 1000);
                }
            }

            startMonitoring() {
                this.isActive = true;
                this.updateThermalDisplay();

                this.monitorTimer = setInterval(() => {
                    this.updateThermalDisplay();
                }, this.updateInterval);

                console.log('🔥 Monitoring mémoire thermique démarré');
            }

            async updateThermalDisplay() {
                try {
                    const system = window.advancedGenerationSystem;
                    if (!system || !system.realThermalData) return;

                    // Créer ou mettre à jour l'affichage thermal
                    this.createThermalDisplay(system.realThermalData, system.neuronCount);

                } catch (error) {
                    console.warn('⚠️ Erreur mise à jour affichage thermal:', error);
                }
            }

            createThermalDisplay(thermalData, neuronCount) {
                let display = document.getElementById('thermal-memory-display');

                if (!display) {
                    display = document.createElement('div');
                    display.id = 'thermal-memory-display';
                    display.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: rgba(0, 0, 0, 0.9);
                        color: #00ff88;
                        padding: 15px;
                        border-radius: 10px;
                        font-family: 'Courier New', monospace;
                        font-size: 12px;
                        z-index: 10000;
                        min-width: 300px;
                        border: 1px solid #00ff88;
                        box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
                    `;
                    document.body.appendChild(display);
                }

                const temp = thermalData.temperature || 37.2;
                const tempColor = temp > 70 ? '#ff4444' : temp > 50 ? '#ffaa00' : '#00ff88';

                display.innerHTML = `
                    <div style="text-align: center; margin-bottom: 10px; color: #00ffff; font-weight: bold;">
                        🧠 MÉMOIRE THERMIQUE RÉELLE 🧠
                    </div>
                    <div style="margin-bottom: 8px;">
                        🌡️ Température: <span style="color: ${tempColor}; font-weight: bold;">${temp.toFixed(1)}°C</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        🧬 Neurones: <span style="color: #ffff00; font-weight: bold;">${neuronCount.toLocaleString()}</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        ⚡ Activité: <span style="color: #ff88ff;">${(0).toFixed(1)}%</span>
                    </div>
                    <div style="border-top: 1px solid #00ff88; padding-top: 8px; font-size: 11px;">
                        ${this.formatZonesDisplay(thermalData.zones)}
                    </div>
                    <div style="text-align: center; margin-top: 8px; font-size: 10px; color: #888;">
                        Mise à jour: ${new Date().toLocaleTimeString()}
                    </div>
                `;
            }

            formatZonesDisplay(zones) {
                if (!zones) return 'Zones: Non disponibles';

                return Object.entries(zones).map(([key, zone]) => {
                    const tempColor = zone.temp > 60 ? '#ff4444' : zone.temp > 40 ? '#ffaa00' : '#00ff88';
                    return `${key}: <span style="color: ${tempColor};">${zone.temp}°C</span>`;
                }).join(' | ');
            }

            stopMonitoring() {
                this.isActive = false;
                if (this.monitorTimer) {
                    clearInterval(this.monitorTimer);
                    this.monitorTimer = null;
                }

                const display = document.getElementById('thermal-memory-display');
                if (display) {
                    display.remove();
                }

                console.log('🔥 Monitoring mémoire thermique arrêté');
            }
        }

        // Démarrer le monitoring automatiquement
        window.thermalMonitor = new ThermalMemoryMonitor();

        // Ajouter contrôles clavier
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 't') {
                e.preventDefault();
                if (window.thermalMonitor.isActive) {
                    window.thermalMonitor.stopMonitoring();
                } else {
                    window.thermalMonitor.startMonitoring();
                }
            }
        });

        // Fonctions de contrôle du panneau thermique
        let thermalPanelActive = true;
        let thermalUpdateInterval = null;

        async function refreshThermalData() {
            try {
                if (window.thermalDataAPI) {
                    const data = await window.thermalDataAPI.getRealThermalData(true);
                    updateThermalPanel(data);
                    console.log('🔄 Données thermiques actualisées');
                }
            } catch (error) {
                console.error('❌ Erreur actualisation données thermiques:', error);
            }
        }

        function toggleThermalMonitoring() {
            thermalPanelActive = !thermalPanelActive;

            if (thermalPanelActive) {
                startThermalPanelUpdates();
                console.log('▶️ Monitoring thermal repris');
            } else {
                stopThermalPanelUpdates();
                console.log('⏸️ Monitoring thermal en pause');
            }
        }

        function startThermalPanelUpdates() {
            if (thermalUpdateInterval) return;

            thermalUpdateInterval = setInterval(async () => {
                if (thermalPanelActive) {
                    await refreshThermalData();
                }
            }, 3000);
        }

        function stopThermalPanelUpdates() {
            if (thermalUpdateInterval) {
                clearInterval(thermalUpdateInterval);
                thermalUpdateInterval = null;
            }
        }

        function updateThermalPanel(data) {
            if (!data) return;

            // Mettre à jour les statistiques neuronales
            const neuronCount = document.getElementById('neuron-count');
            const synapseCount = document.getElementById('synapse-count');
            const neuralActivity = document.getElementById('neural-activity');
            const formationCount = document.getElementById('formation-count');

            if (neuronCount) neuronCount.textContent = (data.neurones?.total || 86000000000).toLocaleString();
            if (synapseCount) synapseCount.textContent = (data.synapses?.total || 602000000000000).toLocaleString();
            if (neuralActivity) neuralActivity.textContent = `${(0).toFixed(1)}%`;
            if (formationCount) formationCount.textContent = data.formations?.total || 14;

            // Mettre à jour les données thermiques
            const systemTemp = document.getElementById('system-temp');
            const cursorPosition = document.getElementById('cursor-position');
            const cpuTemp = document.getElementById('cpu-temp');
            const thermalEfficiency = document.getElementById('thermal-efficiency');

            if (systemTemp) {
                const temp = data.temperature || 37.2;
                const tempColor = temp > 70 ? '#ff4444' : temp > 50 ? '#ffaa00' : '#00ff88';
                systemTemp.innerHTML = `<span style="color: ${tempColor};">${temp.toFixed(1)}°C</span>`;
            }

            if (cursorPosition) cursorPosition.textContent = data.curseur?.zone || 'Zone 5';
            if (cpuTemp) cpuTemp.textContent = `${(data.curseur?.temperatureCPU || 50.4).toFixed(1)}°C`;
            if (thermalEfficiency) thermalEfficiency.textContent = `${(0 + 80).toFixed(0)}%`;

            // Mettre à jour les zones thermiques
            updateZonesDisplay(data.zones);

            // Mettre à jour l'heure
            const lastUpdate = document.getElementById('last-update');
            if (lastUpdate) lastUpdate.textContent = new Date().toLocaleTimeString();
        }

        function updateZonesDisplay(zones) {
            const zonesDisplay = document.getElementById('zones-display');
            if (!zonesDisplay || !zones) return;

            zonesDisplay.innerHTML = '';

            Object.entries(zones).forEach(([key, zone]) => {
                const tempColor = zone.temp > 60 ? '#ff4444' : zone.temp > 40 ? '#ffaa00' : '#00ff88';
                const neuronCount = (zone.neurones || 0).toLocaleString();

                const zoneDiv = document.createElement('div');
                zoneDiv.style.cssText = `
                    background: rgba(0,0,0,0.5);
                    padding: 8px;
                    border-radius: 5px;
                    border: 1px solid ${tempColor};
                    text-align: center;
                `;

                zoneDiv.innerHTML = `
                    <div style="color: #88ffff; font-weight: bold; font-size: 11px;">${key.toUpperCase()}</div>
                    <div style="color: ${tempColor}; font-weight: bold;">${zone.temp}°C</div>
                    <div style="color: #ffff88; font-size: 10px;">${zone.nom}</div>
                    <div style="color: #88ff88; font-size: 10px;">${neuronCount} neurones</div>
                `;

                zonesDisplay.appendChild(zoneDiv);
            });
        }

        // Démarrer les mises à jour automatiques du panneau
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                refreshThermalData();
                startThermalPanelUpdates();
            }, 2000);
        });
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
