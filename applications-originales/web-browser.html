<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 Navigateur Web LOUNA AI</title>
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .browser-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--bg-primary);
        }

        .browser-toolbar {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .url-bar {
            flex: 1;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            color: white;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .url-bar:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-pink);
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.3);
        }

        .browser-btn {
            padding: 10px 15px;
            background: linear-gradient(135deg, var(--secondary-cyan), var(--secondary-dark-cyan));
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .browser-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .browser-frame {
            flex: 1;
            border: none;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .quick-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .quick-link {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            color: white;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .quick-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .browser-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .loading-indicator {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid var(--primary-pink);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="browser-container">
        <!-- Header -->
        <header class="louna-header">
            <div class="louna-header-content">
                <div class="louna-header-title">
                    <i class="fas fa-globe louna-header-icon"></i>
                    <h1>Navigateur Web LOUNA AI</h1>
                </div>
                <div class="louna-nav">
                    <a href="../interface-originale-complete.html" class="louna-nav-btn" onclick="goToHome()">
                        <i class="fas fa-home"></i>
                        <span>Accueil</span>
                    </a>
                    <a href="brain-monitoring-complete.html" class="louna-nav-btn">
                        <i class="fas fa-brain"></i>
                        <span>Monitoring</span>
                    </a>
                    <a href="futuristic-interface.html" class="louna-nav-btn">
                        <i class="fas fa-fire"></i>
                        <span>Mémoire</span>
                    </a>
                </div>
                <div class="louna-status online">
                    <div class="louna-status-dot"></div>
                    <span>Navigateur Actif</span>
                </div>
            </div>
        </header>

        <!-- Barre d'outils du navigateur -->
        <div class="browser-toolbar">
            <button class="browser-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <button class="browser-btn" onclick="goForward()">
                <i class="fas fa-arrow-right"></i>
            </button>
            <button class="browser-btn" onclick="refreshPage()">
                <i class="fas fa-sync-alt"></i>
            </button>
            
            <input type="text" class="url-bar" id="urlBar" placeholder="Entrez une URL ou recherchez..." 
                   onkeypress="if(event.key==='Enter') navigateToUrl()">
            
            <button class="browser-btn" onclick="navigateToUrl()">
                <i class="fas fa-search"></i>
                Aller
            </button>
            
            <div class="browser-status">
                <div class="loading-indicator" id="loadingIndicator"></div>
                <span id="statusText">Prêt</span>
            </div>
        </div>

        <!-- Liens rapides -->
        <div class="browser-toolbar" style="padding: 10px 15px; border-bottom: none;">
            <div class="quick-links">
                <a href="#" class="quick-link" onclick="loadUrl('https://www.google.com')">Google</a>
                <a href="#" class="quick-link" onclick="loadUrl('https://www.wikipedia.org')">Wikipedia</a>
                <a href="#" class="quick-link" onclick="loadUrl('https://www.github.com')">GitHub</a>
                <a href="#" class="quick-link" onclick="loadUrl('https://www.stackoverflow.com')">Stack Overflow</a>
                <a href="#" class="quick-link" onclick="loadUrl('https://www.youtube.com')">YouTube</a>
                <a href="#" class="quick-link" onclick="loadUrl('https://www.openai.com')">OpenAI</a>
            </div>
        </div>

        <!-- Frame du navigateur -->
        <iframe id="browserFrame" class="browser-frame" src="about:blank"></iframe>
    </div>

    <!-- Scripts -->
    <script src="js/thermal-data-api.js"></script>
    <script src="js/louna-navigation.js"></script>
    <script src="js/louna-notifications.js"></script>
    <script>
        let currentUrl = '';
        let history = [];
        let historyIndex = -1;

        // Fonction pour retourner à l'accueil
        function goToHome() {
            try {
                window.location.href = '../interface-originale-complete.html';
            } catch (error) {
                console.error('❌ Erreur navigation accueil:', error);
                window.history.back();
            }
        }

        // Navigation vers une URL
        function navigateToUrl() {
            const urlBar = document.getElementById('urlBar');
            let url = urlBar.value.trim();
            
            if (!url) return;
            
            // Ajouter https:// si nécessaire
            if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('about:')) {
                if (url.includes('.')) {
                    url = 'https://' + url;
                } else {
                    // Recherche Google si ce n'est pas une URL
                    url = 'https://www.google.com/search?q=' + encodeURIComponent(url);
                }
            }
            
            loadUrl(url);
        }

        // Charger une URL
        function loadUrl(url) {
            const frame = document.getElementById('browserFrame');
            const urlBar = document.getElementById('urlBar');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statusText = document.getElementById('statusText');
            
            try {
                // Afficher le chargement
                loadingIndicator.style.display = 'block';
                statusText.textContent = 'Chargement...';
                
                // Mettre à jour l'historique
                if (currentUrl !== url) {
                    history = history.slice(0, historyIndex + 1);
                    history.push(url);
                    historyIndex = history.length - 1;
                }
                
                currentUrl = url;
                urlBar.value = url;
                
                // Charger la page
                frame.src = url;
                
                // Simuler la fin du chargement
                setTimeout(() => {
                    loadingIndicator.style.display = 'none';
                    statusText.textContent = 'Chargé';
                    showSuccess(`📄 Page chargée: ${url}`);
                }, 2000);
                
            } catch (error) {
                console.error('❌ Erreur chargement:', error);
                loadingIndicator.style.display = 'none';
                statusText.textContent = 'Erreur';
                showError('❌ Impossible de charger la page');
            }
        }

        // Navigation arrière
        function goBack() {
            if (historyIndex > 0) {
                historyIndex--;
                loadUrl(history[historyIndex]);
            }
        }

        // Navigation avant
        function goForward() {
            if (historyIndex < history.length - 1) {
                historyIndex++;
                loadUrl(history[historyIndex]);
            }
        }

        // Actualiser la page
        function refreshPage() {
            const frame = document.getElementById('browserFrame');
            if (currentUrl) {
                loadUrl(currentUrl);
            } else {
                frame.src = frame.src;
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Charger une page d'accueil par défaut
            loadUrl('https://www.google.com');
            
            showSuccess('🌐 Navigateur Web LOUNA AI initialisé !');
        });

        // Gestion des erreurs de frame
        document.getElementById('browserFrame').addEventListener('error', function() {
            const statusText = document.getElementById('statusText');
            const loadingIndicator = document.getElementById('loadingIndicator');
            
            loadingIndicator.style.display = 'none';
            statusText.textContent = 'Erreur de chargement';
            showError('❌ Impossible de charger cette page');
        });
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
