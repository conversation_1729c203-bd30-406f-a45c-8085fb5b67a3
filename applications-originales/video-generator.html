<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur Vidéo LTX - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            min-height: calc(100vh - 80px);
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #ffffff;
        }

        .form-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }

        .form-select option {
            background: #1a1a2e;
            color: white;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .ltx-badge {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }

        .preview-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .preview-title {
            font-size: 18px;
            font-weight: 600;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .preview-controls {
            display: flex;
            gap: 10px;
        }

        .preview-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .preview-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 16/9;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-placeholder {
            text-align: center;
            color: #888;
        }

        .video-placeholder i {
            font-size: 64px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .progress-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .progress-title {
            font-size: 16px;
            font-weight: 600;
            color: #ff69b4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff69b4, #e91e63);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .progress-text {
            font-size: 14px;
            color: #ccc;
            text-align: center;
        }

        .video-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .video-thumbnail {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            position: relative;
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 105, 180, 0.8);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .video-info {
            padding: 10px;
        }

        .video-title {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .video-meta {
            font-size: 11px;
            color: #999;
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff69b4;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        /* Styles pour les vidéos générées avec succès */
        .video-success {
            background: linear-gradient(135deg, #4caf50, #2e7d32);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
        }

        .success-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .video-info-display h3 {
            margin: 0 0 15px 0;
            font-size: 1.2em;
        }

        .video-info-display p {
            margin: 5px 0;
            opacity: 0.9;
        }

        .server-response {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .server-response h4 {
            margin: 0 0 10px 0;
            font-size: 1em;
        }

        .server-response pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 6px;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 0.9em;
            margin: 0;
        }

        .video-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Styles pour les erreurs */
        .video-error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
        }

        .error-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .error-info h3 {
            margin: 0 0 15px 0;
            font-size: 1.2em;
        }

        .error-info p {
            margin: 5px 0;
            opacity: 0.9;
        }

        /* Styles pour le fallback */
        .fallback-info {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            text-align: center;
            color: #ff9800;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .video-gallery {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }

            .video-actions {
                flex-direction: column;
            }

            .action-btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-video"></i>
            Générateur Vidéo LTX
            <span class="ltx-badge">LTX TECH</span>
        </h1>
        <div class="nav-buttons">
            <a href="chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="image-generator-simple.html" class="nav-btn">
                <i class="fas fa-image"></i>
                Images
            </a>
            <a href="web-search.html" class="nav-btn">
                <i class="fas fa-search"></i>
                Recherche
            </a>
            <a href="face-recognition.html" class="nav-btn">
                <i class="fas fa-user-check"></i>
                Reconnaissance
            </a>
            <a href="brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-brain"></i>
                Mémoire
            </a>
            <a href="../interface-originale-complete.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Panel de contrôles -->
        <div class="controls-panel">
            <div class="panel-title">
                <i class="fas fa-sliders-h"></i>
                Paramètres LTX Video
            </div>

            <form id="videoForm">
                <div class="form-group">
                    <label class="form-label" for="prompt">
                        <i class="fas fa-pen"></i> Description de la vidéo
                    </label>
                    <textarea
                        id="prompt"
                        class="form-input form-textarea"
                        placeholder="Décrivez la vidéo que vous voulez générer... (ex: un chat robot qui danse dans un environnement cyberpunk avec des néons)"
                        required
                    ></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="style">
                            <i class="fas fa-palette"></i> Style visuel
                        </label>
                        <select id="style" class="form-select">
                            <option value="realistic">Réaliste</option>
                            <option value="cinematic">Cinématique</option>
                            <option value="anime">Anime</option>
                            <option value="cyberpunk">Cyberpunk</option>
                            <option value="fantasy">Fantasy</option>
                            <option value="abstract">Abstrait</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="duration">
                            <i class="fas fa-clock"></i> Durée
                        </label>
                        <select id="duration" class="form-select">
                            <option value="3">3 secondes</option>
                            <option value="5">5 secondes</option>
                            <option value="10">10 secondes</option>
                            <option value="15">15 secondes</option>
                            <option value="30">30 secondes</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="resolution">
                            <i class="fas fa-expand"></i> Résolution
                        </label>
                        <select id="resolution" class="form-select">
                            <option value="720p">720p (HD)</option>
                            <option value="1080p">1080p (Full HD)</option>
                            <option value="4k">4K (Ultra HD)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="fps">
                            <i class="fas fa-tachometer-alt"></i> FPS
                        </label>
                        <select id="fps" class="form-select">
                            <option value="24">24 FPS (Cinéma)</option>
                            <option value="30">30 FPS (Standard)</option>
                            <option value="60">60 FPS (Fluide)</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="motion">
                        <i class="fas fa-running"></i> Intensité du mouvement
                    </label>
                    <select id="motion" class="form-select">
                        <option value="low">Faible (Statique)</option>
                        <option value="medium">Moyen (Naturel)</option>
                        <option value="high">Élevé (Dynamique)</option>
                        <option value="extreme">Extrême (Action)</option>
                    </select>
                </div>

                <button type="submit" class="generate-btn" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    Générer Vidéo LTX
                </button>
            </form>
        </div>

        <!-- Panel de preview -->
        <div class="preview-section">
            <div class="preview-header">
                <div class="preview-title">
                    <i class="fas fa-play-circle"></i>
                    Preview & Galerie
                </div>
                <div class="preview-controls">
                    <button class="preview-btn" onclick="clearGallery()">
                        <i class="fas fa-trash"></i>
                        Effacer
                    </button>
                    <button class="preview-btn" onclick="exportVideo()">
                        <i class="fas fa-download"></i>
                        Exporter
                    </button>
                </div>
            </div>

            <!-- Conteneur vidéo principal -->
            <div class="video-container" id="videoContainer">
                <div class="video-placeholder">
                    <i class="fas fa-video"></i>
                    <p>Aucune vidéo générée</p>
                    <p>Utilisez le panneau de gauche pour créer votre première vidéo LTX !</p>
                </div>
            </div>

            <!-- Barre de progression -->
            <div class="progress-container" id="progressContainer">
                <div class="progress-title">
                    <i class="fas fa-cog fa-spin"></i>
                    Génération LTX en cours...
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Initialisation...</div>
            </div>

            <!-- Galerie de vidéos -->
            <div class="video-gallery" id="videoGallery">
                <!-- Les vidéos générées apparaîtront ici -->
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let generatedVideos = [];
        let isGenerating = false;
        let currentVideo = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎥 Générateur vidéo LTX initialisé');
            setupEventListeners();
            loadSavedVideos();
        });

        function setupEventListeners() {
            const form = document.getElementById('videoForm');
            form.addEventListener('submit', handleVideoGeneration);
        }

        async function handleVideoGeneration(event) {
            event.preventDefault();

            if (isGenerating) return;

            const prompt = document.getElementById('prompt').value.trim();

            if (!prompt) {
                showNotification('Veuillez entrer une description pour la vidéo', 'error');
                return;
            }

            isGenerating = true;
            updateGenerateButton(true);
            showProgress();

            try {
                const videoData = {
                    prompt: prompt,
                    style: document.getElementById('style').value,
                    duration: document.getElementById('duration').value,
                    resolution: document.getElementById('resolution').value,
                    fps: document.getElementById('fps').value,
                    motion: document.getElementById('motion').value,
                    timestamp: new Date().toISOString()
                };

                // Générer la vidéo avec LTX
                await generateVideoLTX(videoData);

            } catch (error) {
                console.error('Erreur génération vidéo:', error);
                showNotification('Erreur lors de la génération de la vidéo', 'error');
            } finally {
                isGenerating = false;
                updateGenerateButton(false);
                hideProgress();
            }
        }

        async function generateVideoLTX(videoData) {
            console.log('🎬 Génération vidéo LTX RÉELLE:', videoData);

            try {
                // Étapes de génération LTX réelles
                const steps = [
                    { progress: 10, text: 'Connexion au serveur MCP...' },
                    { progress: 25, text: 'Analyse du prompt LTX...' },
                    { progress: 40, text: 'Génération vidéo avec ffmpeg...' },
                    { progress: 60, text: 'Rendu des frames...' },
                    { progress: 80, text: 'Assemblage vidéo final...' },
                    { progress: 95, text: 'Optimisation et compression...' },
                    { progress: 100, text: 'Vidéo LTX générée !' }
                ];

                // Progression visuelle
                for (let i = 0; i < 3; i++) {
                    updateProgress(steps[i].progress, steps[i].text);
                    await new Promise(resolve => setTimeout(resolve, 800));
                }

                // VRAIE GÉNÉRATION VIDÉO via notre API multimédia
                updateProgress(40, 'Génération vidéo LTX...');

                const response = await fetch('/api/generate-video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: videoData.prompt,
                        duration: parseInt(videoData.duration),
                        resolution: videoData.resolution,
                        style: videoData.style,
                        fps: parseInt(videoData.fps)
                    })
                });

                const result = await response.json();
                console.log('🎬 Réponse API multimédia:', result);

                // Progression finale
                for (let i = 3; i < steps.length; i++) {
                    updateProgress(steps[i].progress, steps[i].text);
                    await new Promise(resolve => setTimeout(resolve, 600));
                }

                // Traiter la réponse de l'API multimédia
                let videoUrl = null;
                if (result.success && result.video) {
                    videoUrl = result.video.url || result.video.path;
                }

                const newVideo = {
                    id: Date.now(),
                    url: videoUrl || generateVideoPlaceholder(videoData),
                    prompt: videoData.prompt,
                    style: videoData.style,
                    duration: videoData.duration,
                    resolution: videoData.resolution,
                    fps: videoData.fps,
                    motion: videoData.motion,
                    timestamp: videoData.timestamp,
                    source: videoUrl ? 'ltx-api' : 'ltx-placeholder',
                    apiGenerated: !!videoUrl,
                    metadata: result.metadata || {},
                    technology: 'LTX Video'
                };

                generatedVideos.unshift(newVideo);
                addVideoToGallery(newVideo);
                displayMainVideo(newVideo);
                saveVideos();

                // Ajouter à la mémoire thermique
                addToThermalMemory(newVideo);

                if (videoPath) {
                    showNotification('🎉 Vidéo LTX générée avec succès via MCP !', 'success');
                } else {
                    showNotification('⚠️ Vidéo générée en mode fallback', 'warning');
                }

            } catch (error) {
                console.error('❌ Erreur génération LTX:', error);
                updateProgress(100, 'Erreur de génération');
                showNotification('❌ Erreur lors de la génération vidéo: ' + error.message, 'error');

                // Créer une vidéo d'erreur
                const errorVideo = {
                    id: Date.now(),
                    url: generateVideoPlaceholder(videoData),
                    prompt: videoData.prompt,
                    style: videoData.style,
                    duration: videoData.duration,
                    resolution: videoData.resolution,
                    fps: videoData.fps,
                    motion: videoData.motion,
                    timestamp: videoData.timestamp,
                    source: 'ltx-error',
                    error: error.message
                };

                generatedVideos.unshift(errorVideo);
                addVideoToGallery(errorVideo);
                displayMainVideo(errorVideo);
                saveVideos();
            }
        }

        function generateVideoPlaceholder(videoData) {
            // Générer une URL de vidéo placeholder basée sur les paramètres
            const width = videoData.resolution === '4k' ? 3840 : videoData.resolution === '1080p' ? 1920 : 1280;
            const height = videoData.resolution === '4k' ? 2160 : videoData.resolution === '1080p' ? 1080 : 720;

            // Utiliser un service de vidéo placeholder ou une image animée
            return `https://sample-videos.com/zip/10/mp4/SampleVideo_${width}x${height}_1mb.mp4`;
        }

        function displayMainVideo(videoData) {
            const container = document.getElementById('videoContainer');

            // Affichage différent selon la source
            if (videoData.source === 'ltx-real' && videoData.mcpGenerated) {
                container.innerHTML = `
                    <div class="video-success">
                        <div class="success-badge">
                            <i class="fas fa-check-circle"></i>
                            Vidéo générée via MCP
                        </div>
                        <div class="video-info-display">
                            <h3>🎬 ${videoData.prompt}</h3>
                            <p><strong>Fichier:</strong> ${videoData.url}</p>
                            <p><strong>Durée:</strong> ${videoData.duration}s • <strong>Résolution:</strong> ${videoData.resolution}</p>
                            <p><strong>Style:</strong> ${videoData.style} • <strong>Mouvement:</strong> ${videoData.motion}</p>
                            <p><strong>Généré le:</strong> ${new Date(videoData.timestamp).toLocaleString()}</p>
                        </div>
                        <div class="server-response">
                            <h4>Réponse du serveur:</h4>
                            <pre>${videoData.serverResponse}</pre>
                        </div>
                        <div class="video-actions">
                            <button onclick="openVideoFile('${videoData.url}')" class="action-btn">
                                <i class="fas fa-external-link-alt"></i> Ouvrir le fichier
                            </button>
                            <button onclick="copyVideoPath('${videoData.url}')" class="action-btn">
                                <i class="fas fa-copy"></i> Copier le chemin
                            </button>
                        </div>
                    </div>
                `;
            } else if (videoData.source === 'ltx-error') {
                container.innerHTML = `
                    <div class="video-error">
                        <div class="error-badge">
                            <i class="fas fa-exclamation-triangle"></i>
                            Erreur de génération
                        </div>
                        <div class="error-info">
                            <h3>❌ ${videoData.prompt}</h3>
                            <p><strong>Erreur:</strong> ${videoData.error}</p>
                            <p><strong>Tentative le:</strong> ${new Date(videoData.timestamp).toLocaleString()}</p>
                        </div>
                    </div>
                `;
            } else {
                // Mode fallback ou placeholder
                container.innerHTML = `
                    <video class="video-player" controls autoplay muted>
                        <source src="${videoData.url}" type="video/mp4">
                        <div class="video-placeholder">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Impossible de charger la vidéo</p>
                            <p>Format non supporté</p>
                        </div>
                    </video>
                    <div class="fallback-info">
                        <p><i class="fas fa-info-circle"></i> Vidéo en mode fallback</p>
                    </div>
                `;
            }

            currentVideo = videoData;
        }

        function openVideoFile(path) {
            // Ouvrir le fichier vidéo dans le système
            if (path.startsWith('/tmp/')) {
                showNotification(`Fichier vidéo: ${path}`, 'info');
                // Dans un environnement Electron, on pourrait utiliser shell.openPath(path)
            } else {
                window.open(path, '_blank');
            }
        }

        function copyVideoPath(path) {
            navigator.clipboard.writeText(path).then(() => {
                showNotification('Chemin copié dans le presse-papiers', 'success');
            }).catch(() => {
                showNotification('Impossible de copier le chemin', 'error');
            });
        }

        function addVideoToGallery(videoData) {
            const gallery = document.getElementById('videoGallery');

            const videoCard = document.createElement('div');
            videoCard.className = 'video-card';
            videoCard.innerHTML = `
                <div class="video-thumbnail">
                    <i class="fas fa-video"></i>
                    <div class="play-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
                <div class="video-info">
                    <div class="video-title">${videoData.prompt}</div>
                    <div class="video-meta">
                        ${videoData.style} • ${videoData.duration}s • ${videoData.resolution}
                    </div>
                </div>
            `;

            videoCard.addEventListener('click', () => displayMainVideo(videoData));
            gallery.insertBefore(videoCard, gallery.firstChild);
        }

        function updateGenerateButton(generating) {
            const btn = document.getElementById('generateBtn');
            if (generating) {
                btn.innerHTML = '<div class="loading-spinner"></div> Génération LTX...';
                btn.disabled = true;
            } else {
                btn.innerHTML = '<i class="fas fa-magic"></i> Générer Vidéo LTX';
                btn.disabled = false;
            }
        }

        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function clearGallery() {
            if (confirm('Êtes-vous sûr de vouloir effacer toutes les vidéos ?')) {
                generatedVideos = [];
                document.getElementById('videoGallery').innerHTML = '';
                document.getElementById('videoContainer').innerHTML = `
                    <div class="video-placeholder">
                        <i class="fas fa-video"></i>
                        <p>Galerie effacée</p>
                        <p>Générez de nouvelles vidéos !</p>
                    </div>
                `;
                saveVideos();
                showNotification('Galerie vidéo effacée', 'warning');
            }
        }

        function exportVideo() {
            if (currentVideo) {
                const link = document.createElement('a');
                link.href = currentVideo.url;
                link.download = `louna-video-${currentVideo.id}.mp4`;
                link.click();
                showNotification('Export vidéo démarré', 'success');
            } else {
                showNotification('Aucune vidéo à exporter', 'warning');
            }
        }

        function saveVideos() {
            localStorage.setItem('lounaGeneratedVideos', JSON.stringify(generatedVideos));
        }

        function loadSavedVideos() {
            const saved = localStorage.getItem('lounaGeneratedVideos');
            if (saved) {
                generatedVideos = JSON.parse(saved);
                generatedVideos.forEach(video => addVideoToGallery(video));

                if (generatedVideos.length > 0) {
                    displayMainVideo(generatedVideos[0]);
                }
            }
        }

        function addToThermalMemory(videoData) {
            // Intégration avec la mémoire thermique
            console.log('💾 Ajout vidéo à la mémoire thermique:', {
                type: 'video_generation',
                prompt: videoData.prompt,
                style: videoData.style,
                duration: videoData.duration,
                resolution: videoData.resolution,
                timestamp: videoData.timestamp,
                creator: 'Jean-Luc Passave',
                location: 'Sainte-Anne, Guadeloupe'
            });
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i> ${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
