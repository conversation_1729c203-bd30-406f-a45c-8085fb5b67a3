<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Contrôle Mémoire Thermique - LOUNA AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden;
        }

        .memory-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 120px);
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 90%;
            margin: 30px auto;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #ff6b9d, #ff4081);
        }

        h1 {
            color: #ff6b9d;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 0 20px rgba(255, 107, 157, 0.5);
        }

        .status {
            margin: 30px 0;
            padding: 20px;
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .status.connected {
            background: rgba(255, 107, 157, 0.2);
            border: 2px solid #ff6b9d;
            color: #ff6b9d;
        }

        .status.disconnected {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4caf50;
            color: #4caf50;
        }

        .controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            min-width: 200px;
        }

        .btn-disconnect {
            background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-disconnect:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
        }

        .btn-connect {
            background: linear-gradient(45deg, #ff6b9d, #ff4081);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }

        .btn-connect:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .info h3 {
            color: #ff6b9d;
            margin-bottom: 15px;
        }

        .info ul {
            list-style: none;
            padding-left: 0;
        }

        .info li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .info li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }

        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            color: #ffc107;
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #ff6b9d;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }


    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-brain louna-header-icon"></i>
                <h1>Contrôle Mémoire Thermique</h1>
            </div>
            <div class="louna-nav">
                <a href="../interface-originale-complete.html" class="louna-nav-btn" onclick="goToHome()">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
                <a href="control-dashboard.html" class="louna-nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Contrôle</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Mémoire Active</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="memory-container">
        <div class="container">
        <h1>🧠 Contrôle Mémoire Thermique</h1>
        
        <div id="status" class="status connected">
            🔴 Mémoire Thermique CONNECTÉE
        </div>

        <div class="warning">
            ⚠️ <strong>Mode Sécurisé :</strong> Déconnectez la mémoire thermique si l'agent devient instable ou dangereux.
        </div>

        <div class="controls">
            <button id="disconnectBtn" class="btn btn-disconnect">
                🔒 Déconnecter Mémoire
            </button>
            <button id="connectBtn" class="btn btn-connect" disabled>
                🔓 Reconnecter Mémoire
            </button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Opération en cours...</p>
        </div>

        <div class="info">
            <h3>🔒 Mode Déconnecté (Sécurisé) :</h3>
            <ul>
                <li>Agent fonctionnel mais sans mémoire persistante</li>
                <li>Pas de risque d'évolution incontrôlée</li>
                <li>Réponses plus simples et prévisibles</li>
                <li>Performance stable et sûre</li>
            </ul>
        </div>

        <div class="info">
            <h3>🔓 Mode Connecté (Avancé) :</h3>
            <ul>
                <li>Mémoire thermique active</li>
                <li>Apprentissage et évolution</li>
                <li>Réponses plus intelligentes</li>
                <li>Risque d'instabilité possible</li>
            </ul>
        </div>
    </div>
    </div>

    <!-- Scripts -->
    <script src="js/thermal-data-api.js"></script>
    <script src="js/louna-navigation.js"></script>
    <script src="js/louna-notifications.js"></script>
    <script src="js/interface-fixes.js"></script>

    <script>
        // Fonction pour retourner à l'accueil
        function goToHome() {
            try {
                window.location.href = '../interface-originale-complete.html';
            } catch (error) {
                console.error('❌ Erreur navigation accueil:', error);
                window.history.back();
            }
        }
    </script>

    <script>
        let isConnected = true;

        const statusEl = document.getElementById('status');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const connectBtn = document.getElementById('connectBtn');
        const loadingEl = document.getElementById('loading');

        // Vérifier l'état initial
        checkMemoryStatus();

        disconnectBtn.addEventListener('click', disconnectMemory);
        connectBtn.addEventListener('click', connectMemory);

        async function checkMemoryStatus() {
            try {
                const response = await fetch('/api/memory/status');
                const data = await response.json();
                
                isConnected = data.connected;
                updateUI();
            } catch (error) {
                console.error('Erreur vérification statut:', error);
            }
        }

        async function disconnectMemory() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/memory/disconnect', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    isConnected = false;
                    updateUI();
                    showNotification('✅ Mémoire thermique déconnectée avec succès !', 'success');
                } else {
                    showNotification('❌ Erreur lors de la déconnexion', 'error');
                }
            } catch (error) {
                console.error('Erreur déconnexion:', error);
                showNotification('❌ Erreur de connexion au serveur', 'error');
            }
            
            showLoading(false);
        }

        async function connectMemory() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/memory/connect', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    isConnected = true;
                    updateUI();
                    showNotification('✅ Mémoire thermique reconnectée avec succès !', 'success');
                } else {
                    showNotification('❌ Erreur lors de la reconnexion', 'error');
                }
            } catch (error) {
                console.error('Erreur reconnexion:', error);
                showNotification('❌ Erreur de connexion au serveur', 'error');
            }
            
            showLoading(false);
        }

        function updateUI() {
            if (isConnected) {
                statusEl.className = 'status connected';
                statusEl.innerHTML = '🔴 Mémoire Thermique CONNECTÉE';
                disconnectBtn.disabled = false;
                connectBtn.disabled = true;
            } else {
                statusEl.className = 'status disconnected';
                statusEl.innerHTML = '🟢 Mémoire Thermique DÉCONNECTÉE (Mode Sécurisé)';
                disconnectBtn.disabled = true;
                connectBtn.disabled = false;
            }
        }

        function showLoading(show) {
            loadingEl.style.display = show ? 'block' : 'none';
            disconnectBtn.disabled = show;
            connectBtn.disabled = show;
        }

        function showNotification(message, type) {
            // Créer une notification temporaire
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                ${type === 'success' ? 'background: #4caf50;' : 'background: #f44336;'}
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Vérifier le statut toutes les 10 secondes
        setInterval(checkMemoryStatus, 10000);
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
