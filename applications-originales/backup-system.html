<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💾 Système de Sauvegarde - LOUNA AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden;
        }

        .backup-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 120px);
        }

        .backup-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 25px;
            margin-top: 30px;
        }

        .backup-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .backup-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #00bcd4, #4ecdc4);
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #00bcd4;
        }

        .panel-icon {
            font-size: 1.6rem;
            background: linear-gradient(135deg, #00bcd4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .backup-status {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, rgba(0, 188, 212, 0.2), rgba(78, 205, 196, 0.1));
            border: 2px solid #00bcd4;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(0, 188, 212, 0.3);
        }

        .status-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00bcd4;
            margin-bottom: 8px;
        }

        .status-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .backup-list {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .backup-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #00bcd4;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .backup-info {
            flex: 1;
        }

        .backup-name {
            font-weight: 600;
            color: #00bcd4;
            margin-bottom: 5px;
        }

        .backup-details {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .backup-actions {
            display: flex;
            gap: 10px;
        }

        .backup-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .backup-btn.primary {
            background: linear-gradient(135deg, #00bcd4, #0097a7);
            color: white;
        }

        .backup-btn.secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .backup-btn.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .backup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #00bcd4, #4ecdc4);
            border-radius: 6px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            min-width: 150px;
            justify-content: center;
        }

        .action-btn.create {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            color: white;
        }

        .action-btn.restore {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .action-btn.sync {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .drive-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .drive-icon {
            font-size: 1.2rem;
            color: #00bcd4;
        }

        .drive-info {
            flex: 1;
        }

        .drive-name {
            font-weight: 600;
            color: #00bcd4;
        }

        .drive-space {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .backup-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .action-btn {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-save louna-header-icon"></i>
                <h1>Système de Sauvegarde</h1>
            </div>
            <div class="louna-nav">
                <a href="../interface-originale-complete.html" class="louna-nav-btn" onclick="goToHome()">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="advanced-settings.html" class="louna-nav-btn">
                    <i class="fas fa-cogs"></i>
                    <span>Paramètres</span>
                </a>
                <a href="futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Sauvegarde Active</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="backup-container">
        <!-- Statut de sauvegarde -->
        <div class="backup-panel backup-status">
            <div class="panel-title">
                <i class="fas fa-chart-line panel-icon"></i>
                Statut du Système de Sauvegarde
            </div>
            
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="totalBackups">12</div>
                    <div class="status-label">Sauvegardes Totales</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="lastBackup">2h ago</div>
                    <div class="status-label">Dernière Sauvegarde</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="backupSize">2.4 GB</div>
                    <div class="status-label">Taille Totale</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="successRate">98%</div>
                    <div class="status-label">Taux de Succès</div>
                </div>
            </div>
        </div>

        <div class="backup-grid">
            <!-- Sauvegardes Récentes -->
            <div class="backup-panel">
                <div class="panel-title">
                    <i class="fas fa-history panel-icon"></i>
                    Sauvegardes Récentes
                </div>
                
                <div class="backup-list" id="backupList">
                    <!-- Les sauvegardes seront générées dynamiquement -->
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn create" onclick="createBackup()">
                        <i class="fas fa-plus"></i>
                        Nouvelle Sauvegarde
                    </button>
                </div>
            </div>

            <!-- Gestion des Disques -->
            <div class="backup-panel">
                <div class="panel-title">
                    <i class="fas fa-hdd panel-icon"></i>
                    Disques de Sauvegarde
                </div>
                
                <div class="drive-status">
                    <i class="fas fa-hdd drive-icon"></i>
                    <div class="drive-info">
                        <div class="drive-name">Disque Local (C:)</div>
                        <div class="drive-space">450 GB libre / 1 TB total</div>
                    </div>
                    <div class="status-indicator"></div>
                </div>
                
                <div class="drive-status">
                    <i class="fas fa-external-link-alt drive-icon"></i>
                    <div class="drive-info">
                        <div class="drive-name">T7 External Drive</div>
                        <div class="drive-space">1.2 TB libre / 2 TB total</div>
                    </div>
                    <div class="status-indicator"></div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn sync" onclick="syncToT7()">
                        <i class="fas fa-sync"></i>
                        Sync T7
                    </button>
                </div>
            </div>

            <!-- Restauration -->
            <div class="backup-panel">
                <div class="panel-title">
                    <i class="fas fa-undo panel-icon"></i>
                    Restauration
                </div>
                
                <div class="progress-container" id="restoreProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="restoreProgressFill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="restoreProgressText">Restauration en cours...</div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn restore" onclick="restoreFromBackup()">
                        <i class="fas fa-download"></i>
                        Restaurer
                    </button>
                </div>
            </div>

            <!-- Configuration Auto -->
            <div class="backup-panel">
                <div class="panel-title">
                    <i class="fas fa-cogs panel-icon"></i>
                    Configuration Automatique
                </div>
                
                <div class="backup-item">
                    <div class="backup-info">
                        <div class="backup-name">Sauvegarde Automatique</div>
                        <div class="backup-details">Toutes les 2 heures - Mémoire thermique + Configurations</div>
                    </div>
                    <div class="backup-actions">
                        <button class="backup-btn primary" onclick="toggleAutoBackup()">
                            <i class="fas fa-play"></i>
                            Activé
                        </button>
                    </div>
                </div>
                
                <div class="backup-item">
                    <div class="backup-info">
                        <div class="backup-name">Sync Cloud</div>
                        <div class="backup-details">Synchronisation avec le disque T7 externe</div>
                    </div>
                    <div class="backup-actions">
                        <button class="backup-btn secondary" onclick="toggleCloudSync()">
                            <i class="fas fa-cloud"></i>
                            Activé
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/thermal-data-api.js"></script>
    <script src="js/louna-navigation.js"></script>
    <script src="js/louna-notifications.js"></script>
    <script src="js/interface-fixes.js"></script>
    
    <script>
        // Fonction pour retourner à l'accueil
        function goToHome() {
            try {
                window.location.href = '../interface-originale-complete.html';
            } catch (error) {
                console.error('❌ Erreur navigation accueil:', error);
                window.history.back();
            }
        }

        // Générer la liste des sauvegardes
        function generateBackupList() {
            const backupList = document.getElementById('backupList');
            const backups = [
                {
                    name: 'Sauvegarde Complète',
                    date: '10 Juin 2025 - 14:30',
                    size: '1.2 GB',
                    type: 'complete'
                },
                {
                    name: 'Mémoire Thermique',
                    date: '10 Juin 2025 - 12:15',
                    size: '450 MB',
                    type: 'thermal'
                },
                {
                    name: 'Configurations IA',
                    date: '10 Juin 2025 - 10:00',
                    size: '85 MB',
                    type: 'config'
                },
                {
                    name: 'Données Neuronales',
                    date: '9 Juin 2025 - 22:45',
                    size: '2.1 GB',
                    type: 'neural'
                },
                {
                    name: 'Sauvegarde Auto',
                    date: '9 Juin 2025 - 20:30',
                    size: '890 MB',
                    type: 'auto'
                }
            ];

            backupList.innerHTML = backups.map(backup => `
                <div class="backup-item">
                    <div class="backup-info">
                        <div class="backup-name">${backup.name}</div>
                        <div class="backup-details">${backup.date} • ${backup.size}</div>
                    </div>
                    <div class="backup-actions">
                        <button class="backup-btn primary" onclick="restoreBackup('${backup.name}')">
                            <i class="fas fa-download"></i>
                            Restaurer
                        </button>
                        <button class="backup-btn danger" onclick="deleteBackup('${backup.name}')">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Créer une nouvelle sauvegarde
        function createBackup() {
            if (window.showInfo) {
                window.showInfo('💾 Création de la sauvegarde en cours...');
            }
            
            // Simuler la création de sauvegarde
            setTimeout(() => {
                if (window.showSuccess) {
                    window.showSuccess('✅ Sauvegarde créée avec succès !');
                }
                generateBackupList();
                updateStats();
            }, 3000);
        }

        // Restaurer une sauvegarde
        function restoreBackup(name) {
            if (confirm(`Êtes-vous sûr de vouloir restaurer "${name}" ?`)) {
                const progressContainer = document.getElementById('restoreProgress');
                const progressFill = document.getElementById('restoreProgressFill');
                const progressText = document.getElementById('restoreProgressText');
                
                progressContainer.style.display = 'block';
                
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 0;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        setTimeout(() => {
                            progressContainer.style.display = 'none';
                            if (window.showSuccess) {
                                window.showSuccess(`✅ Restauration de "${name}" terminée !`);
                            }
                        }, 1000);
                    }
                    
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `Restauration en cours... ${Math.round(progress)}%`;
                }, 200);
                
                if (window.showInfo) {
                    window.showInfo(`🔄 Restauration de "${name}" en cours...`);
                }
            }
        }

        // Supprimer une sauvegarde
        function deleteBackup(name) {
            if (confirm(`Êtes-vous sûr de vouloir supprimer "${name}" ?`)) {
                if (window.showSuccess) {
                    window.showSuccess(`🗑️ Sauvegarde "${name}" supprimée`);
                }
                generateBackupList();
                updateStats();
            }
        }

        // Synchroniser avec T7
        function syncToT7() {
            if (window.showInfo) {
                window.showInfo('🔄 Synchronisation avec le disque T7 en cours...');
            }
            
            setTimeout(() => {
                if (window.showSuccess) {
                    window.showSuccess('✅ Synchronisation T7 terminée !');
                }
            }, 2000);
        }

        // Restaurer depuis sauvegarde
        function restoreFromBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.backup,.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    if (window.showInfo) {
                        window.showInfo(`📁 Restauration depuis ${file.name}...`);
                    }
                    
                    setTimeout(() => {
                        if (window.showSuccess) {
                            window.showSuccess('✅ Restauration terminée !');
                        }
                    }, 3000);
                }
            };
            input.click();
        }

        // Toggle sauvegarde automatique
        function toggleAutoBackup() {
            const btn = event.target.closest('button');
            const isActive = btn.innerHTML.includes('Activé');
            
            if (isActive) {
                btn.innerHTML = '<i class="fas fa-pause"></i> Désactivé';
                btn.className = 'backup-btn secondary';
                if (window.showWarning) {
                    window.showWarning('⏸️ Sauvegarde automatique désactivée');
                }
            } else {
                btn.innerHTML = '<i class="fas fa-play"></i> Activé';
                btn.className = 'backup-btn primary';
                if (window.showSuccess) {
                    window.showSuccess('▶️ Sauvegarde automatique activée');
                }
            }
        }

        // Toggle sync cloud
        function toggleCloudSync() {
            const btn = event.target.closest('button');
            const isActive = btn.innerHTML.includes('Activé');
            
            if (isActive) {
                btn.innerHTML = '<i class="fas fa-cloud-slash"></i> Désactivé';
                btn.className = 'backup-btn danger';
                if (window.showWarning) {
                    window.showWarning('☁️ Synchronisation cloud désactivée');
                }
            } else {
                btn.innerHTML = '<i class="fas fa-cloud"></i> Activé';
                btn.className = 'backup-btn secondary';
                if (window.showSuccess) {
                    window.showSuccess('☁️ Synchronisation cloud activée');
                }
            }
        }

        // Mettre à jour les statistiques
        function updateStats() {
            const stats = {
                totalBackups: Math.floor(0) + 10,
                lastBackup: ['1h ago', '2h ago', '3h ago'][Math.floor(0)],
                backupSize: (0 + 1).toFixed(1) + ' GB',
                successRate: Math.floor(0) + 95 + '%'
            };

            Object.keys(stats).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = stats[key];
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            generateBackupList();
            updateStats();
            
            // Mise à jour automatique des stats
            setInterval(updateStats, 30000);
            
            if (window.showSuccess) {
                window.showSuccess('💾 Système de sauvegarde initialisé !');
            }
        });
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
