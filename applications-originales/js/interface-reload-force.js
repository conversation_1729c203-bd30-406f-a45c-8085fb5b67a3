// ===== SCRIPT DE RECHARGEMENT FORCÉ DE L'INTERFACE MODERNE =====
// Ce script force le rechargement complet de l'interface moderne de Louna

console.log('🔄 Script de rechargement forcé chargé');

// Fonction pour vider complètement le cache
function clearAllCache() {
    console.log('🗑️ Vidage du cache...');
    
    // Vider le cache du service worker
    if ('caches' in window) {
        caches.keys().then(names => {
            names.forEach(name => {
                caches.delete(name);
                console.log(`🗑️ Cache supprimé: ${name}`);
            });
        });
    }
    
    // Vider le localStorage
    if (typeof(Storage) !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
        console.log('🗑️ Storage vidé');
    }
    
    // Forcer le rechargement des CSS
    const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
    cssLinks.forEach(link => {
        const href = link.href;
        const newHref = href.includes('?') ? 
            href.split('?')[0] + '?v=' + Date.now() : 
            href + '?v=' + Date.now();
        link.href = newHref;
        console.log(`🎨 CSS rechargé: ${newHref}`);
    });
    
    // Forcer le rechargement des JS
    const jsScripts = document.querySelectorAll('script[src]');
    jsScripts.forEach(script => {
        if (script.src && !script.src.includes('socket.io')) {
            const src = script.src;
            const newSrc = src.includes('?') ? 
                src.split('?')[0] + '?v=' + Date.now() : 
                src + '?v=' + Date.now();
            
            // Créer un nouveau script
            const newScript = document.createElement('script');
            newScript.src = newSrc;
            newScript.async = script.async;
            newScript.defer = script.defer;
            
            // Remplacer l'ancien script
            script.parentNode.insertBefore(newScript, script);
            script.parentNode.removeChild(script);
            console.log(`📜 JS rechargé: ${newSrc}`);
        }
    });
}

// Fonction pour appliquer les styles modernes
function applyModernStyles() {
    console.log('🎨 Application des styles modernes...');
    
    // Ajouter la classe moderne au body
    document.body.classList.add('modern-interface', 'louna-2025');
    
    // Styles CSS modernes injectés directement
    const modernStyles = `
        <style id="modern-interface-styles">
            .modern-interface {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
                color: #ffffff !important;
            }
            
            .chat-container {
                background: rgba(255, 255, 255, 0.05) !important;
                backdrop-filter: blur(10px) !important;
                border-radius: 15px !important;
                border: 1px solid rgba(255, 105, 180, 0.3) !important;
                box-shadow: 0 8px 32px rgba(255, 105, 180, 0.1) !important;
            }
            
            .message-bubble {
                border-radius: 20px !important;
                padding: 15px 20px !important;
                margin: 10px 0 !important;
                max-width: 70% !important;
                word-wrap: break-word !important;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
            }
            
            .user-message {
                background: linear-gradient(135deg, #ff69b4, #ff1493) !important;
                color: white !important;
                margin-left: auto !important;
                margin-right: 20px !important;
            }
            
            .agent-message {
                background: linear-gradient(135deg, #4a90e2, #357abd) !important;
                color: white !important;
                margin-left: 20px !important;
                margin-right: auto !important;
            }
            
            .input-container {
                background: rgba(255, 255, 255, 0.1) !important;
                border-radius: 25px !important;
                padding: 10px !important;
                border: 1px solid rgba(255, 105, 180, 0.3) !important;
            }
            
            .message-input {
                background: transparent !important;
                border: none !important;
                color: white !important;
                font-size: 16px !important;
                padding: 10px 15px !important;
                border-radius: 20px !important;
            }
            
            .message-input::placeholder {
                color: rgba(255, 255, 255, 0.7) !important;
            }
            
            .send-button {
                background: linear-gradient(135deg, #ff69b4, #ff1493) !important;
                border: none !important;
                border-radius: 50% !important;
                width: 45px !important;
                height: 45px !important;
                color: white !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
            }
            
            .send-button:hover {
                transform: scale(1.1) !important;
                box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4) !important;
            }
            
            .home-button {
                background: linear-gradient(135deg, #4a90e2, #357abd) !important;
                border: none !important;
                border-radius: 10px !important;
                padding: 10px 20px !important;
                color: white !important;
                cursor: pointer !important;
                transition: all 0.3s ease !important;
            }
            
            .home-button:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4) !important;
            }
        </style>
    `;
    
    // Supprimer les anciens styles modernes
    const oldStyles = document.getElementById('modern-interface-styles');
    if (oldStyles) {
        oldStyles.remove();
    }
    
    // Injecter les nouveaux styles
    document.head.insertAdjacentHTML('beforeend', modernStyles);
    console.log('✅ Styles modernes appliqués');
}

// Fonction principale de rechargement forcé
function forceCompleteReload() {
    console.log('🚀 Début du rechargement forcé complet...');
    
    // Étape 1: Vider le cache
    clearAllCache();
    
    // Étape 2: Appliquer les styles modernes
    setTimeout(() => {
        applyModernStyles();
    }, 500);
    
    // Étape 3: Recharger la page après un délai
    setTimeout(() => {
        console.log('🔄 Rechargement de la page...');
        const url = new URL(window.location);
        url.searchParams.set('v', Date.now());
        url.searchParams.set('modern', 'true');
        window.location.href = url.toString();
    }, 1000);
}

// Fonction pour détecter si l'interface moderne est chargée
function checkModernInterface() {
    const isModern = document.body.classList.contains('modern-interface');
    const hasModernStyles = document.getElementById('modern-interface-styles');
    
    if (!isModern || !hasModernStyles) {
        console.log('⚠️ Interface moderne non détectée, application forcée...');
        applyModernStyles();
        document.body.classList.add('modern-interface', 'louna-2025');
    } else {
        console.log('✅ Interface moderne détectée et active');
    }
}

// Auto-exécution au chargement
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Vérification de l\'interface moderne...');
    
    // Vérifier et appliquer l'interface moderne
    checkModernInterface();
    
    // Ajouter un bouton de rechargement forcé si en mode debug
    if (window.location.search.includes('debug=true') || window.location.search.includes('force=true')) {
        const forceBtn = document.createElement('button');
        forceBtn.textContent = '🔄 FORCER RECHARGEMENT COMPLET';
        forceBtn.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            z-index: 99999;
            background: #ff4444;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.4);
        `;
        forceBtn.onclick = forceCompleteReload;
        document.body.appendChild(forceBtn);
        console.log('🔧 Bouton de rechargement forcé ajouté');
    }
});

// Exposer les fonctions globalement
window.forceCompleteReload = forceCompleteReload;
window.checkModernInterface = checkModernInterface;
window.applyModernStyles = applyModernStyles;

console.log('✅ Script de rechargement forcé initialisé');
