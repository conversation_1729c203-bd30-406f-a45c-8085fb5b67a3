/**
 * CORRECTION UNIVERSELLE DES BOUTONS D'ENVOI DE CHAT
 * Ce script corrige tous les problèmes de boutons d'envoi dans l'application Louna
 */

(function() {
    'use strict';
    
    console.log('🔧 [ChatButtonsFix] Initialisation de la correction des boutons d\'envoi');
    
    // Variables globales
    let socket = null;
    let isConnected = false;
    let messageHistory = [];
    
    // Configuration
    const CONFIG = {
        socketEvents: {
            send: 'louna message',
            receive: 'louna response'
        },
        selectors: {
            sendButtons: [
                '#send-button',
                '.send-button',
                'button[onclick*="send"]',
                'button[onclick*="Send"]',
                'button[onclick*="Envoyer"]'
            ],
            messageInputs: [
                '#user-input',
                '#message-input',
                '.message-input',
                'input[placeholder*="message"]',
                'input[placeholder*="Message"]'
            ]
        },
        debug: true
    };
    
    // Fonction de log
    function log(message, type = 'info') {
        if (CONFIG.debug) {
            const prefix = '🔧 [ChatButtonsFix]';
            console[type](`${prefix} ${message}`);
        }
    }
    
    // Fonction pour initialiser Socket.io
    function initializeSocket() {
        log('Initialisation de Socket.io...');
        
        try {
            if (typeof io === 'undefined') {
                log('Socket.io non disponible', 'error');
                return false;
            }
            
            socket = io();
            
            socket.on('connect', () => {
                isConnected = true;
                log('✅ Socket.io connecté avec ID: ' + socket.id);
            });
            
            socket.on('disconnect', () => {
                isConnected = false;
                log('❌ Socket.io déconnecté', 'warn');
            });
            
            socket.on('connect_error', (error) => {
                log('❌ Erreur de connexion Socket.io: ' + error.message, 'error');
            });
            
            // Écouter les réponses
            socket.on(CONFIG.socketEvents.receive, (data) => {
                log('📨 Réponse reçue: ' + JSON.stringify(data));
                handleAgentResponse(data);
            });
            
            // Écouter d'autres événements possibles
            socket.on('agent response', (data) => {
                log('📨 Agent response reçue: ' + JSON.stringify(data));
                handleAgentResponse(data);
            });
            
            return true;
        } catch (error) {
            log('❌ Erreur lors de l\'initialisation Socket.io: ' + error.message, 'error');
            return false;
        }
    }
    
    // Fonction pour gérer les réponses de l'agent
    function handleAgentResponse(data) {
        const message = data.message || data;
        
        // Chercher un conteneur de messages pour afficher la réponse
        const messageContainers = [
            '#chat-messages',
            '#conversation-container',
            '.chat-messages',
            '.messages-container'
        ];
        
        for (const selector of messageContainers) {
            const container = document.querySelector(selector);
            if (container) {
                addMessageToContainer(container, message, 'agent');
                break;
            }
        }
    }
    
    // Fonction pour ajouter un message au conteneur
    function addMessageToContainer(container, message, type = 'agent') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.innerHTML = `
            <div class="message-content">${message}</div>
            <div class="message-time">${new Date().toLocaleTimeString()}</div>
        `;
        
        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }
    
    // Fonction pour envoyer un message
    function sendMessage(messageText, inputElement) {
        if (!messageText || !messageText.trim()) {
            log('❌ Message vide', 'warn');
            return false;
        }
        
        if (!socket || !isConnected) {
            log('❌ Socket non connecté', 'error');
            return false;
        }
        
        const message = messageText.trim();
        log('📤 Envoi du message: ' + message);
        
        // Ajouter le message à l'historique
        messageHistory.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });
        
        // Limiter l'historique
        if (messageHistory.length > 10) {
            messageHistory = messageHistory.slice(-10);
        }
        
        // Envoyer via Socket.io
        socket.emit(CONFIG.socketEvents.send, {
            message: message,
            history: messageHistory
        });
        
        // Vider le champ de saisie
        if (inputElement) {
            inputElement.value = '';
        }
        
        // Afficher le message de l'utilisateur
        const messageContainers = [
            '#chat-messages',
            '#conversation-container',
            '.chat-messages',
            '.messages-container'
        ];
        
        for (const selector of messageContainers) {
            const container = document.querySelector(selector);
            if (container) {
                addMessageToContainer(container, message, 'user');
                break;
            }
        }
        
        return true;
    }
    
    // Fonction pour trouver le champ de saisie associé à un bouton
    function findAssociatedInput(button) {
        // Chercher dans le même conteneur parent
        const parent = button.closest('form, .input-group, .chat-input, .message-form');
        if (parent) {
            for (const selector of CONFIG.selectors.messageInputs) {
                const input = parent.querySelector(selector);
                if (input) return input;
            }
        }
        
        // Chercher dans tout le document
        for (const selector of CONFIG.selectors.messageInputs) {
            const input = document.querySelector(selector);
            if (input) return input;
        }
        
        return null;
    }
    
    // Fonction pour corriger un bouton d'envoi
    function fixSendButton(button) {
        if (button.dataset.chatFixed) {
            return; // Déjà corrigé
        }
        
        log('🔧 Correction du bouton: ' + button.outerHTML.substring(0, 100));
        
        // Marquer comme corrigé
        button.dataset.chatFixed = 'true';
        
        // Supprimer les anciens gestionnaires
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Ajouter le nouveau gestionnaire
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            log('🖱️ Clic sur bouton d\'envoi détecté');
            
            const input = findAssociatedInput(newButton);
            if (!input) {
                log('❌ Aucun champ de saisie trouvé', 'error');
                return;
            }
            
            const message = input.value;
            sendMessage(message, input);
        });
        
        // Ajouter un style visuel pour indiquer que le bouton est actif
        newButton.style.position = 'relative';
        newButton.style.cursor = 'pointer';
        
        log('✅ Bouton corrigé avec succès');
    }
    
    // Fonction pour corriger un champ de saisie
    function fixMessageInput(input) {
        if (input.dataset.chatFixed) {
            return; // Déjà corrigé
        }
        
        log('🔧 Correction du champ de saisie: ' + input.outerHTML.substring(0, 100));
        
        // Marquer comme corrigé
        input.dataset.chatFixed = 'true';
        
        // Ajouter le gestionnaire pour la touche Entrée
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                
                log('⌨️ Touche Entrée détectée');
                
                const message = input.value;
                sendMessage(message, input);
            }
        });
        
        log('✅ Champ de saisie corrigé avec succès');
    }
    
    // Fonction pour scanner et corriger tous les éléments
    function scanAndFixElements() {
        log('🔍 Scan des éléments à corriger...');
        
        let buttonsFixed = 0;
        let inputsFixed = 0;
        
        // Corriger les boutons d'envoi
        CONFIG.selectors.sendButtons.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            buttons.forEach(button => {
                if (!button.dataset.chatFixed) {
                    fixSendButton(button);
                    buttonsFixed++;
                }
            });
        });
        
        // Corriger les champs de saisie
        CONFIG.selectors.messageInputs.forEach(selector => {
            const inputs = document.querySelectorAll(selector);
            inputs.forEach(input => {
                if (!input.dataset.chatFixed) {
                    fixMessageInput(input);
                    inputsFixed++;
                }
            });
        });
        
        log(`✅ Scan terminé: ${buttonsFixed} boutons et ${inputsFixed} champs corrigés`);
    }
    
    // Fonction d'initialisation principale
    function initialize() {
        log('🚀 Démarrage de la correction des boutons d\'envoi');
        
        // Initialiser Socket.io
        const socketInitialized = initializeSocket();
        if (!socketInitialized) {
            log('❌ Impossible d\'initialiser Socket.io', 'error');
        }
        
        // Scanner et corriger les éléments existants
        scanAndFixElements();
        
        // Observer les changements DOM pour les nouveaux éléments
        const observer = new MutationObserver(() => {
            scanAndFixElements();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        log('✅ Correction des boutons d\'envoi initialisée avec succès');
    }
    
    // Démarrer quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Exposer des fonctions globales pour le debug
    window.ChatButtonsFix = {
        sendMessage: sendMessage,
        scanAndFixElements: scanAndFixElements,
        isConnected: () => isConnected,
        getSocket: () => socket,
        getHistory: () => messageHistory
    };
    
})();
