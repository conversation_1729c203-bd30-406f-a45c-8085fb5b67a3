/**
 * CONFIGURATION VOCALE GLOBALE LOUNA AI
 * Configuration centralisée pour une voix féminine unique
 * C<PERSON>é par Jean<PERSON>, Guadeloupe
 */

// Configuration globale de la voix
window.LounaVoiceConfig = {
    // VOIX FÉMININE UNIQUE POUR TOUTES LES INTERFACES
    unified: true,
    
    // PARAMÈTRES DE LA VOIX FÉMININE
    voice: {
        language: 'fr-FR',
        rate: 0.85,        // Vitesse naturelle féminine
        pitch: 1.2,        // Ton plus aigu pour voix féminine
        volume: 0.8,       // Volume optimal
        quality: 'high',
        gender: 'female',
        personality: 'friendly'
    },
    
    // NOMS DE VOIX PRÉFÉRÉES (par ordre de priorité)
    preferredVoices: [
        '<PERSON><PERSON><PERSON>',
        'Audrey', 
        '<PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        'Cé<PERSON>',
        'Female',
        'Femme',
        'Woman'
    ],
    
    // INTERFACES SUPPORTÉES
    interfaces: [
        'chat-cognitif-complet',
        'chat-agents',
        'voice-system-enhanced',
        'advanced-voice-system',
        'phone-camera-system',
        'generation-center'
    ],
    
    // MESSAGES DE TEST
    testMessages: {
        initialization: 'Bonjour <PERSON>-<PERSON>, je suis Louna, votre assistante IA. Mon système vocal unifié est maintenant opérationnel.',
        connection: 'Système vocal unifié connecté avec succès !',
        error: 'Erreur dans le système vocal unifié.',
        ready: 'Je suis prête à vous écouter et à vous répondre.'
    },
    
    // FONCTIONS UTILITAIRES
    isUnifiedVoiceAvailable: function() {
        return window.LounaVoice && window.LounaVoice.isAvailable();
    },
    
    getVoiceInfo: function() {
        if (this.isUnifiedVoiceAvailable()) {
            return window.LounaVoice.getVoiceInfo();
        }
        return null;
    },
    
    speak: function(text, options = {}) {
        if (this.isUnifiedVoiceAvailable()) {
            const settings = Object.assign({}, this.voice, options);
            window.LounaVoice.speak(text, settings);
            return true;
        }
        console.warn('⚠️ Système vocal unifié non disponible');
        return false;
    },
    
    startListening: function() {
        if (this.isUnifiedVoiceAvailable()) {
            return window.LounaVoice.startListening();
        }
        return false;
    },
    
    stopListening: function() {
        if (this.isUnifiedVoiceAvailable()) {
            window.LounaVoice.stopListening();
        }
    },
    
    stopSpeaking: function() {
        if (this.isUnifiedVoiceAvailable()) {
            window.LounaVoice.stopSpeaking();
        }
    },
    
    // INITIALISATION AUTOMATIQUE
    init: function() {
        console.log('🎤 Configuration vocale Louna chargée');
        
        // Charger le système vocal unifié si pas déjà chargé
        if (!window.LounaVoice) {
            this.loadUnifiedVoiceSystem();
        }
    },
    
    loadUnifiedVoiceSystem: function() {
        if (document.querySelector('script[src="/js/unified-voice-system.js"]')) {
            console.log('🎤 Système vocal unifié déjà chargé');
            return;
        }
        
        const script = document.createElement('script');
        script.src = '/js/unified-voice-system.js';
        script.onload = () => {
            console.log('✅ Système vocal unifié chargé automatiquement');
            setTimeout(() => {
                if (this.isUnifiedVoiceAvailable()) {
                    this.speak(this.testMessages.connection);
                }
            }, 1000);
        };
        script.onerror = () => {
            console.error('❌ Erreur chargement système vocal unifié');
        };
        document.head.appendChild(script);
    },
    
    // CONFIGURATION POUR CHAQUE INTERFACE
    configureInterface: function(interfaceName) {
        console.log(`🎤 Configuration vocale pour ${interfaceName}`);
        
        switch(interfaceName) {
            case 'chat-cognitif-complet':
                this.configureChatCognitif();
                break;
            case 'chat-agents':
                this.configureChatAgents();
                break;
            case 'voice-system-enhanced':
                this.configureVoiceSystem();
                break;
            default:
                this.configureGeneric();
        }
    },
    
    configureChatCognitif: function() {
        // Configuration spécifique pour le chat cognitif
        if (this.isUnifiedVoiceAvailable()) {
            window.LounaVoice.onSpeechRecognized = function(transcript) {
                if (window.chatInput) {
                    window.chatInput.value = transcript;
                    if (window.addReflection) {
                        window.addReflection('response', '🎤 Texte reconnu: "' + transcript + '"');
                    }
                    setTimeout(() => {
                        if (window.sendMessage) window.sendMessage();
                    }, 500);
                }
            };
        }
    },
    
    configureChatAgents: function() {
        // Configuration spécifique pour le chat agents
        if (this.isUnifiedVoiceAvailable()) {
            window.LounaVoice.onSpeechRecognized = function(transcript) {
                const messageInput = document.getElementById('messageInput');
                if (messageInput) {
                    messageInput.value = transcript;
                    if (window.addReflection) {
                        window.addReflection(`Reconnaissance vocale: "${transcript}"`);
                    }
                    setTimeout(() => {
                        if (window.sendMessage) window.sendMessage();
                    }, 500);
                }
            };
        }
    },
    
    configureVoiceSystem: function() {
        // Configuration spécifique pour le système vocal avancé
        if (this.isUnifiedVoiceAvailable()) {
            window.LounaVoice.onSpeechRecognized = function(transcript) {
                const speechResult = document.getElementById('speechResult');
                if (speechResult) {
                    speechResult.textContent = transcript;
                }
                if (window.processVoiceCommand) {
                    window.processVoiceCommand(transcript);
                }
            };
        }
    },
    
    configureGeneric: function() {
        // Configuration générique
        console.log('🎤 Configuration vocale générique appliquée');
    }
};

// Auto-initialisation
document.addEventListener('DOMContentLoaded', function() {
    window.LounaVoiceConfig.init();
});

// Export pour les modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.LounaVoiceConfig;
}

console.log('🎤 Configuration vocale Louna chargée - Une seule voix féminine pour toutes les interfaces !');
