/**
 * 🔄 SYSTÈME DE MISE À JOUR AUTOMATIQUE DU QI
 */

class AutoQIUpdater {
    constructor() {
        this.updateInterval = 5000; // 5 secondes
        this.isRunning = false;
        this.lastQI = null;
    }

    start() {
        if (this.isRunning) return;
        
        console.log('🔄 Démarrage mise à jour automatique QI...');
        this.isRunning = true;
        
        // Première mise à jour immédiate
        this.updateQI();
        
        // Puis mise à jour périodique
        this.intervalId = setInterval(() => {
            this.updateQI();
        }, this.updateInterval);
    }

    async updateQI() {
        try {
            const response = await fetch('/api/qi/current');
            const data = await response.json();
            
            if (data.success && data.qi) {
                const newQI = data.qi.qi || data.qi;
                
                if (this.lastQI !== newQI) {
                    console.log(`🧠 QI mis à jour: ${this.lastQI} → ${newQI}`);
                    this.lastQI = newQI;
                    this.updateQIDisplay(newQI);
                    
                    // Notification si changement significatif
                    if (this.lastQI && Math.abs(newQI - this.lastQI) >= 5) {
                        this.showNotification(this.lastQI, newQI);
                    }
                }
            }
        } catch (error) {
            console.warn('⚠️ Erreur mise à jour QI:', error.message);
        }
    }

    updateQIDisplay(qi) {
        // Mettre à jour tous les éléments d'affichage du QI
        const selectors = [
            '#qi-value', '.qi-value', '[data-qi]', 
            '.status-value', '#current-qi', '.qi-display'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element) {
                    element.textContent = qi;
                    element.classList.add('qi-updated');
                    setTimeout(() => element.classList.remove('qi-updated'), 1000);
                }
            });
        });
    }

    showNotification(oldQI, newQI) {
        const change = newQI - oldQI;
        const icon = change > 0 ? '📈' : '📉';
        const color = change > 0 ? '#4CAF50' : '#FF9800';
        
        const notification = document.createElement('div');
        notification.innerHTML = `
            <div style="
                position: fixed; top: 20px; right: 20px;
                background: ${color}; color: white;
                padding: 15px 20px; border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000; font-weight: bold;
            ">
                ${icon} QI ${change > 0 ? 'augmenté' : 'diminué'}: ${oldQI} → ${newQI}
            </div>
        `;
        
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 5000);
    }
}

// Créer l'instance globale
window.autoQIUpdater = new AutoQIUpdater();

// Démarrer automatiquement
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.autoQIUpdater.start();
    });
} else {
    window.autoQIUpdater.start();
}

// Styles CSS
const style = document.createElement('style');
style.textContent = `
    .qi-updated {
        animation: qiPulse 0.5s ease-in-out;
    }
    
    @keyframes qiPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

console.log('🔄 Système de mise à jour automatique du QI chargé');
