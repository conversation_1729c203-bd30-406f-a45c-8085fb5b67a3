/**
 * 🔍 VALIDATEUR DE DONNÉES D'ÉVOLUTION LOUNA AI
 * Vérifie que toutes les données de mémoire thermique sont correctement chargées
 * Version: 1.0.0 - Juin 2025
 */

class EvolutionDataValidator {
    constructor() {
        this.validationResults = {
            thermalData: false,
            neuronData: false,
            evolutionData: false,
            apiConnection: false,
            overallHealth: 0
        };
        
        this.requiredFields = {
            thermalData: ['temperature', 'zones', 'curseur', 'neurones', 'synapses'],
            neuronData: ['total', 'parZone'],
            evolutionData: ['brain', 'learning', 'training', 'evolution']
        };
        
        console.log('🔍 Validateur de données d\'évolution initialisé');
    }

    /**
     * Valider toutes les données d'évolution
     */
    async validateAllData() {
        console.log('🔍 === VALIDATION COMPLÈTE DES DONNÉES ===');
        
        try {
            // Valider l'API thermique
            await this.validateThermalAPI();
            
            // Valider les données thermiques
            await this.validateThermalData();
            
            // Valider les données neuronales
            await this.validateNeuronData();
            
            // Valider les données d'évolution
            await this.validateEvolutionData();
            
            // Calculer la santé globale
            this.calculateOverallHealth();
            
            // Afficher le rapport
            this.displayValidationReport();
            
            return this.validationResults;
            
        } catch (error) {
            console.error('❌ Erreur validation globale:', error);
            return this.validationResults;
        }
    }

    /**
     * Valider l'API de données thermiques
     */
    async validateThermalAPI() {
        try {
            if (window.thermalDataAPI) {
                const testData = await window.thermalDataAPI.getRealThermalData();
                if (testData && testData.neurones) {
                    this.validationResults.apiConnection = true;
                    console.log('✅ API thermique: CONNECTÉE');
                } else {
                    console.warn('⚠️ API thermique: DONNÉES INCOMPLÈTES');
                }
            } else {
                console.warn('⚠️ API thermique: NON DISPONIBLE');
            }
        } catch (error) {
            console.error('❌ API thermique: ERREUR -', error.message);
        }
    }

    /**
     * Valider les données thermiques
     */
    async validateThermalData() {
        try {
            let thermalData = null;
            
            // Essayer de récupérer via l'API
            if (window.thermalDataAPI) {
                thermalData = await window.thermalDataAPI.getRealThermalData();
            }
            
            if (thermalData) {
                const missingFields = this.requiredFields.thermalData.filter(
                    field => !thermalData.hasOwnProperty(field)
                );
                
                if (missingFields.length === 0) {
                    this.validationResults.thermalData = true;
                    console.log('✅ Données thermiques: COMPLÈTES');
                    console.log(`   🌡️ Température: ${thermalData.temperature}°C`);
                    console.log(`   🏠 Zones: ${Object.keys(thermalData.zones || {}).length}`);
                    console.log(`   📍 Curseur: ${thermalData.curseur?.zone || 'N/A'}`);
                } else {
                    console.warn('⚠️ Données thermiques: INCOMPLÈTES');
                    console.warn('   Champs manquants:', missingFields);
                }
            } else {
                console.error('❌ Données thermiques: NON DISPONIBLES');
            }
        } catch (error) {
            console.error('❌ Validation données thermiques:', error.message);
        }
    }

    /**
     * Valider les données neuronales
     */
    async validateNeuronData() {
        try {
            let neuronData = null;
            
            // Récupérer via l'API thermique
            if (window.thermalDataAPI) {
                const thermalData = await window.thermalDataAPI.getRealThermalData();
                neuronData = thermalData?.neurones;
            }
            
            if (neuronData) {
                const missingFields = this.requiredFields.neuronData.filter(
                    field => !neuronData.hasOwnProperty(field)
                );
                
                if (missingFields.length === 0 && neuronData.total > 0) {
                    this.validationResults.neuronData = true;
                    console.log('✅ Données neuronales: VALIDÉES');
                    console.log(`   🧬 Total neurones: ${neuronData.total.toLocaleString()}`);
                    console.log(`   📊 Zones avec neurones: ${Object.keys(neuronData.parZone || {}).length}`);
                } else {
                    console.warn('⚠️ Données neuronales: INCOMPLÈTES');
                    console.warn('   Champs manquants:', missingFields);
                }
            } else {
                console.error('❌ Données neuronales: NON DISPONIBLES');
            }
        } catch (error) {
            console.error('❌ Validation données neuronales:', error.message);
        }
    }

    /**
     * Valider les données d'évolution
     */
    async validateEvolutionData() {
        try {
            // Vérifier si evolutionData existe globalement
            if (window.evolutionData) {
                const missingFields = this.requiredFields.evolutionData.filter(
                    field => !window.evolutionData.hasOwnProperty(field)
                );
                
                if (missingFields.length === 0) {
                    this.validationResults.evolutionData = true;
                    console.log('✅ Données d\'évolution: VALIDÉES');
                    console.log(`   🧠 QI: ${window.evolutionData.brain?.intelligence?.qi || 'N/A'}`);
                    console.log(`   🔄 Cycles: ${window.evolutionData.evolution?.cycles || 'N/A'}`);
                    console.log(`   📚 Formations: ${window.evolutionData.training?.completedSessions || 'N/A'}`);
                } else {
                    console.warn('⚠️ Données d\'évolution: INCOMPLÈTES');
                    console.warn('   Champs manquants:', missingFields);
                }
            } else {
                console.error('❌ Données d\'évolution: NON DISPONIBLES');
            }
        } catch (error) {
            console.error('❌ Validation données évolution:', error.message);
        }
    }

    /**
     * Calculer la santé globale du système
     */
    calculateOverallHealth() {
        const validations = Object.values(this.validationResults);
        const validCount = validations.filter(v => v === true).length;
        const totalChecks = validations.length - 1; // Exclure overallHealth
        
        this.validationResults.overallHealth = Math.round((validCount / totalChecks) * 100);
    }

    /**
     * Afficher le rapport de validation
     */
    displayValidationReport() {
        console.log('\n🔍 === RAPPORT DE VALIDATION ===');
        console.log(`📊 Santé globale: ${this.validationResults.overallHealth}%`);
        console.log('📋 Détails:');
        console.log(`   🔌 API Thermique: ${this.validationResults.apiConnection ? '✅' : '❌'}`);
        console.log(`   🌡️ Données Thermiques: ${this.validationResults.thermalData ? '✅' : '❌'}`);
        console.log(`   🧬 Données Neuronales: ${this.validationResults.neuronData ? '✅' : '❌'}`);
        console.log(`   🧠 Données Évolution: ${this.validationResults.evolutionData ? '✅' : '❌'}`);
        
        // Recommandations
        this.displayRecommendations();
    }

    /**
     * Afficher les recommandations
     */
    displayRecommendations() {
        console.log('\n💡 === RECOMMANDATIONS ===');
        
        if (!this.validationResults.apiConnection) {
            console.log('🔧 Charger thermal-data-api.js');
        }
        
        if (!this.validationResults.thermalData) {
            console.log('🔧 Vérifier les fichiers de mémoire thermique');
        }
        
        if (!this.validationResults.neuronData) {
            console.log('🔧 Vérifier les compteurs de neurones');
        }
        
        if (!this.validationResults.evolutionData) {
            console.log('🔧 Initialiser les données d\'évolution');
        }
        
        if (this.validationResults.overallHealth === 100) {
            console.log('🎉 Système parfaitement configuré !');
        }
    }

    /**
     * Validation en temps réel
     */
    startRealTimeValidation(interval = 30000) {
        return setInterval(async () => {
            await this.validateAllData();
        }, interval);
    }

    /**
     * Arrêter la validation en temps réel
     */
    stopRealTimeValidation(validationId) {
        if (validationId) {
            clearInterval(validationId);
            console.log('🔍 Validation temps réel arrêtée');
        }
    }

    /**
     * Obtenir un résumé rapide
     */
    getQuickSummary() {
        return {
            health: this.validationResults.overallHealth,
            status: this.validationResults.overallHealth >= 75 ? 'GOOD' : 
                   this.validationResults.overallHealth >= 50 ? 'WARNING' : 'ERROR',
            issues: Object.entries(this.validationResults)
                .filter(([key, value]) => key !== 'overallHealth' && !value)
                .map(([key]) => key)
        };
    }
}

// Instance globale
window.evolutionDataValidator = new EvolutionDataValidator();

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EvolutionDataValidator;
}

console.log('🔍 Validateur de données d\'évolution chargé et prêt');
