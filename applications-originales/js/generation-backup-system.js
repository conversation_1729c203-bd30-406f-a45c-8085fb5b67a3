/**
 * 💾 SYSTÈME DE SAUVEGARDE AUTOMATIQUE DES GÉNÉRATIONS
 * Sauvegarde intelligente avec compression et récupération
 * Version: 2.1.0 - Juin 2025
 */

class GenerationBackupSystem {
    constructor() {
        this.backupInterval = null;
        this.compressionEnabled = true;
        this.maxBackupSize = 100 * 1024 * 1024; // 100MB
        this.backupFrequency = 5 * 60 * 1000; // 5 minutes
        this.backupHistory = [];
        
        console.log('💾 Système de sauvegarde des générations initialisé');
        this.init();
    }

    async init() {
        try {
            // Vérifier l'espace de stockage disponible
            await this.checkStorageSpace();
            
            // Charger l'historique des sauvegardes
            this.loadBackupHistory();
            
            // Démarrer les sauvegardes automatiques
            this.startAutoBackup();
            
            // Nettoyer les anciennes sauvegardes
            this.cleanupOldBackups();
            
            console.log('✅ Système de sauvegarde prêt');
            
        } catch (error) {
            console.error('❌ Erreur initialisation sauvegarde:', error);
        }
    }

    async checkStorageSpace() {
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                const usedMB = Math.round(estimate.usage / 1024 / 1024);
                const quotaMB = Math.round(estimate.quota / 1024 / 1024);
                
                console.log(`📊 Stockage: ${usedMB}MB utilisés sur ${quotaMB}MB disponibles`);
                
                if (usedMB > quotaMB * 0.9) {
                    console.warn('⚠️ Espace de stockage faible');
                    this.cleanupOldBackups(true); // Nettoyage forcé
                }
            }
        } catch (error) {
            console.warn('⚠️ Impossible de vérifier l\'espace de stockage');
        }
    }

    startAutoBackup() {
        // Sauvegarde immédiate
        this.performBackup();
        
        // Sauvegardes périodiques
        this.backupInterval = setInterval(() => {
            this.performBackup();
        }, this.backupFrequency);
        
        console.log(`🔄 Sauvegardes automatiques démarrées (${this.backupFrequency / 60000}min)`);
    }

    async performBackup() {
        try {
            const timestamp = Date.now();
            const backupId = `backup_${timestamp}`;
            
            // Collecter toutes les données de génération
            const generationData = this.collectGenerationData();
            
            if (!generationData || Object.keys(generationData).length === 0) {
                console.log('📝 Aucune nouvelle donnée à sauvegarder');
                return;
            }

            // Compression des données si activée
            let backupData = generationData;
            if (this.compressionEnabled) {
                backupData = await this.compressData(generationData);
            }

            // Vérifier la taille
            const dataSize = this.getDataSize(backupData);
            if (dataSize > this.maxBackupSize) {
                console.warn('⚠️ Sauvegarde trop volumineuse, compression forcée');
                backupData = await this.compressData(generationData, true);
            }

            // Sauvegarder
            const backup = {
                id: backupId,
                timestamp: timestamp,
                data: backupData,
                compressed: this.compressionEnabled,
                size: dataSize,
                version: '2.1.0'
            };

            localStorage.setItem(`louna_backup_${backupId}`, JSON.stringify(backup));
            
            // Mettre à jour l'historique
            this.backupHistory.push({
                id: backupId,
                timestamp: timestamp,
                size: dataSize,
                compressed: this.compressionEnabled
            });

            this.saveBackupHistory();
            
            console.log(`💾 Sauvegarde créée: ${backupId} (${this.formatSize(dataSize)})`);
            
            // Nettoyer les anciennes sauvegardes
            this.cleanupOldBackups();
            
        } catch (error) {
            console.error('❌ Erreur lors de la sauvegarde:', error);
        }
    }

    collectGenerationData() {
        const data = {};
        
        try {
            // Images générées
            const images = localStorage.getItem('lounaGeneratedImages');
            if (images) {
                data.images = JSON.parse(images);
            }

            // Vidéos générées
            const videos = localStorage.getItem('lounaGeneratedVideos');
            if (videos) {
                data.videos = JSON.parse(videos);
            }

            // Musiques générées
            const music = localStorage.getItem('lounaGeneratedMusic');
            if (music) {
                data.music = JSON.parse(music);
            }

            // Modèles 3D générés
            const models3d = localStorage.getItem('lounaGenerated3DModels');
            if (models3d) {
                data.models3d = JSON.parse(models3d);
            }

            // Historique des générations
            const history = localStorage.getItem('lounaGenerationHistory');
            if (history) {
                data.history = JSON.parse(history);
            }

            // Paramètres de génération
            const settings = localStorage.getItem('lounaGenerationSettings');
            if (settings) {
                data.settings = JSON.parse(settings);
            }

        } catch (error) {
            console.error('❌ Erreur collecte données:', error);
        }

        return data;
    }

    async compressData(data, forceCompression = false) {
        try {
            const jsonString = JSON.stringify(data);
            
            if (forceCompression || jsonString.length > 50000) {
                // Compression simple par suppression des espaces et optimisation
                const compressed = jsonString
                    .replace(/\s+/g, ' ')
                    .replace(/,\s*}/g, '}')
                    .replace(/,\s*]/g, ']');
                
                console.log(`🗜️ Compression: ${jsonString.length} → ${compressed.length} caractères`);
                return compressed;
            }
            
            return jsonString;
            
        } catch (error) {
            console.error('❌ Erreur compression:', error);
            return JSON.stringify(data);
        }
    }

    async restoreBackup(backupId) {
        try {
            const backupData = localStorage.getItem(`louna_backup_${backupId}`);
            if (!backupData) {
                throw new Error('Sauvegarde introuvable');
            }

            const backup = JSON.parse(backupData);
            let data = backup.data;

            // Décompression si nécessaire
            if (backup.compressed && typeof data === 'string') {
                data = JSON.parse(data);
            }

            // Restaurer les données
            if (data.images) {
                localStorage.setItem('lounaGeneratedImages', JSON.stringify(data.images));
            }
            if (data.videos) {
                localStorage.setItem('lounaGeneratedVideos', JSON.stringify(data.videos));
            }
            if (data.music) {
                localStorage.setItem('lounaGeneratedMusic', JSON.stringify(data.music));
            }
            if (data.models3d) {
                localStorage.setItem('lounaGenerated3DModels', JSON.stringify(data.models3d));
            }
            if (data.history) {
                localStorage.setItem('lounaGenerationHistory', JSON.stringify(data.history));
            }
            if (data.settings) {
                localStorage.setItem('lounaGenerationSettings', JSON.stringify(data.settings));
            }

            console.log(`✅ Sauvegarde restaurée: ${backupId}`);
            
            // Notifier la restauration
            this.notifyRestore(backupId);
            
            return true;

        } catch (error) {
            console.error('❌ Erreur restauration:', error);
            throw error;
        }
    }

    cleanupOldBackups(force = false) {
        try {
            const maxBackups = force ? 3 : 10;
            
            if (this.backupHistory.length > maxBackups) {
                const toDelete = this.backupHistory
                    .sort((a, b) => a.timestamp - b.timestamp)
                    .slice(0, this.backupHistory.length - maxBackups);

                toDelete.forEach(backup => {
                    localStorage.removeItem(`louna_backup_${backup.id}`);
                    console.log(`🗑️ Sauvegarde supprimée: ${backup.id}`);
                });

                this.backupHistory = this.backupHistory
                    .filter(backup => !toDelete.find(del => del.id === backup.id));

                this.saveBackupHistory();
                
                console.log(`🧹 ${toDelete.length} anciennes sauvegardes supprimées`);
            }

        } catch (error) {
            console.error('❌ Erreur nettoyage:', error);
        }
    }

    loadBackupHistory() {
        try {
            const saved = localStorage.getItem('lounaBackupHistory');
            if (saved) {
                this.backupHistory = JSON.parse(saved);
                console.log(`📚 ${this.backupHistory.length} sauvegardes trouvées`);
            }
        } catch (error) {
            console.warn('⚠️ Erreur chargement historique sauvegardes');
        }
    }

    saveBackupHistory() {
        try {
            localStorage.setItem('lounaBackupHistory', JSON.stringify(this.backupHistory));
        } catch (error) {
            console.warn('⚠️ Erreur sauvegarde historique');
        }
    }

    getDataSize(data) {
        return new Blob([typeof data === 'string' ? data : JSON.stringify(data)]).size;
    }

    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    notifyRestore(backupId) {
        const event = new CustomEvent('backupRestored', {
            detail: { backupId: backupId }
        });
        document.dispatchEvent(event);
    }

    getBackupList() {
        return this.backupHistory
            .sort((a, b) => b.timestamp - a.timestamp)
            .map(backup => ({
                ...backup,
                date: new Date(backup.timestamp).toLocaleString(),
                sizeFormatted: this.formatSize(backup.size)
            }));
    }

    exportBackup(backupId) {
        try {
            const backupData = localStorage.getItem(`louna_backup_${backupId}`);
            if (!backupData) {
                throw new Error('Sauvegarde introuvable');
            }

            const blob = new Blob([backupData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `louna_backup_${backupId}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log(`📤 Sauvegarde exportée: ${backupId}`);

        } catch (error) {
            console.error('❌ Erreur export:', error);
            throw error;
        }
    }

    destroy() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
        }
        console.log('💾 Système de sauvegarde arrêté');
    }
}

// Initialisation globale
window.generationBackupSystem = new GenerationBackupSystem();

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GenerationBackupSystem;
}
