/**
 * Hub Central Intelligent - Louna
 * Gestionnaire principal de l'interface unifiée
 */

class UnifiedHub {
    constructor() {
        this.currentSection = 'chat';
        this.systemStatus = {
            qi: 120,
            neurons: 71,
            memoryTemp: 42,
            efficiency: 84
        };
        this.chatMessages = [];
        this.isConnected = false;
        this.init();
    }

    init() {
        console.log('🚀 Initialisation du Hub Central Intelligent');
        this.setupEventListeners();
        this.connectToSystem();
        this.startStatusUpdates();
        this.loadRecentActivity();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Chat
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Auto-resize chat input
        chatInput.addEventListener('input', (e) => {
            e.target.style.height = 'auto';
            e.target.style.height = e.target.scrollHeight + 'px';
        });
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Update header
        const titles = {
            chat: 'Chat Intelligent',
            generation: 'Studio de Génération',
            code: 'Environnement de Développement',
            analysis: 'Analyse et Insights',
            memory: 'Mémoire Thermique',
            monitoring: 'Monitoring Système',
            agents: 'Gestion des Agents',
            settings: 'Paramètres'
        };

        const breadcrumbs = {
            chat: 'Accueil > Chat',
            generation: 'Accueil > Génération',
            code: 'Accueil > Développement',
            analysis: 'Accueil > Analyse',
            memory: 'Système > Mémoire',
            monitoring: 'Système > Monitoring',
            agents: 'Système > Agents',
            settings: 'Système > Paramètres'
        };

        document.getElementById('content-title').textContent = titles[sectionName];
        document.getElementById('content-breadcrumb').textContent = breadcrumbs[sectionName];

        this.currentSection = sectionName;
        this.loadSectionContent(sectionName);
    }

    async loadSectionContent(sectionName) {
        const section = document.getElementById(`${sectionName}-section`);

        switch(sectionName) {
            case 'generation':
                await this.loadGenerationStudio(section);
                break;
            case 'code':
                await this.loadCodeEditor(section);
                break;
            case 'analysis':
                await this.loadAnalysisTools(section);
                break;
            case 'memory':
                await this.loadMemoryInterface(section);
                break;
            case 'monitoring':
                await this.loadMonitoringDashboard(section);
                break;
            case 'agents':
                await this.loadAgentsManager(section);
                break;
            case 'settings':
                await this.loadSettings(section);
                break;
        }
    }

    async loadGenerationStudio(container) {
        container.innerHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; height: 100%;">
                <div>
                    <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                        <i class="fas fa-magic"></i> Génération de Contenu
                    </h3>
                    <div style="background: rgba(255,255,255,0.05); padding: 20px; border-radius: 15px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #888;">Type de contenu :</label>
                            <select id="content-type" style="width: 100%; padding: 10px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white;">
                                <option value="image">Image</option>
                                <option value="video">Vidéo</option>
                                <option value="audio">Audio</option>
                                <option value="3d">Modèle 3D</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #888;">Description :</label>
                            <textarea id="generation-prompt" placeholder="Décrivez ce que vous voulez générer..." style="width: 100%; height: 100px; padding: 10px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: white; resize: vertical;"></textarea>
                        </div>
                        <button onclick="hub.generateContent()" style="width: 100%; padding: 12px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border: none; border-radius: 8px; color: white; font-weight: bold; cursor: pointer;">
                            <i class="fas fa-magic"></i> Générer
                        </button>
                    </div>
                </div>
                <div>
                    <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                        <i class="fas fa-eye"></i> Aperçu
                    </h3>
                    <div id="generation-preview" style="background: rgba(255,255,255,0.05); padding: 20px; border-radius: 15px; height: 300px; display: flex; align-items: center; justify-content: center; color: #888;">
                        Sélectionnez un type de contenu et cliquez sur "Générer"
                    </div>
                </div>
            </div>
        `;
    }

    async loadCodeEditor(container) {
        container.innerHTML = `
            <div style="display: grid; grid-template-columns: 1fr 300px; gap: 20px; height: 100%;">
                <div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="color: #4ecdc4;"><i class="fas fa-code"></i> Éditeur de Code</h3>
                        <div>
                            <select id="code-language" style="padding: 8px; background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); border-radius: 5px; color: white; margin-right: 10px;">
                                <option value="javascript">JavaScript</option>
                                <option value="python">Python</option>
                                <option value="html">HTML</option>
                                <option value="css">CSS</option>
                            </select>
                            <button onclick="hub.runCode()" style="padding: 8px 15px; background: #4ecdc4; border: none; border-radius: 5px; color: white; cursor: pointer;">
                                <i class="fas fa-play"></i> Exécuter
                            </button>
                        </div>
                    </div>
                    <textarea id="code-editor" style="width: 100%; height: 400px; padding: 15px; background: #1e1e1e; border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; color: #f8f8f2; font-family: 'Courier New', monospace; font-size: 14px;" placeholder="// Écrivez votre code ici..."></textarea>
                </div>
                <div>
                    <h3 style="margin-bottom: 15px; color: #4ecdc4;"><i class="fas fa-terminal"></i> Console</h3>
                    <div id="code-console" style="background: #1e1e1e; border: 1px solid rgba(255,255,255,0.2); border-radius: 8px; padding: 15px; height: 400px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px; color: #f8f8f2;">
                        Console prête...<br>
                    </div>
                </div>
            </div>
        `;
    }

    async loadAnalysisTools(container) {
        container.innerHTML = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div>
                    <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                        <i class="fas fa-chart-line"></i> Analyse de Performance
                    </h3>
                    <div style="background: rgba(255,255,255,0.05); padding: 20px; border-radius: 15px;">
                        <canvas id="performance-chart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div>
                    <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                        <i class="fas fa-brain"></i> Analyse Cognitive
                    </h3>
                    <div style="background: rgba(255,255,255,0.05); padding: 20px; border-radius: 15px;">
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>Intelligence</span>
                                <span>120 QI</span>
                            </div>
                            <div style="background: rgba(255,255,255,0.1); height: 8px; border-radius: 4px;">
                                <div style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); height: 100%; width: 85%; border-radius: 4px;"></div>
                            </div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>Créativité</span>
                                <span>92%</span>
                            </div>
                            <div style="background: rgba(255,255,255,0.1); height: 8px; border-radius: 4px;">
                                <div style="background: linear-gradient(45deg, #4ecdc4, #45b7d1); height: 100%; width: 92%; border-radius: 4px;"></div>
                            </div>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <span>Apprentissage</span>
                                <span>78%</span>
                            </div>
                            <div style="background: rgba(255,255,255,0.1); height: 8px; border-radius: 4px;">
                                <div style="background: linear-gradient(45deg, #45b7d1, #96ceb4); height: 100%; width: 78%; border-radius: 4px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadMemoryInterface(container) {
        container.innerHTML = `
            <div style="height: 100%;">
                <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                    <i class="fas fa-brain"></i> Mémoire Thermique
                </h3>
                <iframe src="/futuristic-interface.html" style="width: 100%; height: 500px; border: none; border-radius: 15px;"></iframe>
            </div>
        `;
    }

    async loadMonitoringDashboard(container) {
        container.innerHTML = `
            <div style="height: 100%;">
                <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                    <i class="fas fa-tachometer-alt"></i> Monitoring Système
                </h3>
                <iframe src="/brain-monitor" style="width: 100%; height: 500px; border: none; border-radius: 15px;"></iframe>
            </div>
        `;
    }

    async loadAgentsManager(container) {
        container.innerHTML = `
            <div style="height: 100%;">
                <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                    <i class="fas fa-robot"></i> Gestion des Agents
                </h3>
                <iframe src="/agents.html" style="width: 100%; height: 500px; border: none; border-radius: 15px;"></iframe>
            </div>
        `;
    }

    async loadSettings(container) {
        container.innerHTML = `
            <div style="height: 100%;">
                <h3 style="margin-bottom: 20px; color: #4ecdc4;">
                    <i class="fas fa-cog"></i> Paramètres Système
                </h3>
                <iframe src="/settings" style="width: 100%; height: 500px; border: none; border-radius: 15px;"></iframe>
            </div>
        `;
    }

    async sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();

        if (!message) return;

        // Add user message
        this.addChatMessage('user', message);
        input.value = '';

        // Show typing indicator
        this.addChatMessage('assistant', '<i class="loading"></i> Louna réfléchit...', 'typing');

        try {
            const response = await fetch('/api/chat/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    userId: 'unified-hub'
                })
            });

            const data = await response.json();

            // Remove typing indicator
            this.removeChatMessage('typing');

            // Add assistant response
            this.addChatMessage('assistant', data.response || 'Désolé, je n\'ai pas pu traiter votre demande.');

        } catch (error) {
            console.error('Erreur chat:', error);
            this.removeChatMessage('typing');
            this.addChatMessage('assistant', 'Erreur de connexion. Veuillez réessayer.');
        }
    }

    addChatMessage(sender, content, id = null) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');

        if (id) messageDiv.id = id;

        messageDiv.style.cssText = `
            margin-bottom: 15px;
            display: flex;
            ${sender === 'user' ? 'justify-content: flex-end;' : 'justify-content: flex-start;'}
        `;

        const bubble = document.createElement('div');
        bubble.style.cssText = `
            max-width: 70%;
            padding: 12px 18px;
            border-radius: 18px;
            ${sender === 'user'
                ? 'background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white;'
                : 'background: rgba(255,255,255,0.1); color: white; border: 1px solid rgba(255,255,255,0.2);'
            }
            word-wrap: break-word;
        `;

        bubble.innerHTML = content;
        messageDiv.appendChild(bubble);
        messagesContainer.appendChild(messageDiv);

        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    removeChatMessage(id) {
        const element = document.getElementById(id);
        if (element) element.remove();
    }

    async connectToSystem() {
        try {
            const response = await fetch('/api/system/status');
            const data = await response.json();
            this.isConnected = true;
            console.log('✅ Connecté au système Louna');
        } catch (error) {
            console.error('❌ Erreur de connexion:', error);
            this.isConnected = false;
        }
    }

    startStatusUpdates() {
        setInterval(() => {
            this.updateSystemStatus();
        }, 2000);
    }

    async updateSystemStatus() {
        try {
            const response = await fetch('/api/monitoring/status');
            const data = await response.json();

            if (data.qi) document.getElementById('qi-value').textContent = data.qi;
            if (data.neurons) document.getElementById('neurons-value').textContent = data.neurons;
            if (data.memoryTemp) document.getElementById('memory-temp').textContent = data.memoryTemp + '°C';
            if (data.efficiency) document.getElementById('efficiency').textContent = data.efficiency + '%';

        } catch (error) {
            console.log('Status update failed:', error);
        }
    }

    async loadRecentActivity() {
        const container = document.getElementById('recent-activity');

        try {
            const response = await fetch('/api/activity/recent');
            const activities = await response.json();

            container.innerHTML = activities.map(activity => `
                <div style="padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 12px;">
                    <div style="color: #4ecdc4;">${activity.type}</div>
                    <div style="color: #888; margin-top: 2px;">${activity.time}</div>
                </div>
            `).join('');

        } catch (error) {
            container.innerHTML = `
                <div style="text-align: center; color: #888; font-size: 12px;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p style="margin-top: 5px;">Aucune activité récente</p>
                </div>
            `;
        }
    }

    async generateContent() {
        const type = document.getElementById('content-type').value;
        const prompt = document.getElementById('generation-prompt').value;
        const preview = document.getElementById('generation-preview');

        if (!prompt.trim()) {
            alert('Veuillez entrer une description');
            return;
        }

        preview.innerHTML = '<i class="loading"></i> Génération en cours...';

        try {
            // Utiliser le vrai générateur de contenu
            if (window.realContentGenerator && window.realContentGenerator.isInitialized) {
                const options = {
                    width: 512,
                    height: 512,
                    language: type === 'code' ? 'javascript' : undefined
                };

                const generationResult = await window.realContentGenerator.generate(type, prompt, options);

                // Surveiller le progrès
                this.monitorGeneration(generationResult.id, preview);

            } else {
                // Fallback vers l'ancienne API
                const response = await fetch('/api/generation/create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type, prompt })
                });

                const result = await response.json();
                this.displayGenerationResult(result, preview);
            }

        } catch (error) {
            preview.innerHTML = `
                <div style="text-align: center; color: #ff6b6b;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                    <h4>Erreur de génération</h4>
                    <p style="color: #888;">${error.message}</p>
                </div>
            `;
        }
    }

    async monitorGeneration(generationId, preview) {
        const checkInterval = setInterval(async () => {
            try {
                const status = window.realContentGenerator.getGenerationStatus(generationId);

                if (!status) {
                    clearInterval(checkInterval);
                    return;
                }

                // Mettre à jour le progrès
                preview.innerHTML = `
                    <div style="text-align: center;">
                        <i class="loading"></i>
                        <h4>Génération en cours...</h4>
                        <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 10px; margin: 10px 0;">
                            <div style="background: #4ecdc4; height: 8px; border-radius: 4px; width: ${status.progress}%; transition: width 0.3s ease;"></div>
                        </div>
                        <p style="color: #888; font-size: 12px;">Statut: ${status.status} (${status.progress}%)</p>
                    </div>
                `;

                if (status.status === 'completed') {
                    clearInterval(checkInterval);
                    this.displayRealGenerationResult(status, preview);
                } else if (status.status === 'failed') {
                    clearInterval(checkInterval);
                    preview.innerHTML = `
                        <div style="text-align: center; color: #ff6b6b;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                            <h4>Erreur de génération</h4>
                            <p style="color: #888;">${status.error}</p>
                        </div>
                    `;
                }

            } catch (error) {
                clearInterval(checkInterval);
                console.error('Erreur monitoring génération:', error);
            }
        }, 1000);
    }

    displayRealGenerationResult(status, preview) {
        const result = status.result;

        if (!result || !result.success) {
            preview.innerHTML = `
                <div style="text-align: center; color: #ff6b6b;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i>
                    <h4>Génération échouée</h4>
                    <p style="color: #888;">Impossible de générer le contenu</p>
                </div>
            `;
            return;
        }

        let content = '';

        if (status.type === 'image') {
            content = `
                <div style="text-align: center;">
                    <i class="fas fa-check-circle" style="color: #4ecdc4; font-size: 24px; margin-bottom: 10px;"></i>
                    <h4>Image générée avec succès !</h4>
                    <div style="margin: 15px 0;">
                        <img src="${result.url}" style="max-width: 100%; max-height: 200px; border-radius: 10px; border: 2px solid #4ecdc4;" alt="Image générée">
                    </div>
                    <p style="color: #888; font-size: 12px;">Taille: ${result.size} octets</p>
                    <div style="margin-top: 15px;">
                        <button onclick="window.open('${result.url}')" style="padding: 8px 15px; background: #4ecdc4; border: none; border-radius: 5px; color: white; cursor: pointer; margin-right: 10px;">
                            Voir en grand
                        </button>
                        <button onclick="hub.downloadFile('${result.url}', '${result.filename}')" style="padding: 8px 15px; background: #45b7d1; border: none; border-radius: 5px; color: white; cursor: pointer;">
                            Télécharger
                        </button>
                    </div>
                </div>
            `;
        } else if (status.type === 'code') {
            content = `
                <div style="text-align: center;">
                    <i class="fas fa-check-circle" style="color: #4ecdc4; font-size: 24px; margin-bottom: 10px;"></i>
                    <h4>Code généré avec succès !</h4>
                    <div style="background: #1e1e1e; border-radius: 10px; padding: 15px; margin: 15px 0; text-align: left; overflow-x: auto;">
                        <pre style="color: #f8f8f2; font-family: 'Courier New', monospace; font-size: 12px; margin: 0; white-space: pre-wrap;">${this.escapeHtml(result.content.code)}</pre>
                    </div>
                    <p style="color: #888; font-size: 12px;">Langage: ${result.content.language} | Type: ${result.content.codeType}</p>
                    <div style="margin-top: 15px;">
                        <button onclick="hub.copyToClipboard(\`${this.escapeForJs(result.content.code)}\`)" style="padding: 8px 15px; background: #4ecdc4; border: none; border-radius: 5px; color: white; cursor: pointer; margin-right: 10px;">
                            Copier
                        </button>
                        <button onclick="window.open('${result.url}')" style="padding: 8px 15px; background: #45b7d1; border: none; border-radius: 5px; color: white; cursor: pointer;">
                            Ouvrir fichier
                        </button>
                    </div>
                </div>
            `;
        } else {
            content = `
                <div style="text-align: center;">
                    <i class="fas fa-check-circle" style="color: #4ecdc4; font-size: 24px; margin-bottom: 10px;"></i>
                    <h4>Contenu généré avec succès !</h4>
                    <p style="color: #888; margin: 10px 0;">Type: ${status.type}</p>
                    <button onclick="window.open('${result.url}')" style="padding: 8px 15px; background: #4ecdc4; border: none; border-radius: 5px; color: white; cursor: pointer;">
                        Voir le résultat
                    </button>
                </div>
            `;
        }

        preview.innerHTML = content;
    }

    displayGenerationResult(result, preview) {
        if (result.success) {
            preview.innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-check-circle" style="color: #4ecdc4; font-size: 24px; margin-bottom: 10px;"></i>
                    <h4>Génération réussie !</h4>
                    <p style="color: #888; margin: 10px 0;">Type: ${result.type}</p>
                    <button onclick="window.open('${result.url}')" style="padding: 8px 15px; background: #4ecdc4; border: none; border-radius: 5px; color: white; cursor: pointer;">
                        Voir le résultat
                    </button>
                </div>
            `;
        } else {
            throw new Error(result.error);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    escapeForJs(text) {
        return text.replace(/`/g, '\\`').replace(/\$/g, '\\$');
    }

    downloadFile(url, filename) {
        const a = document.createElement('a');
        a.href = url;
        a.download = filename || 'generated_file';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // Afficher une notification de succès
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4ecdc4;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 10001;
                font-size: 14px;
            `;
            notification.textContent = 'Code copié dans le presse-papiers !';
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }).catch(err => {
            console.error('Erreur copie:', err);
            alert('Impossible de copier le code');
        });
    }

    runCode() {
        const code = document.getElementById('code-editor').value;
        const language = document.getElementById('code-language').value;
        const console = document.getElementById('code-console');

        console.innerHTML += `<span style="color: #4ecdc4;">> Exécution du code ${language}...</span><br>`;

        if (language === 'javascript') {
            try {
                const result = eval(code);
                console.innerHTML += `<span style="color: #96ceb4;">Résultat: ${result}</span><br>`;
            } catch (error) {
                console.innerHTML += `<span style="color: #ff6b6b;">Erreur: ${error.message}</span><br>`;
            }
        } else {
            console.innerHTML += `<span style="color: #888;">Exécution ${language} non supportée dans le navigateur</span><br>`;
        }

        console.scrollTop = console.scrollHeight;
    }

    // Nouvelle méthode pour envoyer des messages de chat
    async sendChatMessage(message) {
        try {
            const response = await fetch('/api/chat/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    timestamp: new Date().toISOString()
                })
            });

            const result = await response.json();

            // Cacher l'indicateur de frappe
            hideTypingIndicator();

            if (result.success) {
                // Ajouter la réponse de l'agent
                addMessageToChat(result.response, 'agent');
            } else {
                addMessageToChat('Désolé, je n\'ai pas pu traiter votre message. Veuillez réessayer.', 'agent');
            }
        } catch (error) {
            console.error('Erreur envoi message:', error);
            hideTypingIndicator();
            addMessageToChat('Erreur de connexion. Veuillez vérifier votre connexion et réessayer.', 'agent');
        }
    }
}

// Actions rapides
function quickAction(action) {
    switch(action) {
        case 'generate-image':
            hub.switchSection('generation');
            document.getElementById('content-type').value = 'image';
            break;
        case 'generate-code':
            hub.switchSection('code');
            break;
        case 'analyze-data':
            hub.switchSection('analysis');
            break;
        case 'optimize-system':
            fetch('/api/system/optimize', { method: 'POST' })
                .then(() => alert('Optimisation lancée !'))
                .catch(() => alert('Erreur d\'optimisation'));
            break;
    }
}

// Initialiser le hub
const hub = new UnifiedHub();
window.hub = hub;

// ===== FONCTIONS POUR LE NOUVEAU CHAT =====

// Gestion du menu déroulant
function toggleChatMenu() {
    const dropdown = document.getElementById('chat-dropdown');
    dropdown.classList.toggle('show');
}

// Fermer le menu si on clique ailleurs
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('chat-dropdown');
    const button = event.target.closest('.dropdown-btn');

    if (!button && dropdown) {
        dropdown.classList.remove('show');
    }
});

// Fonctions du menu déroulant
function clearChat() {
    const chatMessages = document.getElementById('chat-messages');
    chatMessages.innerHTML = `
        <div class="welcome-message">
            <div class="message agent">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <p>Chat effacé ! Comment puis-je vous aider ?</p>
                    <span class="message-time">Maintenant</span>
                </div>
            </div>
        </div>
    `;
    showNotification('Chat effacé avec succès', 'success');
}

function exportChat() {
    const messages = document.querySelectorAll('.message');
    let chatContent = 'Conversation avec Louna\n';
    chatContent += '========================\n\n';

    messages.forEach(message => {
        const isUser = message.classList.contains('user');
        const content = message.querySelector('.message-content p').textContent;
        const time = message.querySelector('.message-time').textContent;

        chatContent += `${isUser ? 'Vous' : 'Louna'} (${time}):\n${content}\n\n`;
    });

    const blob = new Blob([chatContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-louna-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('Chat exporté avec succès', 'success');
}

function importChat() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // Ici on pourrait parser le fichier et restaurer la conversation
                    showNotification('Fonctionnalité d\'import en développement', 'info');
                } catch (error) {
                    showNotification('Erreur lors de l\'import du chat', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function searchInChat() {
    const searchTerm = prompt('Rechercher dans le chat:');
    if (searchTerm) {
        const messages = document.querySelectorAll('.message-content p');
        let found = false;

        messages.forEach(message => {
            const text = message.textContent.toLowerCase();
            if (text.includes(searchTerm.toLowerCase())) {
                message.style.background = 'rgba(255, 255, 0, 0.3)';
                message.scrollIntoView({ behavior: 'smooth', block: 'center' });
                found = true;

                // Retirer le surlignage après 3 secondes
                setTimeout(() => {
                    message.style.background = '';
                }, 3000);
            }
        });

        if (!found) {
            showNotification('Aucun résultat trouvé', 'info');
        }
    }
}

function toggleChatTheme() {
    const chatContainer = document.querySelector('.chat-container');
    const currentTheme = chatContainer.dataset.theme || 'default';

    if (currentTheme === 'default') {
        chatContainer.dataset.theme = 'dark';
        chatContainer.style.background = 'rgba(0, 0, 0, 0.8)';
        showNotification('Thème sombre activé', 'success');
    } else {
        chatContainer.dataset.theme = 'default';
        chatContainer.style.background = 'rgba(255, 255, 255, 0.05)';
        showNotification('Thème par défaut activé', 'success');
    }
}

function chatSettings() {
    showNotification('Paramètres du chat en développement', 'info');
}

// Fonctions des boutons d'action
function attachFile() {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            showNotification(`${files.length} fichier(s) sélectionné(s)`, 'success');
            // Ici on pourrait traiter les fichiers
        }
    };
    input.click();
}

function startVoiceInput() {
    if (window.voiceSystem && window.voiceSystem.isListening) {
        window.voiceSystem.stopListening();
        showNotification('Écoute vocale arrêtée', 'info');
    } else if (window.voiceSystem) {
        window.voiceSystem.startListening();
        showNotification('Écoute vocale démarrée', 'success');
    } else {
        showNotification('Système vocal non disponible', 'error');
    }
}

function insertEmoji() {
    const emojis = ['😊', '😂', '🤔', '👍', '❤️', '🎉', '🚀', '💡', '🔥', '✨'];
    const emoji = emojis[Math.floor(0.5 * emojis.length)];
    const input = document.getElementById('chat-input');
    input.value += emoji;
    input.focus();
}

function takeScreenshot() {
    if (window.visionSystem) {
        window.visionSystem.captureScreen();
        showNotification('Capture d\'écran prise', 'success');
    } else {
        showNotification('Système de vision non disponible', 'error');
    }
}

// Gestion du textarea auto-redimensionnable
function adjustTextareaHeight(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';

    // Mettre à jour le compteur de caractères s'il existe
    const charCounter = document.getElementById('char-counter');
    if (charCounter) {
        const length = textarea.value.length;
        charCounter.textContent = `${length}/2000`;

        if (length > 1800) {
            charCounter.classList.add('warning');
        } else {
            charCounter.classList.remove('warning');
        }
    }
}

// Gestion de l'envoi de messages
function handleChatKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

async function sendMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();

    if (!message) {
        console.log('❌ Message vide, envoi annulé');
        return;
    }

    console.log('📤 Envoi du message à l\'agent Agent Local LOUNA:', message);

    // Ajouter le message utilisateur
    addMessageToChat(message, 'user');

    // Vider l'input
    input.value = '';
    adjustTextareaHeight(input);

    // Afficher l'indicateur de frappe
    showTypingIndicator();

    // Ajouter des réflexions automatiques
    if (typeof addReflection === 'function') {
        addReflection('processing', 'Traitement du message utilisateur...');
    }

    try {
        // Envoyer le message à l'agent Agent Local LOUNA principal
        const response = await fetch('/api/chat/message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                conversationId: 'unified-hub-chat'
            })
        });

        const data = await response.json();

        if (data.success) {
            hideTypingIndicator();

            // Afficher la réponse de l'agent Agent Local LOUNA
            addMessageToChat(data.response, 'agent');

            // Ajouter des réflexions de succès
            if (typeof addReflection === 'function') {
                setTimeout(() => addReflection('thinking', 'Réponse générée par l\'agent Agent Local LOUNA'), 500);
                setTimeout(() => addReflection('learning', 'Interaction sauvegardée en mémoire thermique'), 1500);
            }

            console.log('✅ Réponse reçue de l\'agent Agent Local LOUNA:', data.agent.name);

        } else {
            throw new Error(data.error || 'Erreur de communication avec l\'agent');
        }

    } catch (error) {
        console.error('❌ Erreur communication avec l\'agent Agent Local LOUNA:', error);
        hideTypingIndicator();

        // Fallback avec réponse d'erreur
        addMessageToChat(
            'Désolé, je rencontre des difficultés techniques. L\'agent Agent Local LOUNA principal n\'est pas disponible pour le moment. Veuillez réessayer dans quelques instants.',
            'agent'
        );

        // Ajouter une réflexion d'erreur
        if (typeof addReflection === 'function') {
            addReflection('error', 'Erreur de communication avec l\'agent principal');
        }

        showNotification('Erreur de communication avec l\'agent', 'error');
    }
}

function addMessageToChat(content, type) {
    const chatMessages = document.getElementById('chat-messages');
    const time = new Date().toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.innerHTML = `
        <div class="message-avatar">${type === 'user' ? '👤' : '🤖'}</div>
        <div class="message-content">
            <p>${content}</p>
            <span class="message-time">${time}</span>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    if (indicator) {
        indicator.style.display = 'flex';
    }
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typing-indicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-size: 14px;
        z-index: 10001;
        animation: slideInRight 0.3s ease;
        max-width: 300px;
    `;

    switch (type) {
        case 'success':
            notification.style.background = '#4ecdc4';
            break;
        case 'error':
            notification.style.background = '#ff6b6b';
            break;
        case 'warning':
            notification.style.background = '#ffa726';
            break;
        default:
            notification.style.background = '#45b7d1';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Ajouter les animations CSS pour les notifications
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(notificationStyles);

// ===== FONCTIONS DE NAVIGATION =====

// Gestion du menu déroulant des applications
function toggleAppsMenu() {
    const dropdown = document.getElementById('apps-dropdown');
    dropdown.classList.toggle('show');
}

// Fermer le menu des applications si on clique ailleurs
document.addEventListener('click', function(event) {
    const appsDropdown = document.getElementById('apps-dropdown');
    const appsButton = event.target.closest('.nav-btn');

    if (!appsButton && appsDropdown) {
        appsDropdown.classList.remove('show');
    }
});

// Navigation vers les différentes applications
function goToApp(appName) {
    showNotification('Navigation vers ' + getAppDisplayName(appName), 'info');

    // Fermer le menu déroulant
    const dropdown = document.getElementById('apps-dropdown');
    if (dropdown) dropdown.classList.remove('show');

    // Définir les URLs des applications
    const appUrls = {
        'home': '/',
        'chat': '/chat',
        'memory': '/futuristic-interface.html',
        'monitor': '/brain-monitor',
        'brain3d': '/brain-visualization.html',
        'generation': '/generation-studio.html',
        'kyber': '/kyber-dashboard.html',
        'performance': '/performance.html',
        'settings': '/settings'
    };

    const url = appUrls[appName];
    if (url) {
        console.log(`🚀 Navigation vers: ${url}`);
        // Navigation immédiate
        window.location.href = url;
    } else {
        showNotification('Application non trouvée: ' + appName, 'error');
    }
}

// Retour à l'accueil
function goToHome() {
    showNotification('Retour à l\'accueil', 'success');
    console.log('🏠 Retour à l\'accueil');
    window.location.href = '/';
}

// Obtenir le nom d'affichage de l'application
function getAppDisplayName(appName) {
    const displayNames = {
        'home': 'Page d\'accueil',
        'chat': 'Chat Principal',
        'memory': 'Mémoire Thermique',
        'monitor': 'Monitoring Cerveau',
        'brain3d': 'Cerveau 3D',
        'generation': 'Studio Génération',
        'kyber': 'Accélérateurs Kyber',
        'performance': 'Performances',
        'settings': 'Paramètres'
    };

    return displayNames[appName] || appName;
}

// Fonction pour ouvrir une application dans un nouvel onglet
function openAppInNewTab(appName) {
    const appUrls = {
        'home': '/',
        'chat': '/chat',
        'memory': '/futuristic-interface.html',
        'monitor': '/brain-monitor',
        'brain3d': '/brain-visualization.html',
        'generation': '/generation-studio.html',
        'kyber': '/kyber-dashboard.html',
        'performance': '/performance.html',
        'settings': '/settings'
    };

    const url = appUrls[appName];
    if (url) {
        window.open(url, '_blank');
        showNotification(getAppDisplayName(appName) + ' ouvert dans un nouvel onglet', 'success');
    }
}

// Fonction pour ajouter des raccourcis clavier
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + H = Accueil
    if ((event.ctrlKey || event.metaKey) && event.key === 'h') {
        event.preventDefault();
        goToHome();
    }

    // Ctrl/Cmd + Shift + A = Menu Applications
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        toggleAppsMenu();
    }

    // Échap = Fermer les menus
    if (event.key === 'Escape') {
        const appsDropdown = document.getElementById('apps-dropdown');
        const chatDropdown = document.getElementById('chat-dropdown');

        if (appsDropdown) appsDropdown.classList.remove('show');
        if (chatDropdown) chatDropdown.classList.remove('show');
    }
});

// Fonction pour mettre à jour le breadcrumb
function updateBreadcrumb(section) {
    const breadcrumb = document.getElementById('content-breadcrumb');
    const sectionNames = {
        'chat': 'Chat Intelligent',
        'generation': 'Studio de Génération',
        'code': 'Environnement de Développement',
        'analysis': 'Analyse et Insights',
        'memory': 'Mémoire Thermique',
        'monitoring': 'Monitoring Système',
        'agents': 'Gestion des Agents',
        'settings': 'Paramètres'
    };

    const sectionName = sectionNames[section] || section;
    breadcrumb.textContent = `Accueil > ${sectionName}`;
}

// Améliorer la fonction de changement de section existante
const originalSwitchSection = hub.switchSection;
hub.switchSection = function(section) {
    originalSwitchSection.call(this, section);
    updateBreadcrumb(section);
};

// Fonction pour afficher les raccourcis clavier
function showKeyboardShortcuts() {
    const shortcuts = `
        <div style="background: rgba(30, 30, 46, 0.95); padding: 20px; border-radius: 10px; color: white; max-width: 400px;">
            <h3 style="margin-top: 0; color: #4ecdc4;">Raccourcis Clavier</h3>
            <div style="display: grid; gap: 10px; font-size: 14px;">
                <div><kbd>Ctrl/Cmd + H</kbd> - Retour à l'accueil</div>
                <div><kbd>Ctrl/Cmd + Shift + A</kbd> - Menu Applications</div>
                <div><kbd>Échap</kbd> - Fermer les menus</div>
                <div><kbd>Entrée</kbd> - Envoyer message</div>
                <div><kbd>Shift + Entrée</kbd> - Nouvelle ligne</div>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="margin-top: 15px; padding: 8px 16px; background: #4ecdc4; border: none; border-radius: 5px; color: white; cursor: pointer;">Fermer</button>
        </div>
    `;

    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;
    overlay.innerHTML = shortcuts;
    document.body.appendChild(overlay);

    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            overlay.remove();
        }
    });
}

// Ajouter un bouton d'aide pour les raccourcis (optionnel)
function addHelpButton() {
    const helpBtn = document.createElement('button');
    helpBtn.innerHTML = '<i class="fas fa-question-circle"></i>';
    helpBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(78, 205, 196, 0.9);
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    helpBtn.title = 'Raccourcis clavier (?)';
    helpBtn.onclick = showKeyboardShortcuts;

    helpBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
        this.style.background = 'rgba(78, 205, 196, 1)';
    });

    helpBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.background = 'rgba(78, 205, 196, 0.9)';
    });

    document.body.appendChild(helpBtn);
}

// Initialiser le bouton d'aide
setTimeout(addHelpButton, 1000);

// ===== SYSTÈME DE RÉFLEXIONS DE L'AGENT =====

let reflectionPanel = null;
let reflectionCount = 0;
let reflectionsPaused = false;
let reflectionUpdateInterval = null;

// Initialiser le système de réflexions
function initReflectionSystem() {
    reflectionPanel = document.getElementById('reflection-panel');

    // Initialiser les compteurs
    reflectionCount = 1; // Compter le message d'initialisation
    updateReflectionStats();
    updateReflectionStatus('En temps réel');

    // Ajouter une réflexion de démarrage
    setTimeout(() => {
        addReflection('thinking', 'Système de réflexions initialisé avec succès !');
        addReflection('learning', 'Prêt à analyser vos interactions...');
    }, 2000);

    // Démarrer la données réelles des réflexions
    startReflectionUpdates();

    // Connecter au serveur pour les vraies réflexions
    connectToReflectionStream();

    console.log('🧠 Système de réflexions de l\'agent initialisé');
}

// Basculer l'affichage du panneau de réflexions
function toggleReflectionPanel() {
    const panel = document.getElementById('reflection-panel');
    const mainButton = document.getElementById('main-reflection-btn');
    const actionButton = document.querySelector('[onclick="toggleReflectionPanel()"]');

    if (panel.classList.contains('open')) {
        panel.classList.remove('open');
        if (mainButton) {
            mainButton.classList.remove('active');
            mainButton.querySelector('span').textContent = 'Voir Réflexions';
        }
        if (actionButton) actionButton.classList.remove('reflection-active');
        showNotification('Panneau de réflexions fermé', 'info');
    } else {
        panel.classList.add('open');
        if (mainButton) {
            mainButton.classList.add('active');
            mainButton.querySelector('span').textContent = 'Fermer Réflexions';
        }
        if (actionButton) actionButton.classList.add('reflection-active');

        // Marquer les nouvelles réflexions comme lues
        clearReflectionNotification();
        showNotification('Panneau de réflexions ouvert', 'success');
    }
}

// Ajouter une nouvelle réflexion
function addReflection(type, text, timestamp = null) {
    if (reflectionsPaused) return;

    const content = document.getElementById('reflection-content');
    const time = timestamp || new Date().toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const reflectionItem = document.createElement('div');
    reflectionItem.className = 'reflection-item';
    reflectionItem.setAttribute('data-type', type);
    reflectionItem.innerHTML = `
        <div class="reflection-type">${getReflectionTypeLabel(type)}</div>
        <div class="reflection-text">${text}</div>
        <div class="reflection-time">${time}</div>
    `;

    // Ajouter au début pour avoir les plus récentes en haut
    content.insertBefore(reflectionItem, content.firstChild);

    // Limiter le nombre de réflexions affichées
    const items = content.querySelectorAll('.reflection-item');
    if (items.length > 50) {
        content.removeChild(items[items.length - 1]);
    }

    // Mettre à jour le compteur
    reflectionCount++;
    updateReflectionStats();

    // Afficher une notification si le panneau est fermé
    if (!document.getElementById('reflection-panel').classList.contains('open')) {
        showReflectionNotification();
    }
}

// Obtenir le label du type de réflexion
function getReflectionTypeLabel(type) {
    const labels = {
        'thinking': 'Réflexion',
        'analyzing': 'Analyse',
        'learning': 'Apprentissage',
        'processing': 'Traitement',
        'error': 'Erreur',
        'decision': 'Décision',
        'memory': 'Mémoire',
        'optimization': 'Optimisation'
    };
    return labels[type] || 'Réflexion';
}

// Effacer toutes les réflexions
function clearReflections() {
    const content = document.getElementById('reflection-content');
    content.innerHTML = `
        <div class="reflection-item">
            <div class="reflection-type">Initialisation</div>
            <div class="reflection-text">Réflexions effacées. En attente de nouvelles réflexions...</div>
            <div class="reflection-time">Maintenant</div>
        </div>
    `;
    reflectionCount = 1;
    updateReflectionStats();
    showNotification('Réflexions effacées', 'success');
}

// Pause/Reprendre les réflexions
function pauseReflections() {
    const button = document.getElementById('pause-reflections');
    const icon = button.querySelector('i');

    reflectionsPaused = !reflectionsPaused;

    if (reflectionsPaused) {
        icon.className = 'fas fa-play';
        button.title = 'Reprendre';
        updateReflectionStatus('En pause');
        showNotification('Réflexions mises en pause', 'info');
    } else {
        icon.className = 'fas fa-pause';
        button.title = 'Pause';
        updateReflectionStatus('En temps réel');
        showNotification('Réflexions reprises', 'success');
    }
}

// Mettre à jour les statistiques
function updateReflectionStats() {
    const countElement = document.getElementById('reflection-count');
    const miniCountElement = document.getElementById('mini-reflection-count');

    const countText = `${reflectionCount} réflexion${reflectionCount > 1 ? 's' : ''}`;

    if (countElement) countElement.textContent = countText;
    if (miniCountElement) miniCountElement.textContent = countText;
}

// Mettre à jour le statut
function updateReflectionStatus(status) {
    const statusElement = document.getElementById('reflection-status');
    const miniStatusElement = document.getElementById('mini-reflection-status');

    if (statusElement) statusElement.textContent = status;
    if (miniStatusElement) {
        miniStatusElement.textContent = status;
        miniStatusElement.className = 'mini-status';

        if (status === 'En temps réel') {
            miniStatusElement.classList.add('active');
        } else if (status === 'En pause') {
            miniStatusElement.classList.add('paused');
        }
    }
}

// Afficher la notification de nouvelles réflexions
function showReflectionNotification() {
    const button = document.querySelector('[onclick="toggleReflectionPanel()"]');
    const indicator = document.getElementById('reflection-indicator');

    // Ajouter notification sur le bouton d'action
    if (button && !button.querySelector('.reflection-notification')) {
        const notification = document.createElement('div');
        notification.className = 'reflection-notification';
        button.style.position = 'relative';
        button.appendChild(notification);
    }

    // Activer l'indicateur principal
    if (indicator) {
        indicator.classList.add('active');
    }
}

// Effacer la notification
function clearReflectionNotification() {
    const button = document.querySelector('[onclick="toggleReflectionPanel()"]');
    const indicator = document.getElementById('reflection-indicator');

    // Supprimer notification du bouton d'action
    if (button) {
        const notification = button.querySelector('.reflection-notification');
        if (notification) {
            notification.remove();
        }
    }

    // Désactiver l'indicateur principal
    if (indicator) {
        indicator.classList.remove('active');
    }
}

// Démarrer les mises à jour de réflexions données réelleses
function startReflectionUpdates() {
    const reflectionTypes = ['thinking', 'analyzing', 'learning', 'processing', 'decision', 'memory'];
    const reflectionTexts = {
        'thinking': [
            'Analyse de la demande utilisateur en cours...',
            'Réflexion sur la meilleure approche à adopter...',
            'Évaluation des différentes options disponibles...',
            'Considération des implications de cette action...'
        ],
        'analyzing': [
            'Analyse des données contextuelles...',
            'Examen des patterns dans les interactions...',
            'Évaluation de la performance du système...',
            'Analyse des préférences utilisateur...'
        ],
        'learning': [
            'Intégration de nouvelles informations...',
            'Mise à jour des modèles de comportement...',
            'Apprentissage à partir de cette interaction...',
            'Amélioration des réponses futures...'
        ],
        'processing': [
            'Traitement de la requête en cours...',
            'Optimisation des ressources système...',
            'Coordination des différents modules...',
            'Synchronisation des données...'
        ],
        'decision': [
            'Prise de décision basée sur l\'analyse...',
            'Sélection de la stratégie optimale...',
            'Validation de l\'approche choisie...',
            'Confirmation des paramètres...'
        ],
        'memory': [
            'Sauvegarde en mémoire thermique...',
            'Récupération d\'informations pertinentes...',
            'Mise à jour des connexions neuronales...',
            'Optimisation de la structure mémoire...'
        ]
    };

    reflectionUpdateInterval = setInterval(() => {
        if (!reflectionsPaused) {
            const type = reflectionTypes[Math.floor(0.5 * reflectionTypes.length)];
            const texts = reflectionTexts[type];
            const text = texts[Math.floor(0.5 * texts.length)];

            addReflection(type, text);
        }
    }, 3000 + 0.5 * 7000); // Entre 3 et 10 secondes
}

// Connecter au flux de réflexions du serveur
async function connectToReflectionStream() {
    try {
        // Essayer de se connecter au serveur pour les vraies réflexions
        const response = await fetch('/api/agent/reflections/stream');
        if (response.ok) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            addReflection(data.type, data.text, data.timestamp);
                        } catch (e) {
                            console.log('Erreur parsing réflexion:', e);
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.log('Connexion au flux de réflexions non disponible, utilisation du mode données réelles');
    }
}

// Exporter les réflexions
function exportReflections() {
    const items = document.querySelectorAll('.reflection-item');
    let content = 'Réflexions de l\'Agent Louna\n';
    content += '================================\n\n';

    items.forEach(item => {
        const type = item.querySelector('.reflection-type').textContent;
        const text = item.querySelector('.reflection-text').textContent;
        const time = item.querySelector('.reflection-time').textContent;

        content += `[${time}] ${type}:\n${text}\n\n`;
    });

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `reflexions-louna-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('Réflexions exportées avec succès', 'success');
}

// Initialiser le système de réflexions au chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initReflectionSystem, 1000);
});

// Ajouter des réflexions lors des interactions avec le chat
const originalSendMessage = sendMessage;
sendMessage = function() {
    addReflection('processing', 'Traitement du message utilisateur...');

    setTimeout(() => {
        addReflection('thinking', 'Formulation de la réponse appropriée...');
    }, 1000);

    setTimeout(() => {
        addReflection('learning', 'Intégration de cette interaction dans la mémoire...');
    }, 3000);

    return originalSendMessage.call(this);
};

// Ajouter un raccourci clavier pour les réflexions
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + R = Toggle réflexions
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        toggleReflectionPanel();
    }
});

// ===== FONCTION DE TEST DU SYSTÈME DE RÉFLEXIONS =====
function testReflectionSystem() {
    console.log('🧪 Test du système de réflexions...');

    // Test 1: Ajouter différents types de réflexions
    setTimeout(() => {
        addReflection('thinking', 'Test de réflexion - Analyse en cours...');
    }, 1000);

    setTimeout(() => {
        addReflection('analyzing', 'Test d\'analyse - Examen des données...');
    }, 2000);

    setTimeout(() => {
        addReflection('learning', 'Test d\'apprentissage - Intégration des informations...');
    }, 3000);

    setTimeout(() => {
        addReflection('decision', 'Test de décision - Sélection de la meilleure option...');
    }, 4000);

    setTimeout(() => {
        addReflection('memory', 'Test de mémoire - Sauvegarde en cours...');
    }, 5000);

    setTimeout(() => {
        console.log('✅ Test du système de réflexions terminé');
        showNotification('Test des réflexions terminé avec succès !', 'success');
    }, 6000);
}

// Fonction pour démarrer un test manuel
window.testReflections = testReflectionSystem;

// ===== DIAGNOSTIC COMPLET DU SYSTÈME =====
function runSystemDiagnostic() {
    console.log('🔍 === DIAGNOSTIC COMPLET DU SYSTÈME LOUNA ===');

    // Test 1: Éléments HTML
    const elements = {
        'chat-input': document.getElementById('chat-input'),
        'chat-messages': document.getElementById('chat-messages'),
        'send-btn': document.getElementById('send-btn'),
        'reflection-panel': document.getElementById('reflection-panel'),
        'main-reflection-btn': document.getElementById('main-reflection-btn'),
        'apps-dropdown': document.getElementById('apps-dropdown')
    };

    console.log('📋 Test des éléments HTML:');
    Object.entries(elements).forEach(([name, element]) => {
        console.log(`  ${element ? '✅' : '❌'} ${name}: ${element ? 'Trouvé' : 'MANQUANT'}`);
    });

    // Test 2: Fonctions JavaScript
    const functions = {
        'sendMessage': typeof sendMessage,
        'toggleReflectionPanel': typeof toggleReflectionPanel,
        'addReflection': typeof addReflection,
        'goToApp': typeof goToApp,
        'showNotification': typeof showNotification
    };

    console.log('⚙️ Test des fonctions JavaScript:');
    Object.entries(functions).forEach(([name, type]) => {
        console.log(`  ${type === 'function' ? '✅' : '❌'} ${name}: ${type}`);
    });

    // Test 3: Test du chat
    if (elements['chat-input'] && elements['chat-messages']) {
        console.log('💬 Test du système de chat...');
        elements['chat-input'].value = 'Test automatique du système';
        sendMessage();
        console.log('✅ Message de test envoyé');
    }

    // Test 4: Test des réflexions
    if (typeof addReflection === 'function') {
        console.log('🧠 Test du système de réflexions...');
        addReflection('testing', 'Test automatique du système de réflexions');
        console.log('✅ Réflexion de test ajoutée');
    }

    // Test 5: Test de navigation
    console.log('🧭 Test du système de navigation...');
    if (typeof toggleAppsMenu === 'function') {
        console.log('✅ Menu applications disponible');
    } else {
        console.log('❌ Menu applications non disponible');
    }

    console.log('🎉 === DIAGNOSTIC TERMINÉ ===');
    showNotification('Diagnostic système terminé - Voir la console', 'success');
}

// Ajouter la fonction au scope global
window.runDiagnostic = runSystemDiagnostic;

// Vérifier le statut de l'agent Agent Local LOUNA au chargement
async function checkAgentStatus() {
    try {
        console.log('🔍 Vérification du statut de l\'agent Agent Local LOUNA...');

        const response = await fetch('/api/agent/status');
        const data = await response.json();

        if (data.success) {
            const agent localAgent = data.agent.agent localAgent;

            if (agent localAgent.available) {
                console.log('✅ Agent Agent Local LOUNA disponible:', agent localAgent.name, '-', agent localAgent.model);
                showNotification(`Agent ${agent localAgent.name} connecté`, 'success');

                // Ajouter une réflexion de connexion
                if (typeof addReflection === 'function') {
                    addReflection('thinking', `Agent ${agent localAgent.name} (${agent localAgent.model}) connecté et prêt`);
                }
            } else {
                console.log('⚠️ Agent Agent Local LOUNA non disponible');
                showNotification('Agent Agent Local LOUNA non disponible', 'warning');

                // Ajouter une réflexion d'erreur
                if (typeof addReflection === 'function') {
                    addReflection('error', 'Agent Agent Local LOUNA principal non disponible');
                }
            }
        } else {
            throw new Error(data.error || 'Erreur de vérification du statut');
        }

    } catch (error) {
        console.error('❌ Erreur vérification statut agent:', error);
        showNotification('Erreur de connexion à l\'agent', 'error');

        // Ajouter une réflexion d'erreur
        if (typeof addReflection === 'function') {
            addReflection('error', 'Impossible de vérifier le statut de l\'agent');
        }
    }
}

// Diagnostic automatique au chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🚀 Louna Hub Central - Système initialisé');

        // Vérifier le statut de l'agent
        checkAgentStatus();

        // runSystemDiagnostic(); // Décommenter pour diagnostic automatique
    }, 2000);
});
