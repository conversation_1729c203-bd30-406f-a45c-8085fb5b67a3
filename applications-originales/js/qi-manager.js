/**
 * GESTIONNAIRE CENTRALISÉ DU QI - LOUNA
 * =====================================
 * Créé par <PERSON>, Guadeloupe
 *
 * Ce fichier centralise TOUTE la gestion du QI dans l'application.
 * UNE SEULE SOURCE DE VÉRITÉ pour le QI affiché partout.
 */

class QIManager {
    constructor() {
        // QI DE BASE JEAN-LUC : 225 (peut évoluer vers le haut)
        this.baseQI = 225; // QI de base de Jean-Luc Passave - MINIMUM
        this.currentQI = this.loadSavedQI() || 225; // QI actuel (peut évoluer)
        this.maxQI = 250; // Limite AGI
        this.isInitialized = false;
        this.updateCallbacks = [];
        this.updateInterval = null;
        this.evolutionEnabled = true;

        console.log('🧠 QIManager initialisé - QI évolutif <PERSON>:', this.currentQI);
        this.init();
    }

    /**
     * Initialisation du gestionnaire QI
     */
    async init() {
        try {
            // Récupérer le QI RÉEL depuis l'API
            await this.fetchRealQI();

            // Démarrer les mises à jour automatiques
            this.startAutoUpdate();

            // Mettre à jour tous les éléments QI de la page
            this.updateAllQIElements();

            this.isInitialized = true;
            console.log('✅ QIManager initialisé avec succès - QI actuel:', this.currentQI);

        } catch (error) {
            console.warn('⚠️ Erreur initialisation QIManager:', error);
            // Utiliser la valeur par défaut
            this.updateAllQIElements();
            this.isInitialized = true;
        }
    }

    /**
     * Récupérer le QI RÉEL depuis l'API avec évolution possible
     */
    async fetchRealQI() {
        try {
            // Essayer l'API QI
            const response = await fetch('/api/qi/current');
            if (response.ok) {
                const data = await response.json();
                let newQI = data.qi?.qi || data.qi || this.currentQI;

                // Appliquer les règles d'évolution de Jean-Luc
                newQI = this.applyEvolutionRules(newQI);

                if (newQI !== this.currentQI) {
                    const oldQI = this.currentQI;
                    this.currentQI = newQI;
                    this.saveQI();
                    console.log(`🧠 QI évolué: ${oldQI} → ${this.currentQI}`);
                    this.notifyCallbacks(oldQI, this.currentQI);
                }

                return this.currentQI;
            }
        } catch (error) {
            console.log('🧠 API QI non disponible - QI maintenu à:', this.currentQI);
        }

        try {
            // Essayer la mémoire thermique
            const response = await fetch('/api/thermal/memory/stats');
            if (response.ok) {
                const data = await response.json();
                if (data.qi) {
                    let newQI = Math.round(data.qi);
                    newQI = this.applyEvolutionRules(newQI);

                    if (newQI !== this.currentQI) {
                        const oldQI = this.currentQI;
                        this.currentQI = newQI;
                        this.saveQI();
                        console.log(`🧠 QI évolué depuis mémoire: ${oldQI} → ${this.currentQI}`);
                        this.notifyCallbacks(oldQI, this.currentQI);
                    }
                }
            }
        } catch (error) {
            console.log('🧠 Mémoire thermique non disponible - QI maintenu');
        }

        return this.currentQI;
    }

    /**
     * Appliquer les règles d'évolution du QI de Jean-Luc
     */
    applyEvolutionRules(proposedQI) {
        if (!this.evolutionEnabled) {
            return this.currentQI;
        }

        // Règles d'évolution pour Jean-Luc :
        // 1. Ne jamais descendre en dessous de 225 (QI de base)
        if (proposedQI < this.baseQI) {
            console.log(`🛡️ QI protégé: ${proposedQI} → ${this.baseQI} (minimum Jean-Luc)`);
            return this.baseQI;
        }

        // 2. Limiter à 250 (seuil AGI)
        if (proposedQI > this.maxQI) {
            console.log(`🚀 QI plafonné: ${proposedQI} → ${this.maxQI} (limite AGI)`);
            return this.maxQI;
        }

        // 3. Évolution graduelle (max +5 par mise à jour)
        const maxIncrease = 5;
        if (proposedQI > this.currentQI + maxIncrease) {
            const limitedQI = this.currentQI + maxIncrease;
            console.log(`📈 Évolution graduelle: ${proposedQI} → ${limitedQI} (+${maxIncrease})`);
            return limitedQI;
        }

        return Math.round(proposedQI);
    }

    /**
     * Sauvegarder le QI actuel
     */
    saveQI() {
        try {
            localStorage.setItem('lounaCurrentQI', this.currentQI.toString());
            localStorage.setItem('lounaQIHistory', JSON.stringify({
                current: this.currentQI,
                timestamp: Date.now(),
                base: this.baseQI,
                max: this.maxQI
            }));
        } catch (error) {
            console.warn('⚠️ Erreur sauvegarde QI:', error);
        }
    }

    /**
     * Charger le QI sauvegardé
     */
    loadSavedQI() {
        try {
            const saved = localStorage.getItem('lounaCurrentQI');
            if (saved) {
                const qi = parseInt(saved);
                // Vérifier que le QI sauvegardé respecte les règles
                return this.applyEvolutionRules(qi);
            }
        } catch (error) {
            console.warn('⚠️ Erreur chargement QI:', error);
        }
        return null;
    }

    /**
     * Démarrer les mises à jour automatiques
     */
    startAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // Mettre à jour toutes les 5 secondes
        this.updateInterval = setInterval(async () => {
            await this.fetchRealQI();
            this.updateAllQIElements();
        }, 5000);

        console.log('🔄 Mises à jour automatiques du QI démarrées (5s)');
    }

    /**
     * Arrêter les mises à jour automatiques
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('⏹️ Mises à jour automatiques du QI arrêtées');
        }
    }

    /**
     * Obtenir le QI actuel (évolutif)
     */
    getCurrentQI() {
        return this.currentQI;
    }

    /**
     * Forcer une évolution du QI (pour tests)
     */
    evolveQI(newQI, reason = 'Manuel') {
        const oldQI = this.currentQI;
        this.currentQI = this.applyEvolutionRules(newQI);
        this.saveQI();

        if (this.currentQI !== oldQI) {
            console.log(`🧠 Évolution QI ${reason}: ${oldQI} → ${this.currentQI}`);
            this.notifyCallbacks(oldQI, this.currentQI);
            this.updateAllQIElements();
        }

        return this.currentQI;
    }

    /**
     * Mettre à jour TOUS les éléments QI de la page avec la valeur évolutive
     */
    updateAllQIElements() {
        const qiElements = [
            // IDs courants
            'qi-value', 'current-qi', 'qi-display', 'qi-score', 'qi-level',
            'qi-coefficient', 'qi-current', 'qi-actuel', 'qi-system',

            // Classes courantes
            '.qi-value', '.current-qi', '.qi-display', '.qi-score',
            '.qi-coefficient', '.qi-current', '.qi-actuel'
        ];

        let elementsUpdated = 0;

        // Mettre à jour par ID avec QI évolutif
        qiElements.forEach(selector => {
            if (selector.startsWith('.')) {
                // Sélecteur de classe
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element) {
                        element.textContent = this.currentQI;
                        elementsUpdated++;
                    }
                });
            } else {
                // Sélecteur d'ID
                const element = document.getElementById(selector);
                if (element) {
                    element.textContent = this.currentQI;
                    elementsUpdated++;
                }
            }
        });

        // Mettre à jour les éléments avec "QI:" dans le texte
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.textContent && element.textContent.includes('QI:') &&
                element.children.length === 0) { // Seulement les éléments texte
                const newText = element.textContent.replace(/QI:\s*\d+/, `QI: ${this.currentQI}`);
                if (newText !== element.textContent) {
                    element.textContent = newText;
                    elementsUpdated++;
                }
            }
        });

        if (elementsUpdated > 0) {
            console.log(`🔄 ${elementsUpdated} éléments QI mis à jour avec la valeur évolutive: ${this.currentQI}`);
        }
    }

    /**
     * Ajouter un callback pour les mises à jour du QI
     */
    onQIUpdate(callback) {
        if (typeof callback === 'function') {
            this.updateCallbacks.push(callback);
        }
    }

    /**
     * Notifier tous les callbacks des changements de QI
     */
    notifyCallbacks(oldQI, newQI) {
        this.updateCallbacks.forEach(callback => {
            try {
                callback(newQI, oldQI);
            } catch (error) {
                console.warn('⚠️ Erreur dans callback QI:', error);
            }
        });
    }

    /**
     * Forcer une réévaluation du QI
     */
    async forceReevaluation() {
        try {
            const response = await fetch('/api/qi/reevaluate', { method: 'POST' });
            if (response.ok) {
                const data = await response.json();
                console.log('🔄 Réévaluation du QI forcée:', data);
                await this.fetchRealQI();
                this.updateAllQIElements();
                return data;
            }
        } catch (error) {
            console.warn('⚠️ Erreur réévaluation QI:', error);
        }
    }

    /**
     * Obtenir le niveau QI basé sur la valeur
     */
    getQILevel(qi = this.currentQI) {
        // Utiliser la configuration globale si disponible
        if (window.LounaUtils) {
            return window.LounaUtils.getQILevel(qi);
        }

        // Fallback si la configuration globale n'est pas chargée
        if (qi >= 200) return 'Quasi-AGI';
        if (qi >= 180) return 'Génie Exceptionnel';
        if (qi >= 160) return 'Très Supérieur';
        if (qi >= 140) return 'Supérieur';
        if (qi >= 120) return 'Intelligent';
        if (qi >= 100) return 'Moyen';
        if (qi >= 80) return 'Faible';
        return 'Très Faible';
    }

    /**
     * Détruire le gestionnaire
     */
    destroy() {
        this.stopAutoUpdate();
        this.updateCallbacks = [];
        console.log('🗑️ QIManager détruit');
    }
}

// Instance globale du gestionnaire QI
window.qiManager = new QIManager();

// Fonction globale pour obtenir le QI actuel (évolutif)
window.getCurrentQI = () => window.qiManager.getCurrentQI();

// Fonction globale pour faire évoluer le QI
window.evolveQI = (newQI, reason) => window.qiManager.evolveQI(newQI, reason);

// Fonction globale pour forcer la mise à jour
window.updateQI = () => window.qiManager.updateAllQIElements();

// Fonction globale pour forcer la réévaluation
window.reevaluateQI = () => window.qiManager.forceReevaluation();

console.log('🧠 QI Manager chargé - Gestionnaire centralisé du QI actif');
