/**
 * Gestionnaire de rêves pour l'application Louna
 * Ce module gère l'affichage et l'interaction avec les rêves générés par l'agent
 */

// Référence aux éléments du DOM
let dreamsGrid;
let emptyState;
let dreamModal;
let modalTitle;
let modalBody;
let modalInfo;
let modalDelete;
let generateDreamBtn;
let refreshDreamsBtn;
let emptyGenerateBtn;
let modalClose;

// Rêves actuels
let currentDreams = [];
let selectedDreamId = null;

/**
 * Initialise la page des rêves
 */
async function initializeDreams() {
    // Récupérer les références aux éléments du DOM
    dreamsGrid = document.getElementById('dreams-grid');
    emptyState = document.getElementById('empty-state');
    dreamModal = document.getElementById('dream-modal');
    modalTitle = document.getElementById('modal-title');
    modalBody = document.getElementById('modal-body');
    modalInfo = document.getElementById('modal-info');
    modalDelete = document.getElementById('modal-delete');
    generateDreamBtn = document.getElementById('generate-dream-btn');
    refreshDreamsBtn = document.getElementById('refresh-dreams-btn');
    emptyGenerateBtn = document.getElementById('empty-generate-btn');
    modalClose = document.getElementById('modal-close');
    
    // Configurer les écouteurs d'événements
    setupEventListeners();
    
    // Initialiser la mémoire thermique
    await window.thermalMemory.initialize();
    
    // Charger les rêves
    await loadDreams();
}

/**
 * Configure les écouteurs d'événements
 */
function setupEventListeners() {
    // Bouton de génération de rêve
    generateDreamBtn.addEventListener('click', generateDream);
    emptyGenerateBtn.addEventListener('click', generateDream);
    
    // Bouton d'actualisation
    refreshDreamsBtn.addEventListener('click', loadDreams);
    
    // Bouton de fermeture du modal
    modalClose.addEventListener('click', closeModal);
    
    // Bouton de suppression du rêve
    modalDelete.addEventListener('click', deleteDream);
    
    // Fermer le modal en cliquant en dehors
    dreamModal.addEventListener('click', (e) => {
        if (e.target === dreamModal) {
            closeModal();
        }
    });
    
    // Fermer le modal avec la touche Echap
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && dreamModal.classList.contains('visible')) {
            closeModal();
        }
    });
}

/**
 * Charge les rêves depuis la mémoire thermique
 */
async function loadDreams() {
    try {
        // Afficher un indicateur de chargement
        dreamsGrid.innerHTML = `
            <div class="empty-state">
                <div class="loading-spinner"></div>
                <p>Chargement des rêves...</p>
            </div>
        `;
        
        // Récupérer les rêves depuis la mémoire thermique
        const response = await fetch('/api/thermal/memory/dreams');
        const data = await response.json();
        
        if (data.success) {
            currentDreams = data.dreams || [];
            
            // Afficher les rêves ou l'état vide
            if (currentDreams.length > 0) {
                renderDreams(currentDreams);
                emptyState.style.display = 'none';
            } else {
                dreamsGrid.innerHTML = '';
                emptyState.style.display = 'flex';
            }
        } else {
            showNotification('Erreur lors du chargement des rêves: ' + data.error, 'error');
            dreamsGrid.innerHTML = '';
            emptyState.style.display = 'flex';
        }
    } catch (error) {
        console.error('Erreur lors du chargement des rêves:', error);
        showNotification('Erreur lors du chargement des rêves', 'error');
        dreamsGrid.innerHTML = '';
        emptyState.style.display = 'flex';
    }
}

/**
 * Affiche les rêves dans la grille
 * @param {Array} dreams - Liste des rêves à afficher
 */
function renderDreams(dreams) {
    // Vider la grille
    dreamsGrid.innerHTML = '';
    
    // Trier les rêves par date (du plus récent au plus ancien)
    dreams.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Ajouter chaque rêve à la grille
    dreams.forEach(dream => {
        const dreamCard = createDreamCard(dream);
        dreamsGrid.appendChild(dreamCard);
    });
}

/**
 * Crée une carte de rêve
 * @param {Object} dream - Données du rêve
 * @returns {HTMLElement} - Élément de carte de rêve
 */
function createDreamCard(dream) {
    // Créer l'élément de carte
    const card = document.createElement('div');
    card.className = 'dream-card';
    card.dataset.id = dream.id;
    
    // Créer le titre du rêve
    const title = dream.title || generateDreamTitle(dream);
    
    // Formater la date
    const date = new Date(dream.timestamp);
    const formattedDate = date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    // Extraire les tags
    const tags = dream.tags || [];
    
    // Créer le contenu HTML de la carte
    card.innerHTML = `
        <div class="dream-header">
            <h3 class="dream-title">${title}</h3>
            <span class="dream-date">${formattedDate}</span>
        </div>
        <div class="dream-content">
            ${dream.content}
        </div>
        <button class="dream-expand">
            <span>Voir plus</span>
            <i class="fas fa-chevron-down"></i>
        </button>
        <div class="dream-footer">
            <div class="dream-tags">
                ${tags.map(tag => `<span class="dream-tag">${tag}</span>`).join('')}
            </div>
            <div class="dream-actions">
                <button class="dream-action view-dream" title="Voir le rêve">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="dream-action delete-dream" title="Supprimer le rêve">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    // Ajouter les écouteurs d'événements
    const expandBtn = card.querySelector('.dream-expand');
    expandBtn.addEventListener('click', () => {
        const content = card.querySelector('.dream-content');
        content.classList.toggle('expanded');
        expandBtn.querySelector('span').textContent = content.classList.contains('expanded') ? 'Voir moins' : 'Voir plus';
        expandBtn.querySelector('i').className = content.classList.contains('expanded') ? 'fas fa-chevron-up' : 'fas fa-chevron-down';
    });
    
    const viewBtn = card.querySelector('.view-dream');
    viewBtn.addEventListener('click', () => {
        openDreamModal(dream);
    });
    
    const deleteBtn = card.querySelector('.delete-dream');
    deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        if (confirm(`Êtes-vous sûr de vouloir supprimer le rêve "${title}" ?`)) {
            deleteDreamById(dream.id);
        }
    });
    
    // Ajouter un écouteur pour ouvrir le modal en cliquant sur la carte
    card.addEventListener('click', (e) => {
        // Ne pas ouvrir le modal si on a cliqué sur un bouton
        if (!e.target.closest('.dream-action') && !e.target.closest('.dream-expand')) {
            openDreamModal(dream);
        }
    });
    
    return card;
}

/**
 * Génère un titre pour un rêve
 * @param {Object} dream - Données du rêve
 * @returns {string} - Titre généré
 */
function generateDreamTitle(dream) {
    // Extraire les premiers mots du contenu
    const words = dream.content.split(' ').slice(0, 5).join(' ');
    return words + '...';
}

/**
 * Ouvre le modal pour afficher un rêve
 * @param {Object} dream - Données du rêve
 */
function openDreamModal(dream) {
    // Stocker l'ID du rêve sélectionné
    selectedDreamId = dream.id;
    
    // Définir le titre
    modalTitle.textContent = dream.title || generateDreamTitle(dream);
    
    // Définir le contenu
    modalBody.innerHTML = dream.content;
    
    // Définir les informations
    const date = new Date(dream.timestamp);
    const formattedDate = date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    modalInfo.innerHTML = `
        <div>Généré le ${formattedDate}</div>
        <div>Température: ${dream.temperature ? (dream.temperature * 100).toFixed(0) + '%' : 'N/A'}</div>
    `;
    
    // Afficher le modal
    dreamModal.classList.add('visible');
}

/**
 * Ferme le modal
 */
function closeModal() {
    dreamModal.classList.remove('visible');
    selectedDreamId = null;
}

/**
 * Génère un nouveau rêve
 */
async function generateDream() {
    try {
        // Désactiver le bouton pendant la génération
        generateDreamBtn.disabled = true;
        emptyGenerateBtn.disabled = true;
        
        // Changer le texte du bouton
        generateDreamBtn.innerHTML = '<div class="loading-spinner"></div> Génération...';
        emptyGenerateBtn.innerHTML = '<div class="loading-spinner"></div> Génération...';
        
        // Générer un rêve
        const dream = await window.thermalMemory.generateDream();
        
        if (dream) {
            // Recharger les rêves
            await loadDreams();
            
            // Afficher une notification
            showNotification('Rêve généré avec succès', 'success');
            
            // Ouvrir le modal pour afficher le rêve
            const newDream = currentDreams.find(d => d.id === dream.id);
            if (newDream) {
                openDreamModal(newDream);
            }
        } else {
            showNotification('Erreur lors de la génération du rêve', 'error');
        }
    } catch (error) {
        console.error('Erreur lors de la génération du rêve:', error);
        showNotification('Erreur lors de la génération du rêve', 'error');
    } finally {
        // Réactiver le bouton
        generateDreamBtn.disabled = false;
        emptyGenerateBtn.disabled = false;
        
        // Restaurer le texte du bouton
        generateDreamBtn.innerHTML = '<i class="fas fa-magic"></i> Générer un rêve';
        emptyGenerateBtn.innerHTML = '<i class="fas fa-magic"></i> Générer un rêve';
    }
}

/**
 * Supprime le rêve actuellement affiché dans le modal
 */
async function deleteDream() {
    if (!selectedDreamId) return;
    
    if (confirm('Êtes-vous sûr de vouloir supprimer ce rêve ?')) {
        await deleteDreamById(selectedDreamId);
        closeModal();
    }
}

/**
 * Supprime un rêve par son ID
 * @param {string} id - ID du rêve à supprimer
 */
async function deleteDreamById(id) {
    try {
        // Supprimer le rêve
        const response = await fetch(`/api/thermal/memory/dreams/${id}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Recharger les rêves
            await loadDreams();
            
            // Afficher une notification
            showNotification('Rêve supprimé avec succès', 'success');
        } else {
            showNotification('Erreur lors de la suppression du rêve: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('Erreur lors de la suppression du rêve:', error);
        showNotification('Erreur lors de la suppression du rêve', 'error');
    }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Ajouter la notification au document
    document.body.appendChild(notification);
    
    // Afficher la notification
    setTimeout(() => {
        notification.classList.add('visible');
    }, 10);
    
    // Supprimer la notification après 5 secondes
    setTimeout(() => {
        notification.classList.remove('visible');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Initialiser la page des rêves lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', initializeDreams);
