/**
 * INTERFACE DE CHAT COMPLÈTE POUR LOUNA
 * Version entièrement fonctionnelle avec Socket.io et gestion d'erreurs
 */

(function() {
    'use strict';
    
    console.log('🚀 [Chat Complet] Initialisation de l\'interface de chat complète');
    
    // ===== VARIABLES GLOBALES =====
    let socket = null;
    let isConnected = false;
    let isTyping = false;
    let messageHistory = [];
    let currentUser = 'Utilisateur';
    let agentName = 'Vision Ultra';
    
    // ===== ÉLÉMENTS DOM =====
    const elements = {
        messagesContainer: null,
        messageInput: null,
        sendButton: null,
        typingIndicator: null,
        connectionStatus: null,
        connectionText: null,
        qiValue: null,
        neuronsValue: null,
        hotZone: null,
        warmZone: null,
        coldZone: null
    };
    
    // ===== CONFIGURATION =====
    const CONFIG = {
        socketEvents: {
            send: 'louna message',
            receive: 'louna response'
        },
        maxMessageLength: 2000,
        typingTimeout: 3000,
        reconnectAttempts: 5,
        reconnectDelay: 2000
    };
    
    // ===== FONCTIONS UTILITAIRES =====
    
    function log(message, type = 'info') {
        const prefix = '🚀 [Chat Complet]';
        console[type](`${prefix} ${message}`);
    }
    
    function formatTime(date = new Date()) {
        return date.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    function sanitizeHTML(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }
    
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
    
    // ===== GESTION DE LA CONNEXION SOCKET.IO =====
    
    function initializeSocket() {
        log('Initialisation de Socket.io...');
        
        try {
            if (typeof io === 'undefined') {
                log('Socket.io non disponible - Utilisation du mode fallback HTTP', 'warn');
                updateConnectionStatus(false, 'Mode HTTP');
                return false;
            }
            
            socket = io({
                transports: ['websocket', 'polling'],
                timeout: 10000,
                reconnection: true,
                reconnectionAttempts: CONFIG.reconnectAttempts,
                reconnectionDelay: CONFIG.reconnectDelay
            });
            
            // Événements de connexion
            socket.on('connect', () => {
                isConnected = true;
                log('✅ Socket.io connecté avec ID: ' + socket.id);
                updateConnectionStatus(true, 'Connecté');
            });
            
            socket.on('disconnect', (reason) => {
                isConnected = false;
                log('❌ Socket.io déconnecté: ' + reason, 'warn');
                updateConnectionStatus(false, 'Déconnecté');
            });
            
            socket.on('connect_error', (error) => {
                log('❌ Erreur de connexion Socket.io: ' + error.message, 'error');
                updateConnectionStatus(false, 'Erreur de connexion');
            });
            
            socket.on('reconnect', (attemptNumber) => {
                log('🔄 Reconnexion réussie après ' + attemptNumber + ' tentatives');
                updateConnectionStatus(true, 'Reconnecté');
            });
            
            socket.on('reconnect_failed', () => {
                log('❌ Échec de la reconnexion', 'error');
                updateConnectionStatus(false, 'Connexion échouée');
            });
            
            // Écouter les réponses
            socket.on(CONFIG.socketEvents.receive, (data) => {
                log('📨 Réponse reçue: ' + JSON.stringify(data));
                handleAgentResponse(data);
            });
            
            // Écouter d'autres événements possibles
            socket.on('agent response', (data) => {
                log('📨 Agent response reçue: ' + JSON.stringify(data));
                handleAgentResponse(data);
            });
            
            socket.on('chat response', (data) => {
                log('📨 Chat response reçue: ' + JSON.stringify(data));
                handleAgentResponse(data);
            });
            
            return true;
        } catch (error) {
            log('❌ Erreur lors de l\'initialisation Socket.io: ' + error.message, 'error');
            updateConnectionStatus(false, 'Erreur');
            return false;
        }
    }
    
    function updateConnectionStatus(connected, text) {
        if (elements.connectionStatus && elements.connectionText) {
            elements.connectionStatus.className = `status-dot ${connected ? '' : 'disconnected'}`;
            elements.connectionText.textContent = text;
        }
    }
    
    // ===== GESTION DES MESSAGES =====
    
    function sendMessage(messageText) {
        if (!messageText || !messageText.trim()) {
            log('❌ Message vide', 'warn');
            return false;
        }
        
        if (messageText.length > CONFIG.maxMessageLength) {
            log('❌ Message trop long', 'warn');
            showNotification('Message trop long (max ' + CONFIG.maxMessageLength + ' caractères)', 'error');
            return false;
        }
        
        const message = messageText.trim();
        log('📤 Envoi du message: ' + message);
        
        // Ajouter le message de l'utilisateur à l'interface
        addMessage(message, 'user');
        
        // Vider le champ de saisie
        if (elements.messageInput) {
            elements.messageInput.value = '';
            autoResizeTextarea(elements.messageInput);
        }
        
        // Ajouter à l'historique
        messageHistory.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });
        
        // Limiter l'historique
        if (messageHistory.length > 20) {
            messageHistory = messageHistory.slice(-20);
        }
        
        // Afficher l'indicateur de frappe
        showTypingIndicator();
        
        // Envoyer le message
        if (socket && isConnected) {
            // Via Socket.io
            socket.emit(CONFIG.socketEvents.send, {
                message: message,
                history: messageHistory.slice(-5) // Envoyer seulement les 5 derniers messages
            });
        } else {
            // Via HTTP fallback
            sendMessageHTTP(message);
        }
        
        return true;
    }
    
    function sendMessageHTTP(message) {
        log('📤 Envoi via HTTP fallback: ' + message);
        
        fetch('/api/chat/message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                history: messageHistory.slice(-5)
            })
        })
        .then(response => response.json())
        .then(data => {
            hideTypingIndicator();
            if (data.success) {
                if (data.response) {
                    handleAgentResponse({ message: data.response });
                }
            } else {
                log('❌ Erreur HTTP: ' + data.error, 'error');
                addMessage('Désolé, une erreur s\'est produite lors de l\'envoi de votre message.', 'assistant', true);
            }
        })
        .catch(error => {
            hideTypingIndicator();
            log('❌ Erreur réseau: ' + error.message, 'error');
            addMessage('Désolé, impossible de se connecter au serveur. Veuillez réessayer.', 'assistant', true);
        });
    }
    
    function handleAgentResponse(data) {
        hideTypingIndicator();
        
        let message = '';
        if (typeof data === 'string') {
            message = data;
        } else if (data.message) {
            message = data.message;
        } else if (data.response) {
            message = data.response;
        } else {
            message = 'Réponse reçue mais format non reconnu.';
        }
        
        log('📨 Traitement de la réponse: ' + message);
        
        // Ajouter le message de l'agent à l'interface
        addMessage(message, 'assistant');
        
        // Ajouter à l'historique
        messageHistory.push({
            role: 'assistant',
            content: message,
            timestamp: new Date().toISOString()
        });
        
        // Mettre à jour les statistiques si disponibles
        if (data.qi) updateQI(data.qi);
        if (data.neurons) updateNeurons(data.neurons);
        if (data.thermal) updateThermalMemory(data.thermal);
    }
    
    function addMessage(content, type, isError = false) {
        if (!elements.messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = `message-avatar ${type}`;
        avatar.innerHTML = type === 'user' ? '<i class="bi bi-person-fill"></i>' : '<i class="bi bi-robot"></i>';
        
        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';
        if (isError) {
            bubble.style.background = 'rgba(244, 67, 54, 0.2)';
            bubble.style.borderColor = 'rgba(244, 67, 54, 0.3)';
        }
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = sanitizeHTML(content).replace(/\n/g, '<br>');
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = formatTime();
        
        bubble.appendChild(messageContent);
        bubble.appendChild(messageTime);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(bubble);
        
        elements.messagesContainer.appendChild(messageDiv);
        
        // Scroll vers le bas
        elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
    }
    
    function showTypingIndicator() {
        if (elements.typingIndicator) {
            elements.typingIndicator.classList.add('show');
            isTyping = true;
            
            // Scroll vers le bas
            setTimeout(() => {
                if (elements.messagesContainer) {
                    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
                }
            }, 100);
        }
    }
    
    function hideTypingIndicator() {
        if (elements.typingIndicator) {
            elements.typingIndicator.classList.remove('show');
            isTyping = false;
        }
    }
    
    // ===== MISE À JOUR DES STATISTIQUES =====
    
    function updateQI(qi) {
        if (elements.qiValue) {
            elements.qiValue.textContent = qi;
        }
    }
    
    function updateNeurons(neurons) {
        if (elements.neuronsValue) {
            elements.neuronsValue.textContent = neurons;
        }
    }
    
    function updateThermalMemory(thermal) {
        if (thermal.hot && elements.hotZone) {
            elements.hotZone.textContent = thermal.hot + '°C';
        }
        if (thermal.warm && elements.warmZone) {
            elements.warmZone.textContent = thermal.warm + '°C';
        }
        if (thermal.cold && elements.coldZone) {
            elements.coldZone.textContent = thermal.cold + '°C';
        }
    }
    
    // ===== NOTIFICATIONS =====
    
    function showNotification(message, type = 'info') {
        // Créer une notification simple
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#f44336' : '#4CAF50'};
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    // ===== GESTIONNAIRES D'ÉVÉNEMENTS =====
    
    function setupEventListeners() {
        // Bouton d'envoi
        if (elements.sendButton) {
            elements.sendButton.addEventListener('click', (e) => {
                e.preventDefault();
                const message = elements.messageInput?.value;
                if (message) {
                    sendMessage(message);
                }
            });
        }
        
        // Champ de saisie
        if (elements.messageInput) {
            // Touche Entrée
            elements.messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const message = elements.messageInput.value;
                    if (message) {
                        sendMessage(message);
                    }
                }
            });
            
            // Auto-resize
            elements.messageInput.addEventListener('input', () => {
                autoResizeTextarea(elements.messageInput);
            });
            
            // Focus initial
            elements.messageInput.focus();
        }
    }
    
    // ===== INITIALISATION =====
    
    function initializeElements() {
        elements.messagesContainer = document.getElementById('messagesContainer');
        elements.messageInput = document.getElementById('messageInput');
        elements.sendButton = document.getElementById('sendButton');
        elements.typingIndicator = document.getElementById('typingIndicator');
        elements.connectionStatus = document.getElementById('connectionStatus');
        elements.connectionText = document.getElementById('connectionText');
        elements.qiValue = document.getElementById('qiValue');
        elements.neuronsValue = document.getElementById('neuronsValue');
        elements.hotZone = document.getElementById('hotZone');
        elements.warmZone = document.getElementById('warmZone');
        elements.coldZone = document.getElementById('coldZone');
        
        // Vérifier que les éléments essentiels sont présents
        if (!elements.messagesContainer || !elements.messageInput || !elements.sendButton) {
            log('❌ Éléments DOM essentiels manquants', 'error');
            return false;
        }
        
        return true;
    }
    
    function initialize() {
        log('🚀 Démarrage de l\'interface de chat complète');
        
        // Initialiser les éléments DOM
        if (!initializeElements()) {
            log('❌ Échec de l\'initialisation des éléments DOM', 'error');
            return;
        }
        
        // Configurer les gestionnaires d'événements
        setupEventListeners();
        
        // Initialiser Socket.io
        initializeSocket();
        
        // Afficher l'heure de bienvenue
        const welcomeTime = document.getElementById('welcomeTime');
        if (welcomeTime) {
            welcomeTime.textContent = formatTime();
        }
        
        log('✅ Interface de chat complète initialisée avec succès');
    }
    
    // ===== DÉMARRAGE =====
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // ===== EXPOSITION GLOBALE POUR LE DEBUG =====
    
    window.ChatComplet = {
        sendMessage: sendMessage,
        isConnected: () => isConnected,
        getSocket: () => socket,
        getHistory: () => messageHistory,
        updateQI: updateQI,
        updateNeurons: updateNeurons,
        updateThermalMemory: updateThermalMemory
    };
    
})();
