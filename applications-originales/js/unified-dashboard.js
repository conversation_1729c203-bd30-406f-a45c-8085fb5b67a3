/**
 * TABLEAU DE BORD UNIFIÉ POUR LOUNA
 * Synchronise les données entre toutes les interfaces
 * Évite la perte de mémoire et assure la cohérence
 */

class UnifiedDashboard {
    constructor() {
        this.data = {
            agent: {
                qi: 150,
                neurones: 71,
                temperature: 42.3,
                evolution_level: 85,
                accelerateurs: 8
            },
            thermal_memory: {
                global_temp: 0.38,
                total_memories: 85,
                zones: [],
                hot_memories: 0,
                warm_memories: 0,
                cold_memories: 0,
                efficiency: 85
            },
            performance: {
                cpu_usage: 45,
                memory_usage: 67,
                disk_usage: 23,
                network_status: 'connected',
                uptime: 0
            },
            last_sync: new Date().toISOString()
        };
        
        this.updateInterval = null;
        this.syncInterval = null;
        
        console.log('🌐 Tableau de bord unifié initialisé');
        this.startAutoUpdate();
    }
    
    /**
     * Démarrer la mise à jour automatique
     */
    startAutoUpdate() {
        // Mise à jour des données toutes les 5 secondes
        this.updateInterval = setInterval(() => {
            this.fetchGlobalState();
        }, 5000);
        
        // Synchronisation globale toutes les 30 secondes
        this.syncInterval = setInterval(() => {
            this.syncGlobalState();
        }, 30000);
        
        // Première mise à jour immédiate
        this.fetchGlobalState();
        
        console.log('🔄 Mise à jour automatique démarrée');
    }
    
    /**
     * Arrêter la mise à jour automatique
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        
        console.log('⏹️ Mise à jour automatique arrêtée');
    }
    
    /**
     * Récupérer l'état global depuis l'API
     */
    async fetchGlobalState() {
        try {
            const response = await fetch('/api/global/state');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.data = { ...this.data, ...result.state };
                    this.data.last_sync = result.timestamp;
                    this.updateAllInterfaces();
                }
            }
        } catch (error) {
            console.error('❌ Erreur récupération état global:', error);
        }
    }
    
    /**
     * Synchroniser l'état global
     */
    async syncGlobalState() {
        try {
            const response = await fetch('/api/global/sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('🔄 Synchronisation globale effectuée');
                    this.fetchGlobalState(); // Récupérer les nouvelles données
                }
            }
        } catch (error) {
            console.error('❌ Erreur synchronisation globale:', error);
        }
    }
    
    /**
     * Récupérer les données détaillées de la mémoire thermique
     */
    async fetchThermalDetails() {
        try {
            const response = await fetch('/api/global/thermal-detailed');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.data.thermal_memory = { ...this.data.thermal_memory, ...result.thermal_memory };
                    this.updateThermalInterface();
                }
            }
        } catch (error) {
            console.error('❌ Erreur récupération mémoire thermique:', error);
        }
    }
    
    /**
     * Récupérer les données détaillées du QI
     */
    async fetchQiDetails() {
        try {
            const response = await fetch('/api/global/qi-detailed');
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.data.agent = { ...this.data.agent, ...result.qi_data };
                    this.updateQiInterface();
                }
            }
        } catch (error) {
            console.error('❌ Erreur récupération QI:', error);
        }
    }
    
    /**
     * Mettre à jour toutes les interfaces
     */
    updateAllInterfaces() {
        this.updateQiInterface();
        this.updateThermalInterface();
        this.updatePerformanceInterface();
        this.updateSystemInterface();
    }
    
    /**
     * Mettre à jour l'interface QI/Neurones
     */
    updateQiInterface() {
        // Mettre à jour les éléments QI
        const qiElements = document.querySelectorAll('[data-qi]');
        qiElements.forEach(el => {
            el.textContent = this.data.agent.qi || 150;
        });
        
        // Mettre à jour les éléments neurones
        const neuroneElements = document.querySelectorAll('[data-neurones]');
        neuroneElements.forEach(el => {
            el.textContent = this.data.agent.neurones || 71;
        });
        
        // Mettre à jour les éléments température
        const tempElements = document.querySelectorAll('[data-temperature]');
        tempElements.forEach(el => {
            el.textContent = `${this.data.agent.temperature || 42.3}°C`;
        });
        
        // Mettre à jour les éléments niveau d'évolution
        const evolutionElements = document.querySelectorAll('[data-evolution]');
        evolutionElements.forEach(el => {
            el.textContent = `${this.data.agent.evolution_level || 85}%`;
        });
        
        // Mettre à jour les barres de progression
        this.updateProgressBars();
    }
    
    /**
     * Mettre à jour l'interface mémoire thermique
     */
    updateThermalInterface() {
        // Mettre à jour la température globale
        const globalTempElements = document.querySelectorAll('[data-global-temp]');
        globalTempElements.forEach(el => {
            el.textContent = `${this.data.thermal_memory.global_temp || 0.38}°C`;
        });
        
        // Mettre à jour le nombre total de mémoires
        const totalMemoriesElements = document.querySelectorAll('[data-total-memories]');
        totalMemoriesElements.forEach(el => {
            el.textContent = this.data.thermal_memory.total_memories || 85;
        });
        
        // Mettre à jour l'efficacité
        const efficiencyElements = document.querySelectorAll('[data-efficiency]');
        efficiencyElements.forEach(el => {
            el.textContent = `${this.data.thermal_memory.efficiency || 85}%`;
        });
        
        // Mettre à jour les zones de mémoire
        this.updateMemoryZones();
    }
    
    /**
     * Mettre à jour l'interface de performance
     */
    updatePerformanceInterface() {
        // Mettre à jour l'utilisation CPU
        const cpuElements = document.querySelectorAll('[data-cpu]');
        cpuElements.forEach(el => {
            el.textContent = `${this.data.performance.cpu_usage || 45}%`;
        });
        
        // Mettre à jour l'utilisation mémoire
        const memoryElements = document.querySelectorAll('[data-memory-usage]');
        memoryElements.forEach(el => {
            el.textContent = `${this.data.performance.memory_usage || 67}%`;
        });
        
        // Mettre à jour l'utilisation disque
        const diskElements = document.querySelectorAll('[data-disk]');
        diskElements.forEach(el => {
            el.textContent = `${this.data.performance.disk_usage || 23}%`;
        });
        
        // Mettre à jour le statut réseau
        const networkElements = document.querySelectorAll('[data-network]');
        networkElements.forEach(el => {
            el.textContent = this.data.performance.network_status || 'connected';
            el.className = `status ${this.data.performance.network_status === 'connected' ? 'connected' : 'disconnected'}`;
        });
    }
    
    /**
     * Mettre à jour l'interface système
     */
    updateSystemInterface() {
        // Mettre à jour le timestamp de dernière synchronisation
        const syncElements = document.querySelectorAll('[data-last-sync]');
        syncElements.forEach(el => {
            const syncTime = new Date(this.data.last_sync);
            el.textContent = syncTime.toLocaleTimeString();
        });
        
        // Mettre à jour l'uptime
        const uptimeElements = document.querySelectorAll('[data-uptime]');
        uptimeElements.forEach(el => {
            const uptime = this.data.performance.uptime || 0;
            const hours = Math.floor(uptime / 60);
            const minutes = uptime % 60;
            el.textContent = `${hours}h ${minutes}m`;
        });
    }
    
    /**
     * Mettre à jour les barres de progression
     */
    updateProgressBars() {
        // Barre QI
        const qiBar = document.querySelector('[data-qi-bar]');
        if (qiBar) {
            const qiPercent = Math.min((this.data.agent.qi / 200) * 100, 100);
            qiBar.style.width = `${qiPercent}%`;
        }
        
        // Barre neurones
        const neuroneBar = document.querySelector('[data-neurone-bar]');
        if (neuroneBar) {
            const neuronePercent = Math.min((this.data.agent.neurones / 100) * 100, 100);
            neuroneBar.style.width = `${neuronePercent}%`;
        }
        
        // Barre évolution
        const evolutionBar = document.querySelector('[data-evolution-bar]');
        if (evolutionBar) {
            evolutionBar.style.width = `${this.data.agent.evolution_level || 85}%`;
        }
    }
    
    /**
     * Mettre à jour les zones de mémoire
     */
    updateMemoryZones() {
        const zonesContainer = document.querySelector('[data-memory-zones]');
        if (zonesContainer && this.data.thermal_memory.zones) {
            zonesContainer.innerHTML = '';
            
            this.data.thermal_memory.zones.forEach(zone => {
                const zoneElement = document.createElement('div');
                zoneElement.className = 'memory-zone';
                zoneElement.innerHTML = `
                    <div class="zone-header">
                        <span class="zone-name">${zone.name}</span>
                        <span class="zone-temp">${zone.temperature}°C</span>
                    </div>
                    <div class="zone-stats">
                        <span class="zone-count">${zone.count} mémoires</span>
                        <span class="zone-status ${zone.active ? 'active' : 'inactive'}">
                            ${zone.active ? 'Actif' : 'Inactif'}
                        </span>
                    </div>
                    <div class="zone-bar">
                        <div class="zone-progress" style="width: ${(zone.temperature * 100)}%"></div>
                    </div>
                `;
                zonesContainer.appendChild(zoneElement);
            });
        }
    }
    
    /**
     * Obtenir les données actuelles
     */
    getData() {
        return this.data;
    }
    
    /**
     * Forcer une mise à jour complète
     */
    async forceUpdate() {
        console.log('🔄 Mise à jour forcée...');
        await this.fetchGlobalState();
        await this.fetchThermalDetails();
        await this.fetchQiDetails();
        this.updateAllInterfaces();
        console.log('✅ Mise à jour forcée terminée');
    }
}

// Créer l'instance globale du tableau de bord unifié
window.unifiedDashboard = new UnifiedDashboard();

// Exposer les fonctions utiles
window.forceUpdate = () => window.unifiedDashboard.forceUpdate();
window.getDashboardData = () => window.unifiedDashboard.getData();

console.log('🎯 Tableau de bord unifié Louna chargé et opérationnel');
