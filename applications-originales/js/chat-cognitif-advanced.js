// ===== SYSTÈME AVANCÉ POUR LE CHAT COGNITIF =====

class ChatCognitifAdvanced {
    constructor() {
        this.isConnected = false;
        this.performanceMetrics = {
            responseTime: 0,
            kyberBoost: 1,
            memoryUsage: 0,
            thermalTemp: 0,
            qi: 100,
            neurons: 0
        };
        this.notifications = [];
        this.accelerators = new Map();
        this.reflectionFilters = ['all'];
        
        this.init();
    }

    async init() {
        console.log('🧠 Initialisation du système de chat cognitif avancé...');
        
        // Initialiser les connexions WebSocket
        this.initWebSocket();
        
        // Démarrer le monitoring en temps réel
        this.startRealTimeMonitoring();
        
        // Configurer les gestionnaires d'événements
        this.setupEventHandlers();
        
        // Charger l'état initial
        await this.loadInitialState();
        
        console.log('✅ Chat cognitif avancé initialisé');
    }

    initWebSocket() {
        try {
            this.socket = io();
            
            this.socket.on('connect', () => {
                this.isConnected = true;
                this.showNotification('🔗 Connexion WebSocket établie', 'success');
                this.updateConnectionStatus(true);
            });

            this.socket.on('disconnect', () => {
                this.isConnected = false;
                this.showNotification('❌ Connexion WebSocket perdue', 'error');
                this.updateConnectionStatus(false);
            });

            this.socket.on('new_reflection', (reflection) => {
                this.addReflectionToUI(reflection);
            });

            this.socket.on('performance_update', (metrics) => {
                this.updatePerformanceMetrics(metrics);
            });

            this.socket.on('accelerator_update', (accelerator) => {
                this.updateAcceleratorStatus(accelerator);
            });

            this.socket.on('memory_update', (memoryData) => {
                this.updateMemoryVisualization(memoryData);
            });

        } catch (error) {
            console.error('❌ Erreur WebSocket:', error);
            this.showNotification('❌ Impossible de se connecter au WebSocket', 'error');
        }
    }

    startRealTimeMonitoring() {
        // Monitoring des performances toutes les 2 secondes
        setInterval(() => {
            this.fetchPerformanceMetrics();
        }, 2000);

        // Monitoring des accélérateurs toutes les 5 secondes
        setInterval(() => {
            this.fetchAcceleratorStatus();
        }, 5000);

        // Monitoring de la mémoire thermique toutes les 3 secondes
        setInterval(() => {
            this.fetchThermalMemoryStatus();
        }, 3000);
    }

    async fetchPerformanceMetrics() {
        try {
            const response = await fetch('/api/monitoring/performance');
            if (response.ok) {
                const data = await response.json();
                this.updatePerformanceMetrics(data);
            }
        } catch (error) {
            console.warn('⚠️ Erreur récupération métriques:', error);
        }
    }

    async fetchAcceleratorStatus() {
        try {
            const response = await fetch('/api/kyber/status');
            if (response.ok) {
                const data = await response.json();
                this.updateAcceleratorDisplay(data);
            }
        } catch (error) {
            console.warn('⚠️ Erreur récupération accélérateurs:', error);
        }
    }

    async fetchThermalMemoryStatus() {
        try {
            const response = await fetch('/api/thermal/memory/stats');
            if (response.ok) {
                const data = await response.json();
                this.updateThermalMemoryDisplay(data);
            }
        } catch (error) {
            console.warn('⚠️ Erreur récupération mémoire thermique:', error);
        }
    }

    updatePerformanceMetrics(metrics) {
        this.performanceMetrics = { ...this.performanceMetrics, ...metrics };
        
        // Mettre à jour l'affichage des métriques
        this.updatePerformanceDisplay();
        
        // Vérifier les seuils critiques
        this.checkCriticalThresholds();
    }

    updatePerformanceDisplay() {
        const elements = {
            responseTime: document.getElementById('response-time'),
            kyberBoost: document.getElementById('kyber-boost'),
            memoryUsage: document.getElementById('memory-usage'),
            thermalTemp: document.getElementById('thermal-temp'),
            qi: document.getElementById('qi-level'),
            neurons: document.getElementById('neuron-count')
        };

        Object.entries(elements).forEach(([key, element]) => {
            if (element && this.performanceMetrics[key] !== undefined) {
                const value = this.performanceMetrics[key];
                element.textContent = this.formatMetricValue(key, value);
                element.className = this.getMetricClass(key, value);
            }
        });
    }

    formatMetricValue(key, value) {
        switch (key) {
            case 'responseTime':
                return `${value}ms`;
            case 'kyberBoost':
                return `${value.toFixed(1)}x`;
            case 'memoryUsage':
                return `${value.toFixed(1)}%`;
            case 'thermalTemp':
                return `${value.toFixed(1)}°C`;
            case 'qi':
                return `${Math.round(value)}`;
            case 'neurons':
                return `${value}`;
            default:
                return value.toString();
        }
    }

    getMetricClass(key, value) {
        const thresholds = {
            responseTime: { good: 500, warning: 1000 },
            memoryUsage: { good: 70, warning: 85 },
            thermalTemp: { good: 60, warning: 80 },
            qi: { good: 150, warning: 100 }
        };

        if (!thresholds[key]) return 'metric-normal';

        const { good, warning } = thresholds[key];
        
        if (key === 'responseTime' || key === 'memoryUsage' || key === 'thermalTemp') {
            if (value <= good) return 'metric-good';
            if (value <= warning) return 'metric-warning';
            return 'metric-critical';
        } else {
            if (value >= good) return 'metric-good';
            if (value >= warning) return 'metric-warning';
            return 'metric-critical';
        }
    }

    checkCriticalThresholds() {
        const { memoryUsage, thermalTemp, responseTime } = this.performanceMetrics;

        if (memoryUsage > 90) {
            this.showNotification('🚨 Usage mémoire critique: ' + memoryUsage.toFixed(1) + '%', 'error');
        }

        if (thermalTemp > 85) {
            this.showNotification('🔥 Température thermique élevée: ' + thermalTemp.toFixed(1) + '°C', 'warning');
        }

        if (responseTime > 2000) {
            this.showNotification('⏱️ Temps de réponse élevé: ' + responseTime + 'ms', 'warning');
        }
    }

    updateAcceleratorDisplay(accelerators) {
        const container = document.getElementById('accelerator-list');
        if (!container) return;

        container.innerHTML = '';

        accelerators.forEach(acc => {
            const accElement = this.createAcceleratorElement(acc);
            container.appendChild(accElement);
        });
    }

    createAcceleratorElement(accelerator) {
        const div = document.createElement('div');
        div.className = 'accelerator-item';
        div.innerHTML = `
            <div class="accelerator-header">
                <span class="accelerator-name">${accelerator.name}</span>
                <span class="accelerator-boost">${accelerator.boost}x</span>
            </div>
            <div class="accelerator-details">
                <div class="accelerator-type">${accelerator.type}</div>
                <div class="accelerator-status ${accelerator.active ? 'active' : 'inactive'}">
                    ${accelerator.active ? '🟢 Actif' : '🔴 Inactif'}
                </div>
            </div>
            <div class="accelerator-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${accelerator.efficiency || 0}%"></div>
                </div>
                <span class="progress-text">${accelerator.efficiency || 0}%</span>
            </div>
        `;
        return div;
    }

    addReflectionToUI(reflection) {
        const container = document.getElementById('reflections-content');
        if (!container) return;

        const reflectionElement = document.createElement('div');
        reflectionElement.className = `reflection-item ${reflection.type}`;
        reflectionElement.innerHTML = `
            <div>${reflection.text}</div>
            <div class="reflection-time">${reflection.displayTime}</div>
        `;

        container.appendChild(reflectionElement);
        container.scrollTop = container.scrollHeight;

        // Limiter le nombre de réflexions affichées
        const reflections = container.querySelectorAll('.reflection-item');
        if (reflections.length > 50) {
            reflections[0].remove();
        }
    }

    showNotification(message, type = 'info') {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };

        this.notifications.push(notification);
        this.displayNotification(notification);

        // Auto-suppression après 5 secondes
        setTimeout(() => {
            this.removeNotification(notification.id);
        }, 5000);
    }

    displayNotification(notification) {
        const container = document.getElementById('notification-container') || this.createNotificationContainer();
        
        const notifElement = document.createElement('div');
        notifElement.className = `notification notification-${notification.type}`;
        notifElement.id = `notification-${notification.id}`;
        notifElement.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${notification.message}</span>
                <button class="notification-close" onclick="chatCognitif.removeNotification(${notification.id})">×</button>
            </div>
        `;

        container.appendChild(notifElement);

        // Animation d'entrée
        setTimeout(() => {
            notifElement.classList.add('notification-show');
        }, 10);
    }

    createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container';
        document.body.appendChild(container);
        return container;
    }

    removeNotification(id) {
        const element = document.getElementById(`notification-${id}`);
        if (element) {
            element.classList.add('notification-hide');
            setTimeout(() => {
                element.remove();
            }, 300);
        }

        this.notifications = this.notifications.filter(n => n.id !== id);
    }

    updateConnectionStatus(connected) {
        const statusElement = document.querySelector('.chat-status');
        if (statusElement) {
            const dot = statusElement.querySelector('.status-dot');
            const text = statusElement.querySelector('span');
            
            if (connected) {
                dot.style.background = '#4ade80';
                text.textContent = 'Agent connecté';
                statusElement.classList.remove('disconnected');
            } else {
                dot.style.background = '#ef4444';
                text.textContent = 'Agent déconnecté';
                statusElement.classList.add('disconnected');
            }
        }
    }

    setupEventHandlers() {
        // Gestionnaire pour les filtres de réflexions
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.target.dataset.filter;
                this.applyReflectionFilter(filter);
            });
        });

        // Gestionnaire pour les contrôles avancés
        const advancedToggle = document.getElementById('advanced-controls-toggle');
        if (advancedToggle) {
            advancedToggle.addEventListener('click', () => {
                this.toggleAdvancedControls();
            });
        }
    }

    applyReflectionFilter(filter) {
        const reflections = document.querySelectorAll('.reflection-item');
        
        reflections.forEach(reflection => {
            if (filter === 'all' || reflection.classList.contains(filter)) {
                reflection.style.display = 'block';
            } else {
                reflection.style.display = 'none';
            }
        });

        // Mettre à jour l'état des boutons de filtre
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });
    }

    toggleAdvancedControls() {
        const panel = document.getElementById('advanced-controls-panel');
        if (panel) {
            panel.classList.toggle('expanded');
        }
    }

    async loadInitialState() {
        try {
            // Charger l'état initial des métriques
            await this.fetchPerformanceMetrics();
            await this.fetchAcceleratorStatus();
            await this.fetchThermalMemoryStatus();
            
            console.log('✅ État initial chargé');
        } catch (error) {
            console.error('❌ Erreur chargement état initial:', error);
        }
    }

    // Méthodes utilitaires
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
    }

    getStatusColor(value, thresholds) {
        if (value <= thresholds.good) return '#4ade80';
        if (value <= thresholds.warning) return '#f59e0b';
        return '#ef4444';
    }
}

// Initialiser le système avancé
let chatCognitif;

document.addEventListener('DOMContentLoaded', () => {
    chatCognitif = new ChatCognitifAdvanced();
});

// Exporter pour utilisation globale
window.ChatCognitifAdvanced = ChatCognitifAdvanced;
