/**
 * Système de notifications pour l'application Louna
 */

class NotificationSystem {
    constructor() {
        // Conteneur des notifications
        this.container = null;

        // Compteur d'ID
        this.idCounter = 0;

        // Notifications actives
        this.activeNotifications = new Map();

        // Historique des notifications pour éviter les doublons
        this.notificationHistory = new Map();

        // Contexte intelligent
        this.context = {
            userActivity: 'active', // active, idle, away
            systemLoad: 'normal',   // low, normal, high
            lastInteraction: Date.now(),
            preferences: {
                enableSounds: true,
                enableVibration: true,
                priority: 'normal', // low, normal, high
                groupSimilar: true
            }
        };

        // Timer pour détecter l'inactivité
        this.activityTimer = null;

        // Initialiser le système
        this.initialize();
        this.setupActivityDetection();
    }

    /**
     * Initialise le système de notifications
     */
    initialize() {
        // Créer le conteneur des notifications s'il n'existe pas
        if (!document.getElementById('notifications-container')) {
            this.container = document.createElement('div');
            this.container.id = 'notifications-container';
            this.container.className = 'notifications-container';
            document.body.appendChild(this.container);
        } else {
            this.container = document.getElementById('notifications-container');
        }

        // Charger les préférences utilisateur
        this.loadPreferences();
    }

    /**
     * Configure la détection d'activité utilisateur
     */
    setupActivityDetection() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

        const updateActivity = () => {
            this.context.lastInteraction = Date.now();
            this.context.userActivity = 'active';

            // Réinitialiser le timer d'inactivité
            if (this.activityTimer) {
                clearTimeout(this.activityTimer);
            }

            // Détecter l'inactivité après 5 minutes
            this.activityTimer = setTimeout(() => {
                this.context.userActivity = 'idle';

                // Détecter l'absence après 15 minutes
                setTimeout(() => {
                    this.context.userActivity = 'away';
                }, 10 * 60 * 1000); // 10 minutes supplémentaires
            }, 5 * 60 * 1000); // 5 minutes
        };

        // Ajouter les écouteurs d'événements
        events.forEach(event => {
            document.addEventListener(event, updateActivity, true);
        });

        // Initialiser l'activité
        updateActivity();
    }

    /**
     * Charge les préférences utilisateur
     */
    loadPreferences() {
        const saved = localStorage.getItem('louna_notification_preferences');
        if (saved) {
            try {
                const preferences = JSON.parse(saved);
                this.context.preferences = { ...this.context.preferences, ...preferences };
            } catch (error) {
                console.warn('Erreur lors du chargement des préférences de notification:', error);
            }
        }
    }

    /**
     * Sauvegarde les préférences utilisateur
     */
    savePreferences() {
        localStorage.setItem('louna_notification_preferences', JSON.stringify(this.context.preferences));
    }

    /**
     * Détermine si une notification doit être affichée selon le contexte
     */
    shouldShowNotification(options) {
        const priority = options.priority || 'normal';
        const userActivity = this.context.userActivity;

        // Toujours afficher les notifications critiques
        if (priority === 'critical') {
            return true;
        }

        // Ne pas afficher les notifications de faible priorité si l'utilisateur est absent
        if (priority === 'low' && userActivity === 'away') {
            return false;
        }

        // Vérifier les doublons récents
        if (options.message && this.context.preferences.groupSimilar) {
            const messageHash = this.hashMessage(options.message);
            const lastShown = this.notificationHistory.get(messageHash);

            // Ne pas afficher si la même notification a été montrée il y a moins de 30 secondes
            if (lastShown && (Date.now() - lastShown) < 30000) {
                return false;
            }
        }

        return true;
    }

    /**
     * Crée un hash simple pour un message
     */
    hashMessage(message) {
        let hash = 0;
        for (let i = 0; i < message.length; i++) {
            const char = message.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convertir en 32bit integer
        }
        return hash.toString();
    }

    /**
     * Ajoute des effets contextuels à la notification
     */
    addContextualEffects(notification, options) {
        // Son de notification
        if (this.context.preferences.enableSounds && options.type !== 'info') {
            this.playNotificationSound(options.type);
        }

        // Vibration sur mobile
        if (this.context.preferences.enableVibration && 'vibrate' in navigator) {
            const pattern = this.getVibrationPattern(options.type);
            navigator.vibrate(pattern);
        }

        // Animation spéciale pour les notifications critiques
        if (options.priority === 'critical') {
            notification.classList.add('critical-pulse');
        }

        // Couleur adaptative selon le thème
        const currentTheme = document.documentElement.getAttribute('data-theme');
        if (currentTheme) {
            notification.classList.add(`theme-${currentTheme}`);
        }
    }

    /**
     * Joue un son de notification
     */
    playNotificationSound(type) {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Fréquences différentes selon le type
            const frequencies = {
                success: [523, 659, 784], // Do, Mi, Sol
                warning: [440, 554],       // La, Do#
                error: [330, 277],         // Mi, Do#
                info: [440]                // La
            };

            const freq = frequencies[type] || frequencies.info;

            oscillator.frequency.setValueAtTime(freq[0], audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.warn('Impossible de jouer le son de notification:', error);
        }
    }

    /**
     * Retourne un pattern de vibration selon le type
     */
    getVibrationPattern(type) {
        const patterns = {
            success: [100, 50, 100],
            warning: [200, 100, 200],
            error: [300, 100, 300, 100, 300],
            info: [100]
        };

        return patterns[type] || patterns.info;
    }

    /**
     * Crée une notification
     * @param {Object} options - Options de la notification
     * @param {string} options.type - Type de notification (info, success, warning, error)
     * @param {string} options.title - Titre de la notification
     * @param {string} options.message - Message de la notification
     * @param {number} options.duration - Durée d'affichage en millisecondes
     * @param {boolean} options.dismissible - Si la notification peut être fermée manuellement
     * @param {string} options.priority - Priorité (low, normal, high, critical)
     * @returns {string} - ID de la notification
     */
    create(options) {
        // Options par défaut
        const defaultOptions = {
            type: 'info',
            title: '',
            message: '',
            duration: 5000,
            dismissible: true,
            priority: 'normal'
        };

        // Fusionner les options
        const settings = { ...defaultOptions, ...options };

        // Vérifier si la notification doit être affichée selon le contexte
        if (!this.shouldShowNotification(settings)) {
            console.log('Notification ignorée selon le contexte:', settings.message);
            return null;
        }

        // Enregistrer dans l'historique
        if (settings.message) {
            const messageHash = this.hashMessage(settings.message);
            this.notificationHistory.set(messageHash, Date.now());
        }

        // Ajuster la durée selon l'activité utilisateur
        if (this.context.userActivity === 'idle') {
            settings.duration *= 1.5; // Afficher plus longtemps si inactif
        } else if (this.context.userActivity === 'away') {
            settings.duration *= 3; // Afficher beaucoup plus longtemps si absent
        }

        // Générer un ID unique
        const id = `notification-${Date.now()}-${this.idCounter++}`;

        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.id = id;
        notification.className = `notification ${settings.type} priority-${settings.priority}`;

        // Icône en fonction du type
        let icon = 'info-circle';
        switch (settings.type) {
            case 'success':
                icon = 'check-circle';
                break;
            case 'warning':
                icon = 'exclamation-triangle';
                break;
            case 'error':
                icon = 'times-circle';
                break;
            case 'brain':
                icon = 'brain';
                break;
            case 'memory':
                icon = 'memory';
                break;
            case 'kyber':
                icon = 'bolt';
                break;
        }

        // Contenu de la notification avec contexte
        const contextInfo = this.getContextualInfo(settings);
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-${icon}"></i>
            </div>
            <div class="notification-content">
                ${settings.title ? `<div class="notification-title">${settings.title}</div>` : ''}
                ${settings.message ? `<div class="notification-message">${settings.message}</div>` : ''}
                ${contextInfo ? `<div class="notification-context">${contextInfo}</div>` : ''}
            </div>
            ${settings.dismissible ? `
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            ` : ''}
            <div class="notification-progress">
                <div class="notification-progress-bar" style="animation-duration: ${settings.duration}ms"></div>
            </div>
        `;

        // Ajouter la notification au conteneur
        this.container.appendChild(notification);

        // Ajouter les effets contextuels
        this.addContextualEffects(notification, settings);

        // Ajouter la classe pour afficher la notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Ajouter un écouteur pour le bouton de fermeture
        if (settings.dismissible) {
            const closeButton = notification.querySelector('.notification-close');
            closeButton.addEventListener('click', () => {
                this.dismiss(id);
            });
        }

        // Fermer automatiquement la notification après la durée spécifiée
        const timeoutId = setTimeout(() => {
            this.dismiss(id);
        }, settings.duration);

        // Stocker la notification active
        this.activeNotifications.set(id, {
            element: notification,
            timeoutId,
            settings
        });

        return id;
    }

    /**
     * Retourne des informations contextuelles pour la notification
     */
    getContextualInfo(settings) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Ajouter l'heure pour les notifications importantes
        if (settings.priority === 'high' || settings.priority === 'critical') {
            return `${timeString}`;
        }

        return null;
    }

    /**
     * Ferme une notification
     * @param {string} id - ID de la notification
     */
    dismiss(id) {
        // Récupérer la notification
        const notification = this.activeNotifications.get(id);

        if (notification) {
            // Annuler le timeout
            clearTimeout(notification.timeoutId);

            // Ajouter la classe pour masquer la notification
            notification.element.classList.remove('show');
            notification.element.classList.add('hide');

            // Supprimer la notification après l'animation
            setTimeout(() => {
                if (notification.element.parentNode) {
                    notification.element.parentNode.removeChild(notification.element);
                }
                this.activeNotifications.delete(id);
            }, 300);
        }
    }

    /**
     * Ferme toutes les notifications
     */
    dismissAll() {
        // Fermer chaque notification
        this.activeNotifications.forEach((notification, id) => {
            this.dismiss(id);
        });
    }

    /**
     * Crée une notification de type info
     * @param {string} message - Message de la notification
     * @param {string} title - Titre de la notification
     * @param {Object} options - Options supplémentaires
     * @returns {string} - ID de la notification
     */
    info(message, title = 'Information', options = {}) {
        return this.create({
            type: 'info',
            title,
            message,
            ...options
        });
    }

    /**
     * Crée une notification de type succès
     * @param {string} message - Message de la notification
     * @param {string} title - Titre de la notification
     * @param {Object} options - Options supplémentaires
     * @returns {string} - ID de la notification
     */
    success(message, title = 'Succès', options = {}) {
        return this.create({
            type: 'success',
            title,
            message,
            ...options
        });
    }

    /**
     * Crée une notification de type avertissement
     * @param {string} message - Message de la notification
     * @param {string} title - Titre de la notification
     * @param {Object} options - Options supplémentaires
     * @returns {string} - ID de la notification
     */
    warning(message, title = 'Avertissement', options = {}) {
        return this.create({
            type: 'warning',
            title,
            message,
            ...options
        });
    }

    /**
     * Crée une notification de type erreur
     * @param {string} message - Message de la notification
     * @param {string} title - Titre de la notification
     * @param {Object} options - Options supplémentaires
     * @returns {string} - ID de la notification
     */
    error(message, title = 'Erreur', options = {}) {
        return this.create({
            type: 'error',
            title,
            message,
            ...options
        });
    }
}

// Créer une instance du système de notifications
const notifications = new NotificationSystem();

// Exporter l'instance
window.notifications = notifications;
