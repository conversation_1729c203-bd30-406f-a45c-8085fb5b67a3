/**
 * Système d'Accélérateurs Kyber pour l'agent Louna
 * Ce module gère les accélérateurs Kyber côté client et communique avec le backend
 */

class KyberAccelerators {
    constructor() {
        this.initialized = false;
        this.accelerators = {
            reflexive: {
                name: "Accélérateur Réflexif",
                description: "Améliore la vitesse de traitement des informations",
                boostFactor: 3.1,
                stability: 0.92,
                energy: 1.0,
                enabled: true,
                type: "processing"
            },
            thermal: {
                name: "Accélérateur Thermique",
                description: "Optimise les transferts entre zones de mémoire thermique",
                boostFactor: 2.7,
                stability: 0.88,
                energy: 1.0,
                enabled: true,
                type: "memory"
            },
            connector: {
                name: "Connecteur Thermique",
                description: "Facilite les connexions entre informations dans différentes zones",
                boostFactor: 2.1,
                stability: 0.95,
                energy: 1.0,
                enabled: true,
                type: "connection"
            },
            circulation: {
                name: "Accélérateur de Circulation",
                description: "Améliore la circulation des informations entre les différentes zones de mémoire",
                boostFactor: 2.5,
                stability: 0.90,
                energy: 1.0,
                enabled: true,
                type: "memory"
            },
            ltx: {
                name: "Accélérateur LTX",
                description: "Optimise le traitement des flux vidéo et la reconnaissance d'objets",
                boostFactor: 2.8,
                stability: 0.85,
                energy: 1.0,
                enabled: true,
                type: "processing"
            }
        };
        this.stats = {
            totalBoostApplied: 0,
            energyConsumed: 0,
            stabilityEvents: 0,
            lastUpdateTime: null,
            averageBoostFactor: 0,
            efficiency: 0.92
        };
        this.listeners = [];
        this.updateInterval = 5000; // 5 secondes
        this.updateTimer = null;
    }

    /**
     * Initialise les accélérateurs Kyber
     */
    async initialize() {
        if (this.initialized) return;

        console.log('Initialisation des accélérateurs Kyber...');

        try {
            // Charger les données initiales
            await this.loadAcceleratorStats();

            // Démarrer les mises à jour périodiques
            this.startPeriodicUpdates();

            this.initialized = true;
            console.log('Accélérateurs Kyber initialisés avec succès');
        } catch (error) {
            console.error('Erreur lors de l\'initialisation des accélérateurs Kyber:', error);
        }
    }

    /**
     * Charge les statistiques des accélérateurs
     */
    async loadAcceleratorStats() {
        try {
            const response = await fetch('/api/thermal/accelerators/stats');
            const data = await response.json();

            if (data.success) {
                this.stats = data.stats;

                // Mettre à jour les facteurs de boost des accélérateurs
                if (data.stats.reflexiveBoost) {
                    this.accelerators.reflexive.boostFactor = parseFloat(data.stats.reflexiveBoost);
                }
                if (data.stats.thermalBoost) {
                    this.accelerators.thermal.boostFactor = parseFloat(data.stats.thermalBoost);
                }
                if (data.stats.connectorBoost) {
                    this.accelerators.connector.boostFactor = parseFloat(data.stats.connectorBoost);
                }

                this.notifyListeners('stats', this.stats);
            } else {
                console.error('Erreur lors du chargement des statistiques:', data.error);
            }
        } catch (error) {
            console.error('Erreur lors de la requête des statistiques:', error);
        }
    }

    /**
     * Active ou désactive un accélérateur
     * @param {string} acceleratorId - ID de l'accélérateur
     * @param {boolean} enabled - État d'activation
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async toggleAccelerator(acceleratorId, enabled) {
        try {
            const response = await fetch(`/api/thermal/accelerators/toggle/${acceleratorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ enabled })
            });

            const data = await response.json();

            if (data.success) {
                // Mettre à jour l'état local
                if (this.accelerators[acceleratorId]) {
                    this.accelerators[acceleratorId].enabled = enabled;
                    this.notifyListeners('toggle', { id: acceleratorId, enabled });
                }

                // Recharger les statistiques
                await this.loadAcceleratorStats();

                return true;
            } else {
                console.error('Erreur lors de la modification de l\'accélérateur:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête de modification:', error);
            return false;
        }
    }

    /**
     * Ajuste le facteur de boost d'un accélérateur
     * @param {string} acceleratorId - ID de l'accélérateur
     * @param {number} boostFactor - Nouveau facteur de boost
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async adjustBoostFactor(acceleratorId, boostFactor) {
        try {
            const response = await fetch(`/api/thermal/accelerators/boost/${acceleratorId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ boostFactor })
            });

            const data = await response.json();

            if (data.success) {
                // Mettre à jour l'état local
                if (this.accelerators[acceleratorId]) {
                    this.accelerators[acceleratorId].boostFactor = boostFactor;
                    this.notifyListeners('boost', { id: acceleratorId, boostFactor });
                }

                // Recharger les statistiques
                await this.loadAcceleratorStats();

                return true;
            } else {
                console.error('Erreur lors de l\'ajustement du facteur de boost:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête d\'ajustement:', error);
            return false;
        }
    }

    /**
     * Applique un boost à une valeur
     * @param {string} type - Type d'opération (processing, memory, connection)
     * @param {number} baseValue - Valeur de base à booster
     * @returns {Promise<number>} - Valeur boostée
     */
    async applyBoost(type, baseValue) {
        try {
            const response = await fetch('/api/thermal/accelerators/apply', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type, baseValue })
            });

            const data = await response.json();

            if (data.success) {
                return data.boostedValue;
            } else {
                console.error('Erreur lors de l\'application du boost:', data.error);
                return baseValue;
            }
        } catch (error) {
            console.error('Erreur lors de la requête d\'application de boost:', error);
            return baseValue;
        }
    }

    /**
     * Optimise la circulation de la mémoire
     * Cette fonction améliore la circulation des informations entre les différentes zones de mémoire
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async optimizeMemoryCirculation() {
        try {
            // Vérifier si l'accélérateur de circulation est activé
            if (!this.accelerators.circulation.enabled) {
                console.warn('L\'accélérateur de circulation est désactivé');
                return false;
            }

            // Vérifier si l'accélérateur de circulation a assez d'énergie
            if (this.accelerators.circulation.energy < 0.3) {
                console.warn('L\'accélérateur de circulation n\'a pas assez d\'énergie');
                return false;
            }

            const response = await fetch('/api/thermal/memory/optimize-circulation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    boostFactor: this.accelerators.circulation.boostFactor,
                    stability: this.accelerators.circulation.stability
                })
            });

            const data = await response.json();

            if (data.success) {
                // Consommer de l'énergie
                this.accelerators.circulation.energy = Math.max(0, this.accelerators.circulation.energy - 0.2);

                // Notifier les écouteurs
                this.notifyListeners('circulation', {
                    success: true,
                    entriesMoved: data.entriesMoved || 0,
                    energyConsumed: 0.2
                });

                // Recharger les statistiques
                await this.loadAcceleratorStats();

                return true;
            } else {
                console.error('Erreur lors de l\'optimisation de la circulation de la mémoire:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête d\'optimisation de la circulation:', error);
            return false;
        }
    }

    /**
     * Optimise le traitement LTX
     * Cette fonction améliore le traitement des flux vidéo et la reconnaissance d'objets
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async optimizeLTXProcessing() {
        try {
            // Vérifier si l'accélérateur LTX est activé
            if (!this.accelerators.ltx.enabled) {
                console.warn('L\'accélérateur LTX est désactivé');
                return false;
            }

            // Vérifier si l'accélérateur LTX a assez d'énergie
            if (this.accelerators.ltx.energy < 0.3) {
                console.warn('L\'accélérateur LTX n\'a pas assez d\'énergie');
                return false;
            }

            const response = await fetch('/api/thermal/ltx/optimize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    boostFactor: this.accelerators.ltx.boostFactor,
                    stability: this.accelerators.ltx.stability
                })
            });

            const data = await response.json();

            if (data.success) {
                // Consommer de l'énergie
                this.accelerators.ltx.energy = Math.max(0, this.accelerators.ltx.energy - 0.2);

                // Notifier les écouteurs
                this.notifyListeners('ltx', {
                    success: true,
                    processingSpeed: data.processingSpeed || 0,
                    detectionAccuracy: data.detectionAccuracy || 0,
                    energyConsumed: 0.2
                });

                // Recharger les statistiques
                await this.loadAcceleratorStats();

                return true;
            } else {
                console.error('Erreur lors de l\'optimisation du traitement LTX:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête d\'optimisation LTX:', error);
            return false;
        }
    }

    /**
     * Réinitialise les accélérateurs
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async resetAccelerators() {
        try {
            const response = await fetch('/api/thermal/accelerators/reset', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                // Recharger les statistiques
                await this.loadAcceleratorStats();

                return true;
            } else {
                console.error('Erreur lors de la réinitialisation des accélérateurs:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête de réinitialisation:', error);
            return false;
        }
    }

    /**
     * Démarre les mises à jour périodiques
     */
    startPeriodicUpdates() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }

        this.updateTimer = setInterval(async () => {
            await this.loadAcceleratorStats();
        }, this.updateInterval);
    }

    /**
     * Arrête les mises à jour périodiques
     */
    stopPeriodicUpdates() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * Ajoute un écouteur pour les mises à jour
     * @param {Function} listener - Fonction à appeler lors des mises à jour
     */
    addListener(listener) {
        if (typeof listener === 'function' && !this.listeners.includes(listener)) {
            this.listeners.push(listener);
        }
    }

    /**
     * Supprime un écouteur
     * @param {Function} listener - Fonction à supprimer
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * Notifie tous les écouteurs d'une mise à jour
     * @param {string} type - Type de mise à jour
     * @param {Object} data - Données de la mise à jour
     */
    notifyListeners(type, data) {
        this.listeners.forEach(listener => {
            try {
                listener(type, data);
            } catch (error) {
                console.error('Erreur dans un écouteur d\'accélérateurs Kyber:', error);
            }
        });
    }

    /**
     * Récupère tous les accélérateurs
     * @returns {Object} - Les accélérateurs
     */
    getAccelerators() {
        return {
            reflexiveBoost: this.accelerators.reflexive.boostFactor,
            thermalBoost: this.accelerators.thermal.boostFactor,
            connectorBoost: this.accelerators.connector.boostFactor,
            circulationBoost: this.accelerators.circulation.boostFactor,
            ltxBoost: this.accelerators.ltx.boostFactor,
            efficiency: this.stats.efficiency || 0.92
        };
    }
}

// Créer une instance unique
const kyberAccelerators = new KyberAccelerators();

// Exporter l'instance
window.kyberAccelerators = kyberAccelerators;
