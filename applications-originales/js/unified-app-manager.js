/**
 * Gestionnaire d'applications unifié pour toutes les interfaces de chat Louna
 * Ce module peut être intégré dans n'importe quelle interface de chat
 */

class UnifiedAppManager {
    constructor() {
        this.apiBaseUrl = '/api/apps';
        this.isInitialized = false;
        this.availableApps = [];
        this.runningApps = [];

        // Mots-clés pour détecter les commandes d'applications
        this.commandKeywords = {
            launch: ['lance', 'ouvre', 'démarre', 'ouvrir', 'lancer', 'démarrer', 'start', 'open'],
            close: ['ferme', 'quitte', 'arrête', 'fermer', 'quitter', 'arrêter', 'stop', 'close', 'quit'],
            list: ['liste', 'applications', 'apps', 'list', 'show']
        };

        this.init();
    }

    async init() {
        try {
            console.log('🚀 Initialisation du gestionnaire d\'applications unifié...');
            await this.loadAvailableApps();

            // Initialiser le scanner système si disponible
            if (typeof window !== 'undefined' && window.systemScanner) {
                this.systemScanner = window.systemScanner;
                console.log('🔍 Scanner système connecté au gestionnaire d\'applications');
            }

            this.isInitialized = true;
            console.log('✅ Gestionnaire d\'applications unifié initialisé');
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation du gestionnaire d\'applications:', error);
        }
    }

    async loadAvailableApps() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/list`);
            const data = await response.json();

            if (data.success) {
                this.availableApps = data.data.apps;
                this.runningApps = data.data.runningApps;
                console.log(`📱 ${this.availableApps.length} applications disponibles chargées`);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des applications:', error);
        }
    }

    /**
     * Détecte si un message contient une commande d'application
     */
    isAppCommand(message) {
        const lowerMessage = message.toLowerCase().trim();

        // Vérifier les mots-clés de commande
        const hasCommandKeyword = Object.values(this.commandKeywords).flat().some(keyword =>
            lowerMessage.includes(keyword)
        );

        if (!hasCommandKeyword) return false;

        // Vérifier si une application est mentionnée
        const hasAppName = this.availableApps.some(app =>
            lowerMessage.includes(app.name.toLowerCase()) ||
            lowerMessage.includes(app.name.replace(/\s+/g, '').toLowerCase())
        );

        return hasCommandKeyword && (hasAppName || lowerMessage.includes('application'));
    }

    /**
     * Vérifie si une application peut être lancée en toute sécurité
     */
    async canSafelyLaunchApp(appName) {
        if (!this.systemScanner) {
            return { canLaunch: true, reason: 'Scanner non disponible - lancement autorisé' };
        }

        const scanResults = this.systemScanner.getScanResults();
        const resourceStatus = this.systemScanner.getCurrentResourceStatus();

        // Vérifier l'état des ressources
        if (resourceStatus === 'critical') {
            return {
                canLaunch: false,
                reason: 'Ressources système critiques - éviter de lancer de nouvelles applications',
                suggestion: 'Fermez d\'abord quelques applications pour libérer de la mémoire'
            };
        }

        // Vérifier si l'application est gourmande en ressources
        const app = this.findAppByName(appName);
        if (app && this.isResourceIntensiveApp(app)) {
            if (resourceStatus === 'warning') {
                return {
                    canLaunch: false,
                    reason: `${appName} est une application gourmande et les ressources sont limitées`,
                    suggestion: 'Attendez que les ressources se libèrent ou fermez d\'autres applications'
                };
            }
        }

        return { canLaunch: true, reason: 'Ressources suffisantes pour le lancement' };
    }

    /**
     * Détermine si une application est gourmande en ressources
     */
    isResourceIntensiveApp(app) {
        const intensiveApps = [
            'xcode', 'photoshop', 'final cut', 'logic pro', 'chrome', 'firefox',
            'video', 'photo', 'game', 'unity', 'blender', 'maya'
        ];

        const appName = app.name.toLowerCase();
        return intensiveApps.some(intensive => appName.includes(intensive));
    }

    /**
     * Trouve une application par nom dans les résultats du scanner
     */
    findAppByName(appName) {
        if (!this.systemScanner) return null;

        const scanResults = this.systemScanner.getScanResults();
        return scanResults.applications?.find(app =>
            app.name.toLowerCase().includes(appName.toLowerCase()) ||
            app.displayName?.toLowerCase().includes(appName.toLowerCase())
        );
    }

    /**
     * Obtient des suggestions d'applications basées sur les capacités
     */
    getAppSuggestions(intent) {
        if (!this.systemScanner) return [];

        const scanResults = this.systemScanner.getScanResults();
        const capabilities = scanResults.capabilities?.applications || {};

        const intentMap = {
            'photo': capabilities.multimedia?.filter(app =>
                app.capabilities?.includes('image_editing') ||
                app.capabilities?.includes('photo_management')
            ) || [],
            'video': capabilities.multimedia?.filter(app =>
                app.capabilities?.includes('video_editing') ||
                app.capabilities?.includes('video_playback')
            ) || [],
            'code': capabilities.development || [],
            'text': capabilities.productivity?.filter(app =>
                app.capabilities?.includes('text_editing')
            ) || [],
            'music': capabilities.multimedia?.filter(app =>
                app.capabilities?.includes('audio_playback') ||
                app.capabilities?.includes('audio_editing')
            ) || []
        };

        return intentMap[intent] || [];
    }

    /**
     * Traite une commande d'application avec intelligence système
     */
    async processAppCommand(message) {
        try {
            console.log('🖥️ Traitement de la commande d\'application:', message);

            const response = await fetch(`${this.apiBaseUrl}/command`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            const result = await response.json();

            if (result.success) {
                // Mettre à jour la liste des applications en cours d'exécution
                await this.loadAvailableApps();

                return {
                    success: true,
                    message: result.message,
                    action: result.action,
                    data: result.data
                };
            } else {
                return {
                    success: false,
                    message: result.message || 'Erreur lors de l\'exécution de la commande',
                    suggestions: result.suggestions || []
                };
            }
        } catch (error) {
            console.error('❌ Erreur lors du traitement de la commande:', error);
            return {
                success: false,
                message: 'Erreur de communication avec le serveur'
            };
        }
    }

    /**
     * Lance une application spécifique avec vérification intelligente
     */
    async launchApp(appName) {
        try {
            // Vérifier si l'application peut être lancée en toute sécurité
            const safetyCheck = await this.canSafelyLaunchApp(appName);

            if (!safetyCheck.canLaunch) {
                console.warn('⚠️ Lancement bloqué pour des raisons de sécurité:', safetyCheck.reason);
                return {
                    success: false,
                    error: safetyCheck.reason,
                    suggestion: safetyCheck.suggestion,
                    blocked: true
                };
            }

            console.log('✅ Vérification de sécurité passée:', safetyCheck.reason);

            const response = await fetch(`${this.apiBaseUrl}/launch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ appName })
            });

            const result = await response.json();

            if (result.success) {
                await this.loadAvailableApps();

                // Notifier le scanner du lancement
                if (this.systemScanner) {
                    this.systemScanner.notifyUI('appLaunched', { appName, timestamp: Date.now() });
                }
            }

            return result;
        } catch (error) {
            console.error('❌ Erreur lors du lancement de l\'application:', error);
            return {
                success: false,
                error: 'Erreur de communication avec le serveur'
            };
        }
    }

    /**
     * Ferme une application spécifique
     */
    async closeApp(appName) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/close`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ appName })
            });

            const result = await response.json();

            if (result.success) {
                await this.loadAvailableApps();
            }

            return result;
        } catch (error) {
            console.error('❌ Erreur lors de la fermeture de l\'application:', error);
            return {
                success: false,
                error: 'Erreur de communication avec le serveur'
            };
        }
    }

    /**
     * Obtient le statut d'une application
     */
    async getAppStatus(appName) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/status/${encodeURIComponent(appName)}`);
            return await response.json();
        } catch (error) {
            console.error('❌ Erreur lors de la récupération du statut:', error);
            return {
                success: false,
                error: 'Erreur de communication avec le serveur'
            };
        }
    }

    /**
     * Génère une réponse formatée pour l'interface de chat
     */
    formatAppResponse(result) {
        if (!result) return 'Erreur lors du traitement de la commande.';

        if (result.success) {
            let response = `✅ **${result.message}**\n\n`;

            if (result.action === 'launch' && result.data) {
                response += `🚀 **Application lancée :** ${result.data.appName}\n`;
                response += `📁 **Chemin :** ${result.data.path}\n`;
            } else if (result.action === 'close' && result.data) {
                response += `🔴 **Application fermée :** ${result.data.appName}\n`;
            } else if (result.action === 'list' && result.data) {
                response += `📱 **Applications disponibles :** ${result.data.totalApps}\n`;
                response += `🟢 **Applications en cours :** ${result.data.runningApps.length}\n\n`;

                if (result.data.runningApps.length > 0) {
                    response += '**Applications en cours d\'exécution :**\n';
                    result.data.runningApps.forEach(app => {
                        response += `• ${app.name}\n`;
                    });
                }
            }

            return response;
        } else {
            let response = `❌ **Erreur :** ${result.message}\n\n`;

            if (result.suggestions && result.suggestions.length > 0) {
                response += '**Suggestions :**\n';
                result.suggestions.forEach(suggestion => {
                    response += `• ${suggestion}\n`;
                });
            }

            return response;
        }
    }

    /**
     * Intègre le gestionnaire dans une fonction de chat existante
     */
    integrateWithChatFunction(originalSendFunction) {
        return async (message, ...args) => {
            // Vérifier si c'est une commande d'application
            if (this.isAppCommand(message)) {
                console.log('🖥️ Commande d\'application détectée, traitement par le gestionnaire unifié');

                const result = await this.processAppCommand(message);
                const formattedResponse = this.formatAppResponse(result);

                // Retourner la réponse formatée pour l'interface
                return {
                    success: true,
                    response: formattedResponse,
                    isAppCommand: true,
                    appResult: result
                };
            }

            // Si ce n'est pas une commande d'application, utiliser la fonction originale
            return originalSendFunction(message, ...args);
        };
    }

    /**
     * Ajoute des boutons d'applications à une interface
     */
    addAppButtons(container) {
        if (!container) return;

        const appButtonsHtml = `
            <div class="app-quick-actions" style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4 style="margin: 0 0 10px 0; color: #ff69b4;">🖥️ Actions Rapides</h4>
                <div class="app-buttons" style="display: flex; flex-wrap: wrap; gap: 8px;">
                    <button onclick="window.unifiedAppManager.launchApp('Safari')" class="app-btn" style="padding: 5px 10px; background: #ff69b4; color: white; border: none; border-radius: 4px; cursor: pointer;">🌐 Safari</button>
                    <button onclick="window.unifiedAppManager.launchApp('TextEdit')" class="app-btn" style="padding: 5px 10px; background: #ff69b4; color: white; border: none; border-radius: 4px; cursor: pointer;">📝 TextEdit</button>
                    <button onclick="window.unifiedAppManager.launchApp('Terminal')" class="app-btn" style="padding: 5px 10px; background: #ff69b4; color: white; border: none; border-radius: 4px; cursor: pointer;">💻 Terminal</button>
                    <button onclick="window.unifiedAppManager.loadAvailableApps()" class="app-btn" style="padding: 5px 10px; background: #333; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 Actualiser</button>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', appButtonsHtml);
    }
}

// Initialiser le gestionnaire global
if (typeof window !== 'undefined') {
    window.unifiedAppManager = new UnifiedAppManager();

    // Fonction utilitaire pour intégrer facilement dans les interfaces existantes
    window.integrateAppManager = function(sendMessageFunction) {
        if (window.unifiedAppManager && sendMessageFunction) {
            return window.unifiedAppManager.integrateWithChatFunction(sendMessageFunction);
        }
        return sendMessageFunction;
    };

    console.log('🚀 Gestionnaire d\'applications unifié disponible globalement');
}

// Export pour les modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedAppManager;
}
