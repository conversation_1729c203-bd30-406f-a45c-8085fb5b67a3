/**
 * AGENT CONNECTION DIAGNOSTIC v2.1.0
 * Diagnostic de connexion pour l'agent Agent Local LOUNA
 * Créé par <PERSON>, Guadeloupe
 */

class AgentConnectionDiagnostic {
    constructor() {
        this.diagnosticResults = {};
        this.connectionStatus = 'unknown';
        this.lastCheck = null;
    }

    async runFullDiagnostic() {
        console.log('🔍 Démarrage du diagnostic de connexion agent...');
        
        const results = {
            timestamp: new Date().toISOString(),
            tests: {}
        };

        // Test 1: Connectivité serveur local
        results.tests.localServer = await this.testLocalServer();
        
        // Test 2: API Chat endpoint
        results.tests.chatAPI = await this.testChatAPI();
        
        // Test 3: Configuration API Keys
        results.tests.apiKeys = await this.testAPIKeys();
        
        // Test 4: Timeout et performance
        results.tests.performance = await this.testPerformance();
        
        // Test 5: Fallback systems
        results.tests.fallback = await this.testFallbackSystems();

        // Analyse globale
        results.analysis = this.analyzeResults(results.tests);
        
        this.diagnosticResults = results;
        this.lastCheck = Date.now();
        
        return results;
    }

    async testLocalServer() {
        try {
            const startTime = Date.now();
            const response = await fetch('/api/health', {
                method: 'GET',
                timeout: 5000
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            return {
                status: response.ok ? 'success' : 'warning',
                responseTime: responseTime,
                statusCode: response.status,
                message: response.ok ? 'Serveur local accessible' : `Erreur ${response.status}`,
                details: {
                    url: '/api/health',
                    method: 'GET',
                    responseTime: `${responseTime}ms`
                }
            };
        } catch (error) {
            return {
                status: 'error',
                message: 'Serveur local inaccessible',
                error: error.message,
                details: {
                    errorType: error.name,
                    suggestion: 'Vérifier que le serveur Node.js est démarré'
                }
            };
        }
    }

    async testChatAPI() {
        try {
            const startTime = Date.now();
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: 'Test de connexion',
                    testMode: true
                }),
                timeout: 15000
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            const data = await response.json();
            
            return {
                status: data.success ? 'success' : 'error',
                responseTime: responseTime,
                statusCode: response.status,
                message: data.success ? 'API Chat fonctionnelle' : (data.error || 'Erreur API Chat'),
                details: {
                    url: '/api/chat',
                    method: 'POST',
                    responseTime: `${responseTime}ms`,
                    hasResponse: !!data.response,
                    responseLength: data.response ? data.response.length : 0
                }
            };
        } catch (error) {
            return {
                status: 'error',
                message: 'API Chat inaccessible',
                error: error.message,
                details: {
                    errorType: error.name,
                    suggestion: 'Vérifier la configuration de l\'agent Agent Local LOUNA'
                }
            };
        }
    }

    async testAPIKeys() {
        try {
            const response = await fetch('/api/config/check', {
                method: 'GET'
            });
            
            if (response.ok) {
                const data = await response.json();
                return {
                    status: data.hasValidKeys ? 'success' : 'warning',
                    message: data.hasValidKeys ? 'Clés API configurées' : 'Clés API manquantes',
                    details: {
                        anthropicKey: data.keys?.anthropic ? 'Configurée' : 'Manquante',
                        openaiKey: data.keys?.openai ? 'Configurée' : 'Manquante',
                        suggestion: !data.hasValidKeys ? 'Configurer les clés API dans les variables d\'environnement' : null
                    }
                };
            } else {
                return {
                    status: 'warning',
                    message: 'Impossible de vérifier les clés API',
                    details: {
                        statusCode: response.status,
                        suggestion: 'Endpoint de vérification non disponible'
                    }
                };
            }
        } catch (error) {
            return {
                status: 'warning',
                message: 'Test des clés API échoué',
                error: error.message,
                details: {
                    suggestion: 'Vérifier manuellement les variables d\'environnement'
                }
            };
        }
    }

    async testPerformance() {
        const tests = [];
        
        // Test de latence simple
        for (let i = 0; i < 3; i++) {
            try {
                const startTime = Date.now();
                await fetch('/api/ping');
                const endTime = Date.now();
                tests.push(endTime - startTime);
            } catch (error) {
                tests.push(-1); // Erreur
            }
        }
        
        const validTests = tests.filter(t => t > 0);
        const avgLatency = validTests.length > 0 ? 
            validTests.reduce((a, b) => a + b, 0) / validTests.length : -1;
        
        return {
            status: avgLatency > 0 && avgLatency < 1000 ? 'success' : 
                   avgLatency > 0 ? 'warning' : 'error',
            message: avgLatency > 0 ? 
                `Latence moyenne: ${avgLatency.toFixed(0)}ms` : 
                'Tests de performance échoués',
            details: {
                tests: tests,
                averageLatency: avgLatency,
                successRate: `${validTests.length}/3`,
                performance: avgLatency < 200 ? 'Excellente' :
                           avgLatency < 500 ? 'Bonne' :
                           avgLatency < 1000 ? 'Acceptable' : 'Lente'
            }
        };
    }

    async testFallbackSystems() {
        const results = {
            simulator: false,
            qiManager: false,
            globalConfig: false
        };
        
        // Test simulateur Louna
        if (window.lounaSimulator) {
            try {
                const testResponse = window.lounaSimulator.generateResponse('Test');
                results.simulator = testResponse && testResponse.length > 0;
            } catch (error) {
                results.simulator = false;
            }
        }
        
        // Test QI Manager
        if (window.qiManager) {
            try {
                const qi = window.qiManager.getCurrentQI();
                results.qiManager = typeof qi === 'number' && qi > 0;
            } catch (error) {
                results.qiManager = false;
            }
        }
        
        // Test configuration globale
        if (window.LOUNA_CONFIG) {
            results.globalConfig = true;
        }
        
        const workingSystems = Object.values(results).filter(Boolean).length;
        
        return {
            status: workingSystems >= 2 ? 'success' : workingSystems >= 1 ? 'warning' : 'error',
            message: `${workingSystems}/3 systèmes de fallback opérationnels`,
            details: {
                ...results,
                recommendation: workingSystems === 0 ? 
                    'Aucun système de fallback disponible' :
                    'Systèmes de fallback disponibles pour compenser'
            }
        };
    }

    analyzeResults(tests) {
        const statuses = Object.values(tests).map(test => test.status);
        const successCount = statuses.filter(s => s === 'success').length;
        const warningCount = statuses.filter(s => s === 'warning').length;
        const errorCount = statuses.filter(s => s === 'error').length;
        
        let overallStatus, recommendation, priority;
        
        if (errorCount === 0 && warningCount <= 1) {
            overallStatus = 'excellent';
            recommendation = 'Tous les systèmes fonctionnent correctement. L\'agent devrait être pleinement opérationnel.';
            priority = 'low';
        } else if (errorCount <= 1 && successCount >= 3) {
            overallStatus = 'good';
            recommendation = 'La plupart des systèmes fonctionnent. Quelques optimisations possibles.';
            priority = 'medium';
        } else if (errorCount <= 2) {
            overallStatus = 'degraded';
            recommendation = 'Fonctionnement dégradé. Utilisation des systèmes de fallback recommandée.';
            priority = 'high';
        } else {
            overallStatus = 'critical';
            recommendation = 'Problèmes critiques détectés. Intervention nécessaire.';
            priority = 'critical';
        }
        
        return {
            overallStatus,
            recommendation,
            priority,
            summary: {
                total: statuses.length,
                success: successCount,
                warning: warningCount,
                error: errorCount
            },
            nextSteps: this.generateNextSteps(tests)
        };
    }

    generateNextSteps(tests) {
        const steps = [];
        
        if (tests.localServer.status === 'error') {
            steps.push('1. Redémarrer le serveur Node.js');
            steps.push('2. Vérifier le port 3001');
        }
        
        if (tests.chatAPI.status === 'error') {
            steps.push('3. Vérifier la configuration de l\'agent Agent Local LOUNA');
            steps.push('4. Contrôler les clés API');
        }
        
        if (tests.apiKeys.status !== 'success') {
            steps.push('5. Configurer les variables d\'environnement');
            steps.push('6. Redémarrer l\'application après configuration');
        }
        
        if (tests.performance.status === 'warning') {
            steps.push('7. Optimiser la connexion réseau');
        }
        
        if (tests.fallback.status === 'success') {
            steps.push('8. Utiliser le simulateur Louna en attendant');
        }
        
        if (steps.length === 0) {
            steps.push('Aucune action requise - Tous les systèmes fonctionnent');
        }
        
        return steps;
    }

    getConnectionStatus() {
        if (!this.diagnosticResults.analysis) {
            return 'unknown';
        }
        
        return this.diagnosticResults.analysis.overallStatus;
    }

    getLastDiagnosticReport() {
        return this.diagnosticResults;
    }

    generateReport() {
        if (!this.diagnosticResults.analysis) {
            return 'Aucun diagnostic effectué';
        }
        
        const { analysis, tests } = this.diagnosticResults;
        
        let report = `🔍 RAPPORT DE DIAGNOSTIC AGENT AGENT LOCAL\n`;
        report += `==========================================\n\n`;
        report += `Statut global: ${analysis.overallStatus.toUpperCase()}\n`;
        report += `Priorité: ${analysis.priority.toUpperCase()}\n`;
        report += `Date: ${new Date(this.diagnosticResults.timestamp).toLocaleString()}\n\n`;
        
        report += `RÉSULTATS DES TESTS:\n`;
        report += `--------------------\n`;
        Object.entries(tests).forEach(([testName, result]) => {
            const icon = result.status === 'success' ? '✅' : 
                        result.status === 'warning' ? '⚠️' : '❌';
            report += `${icon} ${testName}: ${result.message}\n`;
        });
        
        report += `\nRECOMMENDATION:\n`;
        report += `${analysis.recommendation}\n\n`;
        
        report += `PROCHAINES ÉTAPES:\n`;
        report += `------------------\n`;
        analysis.nextSteps.forEach(step => {
            report += `${step}\n`;
        });
        
        return report;
    }
}

// Export global
window.AgentConnectionDiagnostic = AgentConnectionDiagnostic;

// Instance globale
window.agentDiagnostic = new AgentConnectionDiagnostic();

console.log('🔍 Agent Connection Diagnostic v2.1.0 initialisé');
