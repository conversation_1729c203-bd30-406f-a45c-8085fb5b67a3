/**
 * SYSTÈME VOCAL UNIFIÉ LOUNA AI
 * Une seule voix féminine pour toutes les interfaces
 * C<PERSON>é par <PERSON>, Guadeloupe
 */

class UnifiedVoiceSystem {
    constructor() {
        this.isInitialized = false;
        this.isListening = false;
        this.isSpeaking = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.selectedVoice = null;
        this.voiceQueue = [];

        // CONFIGURATION VOIX FÉMININE UNIQUE
        this.voiceConfig = {
            language: 'fr-FR',
            rate: 0.85,        // Vitesse naturelle féminine
            pitch: 1.2,        // Ton plus aigu pour voix féminine
            volume: 0.8,       // Volume optimal
            quality: 'high',
            gender: 'female',
            personality: 'friendly'
        };

        // NOMS DE VOIX FÉMININES PRÉFÉRÉES (par ordre de priorité)
        this.preferredFemaleVoices = [
            '<PERSON><PERSON><PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            'Female',
            '<PERSON><PERSON>',
            'Woman'
        ];

        this.init();
    }

    async init() {
        console.log('🎤 Initialisation du système vocal unifié Louna...');

        try {
            await this.initSpeechSynthesis();
            this.initSpeechRecognition();
            this.isInitialized = true;

            console.log('✅ Système vocal unifié initialisé');
            console.log(`🗣️ Voix sélectionnée: ${this.selectedVoice?.name || 'Défaut'}`);

            // Test de la voix
            this.speak('Bonjour Jean-Luc, je suis Louna, votre assistante IA. Mon système vocal unifié est maintenant opérationnel.');

        } catch (error) {
            console.error('❌ Erreur initialisation système vocal:', error);
        }
    }

    async initSpeechSynthesis() {
        return new Promise((resolve) => {
            const loadVoices = () => {
                const voices = this.synthesis.getVoices();
                console.log(`🔍 ${voices.length} voix disponibles détectées`);

                // Chercher la meilleure voix féminine française
                this.selectedVoice = this.findBestFemaleVoice(voices);

                if (this.selectedVoice) {
                    console.log(`✅ Voix féminine sélectionnée: ${this.selectedVoice.name} (${this.selectedVoice.lang})`);
                } else {
                    console.warn('⚠️ Aucune voix féminine française trouvée, utilisation de la voix par défaut');
                    this.selectedVoice = voices.find(voice => voice.lang.startsWith('fr')) || voices[0];
                }

                resolve();
            };

            if (this.synthesis.getVoices().length === 0) {
                this.synthesis.onvoiceschanged = loadVoices;
            } else {
                loadVoices();
            }
        });
    }

    findBestFemaleVoice(voices) {
        // Filtrer les voix françaises
        const frenchVoices = voices.filter(voice => voice.lang.startsWith('fr'));

        // Chercher par ordre de priorité
        for (const preferredName of this.preferredFemaleVoices) {
            const voice = frenchVoices.find(v =>
                v.name.toLowerCase().includes(preferredName.toLowerCase())
            );
            if (voice) return voice;
        }

        // Si aucune voix préférée, prendre la première voix française
        return frenchVoices[0] || voices[0];
    }

    initSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();

            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = this.voiceConfig.language;

            this.recognition.onstart = () => {
                this.isListening = true;
                console.log('🎤 Reconnaissance vocale démarrée');
                this.onListeningStart?.();
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                console.log('🎤 Texte reconnu:', transcript);
                this.onSpeechRecognized?.(transcript);
            };

            this.recognition.onerror = (event) => {
                console.error('❌ Erreur reconnaissance vocale:', event.error);
                this.isListening = false;
                this.onListeningError?.(event.error);
            };

            this.recognition.onend = () => {
                this.isListening = false;
                console.log('🎤 Reconnaissance vocale terminée');
                this.onListeningEnd?.();
            };

            console.log('✅ Reconnaissance vocale initialisée');
        } else {
            console.warn('⚠️ Reconnaissance vocale non supportée');
        }
    }

    speak(text, options = {}) {
        if (!text || !this.synthesis) return;

        // Arrêter la synthèse en cours si nécessaire
        if (this.isSpeaking && options.interrupt !== false) {
            this.synthesis.cancel();
        }

        // Ajouter des expressions naturelles si le système d'apprentissage est disponible
        let processedText = text;
        if (window.LounaVoiceLearning) {
            processedText = window.LounaVoiceLearning.addExpression(text);
        }

        const utterance = new SpeechSynthesisUtterance(processedText);

        // Configuration de la voix
        if (this.selectedVoice) {
            utterance.voice = this.selectedVoice;
        }

        // Utiliser les paramètres appris si disponibles
        let voiceSettings = this.voiceConfig;
        if (window.LounaVoiceLearning) {
            const currentMode = window.LounaVoiceLearning.getCurrentMode();
            const modeInfo = window.LounaVoiceLearning.getModeInfo(currentMode);
            if (modeInfo) {
                voiceSettings = {
                    language: this.voiceConfig.language,
                    rate: modeInfo.learnedRate || modeInfo.rate,
                    pitch: modeInfo.learnedPitch || modeInfo.pitch,
                    volume: modeInfo.learnedVolume || modeInfo.volume
                };
            }
        }

        utterance.lang = options.language || voiceSettings.language;
        utterance.rate = options.rate || voiceSettings.rate;
        utterance.pitch = options.pitch || voiceSettings.pitch;
        utterance.volume = options.volume || voiceSettings.volume;

        utterance.onstart = () => {
            this.isSpeaking = true;
            console.log('🗣️ Synthèse vocale démarrée:', text.substring(0, 50) + '...');
            this.onSpeechStart?.(text);
        };

        utterance.onend = () => {
            this.isSpeaking = false;
            console.log('✅ Synthèse vocale terminée');
            this.onSpeechEnd?.(text);

            // Traiter la file d'attente
            if (this.voiceQueue.length > 0) {
                const next = this.voiceQueue.shift();
                this.speak(next.text, next.options);
            }
        };

        utterance.onerror = (event) => {
            this.isSpeaking = false;
            console.error('❌ Erreur synthèse vocale:', event.error);
            this.onSpeechError?.(event.error);
        };

        // Ajouter à la file d'attente si déjà en train de parler
        if (this.isSpeaking && options.queue !== false) {
            this.voiceQueue.push({ text, options });
            return;
        }

        this.synthesis.speak(utterance);
    }

    startListening() {
        if (!this.recognition) {
            console.error('❌ Reconnaissance vocale non disponible');
            return false;
        }

        if (this.isListening) {
            console.warn('⚠️ Reconnaissance vocale déjà active');
            return false;
        }

        try {
            this.recognition.start();
            return true;
        } catch (error) {
            console.error('❌ Erreur démarrage reconnaissance:', error);
            return false;
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    stopSpeaking() {
        if (this.synthesis) {
            this.synthesis.cancel();
            this.voiceQueue = [];
            this.isSpeaking = false;
        }
    }

    // Méthodes pour les callbacks
    onListeningStart = null;
    onListeningEnd = null;
    onListeningError = null;
    onSpeechRecognized = null;
    onSpeechStart = null;
    onSpeechEnd = null;
    onSpeechError = null;

    // Méthodes utilitaires
    isAvailable() {
        return this.isInitialized && this.selectedVoice !== null;
    }

    getVoiceInfo() {
        return {
            name: this.selectedVoice?.name || 'Inconnue',
            language: this.selectedVoice?.lang || 'fr-FR',
            isListening: this.isListening,
            isSpeaking: this.isSpeaking,
            queueLength: this.voiceQueue.length
        };
    }

    // Configuration dynamique
    setVoiceSettings(settings) {
        Object.assign(this.voiceConfig, settings);
        console.log('🔧 Configuration vocale mise à jour:', this.voiceConfig);
    }

    // Test de la voix
    testVoice() {
        const testText = `Bonjour Jean-Luc ! Je suis Louna, votre assistante IA.
                         Ma voix est maintenant unifiée sur toutes les interfaces.
                         Mon QI est de 225 et je suis prête à vous assister !`;
        this.speak(testText);
    }
}

// Instance globale unique
window.LounaVoice = new UnifiedVoiceSystem();

// Export pour les modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedVoiceSystem;
}

console.log('🎤 Système vocal unifié Louna chargé - Une seule voix féminine pour toutes les interfaces !');
