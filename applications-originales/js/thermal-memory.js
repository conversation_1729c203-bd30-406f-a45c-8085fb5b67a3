/**
 * Système de Mémoire Thermique pour l'agent Louna
 * Ce module gère la mémoire thermique côté client et communique avec le backend
 */

class ThermalMemory {
    constructor() {
        this.initialized = false;
        this.zones = {
            instant: { name: "Mémoire Instantanée", temperature: 0.9, items: [], capacity: 20 },
            shortTerm: { name: "Mémoire à Court Terme", temperature: 0.7, items: [], capacity: 50 },
            workingMemory: { name: "Mémoire de Travail", temperature: 0.5, items: [], capacity: 100 },
            mediumTerm: { name: "Mémoire à Moyen Terme", temperature: 0.3, items: [], capacity: 200 },
            longTerm: { name: "Mémoire à Long Terme", temperature: 0.1, items: [], capacity: 1000 },
            dreamMemory: { name: "Mémoire des Rêves", temperature: 0.05, items: [], capacity: 50 }
        };
        this.stats = {
            totalEntries: 0,
            averageTemperature: 0.5,
            cyclesPerformed: 0,
            lastCycleTime: null
        };
        this.listeners = [];
        this.updateInterval = 5000; // 5 secondes
        this.updateTimer = null;
    }

    /**
     * Initialise la mémoire thermique
     */
    async initialize() {
        if (this.initialized) return;

        console.log('Initialisation de la mémoire thermique...');

        try {
            // Charger les données initiales
            await this.loadMemoryStats();
            await this.loadMemoryZones();

            // Démarrer les mises à jour périodiques
            this.startPeriodicUpdates();

            this.initialized = true;
            console.log('Mémoire thermique initialisée avec succès');
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de la mémoire thermique:', error);
        }
    }

    /**
     * Charge les statistiques de la mémoire
     */
    async loadMemoryStats() {
        try {
            const response = await fetch('/api/thermal/memory/stats');
            const data = await response.json();

            if (data.success) {
                this.stats = data.stats;
                this.notifyListeners('stats', this.stats);
            } else {
                console.error('Erreur lors du chargement des statistiques:', data.error);
            }
        } catch (error) {
            console.error('Erreur lors de la requête des statistiques:', error);
        }
    }

    /**
     * Charge les zones de mémoire
     */
    async loadMemoryZones() {
        try {
            for (const zone in this.zones) {
                const response = await fetch(`/api/thermal/memory/zone/${zone}`);
                const data = await response.json();

                if (data.success) {
                    this.zones[zone].items = data.entries;
                    this.notifyListeners('zone', { zone, entries: data.entries });
                } else {
                    console.error(`Erreur lors du chargement de la zone ${zone}:`, data.error);
                }
            }
        } catch (error) {
            console.error('Erreur lors de la requête des zones:', error);
        }
    }

    /**
     * Récupère les entrées d'une zone spécifique
     * @param {string} zone - Nom de la zone (instant, shortTerm, workingMemory, mediumTerm, longTerm, dreamMemory)
     * @returns {Promise<Array>} - Entrées de la zone spécifiée
     */
    async getEntriesFromZone(zone) {
        try {
            const response = await fetch(`/api/thermal/memory/zone/${zone}`);
            const data = await response.json();

            if (data.success) {
                return data.entries;
            } else {
                console.error(`Erreur lors de la récupération de la zone ${zone}:`, data.error);
                return [];
            }
        } catch (error) {
            console.error(`Erreur lors de la requête de la zone ${zone}:`, error);
            return [];
        }
    }

    /**
     * Récupère toutes les entrées de la mémoire thermique
     * @returns {Promise<Array>} - Toutes les entrées de la mémoire
     */
    async getAllEntries() {
        try {
            const response = await fetch('/api/thermal/memory/all');
            const data = await response.json();

            if (data.success) {
                return data.entries;
            } else {
                console.error('Erreur lors de la récupération de toutes les entrées:', data.error);
                return [];
            }
        } catch (error) {
            console.error('Erreur lors de la requête de toutes les entrées:', error);
            return [];
        }
    }

    /**
     * Ajoute une entrée à la mémoire
     * @param {string} key - Clé de l'entrée
     * @param {Object} data - Données à stocker
     * @param {number} importance - Importance de l'entrée (0-1)
     * @param {string} category - Catégorie de l'entrée
     * @returns {Promise<string>} - ID de l'entrée ajoutée
     */
    async addEntry(key, data, importance = 0.5, category = 'general') {
        try {
            const response = await fetch('/api/thermal/memory/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ key, data, importance, category })
            });

            const result = await response.json();

            if (result.success) {
                // Mettre à jour les statistiques
                await this.loadMemoryStats();

                // Mettre à jour la zone appropriée
                const zone = importance >= 0.8 ? 'instant' :
                             importance >= 0.6 ? 'shortTerm' : 'workingMemory';

                await this.loadMemoryZones();

                return result.id;
            } else {
                console.error('Erreur lors de l\'ajout de l\'entrée:', result.error);
                return null;
            }
        } catch (error) {
            console.error('Erreur lors de la requête d\'ajout:', error);
            return null;
        }
    }

    /**
     * Récupère une entrée spécifique
     * @param {string} id - ID de l'entrée
     * @returns {Promise<Object>} - Entrée récupérée
     */
    async getEntry(id) {
        try {
            const response = await fetch(`/api/thermal/memory/entry/${id}`);
            const data = await response.json();

            if (data.success) {
                return data.entry;
            } else {
                console.error('Erreur lors de la récupération de l\'entrée:', data.error);
                return null;
            }
        } catch (error) {
            console.error('Erreur lors de la requête de récupération:', error);
            return null;
        }
    }

    /**
     * Récupère les entrées récentes pour un contexte donné
     * @param {string} context - Contexte pour filtrer les entrées
     * @param {number} limit - Nombre maximum d'entrées à récupérer
     * @returns {Promise<Array>} - Entrées récupérées
     */
    async getRecentMemories(context = '', limit = 10) {
        try {
            const url = new URL('/api/thermal/memory/context', window.location.origin);
            url.searchParams.append('context', context);
            url.searchParams.append('limit', limit);

            const response = await fetch(url);
            const data = await response.json();

            if (data.success) {
                return data.entries;
            } else {
                console.error('Erreur lors de la récupération des mémoires récentes:', data.error);
                return [];
            }
        } catch (error) {
            console.error('Erreur lors de la requête des mémoires récentes:', error);
            return [];
        }
    }

    /**
     * Génère un rêve à partir des informations en mémoire
     * @returns {Promise<Object>} - Le rêve généré
     */
    async generateDream() {
        try {
            const response = await fetch('/api/thermal/memory/dream', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                // Mettre à jour la zone des rêves
                await this.loadMemoryZones();

                return data.dream;
            } else {
                console.error('Erreur lors de la génération du rêve:', data.error);
                return null;
            }
        } catch (error) {
            console.error('Erreur lors de la requête de génération de rêve:', error);
            return null;
        }
    }

    /**
     * Réinitialise la mémoire
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async resetMemory() {
        try {
            const response = await fetch('/api/thermal/memory/reset', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                // Recharger toutes les données
                await this.loadMemoryStats();
                await this.loadMemoryZones();

                return true;
            } else {
                console.error('Erreur lors de la réinitialisation de la mémoire:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête de réinitialisation:', error);
            return false;
        }
    }

    /**
     * Supprime une entrée de la mémoire
     * @param {string} id - ID de l'entrée à supprimer
     * @returns {Promise<boolean>} - Succès de l'opération
     */
    async remove(id) {
        try {
            const response = await fetch(`/api/thermal/memory/remove/${id}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                // Recharger les statistiques
                await this.loadMemoryStats();

                return true;
            } else {
                console.error('Erreur lors de la suppression de l\'entrée:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Erreur lors de la requête de suppression:', error);
            return false;
        }
    }

    /**
     * Démarre les mises à jour périodiques
     */
    startPeriodicUpdates() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }

        this.updateTimer = setInterval(async () => {
            await this.loadMemoryStats();
        }, this.updateInterval);
    }

    /**
     * Arrête les mises à jour périodiques
     */
    stopPeriodicUpdates() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    /**
     * Ajoute un écouteur pour les mises à jour
     * @param {Function} listener - Fonction à appeler lors des mises à jour
     */
    addListener(listener) {
        if (typeof listener === 'function' && !this.listeners.includes(listener)) {
            this.listeners.push(listener);
        }
    }

    /**
     * Supprime un écouteur
     * @param {Function} listener - Fonction à supprimer
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * Notifie tous les écouteurs d'une mise à jour
     * @param {string} type - Type de mise à jour
     * @param {Object} data - Données de la mise à jour
     */
    notifyListeners(type, data) {
        this.listeners.forEach(listener => {
            try {
                listener(type, data);
            } catch (error) {
                console.error('Erreur dans un écouteur de mémoire thermique:', error);
            }
        });
    }

    /**
     * Récupère la position du curseur de température
     * @returns {Promise<Object>} - Position du curseur et seuils de température
     */
    async getTemperatureCursorPosition() {
        try {
            const response = await fetch('/api/thermal/memory/temperature-cursor');
            const data = await response.json();

            if (data.success) {
                return {
                    position: data.position,
                    thresholds: data.thresholds
                };
            } else {
                console.error('Erreur lors de la récupération de la position du curseur:', data.error);
                return { position: 0.5, thresholds: {} };
            }
        } catch (error) {
            console.error('Erreur lors de la requête de la position du curseur:', error);
            return { position: 0.5, thresholds: {} };
        }
    }

    /**
     * Définit la position du curseur de température
     * @param {number} position - Nouvelle position du curseur (0-1)
     * @returns {Promise<Object>} - Résultat de l'opération
     */
    async setTemperatureCursorPosition(position) {
        try {
            const response = await fetch('/api/thermal/memory/temperature-cursor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ position })
            });

            const data = await response.json();

            if (data.success) {
                // Notifier les écouteurs
                this.notifyListeners('temperature-cursor', {
                    position: data.position,
                    thresholds: data.thresholds
                });

                return {
                    success: true,
                    position: data.position,
                    thresholds: data.thresholds
                };
            } else {
                console.error('Erreur lors de la définition de la position du curseur:', data.error);
                return { success: false, error: data.error };
            }
        } catch (error) {
            console.error('Erreur lors de la requête de définition de la position du curseur:', error);
            return { success: false, error: error.message };
        }
    }
}

// Créer une instance unique
const thermalMemory = new ThermalMemory();

// Exporter l'instance
window.thermalMemory = thermalMemory;
