/* ===== LOUNA NAVIGATION SYSTEM ===== */

// Fonction pour corriger les boutons de navigation en double
function fixDuplicateNavigation() {
    const currentPath = window.location.pathname;

    // Si on n'est pas sur la page principale, masquer les contrôles de sécurité
    if (!currentPath.includes('interface-originale-complete.html')) {
        const securityControls = document.querySelector('.security-controls');
        if (securityControls) {
            securityControls.style.display = 'none';
        }

        // S'assurer que l'en-tête unifié est visible
        const lounaHeader = document.querySelector('.louna-header');
        if (lounaHeader) {
            lounaHeader.style.display = 'block';
            lounaHeader.style.position = 'relative';
            lounaHeader.style.zIndex = '1000';
        }
    }
}

// Fonction de navigation globale
function goToHome() {
    try {
        if (window.showInfo) window.showInfo('🔄 Navigation vers l\'accueil...');
        window.location.href = '../interface-originale-complete.html';
    } catch (error) {
        console.error('❌ Erreur navigation accueil:', error);
        window.history.back();
    }
}

class LounaNavigation {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.navigationItems = [
            {
                id: 'home',
                title: 'Hub Central',
                icon: 'fas fa-home',
                url: '/',
                description: 'Page d\'accueil avec toutes les applications'
            },
            {
                id: 'chat',
                title: 'Chat Intelligent',
                icon: 'fas fa-comments',
                url: '/chat',
                description: 'Interface de conversation avec Agent Local LOUNA'
            },
            {
                id: 'memory',
                title: 'Mémoire Thermique',
                icon: 'fas fa-fire',
                url: '/futuristic-interface.html',
                description: 'Système de mémoire thermique avec 6 zones'
            },
            {
                id: 'brain',
                title: 'Visualisation 3D',
                icon: 'fas fa-brain',
                url: '/brain-visualization.html',
                description: 'Visualisation 3D du cerveau artificiel'
            },
            {
                id: 'monitoring',
                title: 'Monitoring QI',
                icon: 'fas fa-yin-yang',
                url: '/qi-neuron-monitor.html',
                description: 'Surveillance QI et activité neuronale'
            },
            {
                id: 'kyber',
                title: 'Accélérateurs Kyber',
                icon: 'fas fa-bolt',
                url: '/kyber-dashboard.html',
                description: 'Système d\'accélération quantique'
            },
            {
                id: 'generation',
                title: 'Studio de Génération',
                icon: 'fas fa-magic',
                url: '/generation-studio.html',
                description: 'Création de contenu multimédia'
            },
            {
                id: 'voice',
                title: 'Interface Vocale',
                icon: 'fas fa-microphone',
                url: '/voice-interface.html',
                description: 'Communication vocale avec Louna'
            },
            {
                id: 'code',
                title: 'Éditeur de Code',
                icon: 'fas fa-code',
                url: '/code-editor.html',
                description: 'Éditeur de code intelligent'
            },
            {
                id: 'dashboard',
                title: 'Tableau de Bord',
                icon: 'fas fa-tachometer-alt',
                url: '/dashboard-master.html',
                description: 'Vue d\'ensemble système'
            },
            {
                id: 'settings',
                title: 'Paramètres',
                icon: 'fas fa-cogs',
                url: '/settings-advanced.html',
                description: 'Configuration avancée'
            },
            {
                id: 'logs',
                title: 'Logs Système',
                icon: 'fas fa-terminal',
                url: '/system-logs.html',
                description: 'Journal système en temps réel'
            }
        ];
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'home';
        if (path === '/chat' || path === '/chat.html') return 'chat';
        if (path.includes('futuristic-interface')) return 'memory';
        if (path.includes('brain-visualization')) return 'brain';
        if (path.includes('qi-neuron-monitor')) return 'monitoring';
        if (path.includes('kyber-dashboard')) return 'kyber';
        if (path.includes('generation-studio')) return 'generation';
        if (path.includes('voice-interface')) return 'voice';
        if (path.includes('code-editor')) return 'code';
        if (path.includes('dashboard-master')) return 'dashboard';
        if (path.includes('settings')) return 'settings';
        if (path.includes('system-logs')) return 'logs';
        return 'unknown';
    }

    createUnifiedHeader(title, subtitle = null) {
        const currentItem = this.navigationItems.find(item => item.id === this.currentPage);
        const headerTitle = title || currentItem?.title || 'Louna';
        const headerSubtitle = subtitle || currentItem?.description || 'Intelligence Artificielle Évolutive';

        return `
            <div class="louna-header">
                <div class="louna-header-content">
                    <div class="louna-header-title">
                        <i class="${currentItem?.icon || 'fas fa-brain'} louna-header-icon"></i>
                        <h1>${headerTitle}</h1>
                    </div>
                    <div class="louna-nav">
                        <a href="/" class="louna-nav-btn ${this.currentPage === 'home' ? 'primary' : ''}">
                            <i class="fas fa-home"></i>
                            <span>Accueil</span>
                        </a>
                        <a href="/chat" class="louna-nav-btn ${this.currentPage === 'chat' ? 'primary' : ''}">
                            <i class="fas fa-comments"></i>
                            <span>Chat</span>
                        </a>
                        <a href="/futuristic-interface.html" class="louna-nav-btn ${this.currentPage === 'memory' ? 'primary' : ''}">
                            <i class="fas fa-fire"></i>
                            <span>Mémoire</span>
                        </a>
                        <a href="/brain-visualization.html" class="louna-nav-btn ${this.currentPage === 'brain' ? 'primary' : ''}">
                            <i class="fas fa-brain"></i>
                            <span>3D</span>
                        </a>
                        <a href="/qi-neuron-monitor.html" class="louna-nav-btn ${this.currentPage === 'monitoring' ? 'primary' : ''}">
                            <i class="fas fa-yin-yang"></i>
                            <span>QI</span>
                        </a>
                    </div>
                    <div class="louna-status online">
                        <div class="louna-status-dot"></div>
                        <span>Système opérationnel</span>
                    </div>
                </div>
                ${headerSubtitle ? `
                <div class="louna-header-subtitle">
                    <p>${headerSubtitle}</p>
                </div>
                ` : ''}
            </div>
        `;
    }

    createQuickNavigation() {
        const primaryItems = this.navigationItems.filter(item => 
            ['home', 'chat', 'memory', 'brain', 'monitoring', 'generation'].includes(item.id)
        );

        return `
            <div class="louna-card">
                <div class="louna-card-header">
                    <div class="louna-card-title">
                        <i class="fas fa-compass louna-card-icon"></i>
                        Navigation Rapide
                    </div>
                </div>
                <div class="louna-grid cols-3">
                    ${primaryItems.map(item => `
                        <a href="${item.url}" class="louna-btn ${item.id === this.currentPage ? 'primary' : 'ghost'}">
                            <i class="${item.icon}"></i>
                            <span>${item.title}</span>
                        </a>
                    `).join('')}
                </div>
            </div>
        `;
    }

    createBreadcrumb() {
        const currentItem = this.navigationItems.find(item => item.id === this.currentPage);
        
        return `
            <nav class="louna-breadcrumb">
                <a href="/" class="louna-breadcrumb-item">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                ${currentItem && currentItem.id !== 'home' ? `
                    <i class="fas fa-chevron-right louna-breadcrumb-separator"></i>
                    <span class="louna-breadcrumb-item active">
                        <i class="${currentItem.icon}"></i>
                        <span>${currentItem.title}</span>
                    </span>
                ` : ''}
            </nav>
        `;
    }

    createSidebar() {
        return `
            <div class="louna-sidebar">
                <div class="louna-sidebar-header">
                    <div class="louna-sidebar-logo">
                        <i class="fas fa-brain"></i>
                        <span>LOUNA</span>
                    </div>
                    <div class="louna-sidebar-subtitle">Intelligence Évolutive</div>
                </div>
                
                <div class="louna-sidebar-nav">
                    <div class="louna-sidebar-section">
                        <div class="louna-sidebar-section-title">PRINCIPAL</div>
                        ${this.navigationItems.slice(0, 6).map(item => `
                            <a href="${item.url}" class="louna-sidebar-item ${item.id === this.currentPage ? 'active' : ''}">
                                <i class="${item.icon}"></i>
                                <span>${item.title}</span>
                            </a>
                        `).join('')}
                    </div>
                    
                    <div class="louna-sidebar-section">
                        <div class="louna-sidebar-section-title">OUTILS</div>
                        ${this.navigationItems.slice(6).map(item => `
                            <a href="${item.url}" class="louna-sidebar-item ${item.id === this.currentPage ? 'active' : ''}">
                                <i class="${item.icon}"></i>
                                <span>${item.title}</span>
                            </a>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    injectHeader(containerId = 'louna-header-container', title = null, subtitle = null) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = this.createUnifiedHeader(title, subtitle);
        } else {
            // Si pas de container spécifique, injecter au début du body
            document.body.insertAdjacentHTML('afterbegin', this.createUnifiedHeader(title, subtitle));
        }
    }

    injectQuickNav(containerId = 'louna-quick-nav-container') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = this.createQuickNavigation();
        }
    }

    injectBreadcrumb(containerId = 'louna-breadcrumb-container') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = this.createBreadcrumb();
        }
    }

    injectSidebar(containerId = 'louna-sidebar-container') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = this.createSidebar();
        }
    }

    // Méthode pour initialiser automatiquement la navigation
    init(options = {}) {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init(options));
            return;
        }

        // Injecter les composants selon les options
        if (options.header !== false) {
            this.injectHeader(options.headerContainer, options.title, options.subtitle);
        }
        
        if (options.quickNav) {
            this.injectQuickNav(options.quickNavContainer);
        }
        
        if (options.breadcrumb) {
            this.injectBreadcrumb(options.breadcrumbContainer);
        }
        
        if (options.sidebar) {
            this.injectSidebar(options.sidebarContainer);
        }

        console.log('🧭 Navigation Louna initialisée pour:', this.currentPage);
    }
}

// Instance globale
window.LounaNav = new LounaNavigation();

// Auto-initialisation si pas de configuration spécifique
if (typeof window.lounaNavConfig === 'undefined') {
    window.LounaNav.init();
}

// Corriger les boutons de navigation en double
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(fixDuplicateNavigation, 500);
});
