/**
 * Script automatique pour unifier toutes les interfaces Louna
 * S'exécute automatiquement sur chaque page pour assurer la cohérence
 */

(function() {
    'use strict';
    
    console.log('🎨 Auto-unification des interfaces Louna...');
    
    // Configuration
    const config = {
        cssUnified: '/css/louna-unified-theme.css?v=2025',
        jsUnified: '/js/louna-unified-nav.js?v=2025',
        homeUrl: '/',
        hubUrl: '/unified-hub.html',
        navUrl: '/navigation'
    };
    
    // Vérifier si l'interface est déjà unifiée
    function isAlreadyUnified() {
        return document.body.classList.contains('louna-unified') ||
               document.querySelector('link[href*="louna-unified-theme"]') ||
               document.querySelector('.louna-nav');
    }
    
    // Charger le CSS unifié
    function loadUnifiedCSS() {
        if (document.querySelector(`link[href="${config.cssUnified}"]`)) return;
        
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = config.cssUnified;
        document.head.appendChild(link);
        console.log('✅ CSS unifié chargé');
    }
    
    // Ajouter la navigation unifiée
    function addUnifiedNavigation() {
        // Supprimer les anciennes navigations
        const oldNavs = document.querySelectorAll('.top-navbar, .nav-container, #homeButton');
        oldNavs.forEach(nav => nav.remove());
        
        // Créer la nouvelle navigation
        const nav = document.createElement('nav');
        nav.className = 'louna-nav';
        nav.innerHTML = getNavigationHTML();
        
        // Injecter au début du body
        document.body.insertBefore(nav, document.body.firstChild);
        
        // Marquer la page active
        markActivePage();
        
        console.log('✅ Navigation unifiée ajoutée');
    }
    
    function getNavigationHTML() {
        return `
            <div class="nav-container">
                <a href="${config.homeUrl}" class="nav-logo">
                    <i class="fas fa-brain"></i>
                    <span>Louna</span>
                </a>
                
                <div class="nav-links">
                    <a href="${config.homeUrl}" class="nav-item" data-page="home">
                        <i class="fas fa-home"></i>
                        <span>Accueil</span>
                    </a>
                    <a href="${config.hubUrl}" class="nav-item" data-page="hub">
                        <i class="fas fa-th-large"></i>
                        <span>Hub Central</span>
                    </a>
                    <a href="/chat-simple" class="nav-item" data-page="chat">
                        <i class="fas fa-comments"></i>
                        <span>Chat</span>
                    </a>
                    <a href="/futuristic-interface.html" class="nav-item" data-page="memory">
                        <i class="fas fa-fire"></i>
                        <span>Mémoire</span>
                    </a>
                    <a href="/brain-visualization.html" class="nav-item" data-page="brain">
                        <i class="fas fa-brain"></i>
                        <span>Cerveau 3D</span>
                    </a>
                    <a href="/qi-neuron-monitor.html" class="nav-item" data-page="qi">
                        <i class="fas fa-yin-yang"></i>
                        <span>QI Monitor</span>
                    </a>
                    <a href="/generation-studio.html" class="nav-item" data-page="studio">
                        <i class="fas fa-magic"></i>
                        <span>Studio</span>
                    </a>
                    <a href="${config.navUrl}" class="nav-item" data-page="navigation">
                        <i class="fas fa-sitemap"></i>
                        <span>Navigation</span>
                    </a>
                </div>
                
                <a href="${config.homeUrl}" class="nav-home-btn">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
            </div>
        `;
    }
    
    function markActivePage() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href === currentPath || 
                (currentPath === '/' && href === config.homeUrl) ||
                (currentPath.includes(href.replace('.html', '')) && href !== config.homeUrl)) {
                item.classList.add('active');
            }
        });
    }
    
    // Ajuster le contenu principal
    function adjustMainContent() {
        // Ajouter un conteneur unifié si nécessaire
        let container = document.querySelector('.louna-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'louna-container';
            
            // Déplacer le contenu existant
            const bodyChildren = Array.from(document.body.children);
            bodyChildren.forEach(child => {
                if (!child.classList.contains('louna-nav') && 
                    !child.classList.contains('quick-nav') &&
                    child.tagName !== 'SCRIPT') {
                    container.appendChild(child);
                }
            });
            
            document.body.appendChild(container);
        }
        
        // Ajouter la navigation rapide
        addQuickNavigation();
    }
    
    function addQuickNavigation() {
        if (document.querySelector('.quick-nav')) return;
        
        const quickNav = document.createElement('div');
        quickNav.className = 'quick-nav';
        quickNav.innerHTML = `
            <button class="quick-nav-btn" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" title="Retour en haut">
                <i class="fas fa-arrow-up"></i>
            </button>
            <button class="quick-nav-btn" onclick="window.history.back()" title="Page précédente">
                <i class="fas fa-arrow-left"></i>
            </button>
            <button class="quick-nav-btn" onclick="window.location.href='${config.navUrl}'" title="Navigation complète">
                <i class="fas fa-th"></i>
            </button>
            <button class="quick-nav-btn" onclick="window.location.href='${config.hubUrl}'" title="Hub Central">
                <i class="fas fa-th-large"></i>
            </button>
        `;
        
        document.body.appendChild(quickNav);
    }
    
    // Standardiser les éléments existants
    function standardizeElements() {
        // Standardiser les cartes
        const cards = document.querySelectorAll('.card, .thermal-card, .kyber-card, .monitor-card, .studio-card');
        cards.forEach(card => {
            if (!card.classList.contains('louna-card')) {
                card.classList.add('louna-card');
            }
        });
        
        // Standardiser les boutons
        const buttons = document.querySelectorAll('button:not(.louna-btn), .btn:not(.louna-btn)');
        buttons.forEach(btn => {
            if (!btn.classList.contains('louna-btn') && 
                !btn.classList.contains('quick-nav-btn') &&
                !btn.classList.contains('nav-home-btn')) {
                btn.classList.add('louna-btn', 'btn-primary');
            }
        });
        
        // Standardiser les grilles
        const grids = document.querySelectorAll('.grid, .main-grid, .content-grid');
        grids.forEach(grid => {
            if (!grid.classList.contains('louna-grid')) {
                grid.classList.add('louna-grid', 'grid-2');
            }
        });
    }
    
    // Ajouter des animations
    function addAnimations() {
        const elements = document.querySelectorAll('.louna-card, .louna-grid > *, .unified-card');
        elements.forEach((el, index) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                el.style.transition = 'all 0.5s ease';
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    // Fonction principale d'unification
    function unifyInterface() {
        if (isAlreadyUnified()) {
            console.log('⏭️ Interface déjà unifiée');
            return;
        }
        
        console.log('🔄 Unification en cours...');
        
        // Marquer comme unifié
        document.body.classList.add('louna-unified', 'theme-2025');
        
        // Appliquer les modifications
        loadUnifiedCSS();
        addUnifiedNavigation();
        adjustMainContent();
        standardizeElements();
        
        // Ajouter les animations après un délai
        setTimeout(addAnimations, 500);
        
        console.log('✅ Interface unifiée avec succès !');
        
        // Afficher une notification
        showUnificationToast();
    }
    
    function showUnificationToast() {
        const toast = document.createElement('div');
        toast.innerHTML = `
            <i class="fas fa-check"></i>
            <span>Interface Louna unifiée</span>
        `;
        
        Object.assign(toast.style, {
            position: 'fixed',
            top: '100px',
            right: '20px',
            background: 'linear-gradient(135deg, #4caf50, #388e3c)',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            zIndex: '9999',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 15px rgba(76, 175, 80, 0.3)'
        });
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    // Initialisation automatique
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', unifyInterface);
    } else {
        unifyInterface();
    }
    
})();
