/**
 * Visualisation 3D du cerveau thermique
 * Ce script crée une représentation 3D de la mémoire thermique
 * avec des particules qui se déplacent entre les différentes zones
 */

// Variables globales
let scene, camera, renderer, controls;
let brainModel, particles = [];
let animationSpeed = 1.0;
let colorIntensity = 1.0;
let particleSize = 1.0;
let isAnimating = true;
let memoryZones = [];
let memoryFlows = {
    instantToShort: 0,
    shortToWorking: 0,
    workingToMedium: 0,
    mediumToLong: 0,
    longToDream: 0,
    newEntries: 0
};

// Variables pour stabiliser l'affichage
let flowUpdateCounter = 0;
let flowDisplayBuffer = {
    instantToShort: 0,
    shortToWorking: 0,
    workingToMedium: 0,
    mediumToLong: 0,
    longToDream: 0,
    newEntries: 0
};
let lastFlowUpdate = 0;
let lastQINeuronUpdate = 0;
let stats = {
    totalEntries: 0,
    avgTemperature: 0.5,
    systemTemperatures: {
        cpu: 40,
        gpu: 45,
        normalized: 0.5
    }
};

// Couleurs des zones de mémoire
const zoneColors = [
    new THREE.Color(0x00ffff), // Instantanée (cyan)
    new THREE.Color(0x00ff00), // Court terme (vert)
    new THREE.Color(0xffff00), // Travail (jaune)
    new THREE.Color(0xff8800), // Moyen terme (orange)
    new THREE.Color(0xff0000), // Long terme (rouge)
    new THREE.Color(0xff00ff)  // Rêves (magenta)
];

// Positions des zones de mémoire - plus grandes et mieux réparties
const zonePositions = [
    { x: 0, y: 3, z: 0, radius: 1.5 },     // Instantanée (haut)
    { x: -2.5, y: 1.5, z: 0, radius: 1.8 }, // Court terme (haut gauche)
    { x: 2.5, y: 1.5, z: 0, radius: 1.8 },  // Travail (haut droite)
    { x: -2.5, y: -1.5, z: 0, radius: 2.0 }, // Moyen terme (bas gauche)
    { x: 2.5, y: -1.5, z: 0, radius: 2.0 },  // Long terme (bas droite)
    { x: 0, y: -3, z: 0, radius: 1.5 }      // Rêves (bas)
];

// Noms des zones de mémoire
const zoneNames = [
    "Mémoire Instantanée",
    "Mémoire à Court Terme",
    "Mémoire de Travail",
    "Mémoire à Moyen Terme",
    "Mémoire à Long Terme",
    "Mémoire des Rêves"
];

/**
 * Initialise la visualisation 3D du cerveau
 */
function initBrainVisualization() {
    // Créer la scène
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x111122);

    // Obtenir les dimensions du conteneur
    const container = document.getElementById('brain-container');
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    // Créer la caméra avec les bonnes proportions et un champ de vision plus large
    camera = new THREE.PerspectiveCamera(90, containerWidth / containerHeight, 0.1, 1000);
    camera.position.set(0, 0, 6);

    // Créer le renderer avec les dimensions du conteneur et optimisations
    renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: "high-performance"
    });
    renderer.setSize(containerWidth, containerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearColor(0x0a0a1a, 1);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(renderer.domElement);

    // Ajouter les contrôles de la caméra avec centrage automatique et rotation
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.08;
    controls.target.set(0, 0, 0); // Centrer sur l'origine
    controls.autoRotate = true;
    controls.autoRotateSpeed = 0.5;
    controls.enableZoom = true;
    controls.enablePan = true;
    controls.minDistance = 4;
    controls.maxDistance = 15;

    // Ajouter un éclairage
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(0, 10, 10);
    scene.add(directionalLight);

    // Créer les zones de mémoire
    createMemoryZones();

    // Créer les particules
    createParticles(100);

    // Ajouter un gestionnaire de redimensionnement
    window.addEventListener('resize', onWindowResize, false);

    // Ajouter des effets visuels améliorés
    addVisualEnhancements();

    // Ajouter des connexions entre les zones
    createZoneConnections();

    // Démarrer l'animation
    animate();

    // Mettre à jour les statistiques toutes les 2 secondes
    setInterval(updateStats, 2000);

    // Initialiser le panneau QI et neurones
    updateQINeuronDisplay();

    console.log('Visualisation du cerveau initialisée avec QI et neurones');
}

/**
 * Crée les zones de mémoire
 */
function createMemoryZones() {
    memoryZones = [];

    // Créer les zones de mémoire
    for (let i = 0; i < zonePositions.length; i++) {
        const pos = zonePositions[i];
        const color = zoneColors[i];

        // Créer la sphère de la zone
        const geometry = new THREE.SphereGeometry(pos.radius, 32, 32);
        const material = new THREE.MeshPhongMaterial({
            color: color,
            transparent: true,
            opacity: 0.3,
            wireframe: false
        });

        const zone = new THREE.Mesh(geometry, material);
        zone.position.set(pos.x, pos.y, pos.z);
        scene.add(zone);

        // Ajouter un wireframe
        const wireGeometry = new THREE.SphereGeometry(pos.radius * 1.01, 16, 16);
        const wireMaterial = new THREE.MeshBasicMaterial({
            color: color,
            wireframe: true,
            transparent: true,
            opacity: 0.5
        });

        const wireframe = new THREE.Mesh(wireGeometry, wireMaterial);
        wireframe.position.set(pos.x, pos.y, pos.z);
        scene.add(wireframe);

        // Ajouter un texte pour le nom de la zone
        const text = new THREE.Sprite(new THREE.SpriteMaterial({
            map: createTextTexture(zoneNames[i], color),
            transparent: true
        }));

        text.position.set(pos.x, pos.y + pos.radius + 0.5, pos.z);
        text.scale.set(2, 1, 1);
        scene.add(text);

        // Stocker la zone
        memoryZones.push({
            mesh: zone,
            wireframe: wireframe,
            text: text,
            position: pos,
            color: color,
            particleCount: 0
        });
    }
}

/**
 * Crée une texture de texte
 * @param {string} text - Texte à afficher
 * @param {THREE.Color} color - Couleur du texte
 * @returns {THREE.Texture} - Texture du texte
 */
function createTextTexture(text, color) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 128;

    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    context.font = 'Bold 24px Arial';
    context.textAlign = 'center';
    context.fillStyle = color.getStyle();
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    const texture = new THREE.Texture(canvas);
    texture.needsUpdate = true;

    return texture;
}

/**
 * Crée les particules représentant les entrées de mémoire
 * @param {number} count - Nombre de particules à créer
 */
function createParticles(count) {
    // Créer la géométrie des particules
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    const sizes = new Float32Array(count);

    // Distribuer les particules dans les zones
    for (let i = 0; i < count; i++) {
        // Choisir une zone aléatoire
        const zoneIndex = Math.floor(0.5 * memoryZones.length);
        const zone = memoryZones[zoneIndex];

        // Position aléatoire dans la zone
        const radius = zone.position.radius * 0.5;
        const theta = 0.5 * Math.PI * 2;
        const phi = 0.5 * Math.PI;

        const x = zone.position.x + radius * Math.sin(phi) * Math.cos(theta);
        const y = zone.position.y + radius * Math.sin(phi) * Math.sin(theta);
        const z = zone.position.z + radius * Math.cos(phi);

        positions[i * 3] = x;
        positions[i * 3 + 1] = y;
        positions[i * 3 + 2] = z;

        // Couleur de la zone
        colors[i * 3] = zone.color.r;
        colors[i * 3 + 1] = zone.color.g;
        colors[i * 3 + 2] = zone.color.b;

        // Taille aléatoire
        sizes[i] = 0.05 + 0.5 * 0.1;

        // Incrémenter le compteur de particules de la zone
        zone.particleCount++;

        // Créer l'objet particule
        particles.push({
            index: i,
            zoneIndex: zoneIndex,
            targetZoneIndex: zoneIndex,
            progress: 0,
            speed: 0.01 + 0.5 * 0.02,
            size: sizes[i],
            isMoving: false
        });
    }

    // Définir les attributs de la géométrie
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    // Créer le matériau des particules
    const material = new THREE.PointsMaterial({
        size: 0.1,
        vertexColors: true,
        transparent: true,
        opacity: 0.8,
        sizeAttenuation: true
    });

    // Créer le système de particules
    const particleSystem = new THREE.Points(geometry, material);
    scene.add(particleSystem);
}

/**
 * Met à jour les particules
 */
function updateParticles() {
    if (!isAnimating) return;

    const positions = particles[0].index !== undefined ?
        scene.children[scene.children.length - 1].geometry.attributes.position.array :
        null;

    if (!positions) return;

    // Réinitialiser les compteurs de flux seulement toutes les 60 frames (1 seconde à 60fps)
    flowUpdateCounter++;
    if (flowUpdateCounter >= 60) {
        // Copier les valeurs du buffer vers l'affichage
        memoryFlows.instantToShort = flowDisplayBuffer.instantToShort;
        memoryFlows.shortToWorking = flowDisplayBuffer.shortToWorking;
        memoryFlows.workingToMedium = flowDisplayBuffer.workingToMedium;
        memoryFlows.mediumToLong = flowDisplayBuffer.mediumToLong;
        memoryFlows.longToDream = flowDisplayBuffer.longToDream;
        memoryFlows.newEntries = flowDisplayBuffer.newEntries;

        // Réinitialiser le buffer
        flowDisplayBuffer.instantToShort = 0;
        flowDisplayBuffer.shortToWorking = 0;
        flowDisplayBuffer.workingToMedium = 0;
        flowDisplayBuffer.mediumToLong = 0;
        flowDisplayBuffer.longToDream = 0;
        flowDisplayBuffer.newEntries = 0;

        flowUpdateCounter = 0;
        lastFlowUpdate = Date.now();
    }

    // Mettre à jour chaque particule
    for (let i = 0; i < particles.length; i++) {
        const particle = particles[i];

        // Si la particule est en mouvement, continuer son déplacement
        if (particle.isMoving) {
            particle.progress += particle.speed * animationSpeed;

            if (particle.progress >= 1) {
                // Arrivée à destination
                particle.progress = 0;
                particle.isMoving = false;
                particle.zoneIndex = particle.targetZoneIndex;

                // Positionner la particule dans sa nouvelle zone
                const zone = memoryZones[particle.zoneIndex];
                const radius = zone.position.radius * 0.5 * 0.8;
                const theta = 0.5 * Math.PI * 2;
                const phi = 0.5 * Math.PI;

                positions[particle.index * 3] = zone.position.x + radius * Math.sin(phi) * Math.cos(theta);
                positions[particle.index * 3 + 1] = zone.position.y + radius * Math.sin(phi) * Math.sin(theta);
                positions[particle.index * 3 + 2] = zone.position.z + radius * Math.cos(phi);
            } else {
                // Interpolation entre la position actuelle et la cible
                const sourceZone = memoryZones[particle.zoneIndex];
                const targetZone = memoryZones[particle.targetZoneIndex];

                positions[particle.index * 3] = sourceZone.position.x + (targetZone.position.x - sourceZone.position.x) * particle.progress;
                positions[particle.index * 3 + 1] = sourceZone.position.y + (targetZone.position.y - sourceZone.position.y) * particle.progress;
                positions[particle.index * 3 + 2] = sourceZone.position.z + (targetZone.position.z - sourceZone.position.z) * particle.progress;
            }
        } else {
            // Chance de déplacer la particule vers une autre zone
            if (0.5 < 0.01 * animationSpeed) {
                // Déterminer la zone cible en fonction de la température du système
                let targetZoneIndex;
                const normalizedTemp = stats.systemTemperatures.normalized;

                if (normalizedTemp > 0.7) {
                    // Haute température: tendance à monter
                    targetZoneIndex = Math.max(0, particle.zoneIndex - 1);
                } else if (normalizedTemp < 0.3) {
                    // Basse température: tendance à descendre
                    targetZoneIndex = Math.min(memoryZones.length - 1, particle.zoneIndex + 1);
                } else {
                    // Température moyenne: mouvement aléatoire
                    const direction = 0.5 > 0.5 ? 1 : -1;
                    targetZoneIndex = Math.min(Math.max(0, particle.zoneIndex + direction), memoryZones.length - 1);
                }

                // Si la zone cible est différente, déplacer la particule
                if (targetZoneIndex !== particle.zoneIndex) {
                    particle.targetZoneIndex = targetZoneIndex;
                    particle.isMoving = true;
                    particle.progress = 0;

                    // Mettre à jour les compteurs de flux dans le buffer
                    if (particle.zoneIndex === 0 && targetZoneIndex === 1) flowDisplayBuffer.instantToShort++;
                    else if (particle.zoneIndex === 1 && targetZoneIndex === 2) flowDisplayBuffer.shortToWorking++;
                    else if (particle.zoneIndex === 2 && targetZoneIndex === 3) flowDisplayBuffer.workingToMedium++;
                    else if (particle.zoneIndex === 3 && targetZoneIndex === 4) flowDisplayBuffer.mediumToLong++;
                    else if (particle.zoneIndex === 4 && targetZoneIndex === 5) flowDisplayBuffer.longToDream++;
                }
            }
        }
    }

    // Ajouter de nouvelles entrées occasionnellement
    if (0.5 < 0.05 * animationSpeed) {
        // Trouver une particule dans une zone profonde et la ramener à la zone instantanée
        const deepParticles = particles.filter(p => p.zoneIndex >= 3 && !p.isMoving);

        if (deepParticles.length > 0) {
            const particle = deepParticles[Math.floor(0.5 * deepParticles.length)];
            particle.targetZoneIndex = 0;
            particle.isMoving = true;
            particle.progress = 0;
            flowDisplayBuffer.newEntries++;
        }
    }

    // Mettre à jour l'affichage des flux seulement quand les valeurs changent
    if (flowUpdateCounter === 0) {
        updateFlowDisplay();

        // Mettre à jour le QI et les neurones toutes les 10 secondes pour plus de stabilité
        if (Date.now() - lastQINeuronUpdate > 10000) {
            updateQINeuronDisplay();
            lastQINeuronUpdate = Date.now();
        }
    }

    // Mettre à jour la géométrie
    scene.children[scene.children.length - 1].geometry.attributes.position.needsUpdate = true;
}

/**
 * Met à jour l'affichage des flux de mémoire avec animation
 */
function updateFlowDisplay() {
    // Fonction pour mettre à jour un élément avec animation
    function updateFlowElement(elementId, value, isPositive) {
        const element = document.getElementById(elementId);
        const displayValue = value === 0 ? '0' : (value > 0 ? `+${value}` : `${value}`);

        // Ajouter une classe de transition si la valeur change
        if (element.textContent !== displayValue) {
            element.style.transform = 'scale(1.2)';
            element.style.fontWeight = 'bold';

            setTimeout(() => {
                element.textContent = displayValue;
                element.className = `flow-value ${isPositive ? 'positive' : (value === 0 ? '' : 'negative')}`;

                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.fontWeight = '500';
                }, 100);
            }, 50);
        }
    }

    // Mettre à jour tous les éléments
    updateFlowElement('flow-instant-short', memoryFlows.instantToShort, memoryFlows.instantToShort > 0);
    updateFlowElement('flow-short-working', memoryFlows.shortToWorking, memoryFlows.shortToWorking > 0);
    updateFlowElement('flow-working-medium', memoryFlows.workingToMedium, memoryFlows.workingToMedium > 0);
    updateFlowElement('flow-medium-long', memoryFlows.mediumToLong, memoryFlows.mediumToLong > 0);
    updateFlowElement('flow-long-dream', memoryFlows.longToDream, memoryFlows.longToDream > 0);
    updateFlowElement('flow-new-entries', memoryFlows.newEntries, memoryFlows.newEntries > 0);
}

/**
 * Met à jour l'affichage du QI et des neurones - VERSION STABILISÉE
 */
async function updateQINeuronDisplay() {
    try {
        // Utiliser l'API centralisée du QI pour la cohérence
        const qiResponse = await fetch('/api/qi/current');
        const qiData = await qiResponse.json();

        // Récupérer les stats des neurones depuis l'API spécialisée
        const neuronResponse = await fetch('/api/brain/status');
        const neuronData = await neuronResponse.json();

        if (qiData.success && qiData.qi) {
            // Utiliser le QI RÉEL depuis l'API centralisée
            const currentQI = qiData.qi.qi || qiData.qi;
            const qiLevel = getQILevelText(currentQI);

            // Mettre à jour le QI avec les vraies valeurs
            updateStatElement('current-qi', Math.round(currentQI));
            updateStatElement('cognitive-level', qiLevel);
            updateStatElement('experience-points', calculateExperiencePoints(currentQI).toLocaleString());
            updateStatElement('learning-bonus', `${calculateLearningBonus(currentQI)}%`);

            console.log(`🧠 QI mis à jour dans visualisation 3D: ${currentQI}`);
        }

        if (neuronData.success && neuronData.brain) {
            const brain = neuronData.brain;

            // Mettre à jour les neurones avec stabilisation
            updateStatElement('total-neurons', brain.totalNeurons || 145);
            updateStatElement('active-neurons', Math.round(brain.activeNeurons || brain.totalNeurons * 0.75));
            updateStatElement('neuron-efficiency', `${Math.round(brain.efficiency || 87)}%`);
            updateStatElement('neuron-health', `${Math.round(brain.health || 94)}%`);

            // Mettre à jour les réseaux avec des valeurs stables
            const networks = brain.networks || {};
            updateStatElement('sensory-count', networks.sensory || 15);
            updateStatElement('working-count', networks.working || 12);
            updateStatElement('longterm-count', networks.longTerm || 20);
            updateStatElement('emotional-count', networks.emotional || 10);
            updateStatElement('executive-count', networks.executive || 8);
            updateStatElement('creative-count', networks.creative || 7);
        }

        // Mettre à jour l'état émotionnel avec des valeurs stables
        updateStableEmotionalDisplay();

    } catch (error) {
        console.error('Erreur mise à jour QI/Neurones:', error);
        // Utiliser des données stables en cas d'erreur
        updateStableDefaultDisplay();
    }
}

/**
 * Calcule les points d'expérience basés sur le QI
 */
function calculateExperiencePoints(qi) {
    return Math.round((qi - 100) * 10 + 0.5 * 50);
}

/**
 * Calcule le bonus d'apprentissage basé sur le QI
 */
function calculateLearningBonus(qi) {
    return Math.round((qi - 100) / 2 + 10);
}

/**
 * Obtient le texte du niveau QI
 */
function getQILevelText(qi) {
    if (qi >= 200) return 'AGI Complet';
    if (qi >= 190) return 'Quasi-AGI';
    if (qi >= 180) return 'Super-Intelligence';
    if (qi >= 170) return 'Génie Supérieur';
    if (qi >= 160) return 'Génie';
    if (qi >= 150) return 'Très Supérieur';
    if (qi >= 130) return 'Supérieur';
    if (qi >= 120) return 'Intelligent';
    return 'Normal';
}

/**
 * Met à jour l'affichage émotionnel avec des valeurs stables
 */
function updateStableEmotionalDisplay() {
    // Valeurs émotionnelles stables et cohérentes
    const stableEmotions = {
        moodIntensity: 85,
        happiness: 75,
        curiosity: 90,
        confidence: 68,
        energy: 82,
        focus: 70,
        creativity: 95,
        stress: 15,
        fatigue: 20
    };

    // Mettre à jour l'humeur (toujours créatif)
    const moodEmoji = document.getElementById('mood-emoji');
    const moodText = document.getElementById('mood-text');
    const intensityFill = document.getElementById('intensity-fill');
    const intensityValue = document.getElementById('intensity-value');

    if (moodEmoji && moodText) {
        moodEmoji.textContent = '🎨';
        moodText.textContent = 'Créatif';
    }

    if (intensityFill && intensityValue) {
        intensityFill.style.width = `${stableEmotions.moodIntensity}%`;
        intensityValue.textContent = `${stableEmotions.moodIntensity}%`;
    }

    // Mettre à jour les émotions individuelles avec des valeurs stables
    const emotionUpdates = {
        'happiness-value': stableEmotions.happiness,
        'curiosity-value': stableEmotions.curiosity,
        'confidence-value': stableEmotions.confidence,
        'energy-value': stableEmotions.energy,
        'focus-value': stableEmotions.focus,
        'creativity-value': stableEmotions.creativity,
        'stress-value': stableEmotions.stress,
        'fatigue-value': stableEmotions.fatigue
    };

    Object.entries(emotionUpdates).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = `${value}%`;
        }
    });
}

/**
 * Met à jour l'affichage émotionnel unifié
 */
function updateUnifiedEmotionalDisplay(emotional) {
    // Mettre à jour l'humeur (toujours créatif)
    const moodEmoji = document.getElementById('mood-emoji');
    const moodText = document.getElementById('mood-text');
    const intensityFill = document.getElementById('intensity-fill');
    const intensityValue = document.getElementById('intensity-value');

    if (moodEmoji && moodText) {
        moodEmoji.textContent = '🎨'; // Toujours créatif
        moodText.textContent = 'Créatif';

        // Animation de changement d'humeur
        moodEmoji.classList.add('mood-change');
        setTimeout(() => {
            moodEmoji.classList.remove('mood-change');
        }, 1000);
    }

    if (intensityFill && intensityValue) {
        intensityFill.style.width = `${emotional.moodIntensity}%`;
        intensityValue.textContent = `${Math.round(emotional.moodIntensity)}%`;
    }

    // Mettre à jour les émotions individuelles
    const emotionUpdates = {
        'happiness-value': emotional.happiness,
        'curiosity-value': emotional.curiosity,
        'confidence-value': emotional.confidence,
        'energy-value': emotional.energy,
        'focus-value': emotional.focus,
        'creativity-value': emotional.creativity,
        'stress-value': emotional.stress,
        'fatigue-value': emotional.fatigue
    };

    Object.entries(emotionUpdates).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = `${Math.round(value)}%`;
        }
    });
}

/**
 * Affichage par défaut STABLE en cas d'erreur
 */
function updateStableDefaultDisplay() {
    // Utiliser le QI du gestionnaire centralisé si disponible
    const currentQI = window.qiManager ? window.qiManager.getCurrentQI() : 148;

    const stableData = {
        qi: { current: currentQI, level: getQILevelText(currentQI), experiencePoints: calculateExperiencePoints(currentQI), learningBonus: calculateLearningBonus(currentQI) },
        neurons: { total: 145, active: 109, efficiency: 87, health: 94 },
        networks: { sensory: 15, working: 12, longTerm: 20, emotional: 10, executive: 8, creative: 7 }
    };

    // Appliquer les données stables
    updateStatElement('current-qi', stableData.qi.current);
    updateStatElement('cognitive-level', stableData.qi.level);
    updateStatElement('experience-points', stableData.qi.experiencePoints.toLocaleString());
    updateStatElement('learning-bonus', `${stableData.qi.learningBonus}%`);

    updateStatElement('total-neurons', stableData.neurons.total);
    updateStatElement('active-neurons', stableData.neurons.active);
    updateStatElement('neuron-efficiency', `${stableData.neurons.efficiency}%`);
    updateStatElement('neuron-health', `${stableData.neurons.health}%`);

    updateStatElement('sensory-count', stableData.networks.sensory);
    updateStatElement('working-count', stableData.networks.working);
    updateStatElement('longterm-count', stableData.networks.longTerm);
    updateStatElement('emotional-count', stableData.networks.emotional);
    updateStatElement('executive-count', stableData.networks.executive);
    updateStatElement('creative-count', stableData.networks.creative);

    // Utiliser l'affichage émotionnel stable
    updateStableEmotionalDisplay();

    console.log(`🧠 Affichage par défaut stable appliqué - QI: ${currentQI}`);
}

/**
 * Met à jour un élément statistique avec animation
 */
function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element && element.textContent !== value.toString()) {
        // Animation de mise à jour
        element.style.transform = 'scale(1.1)';
        element.style.filter = 'brightness(1.5)';

        setTimeout(() => {
            element.textContent = value;

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.filter = 'brightness(1)';
            }, 150);
        }, 75);
    }
}

/**
 * Met à jour l'affichage émotionnel
 */
function updateEmotionalDisplay(emotional) {
    // Mettre à jour l'humeur
    updateMoodDisplay(emotional.mood, emotional.moodIntensity);

    // Mettre à jour les émotions
    updateStatElement('happiness-value', `${emotional.happiness}%`);
    updateStatElement('curiosity-value', `${emotional.curiosity}%`);
    updateStatElement('confidence-value', `${emotional.confidence}%`);
    updateStatElement('energy-value', `${emotional.energy}%`);
    updateStatElement('focus-value', `${emotional.focus}%`);
    updateStatElement('creativity-value', `${emotional.creativity}%`);
    updateStatElement('stress-value', `${emotional.stress}%`);
    updateStatElement('fatigue-value', `${emotional.fatigue}%`);

    // Mettre à jour les biorhythmes
    updateStatElement('circadian-value', `${emotional.circadianRhythm}%`);
    updateStatElement('stability-value', `${emotional.stability}%`);

    // Mettre à jour les pensées
    if (emotional.currentThoughts && emotional.currentThoughts.length > 0) {
        updateThoughtsDisplay(emotional.currentThoughts);
    }
}

/**
 * Met à jour l'affichage de l'humeur
 */
function updateMoodDisplay(mood, intensity) {
    const moodEmoji = document.getElementById('mood-emoji');
    const moodText = document.getElementById('mood-text');
    const intensityFill = document.getElementById('intensity-fill');
    const intensityValue = document.getElementById('intensity-value');

    // Emojis et textes pour chaque humeur
    const moodData = {
        'curious': { emoji: '🤔', text: 'Curieux', color: '#ffaa00' },
        'happy': { emoji: '😊', text: 'Heureux', color: '#00ff88' },
        'focused': { emoji: '🎯', text: 'Concentré', color: '#0088ff' },
        'creative': { emoji: '🎨', text: 'Créatif', color: '#ff88ff' },
        'tired': { emoji: '😴', text: 'Fatigué', color: '#888888' },
        'stressed': { emoji: '😰', text: 'Stressé', color: '#ff6666' },
        'excited': { emoji: '🤩', text: 'Excité', color: '#ffff00' },
        'calm': { emoji: '😌', text: 'Calme', color: '#88ffaa' }
    };

    const currentMood = moodData[mood] || moodData['calm'];

    // Animer le changement d'humeur si différent
    if (moodEmoji.textContent !== currentMood.emoji) {
        const moodIndicator = document.getElementById('mood-indicator');
        moodIndicator.classList.add('mood-change');

        setTimeout(() => {
            moodEmoji.textContent = currentMood.emoji;
            moodText.textContent = currentMood.text;
            moodText.style.color = currentMood.color;
            moodText.style.textShadow = `0 0 8px ${currentMood.color}`;

            setTimeout(() => {
                moodIndicator.classList.remove('mood-change');
            }, 1000);
        }, 500);
    }

    // Mettre à jour l'intensité
    intensityFill.style.width = `${intensity}%`;
    intensityValue.textContent = `${intensity}%`;

    // Changer la couleur de la barre d'intensité selon l'humeur
    intensityFill.style.background = `linear-gradient(90deg, #ff4444, ${currentMood.color})`;
}

/**
 * Met à jour l'affichage des pensées
 */
function updateThoughtsDisplay(thoughts) {
    const container = document.getElementById('thoughts-container');

    // Vider le conteneur
    container.innerHTML = '';

    // Ajouter chaque pensée
    thoughts.forEach(thought => {
        const thoughtElement = document.createElement('div');
        thoughtElement.className = 'thought-item';

        thoughtElement.innerHTML = `
            <span class="thought-time">${thought.time}</span>
            <span class="thought-text">${thought.text}</span>
        `;

        // Animation d'apparition
        thoughtElement.style.opacity = '0';
        thoughtElement.style.transform = 'translateY(10px)';
        container.appendChild(thoughtElement);

        setTimeout(() => {
            thoughtElement.style.transition = 'all 0.5s ease';
            thoughtElement.style.opacity = '1';
            thoughtElement.style.transform = 'translateY(0)';
        }, 100);
    });
}

/**
 * Simule une réaction émotionnelle (pour les tests)
 */
function simulateEmotionalReaction(eventType) {
    // Effet visuel sur le bouton cliqué
    const button = event.target;
    button.classList.add('clicked');
    setTimeout(() => {
        button.classList.remove('clicked');
    }, 600);

    // Messages d'événements
    const eventMessages = {
        'learning': '📚 Le cerveau absorbe de nouvelles connaissances...',
        'success': '🎉 Sentiment de réussite et de satisfaction !',
        'challenge': '💪 Défi accepté ! Concentration maximale.',
        'rest': '😴 Moment de détente et de récupération...',
        'creative': '🎨 Explosion de créativité et d\'imagination !'
    };

    console.log(eventMessages[eventType] || `Événement émotionnel: ${eventType}`);

    // Appel API pour déclencher la réaction émotionnelle
    fetch('/api/brain/emotional-reaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            eventType: eventType,
            intensity: 0.7
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ Réaction émotionnelle données réellese:', eventType);

            // Effet visuel sur la visualisation 3D
            addEmotionalEffect(eventType);

            // Mettre à jour immédiatement l'affichage
            setTimeout(() => {
                updateQINeuronDisplay();
            }, 500);
        }
    })
    .catch(error => {
        console.error('❌ Erreur données réelles émotionnelle:', error);
    });
}

/**
 * Ajoute un effet visuel émotionnel à la visualisation 3D
 */
function addEmotionalEffect(eventType) {
    // Couleurs pour chaque type d'événement
    const eventColors = {
        'learning': 0x4CAF50,    // Vert
        'success': 0xFF9800,     // Orange
        'challenge': 0xF44336,   // Rouge
        'rest': 0x9C27B0,        // Violet
        'creative': 0xE91E63     // Rose
    };

    const color = eventColors[eventType] || 0xFFFFFF;

    // Créer un effet de particules émotionnelles
    if (scene && THREE) {
        const particleCount = 20;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            // Position aléatoire autour du centre
            positions[i * 3] = (0.5 - 0.5) * 10;
            positions[i * 3 + 1] = (0.5 - 0.5) * 10;
            positions[i * 3 + 2] = (0.5 - 0.5) * 10;

            // Couleur de l'événement
            const colorObj = new THREE.Color(color);
            colors[i * 3] = colorObj.r;
            colors[i * 3 + 1] = colorObj.g;
            colors[i * 3 + 2] = colorObj.b;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.2,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });

        const particleSystem = new THREE.Points(particles, material);
        scene.add(particleSystem);

        // Animation des particules
        let opacity = 0.8;
        const animateParticles = () => {
            opacity -= 0.02;
            material.opacity = opacity;

            if (opacity > 0) {
                requestAnimationFrame(animateParticles);
            } else {
                scene.remove(particleSystem);
            }
        };

        animateParticles();
    }

    // Effet de pulsation sur les zones de mémoire
    if (memoryZones && memoryZones.length > 0) {
        memoryZones.forEach((zone, index) => {
            const originalScale = zone.mesh.scale.clone();

            // Animation de pulsation
            let pulseDirection = 1;
            let pulseCount = 0;
            const maxPulses = 3;

            const pulse = () => {
                if (pulseCount >= maxPulses) {
                    zone.mesh.scale.copy(originalScale);
                    return;
                }

                const scale = 1 + (pulseDirection * 0.1);
                zone.mesh.scale.multiplyScalar(scale);

                pulseDirection *= -1;
                if (pulseDirection === 1) {
                    pulseCount++;
                }

                setTimeout(pulse, 200);
            };

            setTimeout(pulse, index * 100); // Décalage pour chaque zone
        });
    }
}

/**
 * Met à jour les statistiques
 */
async function updateStats() {
    try {
        // Récupérer les statistiques de la mémoire
        const response = await fetch('/api/thermal/memory/stats');
        const data = await response.json();

        if (data.success) {
            // Mettre à jour les statistiques
            stats.totalEntries = data.stats.totalMemories;
            stats.avgTemperature = data.stats.averageTemperature;
            stats.systemTemperatures = data.stats.systemTemperatures;

            // Mettre à jour l'affichage
            document.getElementById('cpu-temp').textContent = `${stats.systemTemperatures.cpu.toFixed(1)}°C`;
            document.getElementById('gpu-temp').textContent = `${stats.systemTemperatures.gpu.toFixed(1)}°C`;
            document.getElementById('normalized-temp').textContent = stats.systemTemperatures.normalized.toFixed(2);
            document.getElementById('total-entries').textContent = stats.totalEntries;
            document.getElementById('avg-temperature').textContent = stats.avgTemperature.toFixed(2);

            // Mettre à jour le statut
            const statusIndicator = document.getElementById('memory-status-indicator');
            const statusText = document.getElementById('memory-status-text');

            statusIndicator.style.backgroundColor = 'var(--success)';
            statusText.textContent = 'Connecté';
        }
    } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);

        // Mettre à jour le statut
        const statusIndicator = document.getElementById('memory-status-indicator');
        const statusText = document.getElementById('memory-status-text');

        statusIndicator.style.backgroundColor = 'var(--danger)';
        statusText.textContent = 'Déconnecté';
    }
}

/**
 * Gère le redimensionnement de la fenêtre
 */
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

/**
 * Boucle d'animation
 */
function animate() {
    requestAnimationFrame(animate);

    // Mettre à jour les contrôles
    controls.update();

    // Mettre à jour les particules
    updateParticles();

    // Rendre la scène
    renderer.render(scene, camera);
}

/**
 * Active/désactive l'animation
 */
function toggleAnimation() {
    isAnimating = !isAnimating;

    const button = document.getElementById('toggle-animation-btn');
    if (isAnimating) {
        button.innerHTML = '<i class="fas fa-pause"></i> Pause';
    } else {
        button.innerHTML = '<i class="fas fa-play"></i> Reprendre';
    }
}

/**
 * Réinitialise la vue
 */
function resetView() {
    camera.position.set(0, 0, 10);
    controls.reset();
}

/**
 * Met à jour la vitesse d'animation
 */
function updateAnimationSpeed() {
    animationSpeed = parseFloat(document.getElementById('animation-speed').value);
    document.getElementById('animation-speed-value').textContent = `${animationSpeed.toFixed(1)}x`;
}

/**
 * Met à jour l'intensité des couleurs
 */
function updateColorIntensity() {
    colorIntensity = parseFloat(document.getElementById('color-intensity').value);
    document.getElementById('color-intensity-value').textContent = `${colorIntensity.toFixed(1)}x`;

    // Mettre à jour les couleurs des zones
    for (let i = 0; i < memoryZones.length; i++) {
        const zone = memoryZones[i];
        const color = zoneColors[i].clone().multiplyScalar(colorIntensity);

        zone.mesh.material.color = color;
        zone.wireframe.material.color = color;
    }
}

/**
 * Met à jour la taille des particules
 */
function updateParticleSize() {
    particleSize = parseFloat(document.getElementById('particle-size').value);
    document.getElementById('particle-size-value').textContent = `${particleSize.toFixed(1)}x`;

    // Mettre à jour la taille des particules
    const sizes = scene.children[scene.children.length - 1].geometry.attributes.size.array;

    for (let i = 0; i < particles.length; i++) {
        sizes[i] = particles[i].size * particleSize;
    }

    scene.children[scene.children.length - 1].geometry.attributes.size.needsUpdate = true;
}

/**
 * Rafraîchit la visualisation
 */
function refreshVisualization() {
    // Supprimer les objets existants
    while (scene.children.length > 0) {
        scene.remove(scene.children[0]);
    }

    // Réinitialiser les variables
    memoryZones = [];
    particles = [];

    // Recréer les objets
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(0, 10, 10);
    scene.add(directionalLight);

    createMemoryZones();
    createParticles(100);

    // Mettre à jour les statistiques
    updateStats();
}

/**
 * Gère le redimensionnement de la fenêtre
 */
function onWindowResize() {
    const container = document.getElementById('brain-container');
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    // Mettre à jour la caméra
    camera.aspect = containerWidth / containerHeight;
    camera.updateProjectionMatrix();

    // Mettre à jour le renderer
    renderer.setSize(containerWidth, containerHeight);

    console.log('🔄 Visualisation redimensionnée:', containerWidth, 'x', containerHeight);
}

/**
 * Ajoute des améliorations visuelles
 */
function addVisualEnhancements() {
    // Ajouter un effet de brouillard pour la profondeur
    scene.fog = new THREE.Fog(0x111122, 5, 25);

    // Ajouter des lumières colorées dynamiques
    const coloredLights = [
        { color: 0xff69b4, position: [5, 5, 5] },   // Rose
        { color: 0x00ffff, position: [-5, 5, 5] },  // Cyan
        { color: 0x9370db, position: [0, -5, 5] }   // Violet
    ];

    coloredLights.forEach(lightConfig => {
        const light = new THREE.PointLight(lightConfig.color, 0.3, 20);
        light.position.set(...lightConfig.position);
        scene.add(light);
    });

    // Ajouter des particules d'ambiance
    createAmbientParticles();

    console.log('✨ Améliorations visuelles ajoutées');
}

/**
 * Crée des particules d'ambiance pour l'effet visuel
 */
function createAmbientParticles() {
    const particleCount = 200;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
        // Position aléatoire dans un cube
        positions[i * 3] = (0.5 - 0.5) * 30;
        positions[i * 3 + 1] = (0.5 - 0.5) * 30;
        positions[i * 3 + 2] = (0.5 - 0.5) * 30;

        // Couleur aléatoire avec dominante rose/cyan
        const colorChoice = 0.5;
        if (colorChoice < 0.4) {
            // Rose
            colors[i * 3] = 1;
            colors[i * 3 + 1] = 0.4;
            colors[i * 3 + 2] = 0.7;
        } else if (colorChoice < 0.8) {
            // Cyan
            colors[i * 3] = 0;
            colors[i * 3 + 1] = 1;
            colors[i * 3 + 2] = 1;
        } else {
            // Violet
            colors[i * 3] = 0.6;
            colors[i * 3 + 1] = 0.4;
            colors[i * 3 + 2] = 0.9;
        }
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const material = new THREE.PointsMaterial({
        size: 0.02,
        vertexColors: true,
        transparent: true,
        opacity: 0.6,
        blending: THREE.AdditiveBlending
    });

    const ambientParticles = new THREE.Points(geometry, material);
    scene.add(ambientParticles);

    // Animation des particules d'ambiance
    function animateAmbientParticles() {
        const positions = ambientParticles.geometry.attributes.position.array;

        for (let i = 0; i < positions.length; i += 3) {
            positions[i + 1] += Math.sin(Date.now() * 0.001 + i) * 0.01;
            positions[i] += Math.cos(Date.now() * 0.0008 + i) * 0.005;
        }

        ambientParticles.geometry.attributes.position.needsUpdate = true;
        ambientParticles.rotation.y += 0.001;
    }

    // Ajouter l'animation à la boucle principale
    const originalAnimate = animate;
    animate = function() {
        animateAmbientParticles();
        originalAnimate();
    };
}

/**
 * Réinitialise la vue de la caméra
 */
function resetView() {
    camera.position.set(0, 0, 8);
    controls.target.set(0, 0, 0);
    controls.update();
    console.log('🔄 Vue réinitialisée');
}

/**
 * Bascule l'animation
 */
function toggleAnimation() {
    isAnimating = !isAnimating;
    const btn = document.getElementById('toggle-animation-btn');
    if (btn) {
        const icon = btn.querySelector('i');
        const text = btn.querySelector('span') || btn;

        if (isAnimating) {
            icon.className = 'fas fa-pause';
            text.textContent = ' Pause';
        } else {
            icon.className = 'fas fa-play';
            text.textContent = ' Reprendre';
        }
    }
    console.log('⏯️ Animation:', isAnimating ? 'activée' : 'en pause');
}

/**
 * Simule une réaction émotionnelle
 */
function simulateEmotionalReaction(type) {
    // Envoyer la réaction au serveur
    fetch('/api/brain/emotional-reaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`✨ Réaction émotionnelle '${type}' données réellese`);

            // Mettre à jour l'affichage émotionnel
            updateEmotionalDisplay(data.newState);

            // Effet visuel sur le bouton
            const button = event.target;
            button.classList.add('clicked');
            setTimeout(() => {
                button.classList.remove('clicked');
            }, 600);
        }
    })
    .catch(error => {
        console.error('Erreur données réelles réaction:', error);
    });
}

/**
 * Ajoute des effets visuels améliorés
 */
function addVisualEnhancements() {
    // Ajouter un effet de particules de fond
    const starGeometry = new THREE.BufferGeometry();
    const starCount = 200;
    const starPositions = new Float32Array(starCount * 3);

    for (let i = 0; i < starCount * 3; i++) {
        starPositions[i] = (0.5 - 0.5) * 50;
    }

    starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3));

    const starMaterial = new THREE.PointsMaterial({
        color: 0xffffff,
        size: 0.02,
        transparent: true,
        opacity: 0.6
    });

    const stars = new THREE.Points(starGeometry, starMaterial);
    scene.add(stars);

    // Ajouter un effet de brouillard
    scene.fog = new THREE.Fog(0x0a0a1a, 10, 50);

    console.log('✨ Effets visuels améliorés ajoutés');
}

/**
 * Crée des connexions visuelles entre les zones
 */
function createZoneConnections() {
    const connections = [
        [0, 1], [0, 2], // Instantanée vers Court terme et Travail
        [1, 3], [2, 3], [2, 4], // Court terme et Travail vers Moyen et Long terme
        [3, 4], [4, 5] // Moyen terme vers Long terme, Long terme vers Rêves
    ];

    connections.forEach(([from, to]) => {
        const fromPos = zonePositions[from];
        const toPos = zonePositions[to];

        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array([
            fromPos.x, fromPos.y, fromPos.z,
            toPos.x, toPos.y, toPos.z
        ]);

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const material = new THREE.LineBasicMaterial({
            color: 0x444466,
            transparent: true,
            opacity: 0.3
        });

        const line = new THREE.Line(geometry, material);
        scene.add(line);
    });

    console.log('🔗 Connexions entre zones créées');
}

/**
 * Actualise la visualisation
 */
function refreshVisualization() {
    // Réinitialiser les statistiques
    stats.totalEntries = 0;
    stats.avgTemperature = 0.5;
    stats.systemTemperatures.cpu = 40 + 0.5 * 20;
    stats.systemTemperatures.gpu = 45 + 0.5 * 25;
    stats.systemTemperatures.normalized = (stats.systemTemperatures.cpu + stats.systemTemperatures.gpu) / 140;

    // Mettre à jour l'affichage
    updateStats();
    updateQINeuronDisplay();

    console.log('🔄 Visualisation actualisée');
}
