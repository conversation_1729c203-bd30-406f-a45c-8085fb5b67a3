/**
 * Scanner de système intelligent pour Louna
 * Analyse les applications, capacités et ressources de l'ordinateur
 */

class SystemScanner {
    constructor() {
        this.scanResults = {
            applications: [],
            systemInfo: {},
            capabilities: {},
            resources: {},
            lastScan: null,
            scanHistory: []
        };

        this.scanInProgress = false;
        this.autoScanInterval = null;
        this.resourceMonitorInterval = null;

        // Configuration du scan
        this.config = {
            autoScanEnabled: true,
            autoScanInterval: 30000, // 30 secondes
            resourceMonitorInterval: 5000, // 5 secondes
            deepScanInterval: 300000, // 5 minutes
            maxScanHistory: 50
        };

        this.init();
    }

    async init() {
        console.log('🔍 Initialisation du scanner de système...');

        // Charger les résultats précédents
        await this.loadScanResults();

        // Démarrer le scan initial
        await this.performFullScan();

        // Démarrer le monitoring automatique
        this.startAutoScanning();
        this.startResourceMonitoring();

        console.log('✅ Scanner de système initialisé');
    }

    async performFullScan() {
        if (this.scanInProgress) {
            console.log('⏳ Scan déjà en cours...');
            return this.scanResults;
        }

        this.scanInProgress = true;
        console.log('🔍 Démarrage du scan complet du système...');

        try {
            // Scan des applications
            await this.scanApplications();

            // Scan des informations système
            await this.scanSystemInfo();

            // Scan des capacités
            await this.scanCapabilities();

            // Scan des ressources
            await this.scanResources();

            // Mettre à jour l'historique
            this.updateScanHistory();

            // Sauvegarder les résultats
            await this.saveScanResults();

            console.log('✅ Scan complet terminé');
            this.notifyUI('scanComplete', this.scanResults);

        } catch (error) {
            console.error('❌ Erreur lors du scan:', error);
        } finally {
            this.scanInProgress = false;
        }

        return this.scanResults;
    }

    async scanApplications() {
        console.log('📱 Scan des applications...');

        try {
            // Appeler l'API pour scanner les applications
            const response = await fetch('/api/apps/scan-applications', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();

            if (data.success) {
                this.scanResults.applications = data.applications;
                console.log(`📱 ${data.applications.length} applications détectées`);

                // Analyser les applications pour comprendre leurs capacités
                await this.analyzeApplicationCapabilities();
            }
        } catch (error) {
            console.error('❌ Erreur scan applications:', error);
        }
    }

    async analyzeApplicationCapabilities() {
        console.log('🧠 Analyse des capacités des applications...');

        const capabilities = {
            multimedia: [],
            development: [],
            productivity: [],
            system: [],
            communication: [],
            games: [],
            utilities: [],
            unknown: []
        };

        this.scanResults.applications.forEach(app => {
            const category = this.categorizeApplication(app);
            capabilities[category].push(app);
        });

        this.scanResults.capabilities.applications = capabilities;
        console.log('🧠 Analyse des capacités terminée');
    }

    categorizeApplication(app) {
        const name = app.name.toLowerCase();
        const path = app.path.toLowerCase();

        // Multimédia
        if (name.includes('photo') || name.includes('music') || name.includes('video') ||
            name.includes('quicktime') || name.includes('vlc') || name.includes('spotify')) {
            return 'multimedia';
        }

        // Développement
        if (name.includes('xcode') || name.includes('terminal') || name.includes('code') ||
            name.includes('git') || name.includes('docker') || path.includes('developer')) {
            return 'development';
        }

        // Productivité
        if (name.includes('office') || name.includes('word') || name.includes('excel') ||
            name.includes('powerpoint') || name.includes('pages') || name.includes('numbers')) {
            return 'productivity';
        }

        // Système
        if (path.includes('utilities') || name.includes('system') || name.includes('activity') ||
            name.includes('disk') || name.includes('keychain')) {
            return 'system';
        }

        // Communication
        if (name.includes('mail') || name.includes('message') || name.includes('facetime') ||
            name.includes('zoom') || name.includes('slack') || name.includes('teams')) {
            return 'communication';
        }

        // Jeux
        if (path.includes('games') || name.includes('game') || name.includes('steam')) {
            return 'games';
        }

        // Utilitaires
        if (name.includes('calculator') || name.includes('calendar') || name.includes('notes') ||
            name.includes('finder') || name.includes('preview')) {
            return 'utilities';
        }

        return 'unknown';
    }

    async scanSystemInfo() {
        console.log('💻 Scan des informations système...');

        try {
            const response = await fetch('/api/apps/system-info');
            const data = await response.json();

            if (data.success) {
                this.scanResults.systemInfo = data.systemInfo;
                console.log('💻 Informations système récupérées');
            }
        } catch (error) {
            console.error('❌ Erreur scan système:', error);
        }
    }

    async scanCapabilities() {
        console.log('⚡ Scan des capacités...');

        const capabilities = {
            canRunHeavyApps: this.canRunHeavyApplications(),
            maxConcurrentApps: this.estimateMaxConcurrentApps(),
            recommendedUsage: this.getRecommendedUsage(),
            limitations: this.identifyLimitations(),
            strengths: this.identifyStrengths()
        };

        this.scanResults.capabilities.system = capabilities;
        console.log('⚡ Analyse des capacités terminée');
    }

    async scanResources() {
        console.log('📊 Scan des ressources...');

        try {
            const response = await fetch('/api/apps/system-resources');
            const data = await response.json();

            if (data.success) {
                this.scanResults.resources = {
                    ...data.resources,
                    timestamp: Date.now(),
                    status: this.evaluateResourceStatus(data.resources)
                };
                console.log('📊 Ressources système analysées');
            }
        } catch (error) {
            console.error('❌ Erreur scan ressources:', error);
        }
    }

    canRunHeavyApplications() {
        const { systemInfo } = this.scanResults;
        const ramGB = parseInt(systemInfo.totalMemory) / (1024 * 1024 * 1024);
        const cpuCores = systemInfo.cpuCores || 4;

        return ramGB >= 8 && cpuCores >= 4;
    }

    estimateMaxConcurrentApps() {
        const { systemInfo } = this.scanResults;
        const ramGB = parseInt(systemInfo.totalMemory) / (1024 * 1024 * 1024);

        // Estimation basée sur la RAM disponible
        if (ramGB >= 32) return 20;
        if (ramGB >= 16) return 15;
        if (ramGB >= 8) return 10;
        if (ramGB >= 4) return 6;
        return 3;
    }

    getRecommendedUsage() {
        const capabilities = this.scanResults.capabilities.applications;
        const recommendations = [];

        if (capabilities.development?.length > 0) {
            recommendations.push('Développement de logiciels');
        }
        if (capabilities.multimedia?.length > 0) {
            recommendations.push('Édition multimédia');
        }
        if (capabilities.productivity?.length > 0) {
            recommendations.push('Bureautique et productivité');
        }

        return recommendations;
    }

    identifyLimitations() {
        const limitations = [];
        const { systemInfo } = this.scanResults;
        const ramGB = parseInt(systemInfo.totalMemory) / (1024 * 1024 * 1024);

        if (ramGB < 8) {
            limitations.push('RAM limitée - éviter trop d\'applications simultanées');
        }
        if (systemInfo.cpuCores < 4) {
            limitations.push('CPU limité - éviter les tâches intensives');
        }

        return limitations;
    }

    identifyStrengths() {
        const strengths = [];
        const { systemInfo } = this.scanResults;
        const ramGB = parseInt(systemInfo.totalMemory) / (1024 * 1024 * 1024);

        if (ramGB >= 16) {
            strengths.push('RAM abondante - multitâche excellent');
        }
        if (systemInfo.cpuCores >= 8) {
            strengths.push('CPU puissant - tâches intensives supportées');
        }
        if (systemInfo.platform === 'darwin') {
            strengths.push('macOS - optimisations système avancées');
        }

        return strengths;
    }

    evaluateResourceStatus(resources) {
        const memoryUsage = (resources.usedMemory / resources.totalMemory) * 100;
        const cpuUsage = resources.cpuUsage || 0;

        if (memoryUsage > 90 || cpuUsage > 90) {
            return 'critical';
        } else if (memoryUsage > 75 || cpuUsage > 75) {
            return 'warning';
        } else if (memoryUsage > 50 || cpuUsage > 50) {
            return 'moderate';
        } else {
            return 'good';
        }
    }

    startAutoScanning() {
        if (this.config.autoScanEnabled) {
            this.autoScanInterval = setInterval(() => {
                this.performQuickScan();
            }, this.config.autoScanInterval);

            console.log('🔄 Auto-scan activé');
        }
    }

    startResourceMonitoring() {
        this.resourceMonitorInterval = setInterval(() => {
            this.monitorResources();
        }, this.config.resourceMonitorInterval);

        console.log('📊 Monitoring des ressources activé');
    }

    async performQuickScan() {
        try {
            await this.scanResources();
            this.checkForSaturation();
        } catch (error) {
            console.error('❌ Erreur quick scan:', error);
        }
    }

    async monitorResources() {
        try {
            const response = await fetch('/api/apps/system-resources');
            const data = await response.json();

            if (data.success) {
                const status = this.evaluateResourceStatus(data.resources);

                if (status === 'critical') {
                    this.handleCriticalResources(data.resources);
                } else if (status === 'warning') {
                    this.handleWarningResources(data.resources);
                }
            }
        } catch (error) {
            console.error('❌ Erreur monitoring:', error);
        }
    }

    checkForSaturation() {
        const { resources } = this.scanResults;

        if (resources.status === 'critical') {
            this.preventSaturation();
        }
    }

    handleCriticalResources(resources) {
        console.warn('🚨 Ressources critiques détectées!');
        this.notifyUI('resourcesCritical', resources);
        this.preventSaturation();
    }

    handleWarningResources(resources) {
        console.warn('⚠️ Ressources en tension');
        this.notifyUI('resourcesWarning', resources);
    }

    preventSaturation() {
        console.log('🛡️ Prévention de la saturation...');

        // Suggérer la fermeture d'applications non essentielles
        const suggestions = this.getSaturationPreventionSuggestions();
        this.notifyUI('saturationPrevention', suggestions);
    }

    getSaturationPreventionSuggestions() {
        return {
            message: 'Ressources système critiques détectées',
            suggestions: [
                'Fermer les applications non utilisées',
                'Sauvegarder le travail en cours',
                'Redémarrer les applications gourmandes',
                'Vider le cache système'
            ],
            autoActions: [
                'Nettoyage automatique du cache',
                'Optimisation de la mémoire'
            ]
        };
    }

    updateScanHistory() {
        const scanEntry = {
            timestamp: Date.now(),
            applicationsCount: this.scanResults.applications.length,
            resourceStatus: this.scanResults.resources.status,
            capabilities: Object.keys(this.scanResults.capabilities.applications || {})
        };

        this.scanResults.scanHistory.unshift(scanEntry);

        // Limiter l'historique
        if (this.scanResults.scanHistory.length > this.config.maxScanHistory) {
            this.scanResults.scanHistory = this.scanResults.scanHistory.slice(0, this.config.maxScanHistory);
        }

        this.scanResults.lastScan = Date.now();
    }

    async saveScanResults() {
        try {
            localStorage.setItem('louna_scan_results', JSON.stringify(this.scanResults));
        } catch (error) {
            console.error('❌ Erreur sauvegarde scan:', error);
        }
    }

    async loadScanResults() {
        try {
            const saved = localStorage.getItem('louna_scan_results');
            if (saved) {
                this.scanResults = { ...this.scanResults, ...JSON.parse(saved) };
                console.log('📂 Résultats de scan précédents chargés');
            }
        } catch (error) {
            console.error('❌ Erreur chargement scan:', error);
        }
    }

    notifyUI(event, data) {
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('systemScanUpdate', {
                detail: { event, data }
            }));
        }
    }

    // API publique
    getScanResults() {
        return this.scanResults;
    }

    getApplicationsByCategory(category) {
        return this.scanResults.capabilities.applications?.[category] || [];
    }

    getSystemCapabilities() {
        return this.scanResults.capabilities.system || {};
    }

    getCurrentResourceStatus() {
        return this.scanResults.resources.status || 'unknown';
    }

    canRunApplication(appName) {
        const capabilities = this.getSystemCapabilities();
        const currentStatus = this.getCurrentResourceStatus();

        // Logique pour déterminer si l'application peut être lancée
        if (currentStatus === 'critical') {
            return { canRun: false, reason: 'Ressources système critiques' };
        }

        return { canRun: true, reason: 'Ressources suffisantes' };
    }

    stop() {
        if (this.autoScanInterval) {
            clearInterval(this.autoScanInterval);
        }
        if (this.resourceMonitorInterval) {
            clearInterval(this.resourceMonitorInterval);
        }
        console.log('🛑 Scanner de système arrêté');
    }
}

// Initialiser le scanner global
if (typeof window !== 'undefined') {
    window.systemScanner = new SystemScanner();

    console.log('🔍 Scanner de système disponible globalement');
}

// Export pour les modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemScanner;
}
