/**
 * 🔍 VÉRIFICATEUR D'INTÉGRITÉ SYSTÈME LOUNA AI
 * Vérifie que tous les composants sont présents et fonctionnels
 * Version: 2.1.0 - Juin 2025
 */

class SystemIntegrityChecker {
    constructor() {
        this.requiredFiles = [
            // Pages principales
            'generation-center.html',
            'generation-monitor.html',
            'backup-manager.html',
            'advanced-features.html',
            
            // Générateurs
            'image-generator-simple.html',
            'video-generator.html',
            'music-generator.html',
            '3d-generator.html',
            
            // Systèmes avancés
            'brain-dashboard-live.html',
            'security-center.html',
            'chat-agents.html',
            
            // Scripts JS
            'js/advanced-generation-system.js',
            'js/generation-backup-system.js',
            'js/thermal-memory.js',
            'js/global-config.js'
        ];
        
        this.systemStatus = {
            filesPresent: 0,
            filesMissing: 0,
            scriptsLoaded: 0,
            scriptsError: 0,
            overallHealth: 0
        };
        
        console.log('🔍 Vérificateur d\'intégrité système initialisé');
        this.init();
    }

    async init() {
        try {
            console.log('🔍 Démarrage vérification intégrité...');
            
            // Vérifier les fichiers
            await this.checkFiles();
            
            // Vérifier les scripts
            await this.checkScripts();
            
            // Vérifier les systèmes
            await this.checkSystems();
            
            // Calculer la santé globale
            this.calculateOverallHealth();
            
            // Afficher le rapport
            this.displayReport();
            
        } catch (error) {
            console.error('❌ Erreur vérification intégrité:', error);
        }
    }

    async checkFiles() {
        console.log('📁 Vérification des fichiers...');
        
        for (const file of this.requiredFiles) {
            try {
                const response = await fetch(file, { method: 'HEAD' });
                if (response.ok) {
                    this.systemStatus.filesPresent++;
                    console.log(`✅ ${file} - OK`);
                } else {
                    this.systemStatus.filesMissing++;
                    console.warn(`⚠️ ${file} - Manquant ou inaccessible`);
                }
            } catch (error) {
                this.systemStatus.filesMissing++;
                console.warn(`❌ ${file} - Erreur: ${error.message}`);
            }
        }
    }

    async checkScripts() {
        console.log('📜 Vérification des scripts...');
        
        const scripts = [
            'window.advancedGenerationSystem',
            'window.generationBackupSystem',
            'window.thermalMemorySystem'
        ];

        scripts.forEach(script => {
            try {
                const exists = eval(script);
                if (exists) {
                    this.systemStatus.scriptsLoaded++;
                    console.log(`✅ ${script} - Chargé`);
                } else {
                    this.systemStatus.scriptsError++;
                    console.warn(`⚠️ ${script} - Non chargé`);
                }
            } catch (error) {
                this.systemStatus.scriptsError++;
                console.warn(`❌ ${script} - Erreur: ${error.message}`);
            }
        });
    }

    async checkSystems() {
        console.log('⚙️ Vérification des systèmes...');
        
        // Vérifier le système de génération
        if (window.advancedGenerationSystem) {
            if (window.advancedGenerationSystem.isInitialized) {
                console.log('✅ Système de génération - Initialisé');
            } else {
                console.warn('⚠️ Système de génération - Non initialisé');
            }
        }

        // Vérifier le système de sauvegarde
        if (window.generationBackupSystem) {
            console.log('✅ Système de sauvegarde - Actif');
        } else {
            console.warn('⚠️ Système de sauvegarde - Inactif');
        }

        // Vérifier le localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            console.log('✅ LocalStorage - Fonctionnel');
        } catch (error) {
            console.warn('⚠️ LocalStorage - Problème détecté');
        }

        // Vérifier les APIs du navigateur
        if ('storage' in navigator) {
            console.log('✅ Storage API - Disponible');
        } else {
            console.warn('⚠️ Storage API - Non disponible');
        }

        if ('serviceWorker' in navigator) {
            console.log('✅ Service Worker - Supporté');
        } else {
            console.warn('⚠️ Service Worker - Non supporté');
        }
    }

    calculateOverallHealth() {
        const totalFiles = this.requiredFiles.length;
        const totalScripts = 3; // Nombre de scripts critiques
        
        const fileHealth = (this.systemStatus.filesPresent / totalFiles) * 100;
        const scriptHealth = (this.systemStatus.scriptsLoaded / totalScripts) * 100;
        
        this.systemStatus.overallHealth = Math.round((fileHealth + scriptHealth) / 2);
        
        console.log(`📊 Santé globale du système: ${this.systemStatus.overallHealth}%`);
    }

    displayReport() {
        const report = `
🔍 RAPPORT D'INTÉGRITÉ SYSTÈME LOUNA AI
═══════════════════════════════════════

📁 FICHIERS:
   ✅ Présents: ${this.systemStatus.filesPresent}
   ❌ Manquants: ${this.systemStatus.filesMissing}

📜 SCRIPTS:
   ✅ Chargés: ${this.systemStatus.scriptsLoaded}
   ❌ Erreurs: ${this.systemStatus.scriptsError}

📊 SANTÉ GLOBALE: ${this.systemStatus.overallHealth}%

${this.getHealthStatus()}

═══════════════════════════════════════
        `;
        
        console.log(report);
        
        // Afficher dans l'interface si possible
        this.displayInUI();
    }

    getHealthStatus() {
        const health = this.systemStatus.overallHealth;
        
        if (health >= 90) {
            return '🟢 EXCELLENT - Tous les systèmes opérationnels';
        } else if (health >= 75) {
            return '🟡 BON - Quelques problèmes mineurs détectés';
        } else if (health >= 50) {
            return '🟠 MOYEN - Plusieurs problèmes nécessitent attention';
        } else {
            return '🔴 CRITIQUE - Intervention requise immédiatement';
        }
    }

    displayInUI() {
        // Créer une notification de statut
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #4caf50, #388e3c);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            font-family: 'Segoe UI', sans-serif;
        `;
        
        const health = this.systemStatus.overallHealth;
        let bgColor = '#4caf50';
        let icon = '✅';
        
        if (health < 90) {
            bgColor = '#ff9800';
            icon = '⚠️';
        }
        if (health < 50) {
            bgColor = '#f44336';
            icon = '❌';
        }
        
        notification.style.background = `linear-gradient(135deg, ${bgColor}, ${bgColor}dd)`;
        notification.innerHTML = `
            ${icon} Intégrité Système: ${health}%<br>
            <small>${this.getHealthStatus().split(' - ')[1]}</small>
        `;
        
        document.body.appendChild(notification);
        
        // Supprimer après 5 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    // Méthode pour forcer une nouvelle vérification
    async recheck() {
        console.log('🔄 Nouvelle vérification demandée...');
        
        // Réinitialiser les compteurs
        this.systemStatus = {
            filesPresent: 0,
            filesMissing: 0,
            scriptsLoaded: 0,
            scriptsError: 0,
            overallHealth: 0
        };
        
        await this.init();
    }

    // Méthode pour obtenir le statut actuel
    getStatus() {
        return {
            ...this.systemStatus,
            healthStatus: this.getHealthStatus(),
            timestamp: new Date().toISOString()
        };
    }

    // Méthode pour réparer les problèmes automatiquement
    async autoRepair() {
        console.log('🔧 Tentative de réparation automatique...');
        
        try {
            // Recharger les scripts manquants
            if (this.systemStatus.scriptsError > 0) {
                console.log('🔧 Rechargement des scripts...');
                
                // Recharger le système de génération
                if (!window.advancedGenerationSystem) {
                    await this.loadScript('js/advanced-generation-system.js');
                }
                
                // Recharger le système de sauvegarde
                if (!window.generationBackupSystem) {
                    await this.loadScript('js/generation-backup-system.js');
                }
            }
            
            // Nettoyer le localStorage si nécessaire
            this.cleanupStorage();
            
            console.log('✅ Réparation automatique terminée');
            
            // Nouvelle vérification
            await this.recheck();
            
        } catch (error) {
            console.error('❌ Erreur lors de la réparation:', error);
        }
    }

    async loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    cleanupStorage() {
        try {
            // Supprimer les données corrompues
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('louna_')) {
                    try {
                        JSON.parse(localStorage.getItem(key));
                    } catch (error) {
                        console.warn(`🧹 Suppression donnée corrompue: ${key}`);
                        localStorage.removeItem(key);
                    }
                }
            });
        } catch (error) {
            console.warn('⚠️ Erreur nettoyage storage:', error);
        }
    }
}

// Initialisation automatique
window.systemIntegrityChecker = new SystemIntegrityChecker();

// Vérification périodique (toutes les 5 minutes)
setInterval(() => {
    if (window.systemIntegrityChecker) {
        window.systemIntegrityChecker.recheck();
    }
}, 5 * 60 * 1000);

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemIntegrityChecker;
}
