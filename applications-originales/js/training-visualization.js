/**
 * Script pour la visualisation des résultats de formation
 */

// Variables globales
let trainingResults = null;
let chart = null;

// Éléments DOM
const visualizationContainer = document.getElementById('visualization-container');
const resultsContainer = document.getElementById('results-container');
const accuracyResult = document.getElementById('accuracy-result');
const lossResult = document.getElementById('loss-result');
const samplesResult = document.getElementById('samples-result');
const durationResult = document.getElementById('duration-result');
const memoryEntriesResult = document.getElementById('memory-entries-result');
const chartContainer = document.getElementById('chart-container');

/**
 * Initialise la visualisation des résultats
 * @param {Object} results - Résultats de la formation
 */
function initializeVisualization(results) {
    // Stocker les résultats
    trainingResults = results;

    // Afficher les résultats
    displayResults();

    // Créer le graphique
    createChart();
}

/**
 * Affiche les résultats de la formation
 */
function displayResults() {
    if (!trainingResults) {
        return;
    }

    // Afficher le conteneur de résultats
    resultsContainer.classList.add('visible');

    // Mettre à jour les résultats
    accuracyResult.textContent = `${(trainingResults.accuracy * 100).toFixed(2)}%`;
    lossResult.textContent = trainingResults.loss.toFixed(4);
    samplesResult.textContent = trainingResults.samplesProcessed;
    
    // Formater la durée
    const duration = trainingResults.duration ? Math.round(trainingResults.duration / 1000) : 0;
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    durationResult.textContent = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
    
    // Afficher le nombre d'entrées de mémoire créées
    memoryEntriesResult.textContent = trainingResults.memoryEntries;
}

/**
 * Crée un graphique pour visualiser les résultats de la formation
 */
function createChart() {
    if (!trainingResults || !trainingResults.progressHistory) {
        return;
    }

    // Préparer les données pour le graphique
    const labels = trainingResults.progressHistory.map((_, index) => index + 1);
    const accuracyData = trainingResults.progressHistory.map(entry => entry.accuracy * 100);
    const lossData = trainingResults.progressHistory.map(entry => entry.loss);

    // Créer le graphique
    const ctx = document.getElementById('training-chart').getContext('2d');
    
    // Détruire le graphique existant s'il y en a un
    if (chart) {
        chart.destroy();
    }
    
    // Créer un nouveau graphique
    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Précision (%)',
                    data: accuracyData,
                    borderColor: 'rgba(255, 105, 180, 1)',
                    backgroundColor: 'rgba(255, 105, 180, 0.2)',
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: 'Perte',
                    data: lossData,
                    borderColor: 'rgba(75, 0, 130, 1)',
                    backgroundColor: 'rgba(75, 0, 130, 0.2)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false
            },
            stacked: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Précision (%)'
                    },
                    min: 0,
                    max: 100
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Perte'
                    },
                    min: 0,
                    max: Math.max(...lossData) * 1.2,
                    grid: {
                        drawOnChartArea: false
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Progression de la Formation'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                if (context.datasetIndex === 0) {
                                    label += context.parsed.y.toFixed(2) + '%';
                                } else {
                                    label += context.parsed.y.toFixed(4);
                                }
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
}

/**
 * Crée un graphique radar pour visualiser les performances de l'agent
 * @param {Object} agentPerformance - Performances de l'agent
 */
function createPerformanceRadarChart(agentPerformance) {
    if (!agentPerformance) {
        return;
    }

    // Préparer les données pour le graphique
    const data = {
        labels: [
            'Compréhension',
            'Génération',
            'Raisonnement',
            'Mémoire',
            'Créativité',
            'Précision'
        ],
        datasets: [{
            label: 'Performances',
            data: [
                agentPerformance.understanding * 100,
                agentPerformance.generation * 100,
                agentPerformance.reasoning * 100,
                agentPerformance.memory * 100,
                agentPerformance.creativity * 100,
                agentPerformance.accuracy * 100
            ],
            fill: true,
            backgroundColor: 'rgba(255, 105, 180, 0.2)',
            borderColor: 'rgba(255, 105, 180, 1)',
            pointBackgroundColor: 'rgba(255, 105, 180, 1)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgba(255, 105, 180, 1)'
        }]
    };

    // Créer le graphique
    const ctx = document.getElementById('performance-chart').getContext('2d');
    
    // Créer un nouveau graphique
    new Chart(ctx, {
        type: 'radar',
        data: data,
        options: {
            elements: {
                line: {
                    borderWidth: 3
                }
            },
            scales: {
                r: {
                    angleLines: {
                        display: true
                    },
                    suggestedMin: 0,
                    suggestedMax: 100
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Performances de l\'Agent'
                }
            }
        }
    });
}

/**
 * Crée un graphique de comparaison entre l'agent principal et l'agent de formation
 * @param {Object} mainAgentPerformance - Performances de l'agent principal
 * @param {Object} trainingAgentPerformance - Performances de l'agent de formation
 */
function createComparisonChart(mainAgentPerformance, trainingAgentPerformance) {
    if (!mainAgentPerformance || !trainingAgentPerformance) {
        return;
    }

    // Préparer les données pour le graphique
    const data = {
        labels: [
            'Compréhension',
            'Génération',
            'Raisonnement',
            'Mémoire',
            'Créativité',
            'Précision'
        ],
        datasets: [
            {
                label: 'Agent Principal',
                data: [
                    mainAgentPerformance.understanding * 100,
                    mainAgentPerformance.generation * 100,
                    mainAgentPerformance.reasoning * 100,
                    mainAgentPerformance.memory * 100,
                    mainAgentPerformance.creativity * 100,
                    mainAgentPerformance.accuracy * 100
                ],
                backgroundColor: 'rgba(255, 105, 180, 0.5)'
            },
            {
                label: 'Agent de Formation',
                data: [
                    trainingAgentPerformance.understanding * 100,
                    trainingAgentPerformance.generation * 100,
                    trainingAgentPerformance.reasoning * 100,
                    trainingAgentPerformance.memory * 100,
                    trainingAgentPerformance.creativity * 100,
                    trainingAgentPerformance.accuracy * 100
                ],
                backgroundColor: 'rgba(75, 0, 130, 0.5)'
            }
        ]
    };

    // Créer le graphique
    const ctx = document.getElementById('comparison-chart').getContext('2d');
    
    // Créer un nouveau graphique
    new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Comparaison des Performances'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// Exporter les fonctions
window.trainingVisualization = {
    initializeVisualization,
    createPerformanceRadarChart,
    createComparisonChart
};
