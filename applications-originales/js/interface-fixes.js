/* ===== CORRECTIONS D'INTERFACE LOUNA AI ===== */

// Correction globale des problèmes d'interface
class InterfaceFixes {
    constructor() {
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.applyFixes());
        } else {
            this.applyFixes();
        }
    }

    applyFixes() {
        console.log('🔧 Application des corrections d\'interface...');
        
        // Corriger les boutons de navigation en double
        this.fixDuplicateNavigation();
        
        // Corriger les problèmes de mise à jour des données
        this.fixDataUpdates();
        
        // Corriger les problèmes de positionnement
        this.fixPositioning();
        
        // Corriger les erreurs de chargement
        this.fixLoadingErrors();
        
        console.log('✅ Corrections d\'interface appliquées');
    }

    fixDuplicateNavigation() {
        const currentPath = window.location.pathname;
        
        // Si on n'est pas sur la page principale, masquer les contrôles de sécurité
        if (!currentPath.includes('interface-originale-complete.html')) {
            const securityControls = document.querySelector('.security-controls');
            if (securityControls) {
                securityControls.style.display = 'none';
                console.log('🔧 Boutons de sécurité masqués');
            }
            
            // S'assurer que l'en-tête unifié est visible et bien positionné
            const lounaHeader = document.querySelector('.louna-header');
            if (lounaHeader) {
                lounaHeader.style.display = 'block';
                lounaHeader.style.position = 'relative';
                lounaHeader.style.zIndex = '1000';
                lounaHeader.style.marginBottom = '0';
                console.log('🔧 En-tête unifié corrigé');
            }
        }
    }

    fixDataUpdates() {
        // Corriger les problèmes de mise à jour des données dans les interfaces
        const currentPath = window.location.pathname;
        
        if (currentPath.includes('evolution-learning-center.html')) {
            // Forcer le rechargement des données d'évolution
            setTimeout(() => {
                if (window.loadEvolutionData && typeof window.loadEvolutionData === 'function') {
                    window.loadEvolutionData();
                    console.log('🔧 Données d\'évolution rechargées');
                }
            }, 1000);
        }
        
        if (currentPath.includes('brain-visualization-3d.html')) {
            // Forcer la mise à jour des statistiques 3D
            setTimeout(() => {
                if (window.updateStats && typeof window.updateStats === 'function') {
                    window.updateStats();
                    console.log('🔧 Statistiques 3D mises à jour');
                }
            }, 1000);
        }
    }

    fixPositioning() {
        // Corriger les problèmes de positionnement des éléments
        const style = document.createElement('style');
        style.textContent = `
            /* Corrections de positionnement */
            .louna-header {
                position: relative !important;
                z-index: 1000 !important;
                margin-bottom: 0 !important;
            }
            
            .security-controls {
                position: fixed !important;
                top: 20px !important;
                right: 20px !important;
                z-index: 1001 !important;
            }
            
            /* Éviter les chevauchements */
            .evolution-container,
            .brain-3d-container,
            .visualization-area {
                margin-top: 20px !important;
                padding-top: 20px !important;
            }
            
            /* Améliorer la lisibilité */
            .stat-value {
                word-break: break-word !important;
                overflow-wrap: break-word !important;
                hyphens: auto !important;
            }
            
            /* Corriger les boutons qui se chevauchent */
            .louna-nav {
                display: flex !important;
                gap: 10px !important;
                flex-wrap: wrap !important;
            }
            
            .louna-nav-btn {
                margin: 0 !important;
                flex-shrink: 0 !important;
            }
        `;
        document.head.appendChild(style);
        console.log('🔧 Styles de positionnement appliqués');
    }

    fixLoadingErrors() {
        // Corriger les erreurs de chargement communes
        
        // Vérifier si les scripts essentiels sont chargés
        const essentialScripts = [
            'louna-notifications.js',
            'thermal-data-api.js',
            'louna-navigation.js'
        ];
        
        essentialScripts.forEach(script => {
            if (!this.isScriptLoaded(script)) {
                console.warn(`⚠️ Script manquant: ${script}`);
                this.loadMissingScript(script);
            }
        });
        
        // Corriger les erreurs de données manquantes
        this.setupFallbackData();
    }

    isScriptLoaded(scriptName) {
        const scripts = document.querySelectorAll('script[src]');
        return Array.from(scripts).some(script => script.src.includes(scriptName));
    }

    loadMissingScript(scriptName) {
        const script = document.createElement('script');
        script.src = `js/${scriptName}`;
        script.onerror = () => {
            console.warn(`❌ Impossible de charger: ${scriptName}`);
        };
        document.head.appendChild(script);
    }

    setupFallbackData() {
        // Données de fallback pour éviter les erreurs
        if (!window.thermalDataAPI) {
            window.thermalDataAPI = {
                getRealThermalData: () => Promise.resolve({
                    neurones: { total: 86000000000, actifs: 86000000000 * 0.94 },
                    synapses: { total: 602000000000000, actives: 602000000000000 * 0.94 },
                    formations: { total: 14, actives: 3 },
                    temperature: 37.2,
                    qi: 185,
                    activiteGlobale: 94,
                    zoneActive: "Zone 5"
                })
            };
            console.log('🔧 API thermique de fallback créée');
        }
        
        // Fonctions de notification de fallback
        if (!window.showSuccess) {
            window.showSuccess = (msg) => console.log('✅', msg);
        }
        if (!window.showError) {
            window.showError = (msg) => console.error('❌', msg);
        }
        if (!window.showInfo) {
            window.showInfo = (msg) => console.info('ℹ️', msg);
        }
        if (!window.showWarning) {
            window.showWarning = (msg) => console.warn('⚠️', msg);
        }
    }

    // Méthode pour forcer la correction manuelle
    forceFixAll() {
        console.log('🔧 Correction forcée de tous les problèmes...');
        this.applyFixes();
        
        // Recharger les données après un délai
        setTimeout(() => {
            if (window.location.pathname.includes('evolution-learning-center.html')) {
                if (window.loadEvolutionData) window.loadEvolutionData();
            }
            if (window.location.pathname.includes('brain-visualization-3d.html')) {
                if (window.updateStats) window.updateStats();
                if (window.generateNeuronNetwork) window.generateNeuronNetwork();
            }
        }, 2000);
        
        console.log('✅ Correction forcée terminée');
    }
}

// Initialiser les corrections
window.interfaceFixes = new InterfaceFixes();

// Fonction globale pour forcer les corrections
window.fixAllInterfaces = () => {
    window.interfaceFixes.forceFixAll();
};

// Correction automatique toutes les 30 secondes pour les interfaces problématiques
setInterval(() => {
    const currentPath = window.location.pathname;
    if (currentPath.includes('evolution-learning-center.html') || 
        currentPath.includes('brain-visualization-3d.html')) {
        window.interfaceFixes.fixDataUpdates();
    }
}, 30000);

console.log('🔧 Système de corrections d\'interface initialisé');
