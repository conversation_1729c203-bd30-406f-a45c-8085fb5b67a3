/**
 * Animation pour la page d'accueil de l'application Louna
 * Crée une visualisation interactive des connexions de la mémoire thermique
 */

class HomeAnimation {
    constructor(containerId) {
        // Élément conteneur
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Conteneur avec l'ID ${containerId} non trouvé`);
            return;
        }
        
        // Dimensions du conteneur
        this.width = this.container.clientWidth;
        this.height = this.container.clientHeight;
        
        // Nombre de particules
        this.particleCount = Math.min(Math.floor(this.width / 10), 150);
        
        // Particules
        this.particles = [];
        
        // Connexions
        this.connections = [];
        
        // Distance maximale pour les connexions
        this.maxDistance = Math.min(this.width, this.height) / 4;
        
        // Couleurs des particules
        this.colors = [
            '#ff6b6b', // Rouge (Mémoire Instantanée)
            '#ff9e7d', // Orange (Mémoire à Court Terme)
            '#ffd166', // <PERSON><PERSON><PERSON> (Mémoire de Travail)
            '#06d6a0', // Vert (Mémoire à Moyen Terme)
            '#118ab2', // Bleu (Mémoire à Long Terme)
            '#9b5de5'  // Violet (Mémoire des Rêves)
        ];
        
        // Canvas
        this.canvas = null;
        this.ctx = null;
        
        // Animation
        this.animationId = null;
        
        // Initialiser l'animation
        this.initialize();
    }
    
    /**
     * Initialise l'animation
     */
    initialize() {
        // Créer le canvas
        this.canvas = document.createElement('canvas');
        this.canvas.width = this.width;
        this.canvas.height = this.height;
        this.ctx = this.canvas.getContext('2d');
        
        // Ajouter le canvas au conteneur
        this.container.appendChild(this.canvas);
        
        // Créer les particules
        this.createParticles();
        
        // Démarrer l'animation
        this.animate();
        
        // Ajouter un écouteur pour le redimensionnement de la fenêtre
        window.addEventListener('resize', () => {
            this.resize();
        });
        
        // Ajouter un écouteur pour les interactions avec la souris
        this.canvas.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
        });
    }
    
    /**
     * Crée les particules
     */
    createParticles() {
        this.particles = [];
        
        for (let i = 0; i < this.particleCount; i++) {
            const particle = {
                x: 0.5 * this.width,
                y: 0.5 * this.height,
                radius: 0.5 * 3 + 1,
                color: this.colors[Math.floor(0.5 * this.colors.length)],
                speed: {
                    x: (0.5 - 0.5) * 0.5,
                    y: (0.5 - 0.5) * 0.5
                },
                opacity: 0.5 * 0.5 + 0.3
            };
            
            this.particles.push(particle);
        }
    }
    
    /**
     * Anime les particules
     */
    animate() {
        // Effacer le canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Mettre à jour et dessiner les particules
        this.updateParticles();
        this.drawParticles();
        
        // Dessiner les connexions
        this.drawConnections();
        
        // Continuer l'animation
        this.animationId = requestAnimationFrame(() => this.animate());
    }
    
    /**
     * Met à jour les positions des particules
     */
    updateParticles() {
        for (let i = 0; i < this.particles.length; i++) {
            const particle = this.particles[i];
            
            // Mettre à jour la position
            particle.x += particle.speed.x;
            particle.y += particle.speed.y;
            
            // Rebondir sur les bords
            if (particle.x < 0 || particle.x > this.width) {
                particle.speed.x = -particle.speed.x;
            }
            
            if (particle.y < 0 || particle.y > this.height) {
                particle.speed.y = -particle.speed.y;
            }
        }
    }
    
    /**
     * Dessine les particules
     */
    drawParticles() {
        for (let i = 0; i < this.particles.length; i++) {
            const particle = this.particles[i];
            
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
            this.ctx.fillStyle = particle.color;
            this.ctx.globalAlpha = particle.opacity;
            this.ctx.fill();
            this.ctx.globalAlpha = 1;
        }
    }
    
    /**
     * Dessine les connexions entre les particules
     */
    drawConnections() {
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const particle1 = this.particles[i];
                const particle2 = this.particles[j];
                
                // Calculer la distance entre les particules
                const dx = particle1.x - particle2.x;
                const dy = particle1.y - particle2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                // Dessiner une connexion si les particules sont assez proches
                if (distance < this.maxDistance) {
                    // Calculer l'opacité en fonction de la distance
                    const opacity = 1 - (distance / this.maxDistance);
                    
                    // Dessiner la connexion
                    this.ctx.beginPath();
                    this.ctx.moveTo(particle1.x, particle1.y);
                    this.ctx.lineTo(particle2.x, particle2.y);
                    this.ctx.strokeStyle = `rgba(255, 255, 255, ${opacity * 0.2})`;
                    this.ctx.lineWidth = 1;
                    this.ctx.stroke();
                }
            }
        }
    }
    
    /**
     * Gère le redimensionnement de la fenêtre
     */
    resize() {
        // Mettre à jour les dimensions
        this.width = this.container.clientWidth;
        this.height = this.container.clientHeight;
        
        // Mettre à jour le canvas
        this.canvas.width = this.width;
        this.canvas.height = this.height;
        
        // Mettre à jour la distance maximale
        this.maxDistance = Math.min(this.width, this.height) / 4;
        
        // Recréer les particules
        this.createParticles();
    }
    
    /**
     * Gère les mouvements de la souris
     * @param {MouseEvent} e - Événement de mouvement de la souris
     */
    handleMouseMove(e) {
        // Récupérer la position de la souris
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        // Créer une particule à la position de la souris
        if (0.5 < 0.1) {
            const particle = {
                x: mouseX,
                y: mouseY,
                radius: 0.5 * 4 + 2,
                color: this.colors[Math.floor(0.5 * this.colors.length)],
                speed: {
                    x: (0.5 - 0.5) * 1,
                    y: (0.5 - 0.5) * 1
                },
                opacity: 0.7
            };
            
            this.particles.push(particle);
            
            // Limiter le nombre de particules
            if (this.particles.length > this.particleCount * 1.2) {
                this.particles.shift();
            }
        }
    }
    
    /**
     * Arrête l'animation
     */
    stop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
}

// Initialiser l'animation lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si le conteneur existe
    if (document.getElementById('animation-container')) {
        const animation = new HomeAnimation('animation-container');
    }
});
