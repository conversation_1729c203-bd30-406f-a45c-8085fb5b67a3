/**
 * HUB CENTRAL DE CHAT - MAÎTRE ABSOLU DE TOUTE L'APPLICATION LOUNA
 * Ce script gère l'interface de chat qui CONTRÔLE TOUT
 */

(function() {
    'use strict';
    
    console.log('👑 [MAÎTRE ABSOLU] Initialisation du contrôle total...');
    
    // ===== CONFIGURATION DES APPLICATIONS RÉELLES =====
    const REAL_APPLICATIONS = {
        'Génération & Création': [
            {
                id: 'ltx-video',
                name: 'LTX Vidéo - Génération Films',
                description: 'Génération de films en temps réel',
                icon: 'fas fa-film',
                url: '/generation-studio.html',
                status: 'online',
                keywords: ['film', 'vidéo', 'ltx', 'génération', 'cinéma'],
                masterCommand: 'generateFilm'
            },
            {
                id: 'music-studio',
                name: 'Studio Musical',
                description: 'Composition musicale automatique',
                icon: 'fas fa-music',
                url: '/generation-studio.html',
                status: 'online',
                keywords: ['musique', 'composition', 'audio', 'chanson'],
                masterCommand: 'generateMusic'
            },
            {
                id: 'code-generator',
                name: 'Générateur de Code',
                description: 'Écriture de code automatique',
                icon: 'fas fa-code',
                url: '/code-editor',
                status: 'online',
                keywords: ['code', 'programmation', 'développement', 'script'],
                masterCommand: 'generateCode'
            }
        ],
        'Interface & Monitoring': [
            {
                id: 'thermal-memory',
                name: 'Mémoire Thermique',
                description: 'Interface de mémoire thermique avancée',
                icon: 'fas fa-fire',
                url: '/futuristic-interface.html',
                status: 'online',
                keywords: ['mémoire', 'thermique', 'température', 'zones'],
                masterCommand: 'controlThermalMemory'
            },
            {
                id: 'brain-visualization',
                name: 'Visualisation Cerveau 3D',
                description: 'Visualisation 3D du cerveau artificiel',
                icon: 'fas fa-brain',
                url: '/brain-visualization.html',
                status: 'online',
                keywords: ['cerveau', 'visualisation', '3d', 'neurones'],
                masterCommand: 'showBrain'
            },
            {
                id: 'kyber-dashboard',
                name: 'Accélérateurs Kyber',
                description: 'Tableau de bord des accélérateurs',
                icon: 'fas fa-bolt',
                url: '/kyber-dashboard.html',
                status: 'online',
                keywords: ['kyber', 'accélérateurs', 'performance', 'boost'],
                masterCommand: 'controlKyber'
            },
            {
                id: 'qi-neuron-monitor',
                name: 'Monitoring QI/Neurones',
                description: 'Surveillance QI et neurones en temps réel',
                icon: 'fas fa-chart-line',
                url: '/qi-neuron-monitor.html',
                status: 'online',
                keywords: ['qi', 'neurones', 'monitoring', 'statistiques'],
                masterCommand: 'showStats'
            }
        ],
        'Développement & Code': [
            {
                id: 'code-editor',
                name: 'Éditeur de Code',
                description: 'Éditeur de code intégré',
                icon: 'fas fa-code',
                url: '/code-editor',
                status: 'online',
                keywords: ['code', 'éditeur', 'programmation', 'développement'],
                masterCommand: 'openCodeEditor'
            },
            {
                id: 'coding-evolution',
                name: 'Évolution Codage',
                description: 'Système d\'évolution du codage',
                icon: 'fas fa-dna',
                url: '/coding-evolution.html',
                status: 'online',
                keywords: ['évolution', 'apprentissage', 'amélioration'],
                masterCommand: 'evolveCoding'
            }
        ],
        'Agents & Formation': [
            {
                id: 'agents',
                name: 'Gestion Agents',
                description: 'Gestion des agents IA',
                icon: 'fas fa-users',
                url: '/agents.html',
                status: 'online',
                keywords: ['agents', 'ia', 'gestion', 'équipe'],
                masterCommand: 'manageAgents'
            },
            {
                id: 'training',
                name: 'Formation',
                description: 'Formation et apprentissage',
                icon: 'fas fa-graduation-cap',
                url: '/training.html',
                status: 'online',
                keywords: ['formation', 'apprentissage', 'entraînement'],
                masterCommand: 'startTraining'
            }
        ],
        'Système & Sécurité': [
            {
                id: 'security-dashboard',
                name: 'Sécurité',
                description: 'Tableau de bord sécurité',
                icon: 'fas fa-shield-alt',
                url: '/security-dashboard.html',
                status: 'online',
                keywords: ['sécurité', 'protection', 'firewall'],
                masterCommand: 'controlSecurity'
            },
            {
                id: 'settings',
                name: 'Paramètres',
                description: 'Configuration système',
                icon: 'fas fa-cog',
                url: '/settings',
                status: 'online',
                keywords: ['paramètres', 'configuration', 'réglages'],
                masterCommand: 'openSettings'
            }
        ]
    };

    // ===== COMMANDES MAÎTRES =====
    const MASTER_COMMANDS = {
        // Génération de films
        generateFilm: {
            name: 'Génération de Film',
            action: (prompt) => {
                addSystemMessage('🎬 GÉNÉRATION DE FILM EN COURS...');
                openApplication(REAL_APPLICATIONS['Génération & Création'][0]);
                
                setTimeout(() => {
                    addAssistantMessage(`🎬 <strong>Film en cours de génération !</strong><br><br>
                        📝 <strong>Prompt:</strong> ${prompt}<br>
                        🎯 <strong>Type:</strong> Film LTX Vidéo<br>
                        ⏱️ <strong>Durée estimée:</strong> 2-5 minutes<br>
                        🎨 <strong>Qualité:</strong> 4K Ultra HD<br><br>
                        L'application LTX Vidéo s'ouvre automatiquement pour générer votre film !`, [
                        { text: 'Voir le Progrès', action: () => openApplication(REAL_APPLICATIONS['Génération & Création'][0]) },
                        { text: 'Modifier le Prompt', action: () => promptUser('Nouveau prompt pour le film:') }
                    ]);
                }, 1000);
            }
        },
        
        // Génération de musique
        generateMusic: {
            name: 'Génération de Musique',
            action: (prompt) => {
                addSystemMessage('🎵 COMPOSITION MUSICALE EN COURS...');
                openApplication(REAL_APPLICATIONS['Génération & Création'][1]);
                
                setTimeout(() => {
                    addAssistantMessage(`🎵 <strong>Musique en cours de composition !</strong><br><br>
                        📝 <strong>Style:</strong> ${prompt}<br>
                        🎼 <strong>Durée:</strong> 3-4 minutes<br>
                        🎹 <strong>Instruments:</strong> Orchestration complète<br>
                        🎧 <strong>Qualité:</strong> Studio HD<br><br>
                        Le studio musical génère votre composition en temps réel !`, [
                        { text: 'Écouter l\'Aperçu', action: () => playMusicPreview() },
                        { text: 'Changer le Style', action: () => promptUser('Nouveau style musical:') }
                    ]);
                }, 1000);
            }
        },
        
        // Génération de code
        generateCode: {
            name: 'Génération de Code',
            action: (prompt) => {
                addSystemMessage('💻 ÉCRITURE DE CODE EN COURS...');
                openApplication(REAL_APPLICATIONS['Développement & Code'][0]);
                
                setTimeout(() => {
                    addAssistantMessage(`💻 <strong>Code en cours d'écriture !</strong><br><br>
                        📝 <strong>Demande:</strong> ${prompt}<br>
                        🔧 <strong>Langage:</strong> Auto-détecté<br>
                        📊 <strong>Complexité:</strong> Optimisée<br>
                        ✅ <strong>Tests:</strong> Inclus<br><br>
                        L'éditeur de code s'ouvre et écrit automatiquement votre programme !`, [
                        { text: 'Voir le Code', action: () => openApplication(REAL_APPLICATIONS['Développement & Code'][0]) },
                        { text: 'Modifier la Demande', action: () => promptUser('Nouvelle demande de code:') }
                    ]);
                }, 1000);
            }
        },
        
        // Contrôle mémoire thermique
        controlThermalMemory: {
            name: 'Contrôle Mémoire Thermique',
            action: () => {
                addSystemMessage('🧠 ACCÈS À LA MÉMOIRE THERMIQUE...');
                openApplication(REAL_APPLICATIONS['Interface & Monitoring'][0]);
                
                setTimeout(() => {
                    addAssistantMessage(`🧠 <strong>Mémoire Thermique Activée !</strong><br><br>
                        🌡️ <strong>Température:</strong> Optimale<br>
                        ⚡ <strong>Accélérateurs:</strong> 3 actifs<br>
                        📊 <strong>Performance:</strong> 98.5%<br>
                        🔄 <strong>Cycles:</strong> En cours<br><br>
                        Interface thermique ouverte - Contrôle total disponible !`, [
                        { text: 'Optimiser', action: () => optimizeThermalMemory() },
                        { text: 'Statistiques', action: () => showThermalStats() }
                    ]);
                }, 1000);
            }
        }
    };

    // ===== VARIABLES GLOBALES =====
    let currentActiveApp = null;
    let socket = null;
    let isConnected = false;
    
    // ===== FONCTIONS UTILITAIRES =====
    
    function log(message, type = 'info') {
        const prefix = '👑 [MAÎTRE ABSOLU]';
        console[type](`${prefix} ${message}`);
    }
    
    function formatTime(date = new Date()) {
        return date.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#4CAF50'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-size: 14px;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    // ===== GESTION DES APPLICATIONS =====
    
    function renderApplications() {
        const container = document.getElementById('appsContainer');
        if (!container) return;
        
        container.innerHTML = '';
        
        Object.entries(REAL_APPLICATIONS).forEach(([category, apps]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'app-category';
            
            const categoryTitle = document.createElement('div');
            categoryTitle.className = 'category-title';
            categoryTitle.innerHTML = `<i class="fas fa-folder"></i> ${category}`;
            categoryDiv.appendChild(categoryTitle);
            
            apps.forEach(app => {
                const appItem = document.createElement('div');
                appItem.className = 'app-item';
                appItem.dataset.appId = app.id;
                
                appItem.innerHTML = `
                    <div class="app-icon">
                        <i class="${app.icon}"></i>
                    </div>
                    <div class="app-info">
                        <div class="app-name">${app.name}</div>
                        <div class="app-description">${app.description}</div>
                    </div>
                    <div class="app-status ${app.status}"></div>
                `;
                
                appItem.addEventListener('click', () => openApplication(app));
                categoryDiv.appendChild(appItem);
            });
            
            container.appendChild(categoryDiv);
        });
        
        log('Applications réelles rendues dans la sidebar');
    }
    
    function openApplication(app) {
        log(`Ouverture de l'application: ${app.name}`);
        
        // Marquer comme active
        setActiveApp(app);
        
        // Ajouter un message système
        addSystemMessage(`🚀 Ouverture de ${app.name}...`);
        
        // Ouvrir l'application
        if (window.electronAPI) {
            // Mode Electron - ouvrir dans une nouvelle fenêtre
            window.electronAPI.openWindow({
                url: app.url,
                title: app.name,
                width: 1200,
                height: 800
            });
        } else {
            // Mode navigateur - ouvrir dans un nouvel onglet
            window.open(app.url, '_blank');
        }
        
        // Ajouter des actions contextuelles
        setTimeout(() => {
            addAssistantMessage(`✅ ${app.name} est maintenant ouvert !<br><br>
                📱 <strong>Application:</strong> ${app.name}<br>
                🔗 <strong>URL:</strong> ${app.url}<br>
                ⚡ <strong>Statut:</strong> Opérationnel<br><br>
                Que souhaitez-vous faire avec cette application ?`, [
                { text: 'Aide', action: () => showAppHelp(app) },
                { text: 'Actualiser', action: () => refreshApplication(app) },
                { text: 'Fermer', action: () => closeApplication(app) }
            ]);
        }, 1000);
    }
    
    function setActiveApp(app) {
        // Retirer l'état actif de tous les éléments
        document.querySelectorAll('.app-item.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // Ajouter l'état actif à l'application sélectionnée
        const appElement = document.querySelector(`[data-app-id="${app.id}"]`);
        if (appElement) {
            appElement.classList.add('active');
        }
        
        // Mettre à jour l'indicateur
        const indicator = document.getElementById('activeAppIndicator');
        if (indicator) {
            indicator.textContent = app.name;
        }
        
        currentActiveApp = app;
    }
    
    // ===== GESTION DES MESSAGES =====
    
    function addSystemMessage(content) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system';
        
        messageDiv.innerHTML = `
            <div class="message-avatar system">
                <i class="fas fa-crown"></i>
            </div>
            <div class="message-bubble">
                <div class="message-content">${content}</div>
                <div class="message-time">${formatTime()}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function addAssistantMessage(content, actions = []) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';
        
        let actionsHTML = '';
        if (actions.length > 0) {
            actionsHTML = '<div class="app-action-buttons">';
            actions.forEach((action, index) => {
                actionsHTML += `<button class="app-action-btn" onclick="window.masterActions[${index}]()">${action.text}</button>`;
            });
            actionsHTML += '</div>';
            
            // Stocker les actions globalement
            if (!window.masterActions) window.masterActions = [];
            actions.forEach((action, index) => {
                window.masterActions[index] = action.action;
            });
        }
        
        messageDiv.innerHTML = `
            <div class="message-avatar assistant">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-bubble">
                <div class="message-content">${content}</div>
                ${actionsHTML}
                <div class="message-time">${formatTime()}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // ===== TRAITEMENT DES COMMANDES MAÎTRES =====
    
    function processMasterCommand(message) {
        const lowerMessage = message.toLowerCase();
        
        // Commandes de génération de films
        if (lowerMessage.includes('génère') && (lowerMessage.includes('film') || lowerMessage.includes('vidéo'))) {
            MASTER_COMMANDS.generateFilm.action(message);
            return true;
        }
        
        // Commandes de génération de musique
        if (lowerMessage.includes('compose') || lowerMessage.includes('musique') || lowerMessage.includes('chanson')) {
            MASTER_COMMANDS.generateMusic.action(message);
            return true;
        }
        
        // Commandes de génération de code
        if (lowerMessage.includes('écris') && lowerMessage.includes('code') || lowerMessage.includes('programme')) {
            MASTER_COMMANDS.generateCode.action(message);
            return true;
        }
        
        // Commandes de mémoire thermique
        if (lowerMessage.includes('mémoire') && lowerMessage.includes('thermique')) {
            MASTER_COMMANDS.controlThermalMemory.action();
            return true;
        }
        
        // Rechercher des applications par mots-clés
        let foundApps = [];
        Object.values(REAL_APPLICATIONS).flat().forEach(app => {
            if (app.keywords.some(keyword => lowerMessage.includes(keyword)) ||
                lowerMessage.includes(app.name.toLowerCase())) {
                foundApps.push(app);
            }
        });
        
        if (foundApps.length > 0) {
            if (foundApps.length === 1) {
                openApplication(foundApps[0]);
            } else {
                let response = "🎯 <strong>Plusieurs applications trouvées :</strong><br><br>";
                foundApps.forEach(app => {
                    response += `• <strong>${app.name}</strong> - ${app.description}<br>`;
                });
                response += "<br>Laquelle souhaitez-vous ouvrir ?";
                
                const actions = foundApps.map(app => ({
                    text: app.name,
                    action: () => openApplication(app)
                }));
                
                addAssistantMessage(response, actions);
            }
            return true;
        }
        
        return false; // Commande non reconnue
    }
    
    // ===== GESTIONNAIRES D'ÉVÉNEMENTS =====
    
    function setupEventListeners() {
        // Toggle sidebar
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('appsSidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('open');
            });
        }
        
        // Boutons maîtres
        const generateFilmBtn = document.getElementById('generateFilmBtn');
        if (generateFilmBtn) {
            generateFilmBtn.addEventListener('click', () => {
                const prompt = prompt('Décrivez le film que vous voulez générer:') || 'Film de science-fiction épique';
                MASTER_COMMANDS.generateFilm.action(prompt);
            });
        }
        
        const generateMusicBtn = document.getElementById('generateMusicBtn');
        if (generateMusicBtn) {
            generateMusicBtn.addEventListener('click', () => {
                const prompt = prompt('Quel style de musique voulez-vous?') || 'Musique relaxante et inspirante';
                MASTER_COMMANDS.generateMusic.action(prompt);
            });
        }
        
        const generateCodeBtn = document.getElementById('generateCodeBtn');
        if (generateCodeBtn) {
            generateCodeBtn.addEventListener('click', () => {
                const prompt = prompt('Quel code voulez-vous que j\'écrive?') || 'Application web moderne';
                MASTER_COMMANDS.generateCode.action(prompt);
            });
        }
        
        const thermalMemoryBtn = document.getElementById('thermalMemoryBtn');
        if (thermalMemoryBtn) {
            thermalMemoryBtn.addEventListener('click', () => {
                MASTER_COMMANDS.controlThermalMemory.action();
            });
        }
        
        // Champ de saisie
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        
        if (messageInput && sendButton) {
            sendButton.addEventListener('click', () => {
                const message = messageInput.value.trim();
                if (message) {
                    sendMasterMessage(message);
                    messageInput.value = '';
                }
            });
            
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const message = messageInput.value.trim();
                    if (message) {
                        sendMasterMessage(message);
                        messageInput.value = '';
                    }
                }
            });
        }
    }
    
    function sendMasterMessage(message) {
        // Ajouter le message de l'utilisateur
        addUserMessage(message);
        
        // Traiter comme une commande maître
        if (!processMasterCommand(message)) {
            // Si ce n'est pas une commande reconnue, répondre avec les options
            addAssistantMessage(`🤔 <strong>Commande non reconnue.</strong><br><br>
                Voici ce que je peux faire pour vous :<br><br>
                🎬 <strong>Films:</strong> "Génère un film de science-fiction"<br>
                🎵 <strong>Musique:</strong> "Compose une chanson relaxante"<br>
                💻 <strong>Code:</strong> "Écris du code Python"<br>
                🧠 <strong>Mémoire:</strong> "Ouvre la mémoire thermique"<br><br>
                Ou cliquez sur une application dans la sidebar !`, [
                { text: 'Générer Film', action: () => document.getElementById('generateFilmBtn').click() },
                { text: 'Composer Musique', action: () => document.getElementById('generateMusicBtn').click() },
                { text: 'Écrire Code', action: () => document.getElementById('generateCodeBtn').click() },
                { text: 'Mémoire Thermique', action: () => document.getElementById('thermalMemoryBtn').click() }
            ]);
        }
    }
    
    function addUserMessage(content) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user';
        
        messageDiv.innerHTML = `
            <div class="message-avatar user">
                <i class="fas fa-user"></i>
            </div>
            <div class="message-bubble">
                <div class="message-content">${content}</div>
                <div class="message-time">${formatTime()}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // ===== INITIALISATION =====
    
    function initialize() {
        log('👑 Démarrage du MAÎTRE ABSOLU de l\'application Louna');
        
        // Rendre les applications
        renderApplications();
        
        // Configurer les gestionnaires d'événements
        setupEventListeners();
        
        // Afficher l'heure de bienvenue
        const welcomeTime = document.getElementById('welcomeTime');
        if (welcomeTime) {
            welcomeTime.textContent = formatTime();
        }
        
        // Mettre à jour le statut de connexion
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionText = document.getElementById('connectionText');
        if (connectionStatus && connectionText) {
            connectionStatus.classList.remove('disconnected');
            connectionText.textContent = 'MAÎTRE ACTIF';
        }
        
        log('✅ MAÎTRE ABSOLU initialisé avec succès - Contrôle total activé');
        showNotification('👑 MAÎTRE ABSOLU ACTIVÉ ! Contrôle total de l\'application Louna.', 'success');
    }
    
    // ===== DÉMARRAGE =====
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // ===== EXPOSITION GLOBALE =====
    
    window.MasterHub = {
        openApplication: openApplication,
        processMasterCommand: processMasterCommand,
        REAL_APPLICATIONS: REAL_APPLICATIONS,
        MASTER_COMMANDS: MASTER_COMMANDS
    };
    
})();
