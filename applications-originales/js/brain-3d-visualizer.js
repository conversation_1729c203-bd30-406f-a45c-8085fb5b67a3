/**
 * Visualiseur 3D du Cerveau Artificiel de Louna
 * Affichage en temps réel des neurones, connexions et activité cérébrale
 */

class Brain3DVisualizer {
    constructor(containerId) {
        this.containerId = containerId;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;

        // Objets 3D
        this.neurons = [];
        this.connections = [];
        this.brainRegions = {};

        // Animation
        this.animationId = null;
        this.isAnimating = false;

        // Données du cerveau
        this.brainData = {
            neurons: [],
            connections: [],
            activity: [],
            regions: {
                sensory: { color: 0x00ff00, position: { x: -2, y: 1, z: 0 } },
                working: { color: 0x0088ff, position: { x: 0, y: 1, z: 0 } },
                longTerm: { color: 0xff8800, position: { x: 2, y: 1, z: 0 } },
                emotional: { color: 0xff0088, position: { x: -1, y: -1, z: 1 } },
                executive: { color: 0x8800ff, position: { x: 1, y: -1, z: 1 } },
                creative: { color: 0xffff00, position: { x: 0, y: 0, z: -2 } }
            }
        };

        this.initializeThreeJS();
        this.createBrainStructure();
        this.addInteractiveControls();
        this.startAnimation();
        this.startDataUpdates();
    }

    /**
     * Initialise Three.js
     */
    initializeThreeJS() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error('Container non trouvé:', this.containerId);
            return;
        }

        // Scène
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x0a0a0a);

        // Caméra
        this.camera = new THREE.PerspectiveCamera(
            75,
            container.clientWidth / container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(5, 5, 5);

        // Renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        container.appendChild(this.renderer.domElement);

        // Contrôles
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;

        // Éclairage
        this.setupLighting();

        // Redimensionnement
        window.addEventListener('resize', () => this.onWindowResize());
    }

    /**
     * Configure l'éclairage
     */
    setupLighting() {
        // Lumière ambiante
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);

        // Lumière directionnelle
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);

        // Lumières colorées pour l'ambiance
        const lights = [
            { color: 0x00ff88, position: { x: -5, y: 3, z: 2 }, intensity: 0.3 },
            { color: 0xff0088, position: { x: 5, y: -3, z: 2 }, intensity: 0.3 },
            { color: 0x0088ff, position: { x: 0, y: 5, z: -5 }, intensity: 0.2 }
        ];

        lights.forEach(lightConfig => {
            const light = new THREE.PointLight(lightConfig.color, lightConfig.intensity, 10);
            light.position.set(lightConfig.position.x, lightConfig.position.y, lightConfig.position.z);
            this.scene.add(light);
        });
    }

    /**
     * Crée la structure du cerveau
     */
    createBrainStructure() {
        // Créer les régions du cerveau
        Object.entries(this.brainData.regions).forEach(([regionName, regionData]) => {
            this.createBrainRegion(regionName, regionData);
        });

        // Créer des neurones initiaux
        this.generateInitialNeurons();

        // Créer des connexions initiales
        this.generateInitialConnections();
    }

    /**
     * Crée une région du cerveau
     */
    createBrainRegion(name, data) {
        const geometry = new THREE.SphereGeometry(0.8, 16, 16);
        const material = new THREE.MeshPhongMaterial({
            color: data.color,
            transparent: true,
            opacity: 0.3,
            wireframe: false
        });

        const region = new THREE.Mesh(geometry, material);
        region.position.set(data.position.x, data.position.y, data.position.z);
        region.userData = { type: 'region', name: name };

        this.scene.add(region);
        this.brainRegions[name] = region;

        // Ajouter un label
        this.addRegionLabel(name, data.position);
    }

    /**
     * Ajoute un label à une région
     */
    addRegionLabel(name, position) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;

        context.fillStyle = '#ffffff';
        context.font = '20px Arial';
        context.textAlign = 'center';
        context.fillText(name.toUpperCase(), 128, 40);

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);

        sprite.position.set(position.x, position.y + 1.2, position.z);
        sprite.scale.set(2, 0.5, 1);

        this.scene.add(sprite);
    }

    /**
     * Génère des neurones initiaux
     */
    generateInitialNeurons() {
        const neuronCount = 50;

        for (let i = 0; i < neuronCount; i++) {
            this.createNeuron({
                id: i,
                position: {
                    x: (0.5 - 0.5) * 8,
                    y: (0.5 - 0.5) * 6,
                    z: (0.5 - 0.5) * 8
                },
                activity: 0.5,
                type: ['sensory', 'working', 'longTerm', 'emotional', 'executive', 'creative'][Math.floor(0.5 * 6)]
            });
        }
    }

    /**
     * Crée un neurone
     */
    createNeuron(neuronData) {
        const geometry = new THREE.SphereGeometry(0.05, 8, 8);
        const regionColor = this.brainData.regions[neuronData.type]?.color || 0xffffff;

        const material = new THREE.MeshPhongMaterial({
            color: regionColor,
            emissive: regionColor,
            emissiveIntensity: neuronData.activity * 0.5
        });

        const neuron = new THREE.Mesh(geometry, material);
        neuron.position.set(neuronData.position.x, neuronData.position.y, neuronData.position.z);
        neuron.userData = {
            type: 'neuron',
            id: neuronData.id,
            activity: neuronData.activity,
            neuronType: neuronData.type,
            originalColor: regionColor
        };

        this.scene.add(neuron);
        this.neurons.push(neuron);

        return neuron;
    }

    /**
     * Génère des connexions initiales
     */
    generateInitialConnections() {
        const connectionCount = 80;

        for (let i = 0; i < connectionCount; i++) {
            const neuron1 = this.neurons[Math.floor(0.5 * this.neurons.length)];
            const neuron2 = this.neurons[Math.floor(0.5 * this.neurons.length)];

            if (neuron1 !== neuron2) {
                this.createConnection(neuron1, neuron2, 0.5);
            }
        }
    }

    /**
     * Crée une connexion entre deux neurones
     */
    createConnection(neuron1, neuron2, strength) {
        const geometry = new THREE.BufferGeometry().setFromPoints([
            neuron1.position,
            neuron2.position
        ]);

        const material = new THREE.LineBasicMaterial({
            color: 0x00ffff,
            transparent: true,
            opacity: strength * 0.6
        });

        const connection = new THREE.Line(geometry, material);
        connection.userData = {
            type: 'connection',
            strength: strength,
            neuron1: neuron1.userData.id,
            neuron2: neuron2.userData.id
        };

        this.scene.add(connection);
        this.connections.push(connection);

        return connection;
    }

    /**
     * Met à jour l'activité des neurones
     */
    updateNeuronActivity(activityData) {
        this.neurons.forEach((neuron, index) => {
            const activity = activityData[index] || 0.5 * 0.3 + 0.1;
            neuron.userData.activity = activity;

            // Mettre à jour l'intensité émissive
            neuron.material.emissiveIntensity = activity * 0.8;

            // Animation de pulsation
            const scale = 1 + activity * 0.3;
            neuron.scale.set(scale, scale, scale);
        });
    }

    /**
     * Démarre l'animation
     */
    startAnimation() {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.animate();
    }

    /**
     * Boucle d'animation
     */
    animate() {
        if (!this.isAnimating) return;

        this.animationId = requestAnimationFrame(() => this.animate());

        // Mettre à jour les contrôles
        this.controls.update();

        // Animation des neurones (pulsation)
        this.neurons.forEach(neuron => {
            const time = Date.now() * 0.001;
            const activity = neuron.userData.activity;
            const pulse = Math.sin(time * 5 + neuron.userData.id) * 0.1 * activity;
            neuron.material.emissiveIntensity = activity * 0.5 + pulse;
        });

        // Animation des connexions
        this.connections.forEach(connection => {
            const time = Date.now() * 0.001;
            const strength = connection.userData.strength;
            const pulse = Math.sin(time * 3 + strength * 10) * 0.2;
            connection.material.opacity = strength * 0.4 + pulse;
        });

        // Rotation lente de la scène
        this.scene.rotation.y += 0.001;

        // Rendu
        this.renderer.render(this.scene, this.camera);
    }

    /**
     * Démarre les mises à jour de données
     */
    startDataUpdates() {
        setInterval(() => {
            this.fetchBrainData();
        }, 2000);
    }

    /**
     * Récupère les données du cerveau
     */
    async fetchBrainData() {
        try {
            const response = await fetch('/api/brain/activity');
            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    this.updateVisualization(result.data);
                    this.updateRegionActivity(result.data.regions);
                    this.updateConnections(result.data.connections);
                } else {
                    this.simulateBrainActivity();
                }
            } else {
                this.simulateBrainActivity();
            }
        } catch (error) {
            console.log('Utilisation des données données réelleses:', error.message);
            this.simulateBrainActivity();
        }
    }

    /**
     * Simule l'activité cérébrale
     */
    simulateBrainActivity() {
        const activityData = this.neurons.map(() => 0.5 * 0.8 + 0.2);
        this.updateNeuronActivity(activityData);
    }

    /**
     * Met à jour la visualisation
     */
    updateVisualization(data) {
        if (data.neuronActivity) {
            this.updateNeuronActivity(data.neuronActivity);
        }
    }

    /**
     * Met à jour l'activité des régions
     */
    updateRegionActivity(regionsData) {
        if (!regionsData) return;

        Object.entries(regionsData).forEach(([regionName, regionData]) => {
            const region = this.brainRegions[regionName];
            if (region && regionData) {
                // Mettre à jour l'opacité basée sur l'activité
                const activity = regionData.activity || 0;
                const temperature = regionData.temperature || 0.5;

                region.material.opacity = 0.2 + (activity * 0.3);
                region.material.emissiveIntensity = temperature * 0.5;

                // Animation de pulsation pour les régions actives
                if (activity > 0.5) {
                    const time = Date.now() * 0.001;
                    const pulse = Math.sin(time * 4) * 0.1;
                    region.scale.setScalar(1 + pulse);
                }
            }
        });
    }

    /**
     * Met à jour les connexions
     */
    updateConnections(connectionsData) {
        if (!connectionsData) return;

        this.connections.forEach((connection, index) => {
            const connectionData = connectionsData[index];
            if (connectionData) {
                connection.material.opacity = connectionData.strength * 0.6;

                // Couleur basée sur l'activité
                if (connectionData.active) {
                    connection.material.color.setHex(0x00ffff);
                } else {
                    connection.material.color.setHex(0x004444);
                }
            }
        });
    }

    /**
     * Ajoute des contrôles interactifs
     */
    addInteractiveControls() {
        // Créer un panneau de contrôles
        const controlsPanel = document.createElement('div');
        controlsPanel.id = 'brain-controls-panel';
        controlsPanel.style.cssText = `
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            color: white;
            font-family: Arial, sans-serif;
            z-index: 1000;
        `;

        // Boutons de contrôle
        const controls = [
            { label: 'Stimuler Créativité', action: 'boost_creativity', color: '#ffff00' },
            { label: 'Mode Apprentissage', action: 'trigger_learning', color: '#00ff88' },
            { label: 'Consolidation Mémoire', action: 'memory_consolidation', color: '#ff8800' }
        ];

        controls.forEach(control => {
            const button = document.createElement('button');
            button.textContent = control.label;
            button.style.cssText = `
                display: block;
                width: 100%;
                margin: 5px 0;
                padding: 8px;
                background: ${control.color};
                color: black;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-weight: bold;
            `;

            button.addEventListener('click', () => {
                this.triggerBrainAction(control.action);
            });

            controlsPanel.appendChild(button);
        });

        // Ajouter le panneau au container
        const container = document.getElementById(this.containerId);
        if (container) {
            container.appendChild(controlsPanel);
        }
    }

    /**
     * Déclenche une action sur le cerveau
     */
    async triggerBrainAction(action) {
        try {
            const response = await fetch('/api/brain/visualization/control', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    parameters: { level: 0.9, intensity: 0.8 }
                })
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Action cerveau:', result.message);

                // Effet visuel immédiat
                this.showActionEffect(action);
            }
        } catch (error) {
            console.error('Erreur action cerveau:', error);
        }
    }

    /**
     * Affiche un effet visuel pour une action
     */
    showActionEffect(action) {
        switch (action) {
            case 'boost_creativity':
                // Effet de boost créativité - région créative brille
                const creativeRegion = this.brainRegions.creative;
                if (creativeRegion) {
                    creativeRegion.material.emissiveIntensity = 1.0;
                    setTimeout(() => {
                        creativeRegion.material.emissiveIntensity = 0.5;
                    }, 2000);
                }
                break;

            case 'trigger_learning':
                // Effet d'apprentissage - toutes les connexions s'illuminent
                this.connections.forEach(connection => {
                    connection.material.opacity = 1.0;
                    setTimeout(() => {
                        connection.material.opacity = 0.6;
                    }, 1500);
                });
                break;

            case 'memory_consolidation':
                // Effet de consolidation - région mémoire long terme pulse
                const longTermRegion = this.brainRegions.longTerm;
                if (longTermRegion) {
                    const originalScale = longTermRegion.scale.x;
                    longTermRegion.scale.setScalar(1.5);
                    setTimeout(() => {
                        longTermRegion.scale.setScalar(originalScale);
                    }, 1000);
                }
                break;
        }
    }

    /**
     * Gère le redimensionnement
     */
    onWindowResize() {
        const container = document.getElementById(this.containerId);
        if (!container) return;

        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    /**
     * Arrête l'animation
     */
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    /**
     * Nettoie les ressources
     */
    dispose() {
        this.stopAnimation();

        // Nettoyer les objets Three.js
        this.neurons.forEach(neuron => {
            neuron.geometry.dispose();
            neuron.material.dispose();
        });

        this.connections.forEach(connection => {
            connection.geometry.dispose();
            connection.material.dispose();
        });

        Object.values(this.brainRegions).forEach(region => {
            region.geometry.dispose();
            region.material.dispose();
        });

        if (this.renderer) {
            this.renderer.dispose();
        }
    }
}

// Export pour utilisation
window.Brain3DVisualizer = Brain3DVisualizer;
