/**
 * Script pour la synchronisation de la mémoire entre les agents
 */

// Variables globales
let mainAgentEntries = [];
let trainingAgentEntries = [];
let syncResults = [];
let isSyncing = false;

// Éléments DOM
const mainAgentName = document.getElementById('main-agent-name');
const mainAgentModel = document.getElementById('main-agent-model');
const mainAgentEntryCount = document.getElementById('main-agent-entries');
const mainAgentTemperature = document.getElementById('main-agent-temperature');

const trainingAgentName = document.getElementById('training-agent-name');
const trainingAgentModel = document.getElementById('training-agent-model');
const trainingAgentEntryCount = document.getElementById('training-agent-entries');
const trainingAgentTemperature = document.getElementById('training-agent-temperature');

const importanceThreshold = document.getElementById('importance-threshold');
const importanceThresholdValue = document.getElementById('importance-threshold-value');
const bidirectionalSync = document.getElementById('bidirectional-sync');
const autoSync = document.getElementById('auto-sync');

const syncBtn = document.getElementById('sync-btn');
const resetBtn = document.getElementById('reset-btn');
const refreshBtn = document.getElementById('refresh-btn');

const syncProgressSection = document.getElementById('sync-progress-section');
const syncProgressBar = document.getElementById('sync-progress-bar');
const syncProgressStatus = document.getElementById('sync-progress-status');

const syncResultsSection = document.getElementById('sync-results-section');
const syncResultsContainer = document.getElementById('sync-results-container');

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        console.log('Application Electron détectée');
    } else {
        console.log('Application web standard détectée');
    }

    // Initialiser l'interface
    initializeInterface();

    // Ajouter les écouteurs d'événements
    importanceThreshold.addEventListener('input', () => {
        importanceThresholdValue.textContent = importanceThreshold.value;
    });

    syncBtn.addEventListener('click', startSync);
    resetBtn.addEventListener('click', resetOptions);
    refreshBtn.addEventListener('click', refreshAgentData);
    autoSync.addEventListener('change', toggleAutoSync);
});

/**
 * Initialise l'interface utilisateur
 */
async function initializeInterface() {
    try {
        // Charger les informations des agents
        await loadAgents();

        // Charger les entrées de mémoire des agents
        await loadAgentEntries();

        // Mettre à jour l'interface
        updateInterface();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'interface:', error);
        showNotification('Erreur lors de l\'initialisation de l\'interface', 'error');
    }
}

/**
 * Charge les informations des agents
 */
async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        const data = await response.json();

        if (data.success) {
            const agents = data.agents;

            // Trouver l'agent principal (Agent Local LOUNA)
            const mainAgent = Object.values(agents).find(agent => agent.id === 'agent_agent local');
            if (mainAgent) {
                mainAgentName.textContent = mainAgent.name;
                mainAgentModel.textContent = mainAgent.model;
            }

            // Trouver l'agent de formation (Llama 3)
            const trainingAgent = Object.values(agents).find(agent => agent.id === 'agent_training');
            if (trainingAgent) {
                trainingAgentName.textContent = trainingAgent.name;
                trainingAgentModel.textContent = trainingAgent.model;
            } else {
                // Si l'agent de formation n'est pas disponible, utiliser l'agent principal
                trainingAgentName.textContent = mainAgent.name + ' (Mode de secours)';
                trainingAgentModel.textContent = mainAgent.model;

                // Désactiver la synchronisation bidirectionnelle
                bidirectionalSync.checked = false;
                bidirectionalSync.disabled = true;
            }
        } else {
            showNotification(`Erreur lors du chargement des agents: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des agents:', error);
        showNotification('Erreur lors du chargement des agents', 'error');
    }
}

/**
 * Charge les entrées de mémoire des agents
 */
async function loadAgentEntries() {
    try {
        // Charger les entrées de l'agent principal
        const mainAgentResponse = await fetch('/api/thermal/memory/entries?agentId=agent_agent local');
        const mainAgentData = await mainAgentResponse.json();

        if (mainAgentData.success) {
            mainAgentEntries = mainAgentData.entries;
        } else {
            showNotification(`Erreur lors du chargement des entrées de l'agent principal: ${mainAgentData.error}`, 'error');
        }

        // Charger les entrées de l'agent de formation
        const trainingAgentResponse = await fetch('/api/thermal/memory/entries?agentId=agent_training');
        const trainingAgentData = await trainingAgentResponse.json();

        if (trainingAgentData.success) {
            trainingAgentEntries = trainingAgentData.entries;
        } else {
            showNotification(`Erreur lors du chargement des entrées de l'agent de formation: ${trainingAgentData.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des entrées de mémoire:', error);
        showNotification('Erreur lors du chargement des entrées de mémoire', 'error');
    }
}

/**
 * Met à jour l'interface utilisateur
 */
function updateInterface() {
    // Mettre à jour les statistiques de l'agent principal
    mainAgentEntryCount.textContent = `${mainAgentEntries.length} entrées`;

    // Calculer la température moyenne de l'agent principal
    const mainAgentAvgTemp = calculateAverageTemperature(mainAgentEntries);
    mainAgentTemperature.textContent = `Température: ${mainAgentAvgTemp.toFixed(2)}`;

    // Mettre à jour les statistiques de l'agent de formation
    trainingAgentEntryCount.textContent = `${trainingAgentEntries.length} entrées`;

    // Calculer la température moyenne de l'agent de formation
    const trainingAgentAvgTemp = calculateAverageTemperature(trainingAgentEntries);
    trainingAgentTemperature.textContent = `Température: ${trainingAgentAvgTemp.toFixed(2)}`;
}

/**
 * Calcule la température moyenne des entrées
 * @param {Array} entries - Entrées de mémoire
 * @returns {number} - Température moyenne
 */
function calculateAverageTemperature(entries) {
    if (entries.length === 0) {
        return 0;
    }

    const totalTemperature = entries.reduce((sum, entry) => sum + entry.temperature, 0);
    return totalTemperature / entries.length;
}

/**
 * Démarre la synchronisation de la mémoire
 */
async function startSync() {
    try {
        if (isSyncing) {
            return;
        }

        isSyncing = true;
        syncResults = [];

        // Afficher la section de progression
        syncProgressSection.style.display = 'block';
        syncProgressBar.style.width = '0%';
        syncProgressStatus.textContent = 'Initialisation de la synchronisation...';

        // Masquer la section des résultats
        syncResultsSection.style.display = 'none';

        // Récupérer les options de synchronisation
        const threshold = parseFloat(importanceThreshold.value);
        const isBidirectional = bidirectionalSync.checked;

        // Mettre à jour la barre de progression
        syncProgressBar.style.width = '10%';
        syncProgressStatus.textContent = 'Chargement des entrées de mémoire...';

        // Recharger les entrées de mémoire
        await loadAgentEntries();

        // Mettre à jour la barre de progression
        syncProgressBar.style.width = '30%';
        syncProgressStatus.textContent = 'Identification des entrées à synchroniser...';

        // Identifier les entrées à synchroniser
        const entriesToSync = [];

        // Synchroniser de l'agent principal vers l'agent de formation
        for (const entry of mainAgentEntries) {
            // Vérifier si l'entrée existe déjà dans la mémoire de l'agent de formation
            const existsInTrainingAgent = trainingAgentEntries.some(e => e.key === entry.key);

            // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
            if (!existsInTrainingAgent && entry.importance >= threshold) {
                entriesToSync.push({
                    source: 'agent_agent local',
                    target: 'agent_training',
                    entry
                });
            }
        }

        // Synchroniser de l'agent de formation vers l'agent principal si la synchronisation est bidirectionnelle
        if (isBidirectional) {
            for (const entry of trainingAgentEntries) {
                // Vérifier si l'entrée existe déjà dans la mémoire de l'agent principal
                const existsInMainAgent = mainAgentEntries.some(e => e.key === entry.key);

                // Si l'entrée n'existe pas et qu'elle est importante, l'ajouter à la liste de synchronisation
                if (!existsInMainAgent && entry.importance >= threshold) {
                    entriesToSync.push({
                        source: 'agent_training',
                        target: 'agent_agent local',
                        entry
                    });
                }
            }
        }

        // Mettre à jour la barre de progression
        syncProgressBar.style.width = '50%';
        syncProgressStatus.textContent = `Synchronisation de ${entriesToSync.length} entrées...`;

        // Synchroniser les entrées
        if (entriesToSync.length > 0) {
            let processedCount = 0;

            for (const syncItem of entriesToSync) {
                const { source, target, entry } = syncItem;

                // Vérifier s'il existe des entrées similaires dans l'agent cible
                const similarEntries = target === 'agent_agent local'
                    ? mainAgentEntries.filter(e => calculateSimilarity(e, entry) > 0.7)
                    : trainingAgentEntries.filter(e => calculateSimilarity(e, entry) > 0.7);

                // Déclarer la variable entryCopy
                let entryCopy;

                // Si des entrées similaires existent, les fusionner
                if (similarEntries.length > 0) {
                    // Trouver l'entrée la plus similaire
                    const mostSimilarEntry = similarEntries.reduce((prev, current) => {
                        const prevSimilarity = calculateSimilarity(prev, entry);
                        const currentSimilarity = calculateSimilarity(current, entry);
                        return prevSimilarity > currentSimilarity ? prev : current;
                    });

                    // Fusionner les entrées
                    entryCopy = mergeEntries(mostSimilarEntry, entry);

                    // Ajouter des métadonnées de fusion
                    entryCopy.metadata = entryCopy.metadata || {};
                    entryCopy.metadata.mergedFrom = entry.id;
                    entryCopy.metadata.mergedAt = new Date().toISOString();
                    entryCopy.metadata.agentId = target;
                    entryCopy.metadata.syncedFrom = source;
                    entryCopy.metadata.syncedAt = new Date().toISOString();

                    // Utiliser l'ID de l'entrée existante
                    syncItem.mergedWithId = mostSimilarEntry.id;
                    syncItem.merged = true;
                } else {
                    // Créer une copie de l'entrée pour l'agent cible
                    entryCopy = { ...entry };
                    delete entryCopy.id; // Supprimer l'ID pour en générer un nouveau

                    // Ajouter des métadonnées de synchronisation
                    entryCopy.metadata = entryCopy.metadata || {};
                    entryCopy.metadata.syncedFrom = source;
                    entryCopy.metadata.syncedAt = new Date().toISOString();
                    entryCopy.metadata.agentId = target;

                    syncItem.merged = false;
                }

                // Ajouter l'entrée à la mémoire de l'agent cible
                const response = await fetch('/api/thermal/memory/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agentId: target,
                        key: entryCopy.key,
                        data: entryCopy.data,
                        category: entryCopy.category,
                        importance: entryCopy.importance,
                        metadata: entryCopy.metadata
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // Ajouter le résultat à la liste des résultats
                    syncResults.push({
                        id: data.id,
                        key: entryCopy.key,
                        data: entryCopy.data,
                        source,
                        target,
                        success: true,
                        merged: syncItem.merged,
                        mergedWithId: syncItem.mergedWithId
                    });
                } else {
                    // Ajouter l'erreur à la liste des résultats
                    syncResults.push({
                        key: entryCopy.key,
                        source,
                        target,
                        success: false,
                        error: data.error,
                        merged: syncItem.merged,
                        mergedWithId: syncItem.mergedWithId
                    });
                }

                // Mettre à jour la progression
                processedCount++;
                const progress = 50 + (processedCount / entriesToSync.length) * 40;
                syncProgressBar.style.width = `${progress}%`;
                syncProgressStatus.textContent = `Synchronisation en cours... ${processedCount}/${entriesToSync.length}`;
            }
        }

        // Mettre à jour la barre de progression
        syncProgressBar.style.width = '90%';
        syncProgressStatus.textContent = 'Finalisation de la synchronisation...';

        // Recharger les entrées de mémoire
        await loadAgentEntries();

        // Mettre à jour l'interface
        updateInterface();

        // Afficher les résultats
        displaySyncResults();

        // Terminer la synchronisation
        syncProgressBar.style.width = '100%';
        syncProgressStatus.textContent = 'Synchronisation terminée';

        // Masquer la section de progression après un délai
        setTimeout(() => {
            syncProgressSection.style.display = 'none';
        }, 2000);

        isSyncing = false;
    } catch (error) {
        console.error('Erreur lors de la synchronisation:', error);
        showNotification('Erreur lors de la synchronisation', 'error');

        syncProgressStatus.textContent = 'Erreur lors de la synchronisation';
        isSyncing = false;
    }
}

/**
 * Affiche les résultats de la synchronisation
 */
function displaySyncResults() {
    // Vider le conteneur des résultats
    syncResultsContainer.innerHTML = '';

    // Afficher la section des résultats
    syncResultsSection.style.display = 'block';

    // Si aucun résultat, afficher un message
    if (syncResults.length === 0) {
        syncResultsContainer.innerHTML = `
            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>Aucune entrée de mémoire à synchroniser.</p>
            </div>
        `;
        return;
    }

    // Afficher les résultats
    syncResults.forEach(result => {
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';

        // Déterminer la direction de la synchronisation
        const direction = result.source === 'agent_agent local' ? 'Principal → Formation' : 'Formation → Principal';
        const directionClass = result.source === 'agent_agent local' ? 'main-to-training' : 'training-to-main';

        // Déterminer la couleur de la bordure en fonction du succès et de la fusion
        if (result.merged) {
            resultItem.style.borderLeftColor = 'var(--temp-warm)';
        } else {
            resultItem.style.borderLeftColor = result.success ? 'var(--accent)' : 'var(--error)';
        }

        // Déterminer le statut
        let status = '';
        if (result.success) {
            status = result.merged ? 'Fusionnée' : 'Ajoutée';
        } else {
            status = 'Échec';
        }

        resultItem.innerHTML = `
            <div class="result-header">
                <div class="result-title">${result.key || 'Entrée sans clé'}</div>
                <div class="result-direction ${directionClass}">${direction}</div>
            </div>
            <div class="result-content">${result.success ? (result.data || '').substring(0, 100) + '...' : 'Erreur: ' + result.error}</div>
            <div class="result-footer">
                <div>ID: ${result.id || 'N/A'}</div>
                <div>Statut: ${status} ${result.merged ? `(Fusionnée avec ${result.mergedWithId})` : ''}</div>
            </div>
        `;

        syncResultsContainer.appendChild(resultItem);
    });

    // Afficher un résumé
    const successCount = syncResults.filter(result => result.success).length;
    const failureCount = syncResults.length - successCount;

    const summaryItem = document.createElement('div');
    summaryItem.style.marginTop = '20px';
    summaryItem.style.padding = '10px';
    summaryItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
    summaryItem.style.borderRadius = '5px';
    summaryItem.style.textAlign = 'center';
    summaryItem.style.color = 'var(--text-secondary)';

    summaryItem.innerHTML = `
        <strong>Résumé:</strong> ${syncResults.length} entrées traitées, ${successCount} succès, ${failureCount} échecs
    `;

    syncResultsContainer.appendChild(summaryItem);
}

/**
 * Réinitialise les options de synchronisation
 */
function resetOptions() {
    importanceThreshold.value = 0.5;
    importanceThresholdValue.textContent = '0.5';
    bidirectionalSync.checked = true;
    autoSync.checked = false;
}

/**
 * Actualise les données des agents
 */
async function refreshAgentData() {
    try {
        await loadAgentEntries();
        updateInterface();
        showNotification('Données des agents actualisées', 'success');
    } catch (error) {
        console.error('Erreur lors de l\'actualisation des données des agents:', error);
        showNotification('Erreur lors de l\'actualisation des données des agents', 'error');
    }
}

/**
 * Active ou désactive la synchronisation automatique
 */
function toggleAutoSync() {
    if (autoSync.checked) {
        // Démarrer la synchronisation automatique
        window.autoSyncInterval = setInterval(async () => {
            if (!isSyncing) {
                await startSync();
            }
        }, 30 * 60 * 1000); // Toutes les 30 minutes

        showNotification('Synchronisation automatique activée (toutes les 30 minutes)', 'info');
    } else {
        // Arrêter la synchronisation automatique
        if (window.autoSyncInterval) {
            clearInterval(window.autoSyncInterval);
            window.autoSyncInterval = null;
        }

        showNotification('Synchronisation automatique désactivée', 'info');
    }
}

/**
 * Calcule la similarité entre deux entrées de mémoire en utilisant plusieurs techniques
 * @param {Object} entryA - Première entrée
 * @param {Object} entryB - Deuxième entrée
 * @returns {number} - Score de similarité (0-1)
 */
function calculateSimilarity(entryA, entryB) {
    // Vérifier si les entrées ont des métadonnées qui indiquent une relation
    if (entryA.metadata && entryB.metadata) {
        // Si les entrées sont liées par une synchronisation précédente
        if (entryA.metadata.syncedFrom === entryB.id || entryB.metadata.syncedFrom === entryA.id) {
            return 0.95; // Très forte similarité pour les entrées déjà liées
        }

        // Si les entrées ont été créées par le même processus
        if (entryA.metadata.createdBy && entryB.metadata.createdBy &&
            entryA.metadata.createdBy === entryB.metadata.createdBy) {
            return 0.85; // Forte similarité pour les entrées créées par le même processus
        }
    }

    // Similarité basée sur la clé
    let keySimilarity = 0;
    if (entryA.key && entryB.key) {
        // Vérifier si les clés sont identiques
        if (entryA.key.toLowerCase() === entryB.key.toLowerCase()) {
            keySimilarity = 1;
        } else {
            // Calculer la similarité des clés en utilisant la distance de Levenshtein
            const keyDistance = levenshteinDistance(entryA.key.toLowerCase(), entryB.key.toLowerCase());
            const maxLength = Math.max(entryA.key.length, entryB.key.length);
            keySimilarity = 1 - (keyDistance / maxLength);

            // Amélioration: vérifier si une clé est un préfixe de l'autre
            if (entryA.key.toLowerCase().startsWith(entryB.key.toLowerCase()) ||
                entryB.key.toLowerCase().startsWith(entryA.key.toLowerCase())) {
                keySimilarity = Math.max(keySimilarity, 0.8); // Augmenter la similarité pour les préfixes
            }
        }
    }

    // Similarité basée sur les données
    let dataSimilarity = 0;
    if (entryA.data && entryB.data) {
        // Convertir les données en chaînes si nécessaire
        const dataA = typeof entryA.data === 'string' ? entryA.data : JSON.stringify(entryA.data);
        const dataB = typeof entryB.data === 'string' ? entryB.data : JSON.stringify(entryB.data);

        // Vérifier si les données sont identiques
        if (dataA === dataB) {
            dataSimilarity = 1;
        } else {
            // Extraire les mots-clés
            const keywordsA = extractKeywords(dataA);
            const keywordsB = extractKeywords(dataB);

            // Calculer la similarité basée sur les mots-clés communs (coefficient de Jaccard)
            const commonKeywords = keywordsA.filter(keyword => keywordsB.includes(keyword));
            const unionKeywords = new Set([...keywordsA, ...keywordsB]);
            dataSimilarity = commonKeywords.length / unionKeywords.size;

            // Amélioration: vérifier la similarité des phrases
            const sentenceSimilarity = calculateSentenceSimilarity(dataA, dataB);
            dataSimilarity = Math.max(dataSimilarity, sentenceSimilarity);
        }
    }

    // Similarité basée sur la catégorie
    const categorySimilarity = entryA.category === entryB.category ? 1 : 0;

    // Similarité basée sur l'importance
    const importanceSimilarity = 1 - Math.abs(entryA.importance - entryB.importance);

    // Similarité basée sur la température
    const temperatureSimilarity = 1 - Math.abs(entryA.temperature - entryB.temperature);

    // Calculer la similarité globale (moyenne pondérée)
    return (keySimilarity * 0.35) +
           (dataSimilarity * 0.4) +
           (categorySimilarity * 0.1) +
           (importanceSimilarity * 0.1) +
           (temperatureSimilarity * 0.05);
}

/**
 * Calcule la similarité entre deux textes au niveau des phrases
 * @param {string} textA - Premier texte
 * @param {string} textB - Deuxième texte
 * @returns {number} - Score de similarité (0-1)
 */
function calculateSentenceSimilarity(textA, textB) {
    // Diviser les textes en phrases
    const sentencesA = textA.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 0);
    const sentencesB = textB.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 0);

    if (sentencesA.length === 0 || sentencesB.length === 0) {
        return 0;
    }

    // Calculer la similarité pour chaque paire de phrases
    let totalSimilarity = 0;
    let pairsCount = 0;

    for (const sentenceA of sentencesA) {
        for (const sentenceB of sentencesB) {
            // Calculer la similarité entre les phrases
            const distance = levenshteinDistance(sentenceA.toLowerCase(), sentenceB.toLowerCase());
            const maxLength = Math.max(sentenceA.length, sentenceB.length);
            const similarity = 1 - (distance / maxLength);

            // Ne considérer que les paires avec une similarité significative
            if (similarity > 0.6) {
                totalSimilarity += similarity;
                pairsCount++;
            }
        }
    }

    // Retourner la similarité moyenne des meilleures paires
    return pairsCount > 0 ? totalSimilarity / pairsCount : 0;
}

/**
 * Calcule la distance de Levenshtein entre deux chaînes
 * @param {string} a - Première chaîne
 * @param {string} b - Deuxième chaîne
 * @returns {number} - Distance de Levenshtein
 */
function levenshteinDistance(a, b) {
    const matrix = [];

    // Initialiser la matrice
    for (let i = 0; i <= b.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
        matrix[0][j] = j;
    }

    // Remplir la matrice
    for (let i = 1; i <= b.length; i++) {
        for (let j = 1; j <= a.length; j++) {
            if (b.charAt(i - 1) === a.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1, // substitution
                    matrix[i][j - 1] + 1,     // insertion
                    matrix[i - 1][j] + 1      // suppression
                );
            }
        }
    }

    return matrix[b.length][a.length];
}

/**
 * Extrait les mots-clés d'une chaîne de texte
 * @param {string} text - Texte à analyser
 * @returns {Array} - Mots-clés extraits
 */
function extractKeywords(text) {
    // Convertir en minuscules
    text = text.toLowerCase();

    // Supprimer la ponctuation
    text = text.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, ' ');

    // Diviser en mots
    const words = text.split(/\s+/);

    // Filtrer les mots courts et les mots vides
    const stopWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'de', 'du', 'ce', 'cette', 'ces', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs', 'a', 'à', 'au', 'aux', 'avec', 'ce', 'ces', 'dans', 'en', 'entre', 'il', 'ils', 'je', 'tu', 'nous', 'vous', 'elle', 'elles', 'on', 'par', 'pas', 'pour', 'sur', 'the', 'of', 'and', 'to', 'in', 'is', 'that', 'it', 'with', 'for', 'as', 'was', 'on'];

    const keywords = words.filter(word =>
        word.length > 3 && !stopWords.includes(word)
    );

    // Supprimer les doublons
    return [...new Set(keywords)];
}

/**
 * Fusionne deux entrées de mémoire de manière intelligente
 * @param {Object} targetEntry - Entrée cible (existante)
 * @param {Object} sourceEntry - Entrée source (à fusionner)
 * @returns {Object} - Entrée fusionnée
 */
function mergeEntries(targetEntry, sourceEntry) {
    // Créer une copie de l'entrée cible
    const mergedEntry = { ...targetEntry };

    // Fusionner les métadonnées
    mergedEntry.metadata = mergedEntry.metadata || {};
    const sourceMetadata = sourceEntry.metadata || {};

    // Conserver les métadonnées importantes des deux entrées
    Object.keys(sourceMetadata).forEach(key => {
        // Ne pas écraser certaines métadonnées spécifiques à l'entrée cible
        if (!['id', 'agentId', 'createdAt'].includes(key)) {
            // Si la métadonnée existe déjà, la stocker dans un tableau
            if (mergedEntry.metadata[key]) {
                if (Array.isArray(mergedEntry.metadata[key])) {
                    // Ajouter la nouvelle valeur si elle n'existe pas déjà
                    if (!mergedEntry.metadata[key].includes(sourceMetadata[key])) {
                        mergedEntry.metadata[key].push(sourceMetadata[key]);
                    }
                } else {
                    // Convertir en tableau si ce n'est pas déjà le cas
                    if (mergedEntry.metadata[key] !== sourceMetadata[key]) {
                        mergedEntry.metadata[key] = [mergedEntry.metadata[key], sourceMetadata[key]];
                    }
                }
            } else {
                // Ajouter la nouvelle métadonnée
                mergedEntry.metadata[key] = sourceMetadata[key];
            }
        }
    });

    // Ajouter des métadonnées de fusion
    mergedEntry.metadata.mergedWith = mergedEntry.metadata.mergedWith || [];
    if (!mergedEntry.metadata.mergedWith.includes(sourceEntry.id)) {
        mergedEntry.metadata.mergedWith.push(sourceEntry.id);
    }
    mergedEntry.metadata.lastMergedAt = Date.now();

    // Fusionner les données selon leur type
    if (typeof targetEntry.data === 'string' && typeof sourceEntry.data === 'string') {
        // Fusionner les données textuelles
        mergedEntry.data = mergeTextData(targetEntry.data, sourceEntry.data);
    } else if (typeof targetEntry.data === 'object' && typeof sourceEntry.data === 'object') {
        // Fusionner les objets de manière récursive
        mergedEntry.data = mergeObjectData(targetEntry.data, sourceEntry.data);
    } else if (Array.isArray(targetEntry.data) && Array.isArray(sourceEntry.data)) {
        // Fusionner les tableaux en éliminant les doublons
        mergedEntry.data = [...new Set([...targetEntry.data, ...sourceEntry.data])];
    } else {
        // Si les types sont différents, conserver les deux dans un objet
        mergedEntry.data = {
            target: targetEntry.data,
            source: sourceEntry.data,
            mergedAt: new Date().toISOString()
        };
    }

    // Fusionner les propriétés numériques
    mergedEntry.importance = Math.max(targetEntry.importance, sourceEntry.importance);
    mergedEntry.temperature = Math.min(1.0, (targetEntry.temperature + sourceEntry.temperature) / 2 * 1.1);

    // Mettre à jour les statistiques
    mergedEntry.lastAccessed = Date.now();
    mergedEntry.accessCount = (targetEntry.accessCount || 0) + (sourceEntry.accessCount || 0);
    mergedEntry.mergeCount = (mergedEntry.mergeCount || 0) + 1;

    // Ajouter une trace de la fusion
    mergedEntry.mergeHistory = mergedEntry.mergeHistory || [];
    mergedEntry.mergeHistory.push({
        sourceId: sourceEntry.id,
        timestamp: Date.now(),
        similarityScore: calculateSimilarity(targetEntry, sourceEntry)
    });

    // Limiter la taille de l'historique de fusion
    if (mergedEntry.mergeHistory.length > 10) {
        mergedEntry.mergeHistory = mergedEntry.mergeHistory.slice(-10);
    }

    return mergedEntry;
}

/**
 * Fusionne deux chaînes de texte en préservant les informations uniques
 * @param {string} targetText - Texte cible
 * @param {string} sourceText - Texte source
 * @returns {string} - Texte fusionné
 */
function mergeTextData(targetText, sourceText) {
    // Extraire les phrases de chaque texte
    const targetSentences = targetText.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 0);
    const sourceSentences = sourceText.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 0);

    // Trouver les phrases uniques dans la source
    const uniqueSourceSentences = sourceSentences.filter(sentence => {
        return !targetSentences.some(targetSentence => {
            const similarity = 1 - (levenshteinDistance(targetSentence, sentence) / Math.max(targetSentence.length, sentence.length));
            return similarity > 0.7; // Si la similarité est supérieure à 70%, considérer comme similaire
        });
    });

    // Si aucune phrase unique n'a été trouvée, retourner le texte cible
    if (uniqueSourceSentences.length === 0) {
        return targetText;
    }

    // Déterminer la meilleure façon de fusionner les textes
    if (targetText.length < 100 && sourceText.length < 100) {
        // Pour les textes courts, simplement concaténer avec une séparation claire
        return `${targetText} [Fusion] ${uniqueSourceSentences.join('. ')}.`;
    } else {
        // Pour les textes plus longs, essayer d'intégrer les phrases uniques de manière plus naturelle
        let mergedText = targetText;

        // Ajouter un séparateur si le texte cible ne se termine pas par un signe de ponctuation
        if (!mergedText.endsWith('.') && !mergedText.endsWith('!') && !mergedText.endsWith('?')) {
            mergedText += '.';
        }

        // Ajouter un espace si nécessaire
        if (!mergedText.endsWith(' ')) {
            mergedText += ' ';
        }

        // Ajouter les phrases uniques
        mergedText += uniqueSourceSentences.join('. ');

        // Ajouter un point final si nécessaire
        if (!mergedText.endsWith('.') && !mergedText.endsWith('!') && !mergedText.endsWith('?')) {
            mergedText += '.';
        }

        return mergedText;
    }
}

/**
 * Fusionne deux objets de manière récursive
 * @param {Object} targetObj - Objet cible
 * @param {Object} sourceObj - Objet source
 * @returns {Object} - Objet fusionné
 */
function mergeObjectData(targetObj, sourceObj) {
    // Créer une copie de l'objet cible
    const result = { ...targetObj };

    // Parcourir les propriétés de l'objet source
    Object.keys(sourceObj).forEach(key => {
        // Si la propriété existe dans l'objet cible
        if (key in result) {
            // Si les deux valeurs sont des objets, les fusionner récursivement
            if (typeof result[key] === 'object' && typeof sourceObj[key] === 'object' &&
                !Array.isArray(result[key]) && !Array.isArray(sourceObj[key])) {
                result[key] = mergeObjectData(result[key], sourceObj[key]);
            }
            // Si les deux valeurs sont des tableaux, les fusionner en éliminant les doublons
            else if (Array.isArray(result[key]) && Array.isArray(sourceObj[key])) {
                result[key] = [...new Set([...result[key], ...sourceObj[key]])];
            }
            // Si les valeurs sont différentes, créer un tableau avec les deux valeurs
            else if (result[key] !== sourceObj[key]) {
                // Si la valeur cible est déjà un tableau, ajouter la nouvelle valeur
                if (Array.isArray(result[key])) {
                    if (!result[key].includes(sourceObj[key])) {
                        result[key].push(sourceObj[key]);
                    }
                } else {
                    // Sinon, créer un nouveau tableau
                    result[key] = [result[key], sourceObj[key]];
                }
            }
        } else {
            // Si la propriété n'existe pas dans l'objet cible, l'ajouter
            result[key] = sourceObj[key];
        }
    });

    return result;
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    if (window.notifications) {
        window.notifications[type](message, '', { duration: 3000 });
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}
