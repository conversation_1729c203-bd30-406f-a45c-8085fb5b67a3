/**
 * Gestionnaire de thème pour l'application Louna
 * Permet de basculer entre un thème sombre et un thème clair
 */

class ThemeSwitcher {
    constructor() {
        // Thème par défaut
        this.defaultTheme = 'dark';

        // Élément HTML du document
        this.htmlElement = document.documentElement;

        // Clés de stockage local
        this.storageKey = 'louna_theme_preference';
        this.autoModeKey = 'louna_auto_theme_mode';

        // Mode automatique basé sur l'heure
        this.autoMode = localStorage.getItem(this.autoModeKey) === 'true';

        // Heures pour le mode automatique (format 24h)
        this.dayStartHour = 7;   // 7h du matin
        this.nightStartHour = 19; // 7h du soir

        // Timer pour le mode automatique
        this.autoTimer = null;

        // Initialiser le thème
        this.initialize();
    }

    /**
     * Initialise le thème en fonction des préférences de l'utilisateur
     */
    initialize() {
        // Si le mode automatique est activé, utiliser le thème basé sur l'heure
        if (this.autoMode) {
            this.setAutoTheme();
            this.startAutoMode();
        } else {
            // Récupérer le thème sauvegardé
            const savedTheme = localStorage.getItem(this.storageKey);

            // Vérifier si l'utilisateur préfère le thème sombre au niveau du système
            const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)').matches;

            // Déterminer le thème à utiliser
            const themeToUse = savedTheme || (prefersDarkScheme ? 'dark' : 'light');

            // Appliquer le thème
            this.setTheme(themeToUse);
        }

        // Ajouter un écouteur pour les changements de préférence système
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
            if (!localStorage.getItem(this.storageKey) && !this.autoMode) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    /**
     * Détermine le thème basé sur l'heure actuelle
     */
    getThemeByTime() {
        const now = new Date();
        const currentHour = now.getHours();

        // Thème sombre entre nightStartHour et dayStartHour
        if (currentHour >= this.nightStartHour || currentHour < this.dayStartHour) {
            return 'dark';
        } else {
            return 'light';
        }
    }

    /**
     * Applique le thème automatique basé sur l'heure
     */
    setAutoTheme() {
        const autoTheme = this.getThemeByTime();
        this.setTheme(autoTheme, false); // false = ne pas sauvegarder en tant que préférence manuelle
    }

    /**
     * Démarre le mode automatique
     */
    startAutoMode() {
        // Arrêter le timer existant s'il y en a un
        if (this.autoTimer) {
            clearInterval(this.autoTimer);
        }

        // Vérifier le thème toutes les minutes
        this.autoTimer = setInterval(() => {
            if (this.autoMode) {
                this.setAutoTheme();
            }
        }, 60000); // 60 secondes

        console.log('Mode automatique de thème activé');
    }

    /**
     * Arrête le mode automatique
     */
    stopAutoMode() {
        if (this.autoTimer) {
            clearInterval(this.autoTimer);
            this.autoTimer = null;
        }
        console.log('Mode automatique de thème désactivé');
    }

    /**
     * Active/désactive le mode automatique
     */
    toggleAutoMode() {
        this.autoMode = !this.autoMode;
        localStorage.setItem(this.autoModeKey, this.autoMode.toString());

        if (this.autoMode) {
            this.setAutoTheme();
            this.startAutoMode();
        } else {
            this.stopAutoMode();
        }

        // Mettre à jour l'interface
        this.updateAutoModeToggles();

        return this.autoMode;
    }

    /**
     * Met à jour les boutons de mode automatique
     */
    updateAutoModeToggles() {
        const autoToggles = document.querySelectorAll('.auto-theme-toggle');
        autoToggles.forEach(toggle => {
            if (toggle.type === 'checkbox') {
                toggle.checked = this.autoMode;
            }
        });
    }

    /**
     * Applique le thème spécifié
     * @param {string} theme - Le thème à appliquer ('dark' ou 'light')
     * @param {boolean} savePreference - Si true, sauvegarde comme préférence utilisateur
     */
    setTheme(theme, savePreference = true) {
        // Vérifier que le thème est valide
        if (theme !== 'dark' && theme !== 'light') {
            console.error('Thème invalide:', theme);
            theme = this.defaultTheme;
        }

        // Appliquer l'attribut de thème à l'élément HTML
        this.htmlElement.setAttribute('data-theme', theme);

        // Mettre à jour les boutons de changement de thème
        this.updateThemeToggles(theme);

        // Sauvegarder la préférence seulement si demandé
        if (savePreference) {
            localStorage.setItem(this.storageKey, theme);
            // Désactiver le mode automatique si l'utilisateur change manuellement
            if (this.autoMode) {
                this.autoMode = false;
                localStorage.setItem(this.autoModeKey, 'false');
                this.stopAutoMode();
                this.updateAutoModeToggles();
            }
        }

        // Émettre un événement personnalisé pour notifier le changement de thème
        const themeChangeEvent = new CustomEvent('themeChanged', {
            detail: { theme: theme, auto: !savePreference }
        });
        document.dispatchEvent(themeChangeEvent);

        console.log(`Thème ${theme} appliqué${savePreference ? ' (préférence sauvegardée)' : ' (automatique)'}`);
    }

    /**
     * Bascule entre les thèmes sombre et clair
     */
    toggleTheme() {
        // Récupérer le thème actuel
        const currentTheme = this.htmlElement.getAttribute('data-theme') || this.defaultTheme;

        // Basculer vers l'autre thème
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        // Appliquer le nouveau thème
        this.setTheme(newTheme);
    }

    /**
     * Met à jour tous les boutons de changement de thème
     * @param {string} theme - Le thème actuel
     */
    updateThemeToggles(theme) {
        // Sélectionner tous les boutons de changement de thème
        const themeToggles = document.querySelectorAll('.theme-toggle');

        // Mettre à jour l'état des boutons
        themeToggles.forEach(toggle => {
            if (toggle.type === 'checkbox') {
                toggle.checked = theme === 'light';
            }
        });
    }

    /**
     * Initialise les boutons de changement de thème
     */
    initializeToggles() {
        // Sélectionner tous les boutons de changement de thème
        const themeToggles = document.querySelectorAll('.theme-toggle');

        // Ajouter un écouteur d'événement à chaque bouton
        themeToggles.forEach(toggle => {
            toggle.addEventListener('change', () => {
                this.toggleTheme();
            });
        });

        // Mettre à jour l'état des boutons
        const currentTheme = this.htmlElement.getAttribute('data-theme') || this.defaultTheme;
        this.updateThemeToggles(currentTheme);
    }

    /**
     * Crée un bouton de changement de thème
     * @param {HTMLElement} container - L'élément conteneur où ajouter le bouton
     */
    createToggle(container) {
        // Créer le conteneur du bouton
        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'theme-switcher';

        // Créer le bouton
        const toggle = document.createElement('input');
        toggle.type = 'checkbox';
        toggle.className = 'theme-toggle';

        // Créer le slider
        const slider = document.createElement('span');
        slider.className = 'theme-slider';

        // Créer les icônes
        const sunIcon = document.createElement('i');
        sunIcon.className = 'fas fa-sun theme-icon sun';

        const moonIcon = document.createElement('i');
        moonIcon.className = 'fas fa-moon theme-icon moon';

        // Assembler les éléments
        slider.appendChild(sunIcon);
        slider.appendChild(moonIcon);
        toggleContainer.appendChild(toggle);
        toggleContainer.appendChild(slider);

        // Ajouter le bouton au conteneur
        container.appendChild(toggleContainer);

        // Ajouter un écouteur d'événement
        toggle.addEventListener('change', () => {
            this.toggleTheme();
        });

        // Mettre à jour l'état du bouton
        const currentTheme = this.htmlElement.getAttribute('data-theme') || this.defaultTheme;
        toggle.checked = currentTheme === 'light';
    }
}

// Créer une instance du gestionnaire de thème
const themeSwitcher = new ThemeSwitcher();

// Initialiser les boutons de changement de thème lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    themeSwitcher.initializeToggles();
});
