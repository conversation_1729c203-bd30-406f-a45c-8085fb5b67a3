/**
 * Gestionnaire de paramètres pour l'application Louna
 * Ce module gère les paramètres de l'application et les sauvegarde dans le stockage local
 */

// Clé de stockage local pour les paramètres
const SETTINGS_STORAGE_KEY = 'louna_settings';

// Paramètres par défaut
const DEFAULT_SETTINGS = {
    memory: {
        autoMemoryCycles: false,
        memoryCycleInterval: 30, // minutes
        autoDreams: false,
        dreamGenerationInterval: 12 // heures
    },
    accelerators: {
        autoOptimization: false,
        optimizationInterval: 15, // minutes
        maxBoostFactor: 3.5
    },
    ltx: {
        autoCamera: false,
        videoWidth: 640,
        videoHeight: 480,
        objectDetection: true,
        thermalAnalysis: true,
        facialRecognition: false
    },
    interface: {
        darkTheme: true,
        animations: true,
        fontSize: 1.0
    },
    desktop: {
        autoLaunch: false,
        startMinimized: false,
        minimizeToTray: true
    }
};

// Timers pour les opérations automatiques
let memoryCycleTimer = null;
let dreamGenerationTimer = null;
let acceleratorOptimizationTimer = null;

/**
 * Initialise les paramètres
 */
function initializeSettings() {
    // Charger les paramètres depuis le stockage local
    const settings = loadSettings();

    // Appliquer les paramètres à l'interface
    applySettingsToUI(settings);

    // Configurer les écouteurs d'événements
    setupEventListeners();

    // Démarrer les timers si nécessaire
    startAutomatedTasks(settings);
}

/**
 * Charge les paramètres depuis le stockage local
 * @returns {Object} - Les paramètres chargés ou les paramètres par défaut
 */
function loadSettings() {
    try {
        const storedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);

        if (storedSettings) {
            return JSON.parse(storedSettings);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        showNotification('Erreur lors du chargement des paramètres', 'error');
    }

    return DEFAULT_SETTINGS;
}

/**
 * Sauvegarde les paramètres dans le stockage local
 * @param {Object} settings - Les paramètres à sauvegarder
 */
function saveSettings(settings) {
    try {
        localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(settings));
        showNotification('Paramètres sauvegardés avec succès', 'success');
    } catch (error) {
        console.error('Erreur lors de la sauvegarde des paramètres:', error);
        showNotification('Erreur lors de la sauvegarde des paramètres', 'error');
    }
}

/**
 * Applique les paramètres à l'interface utilisateur
 * @param {Object} settings - Les paramètres à appliquer
 */
function applySettingsToUI(settings) {
    // Paramètres de la mémoire thermique
    document.getElementById('auto-memory-cycles').checked = settings.memory.autoMemoryCycles;
    document.getElementById('memory-cycle-interval').value = settings.memory.memoryCycleInterval;
    document.getElementById('memory-cycle-interval-value').textContent = settings.memory.memoryCycleInterval;
    document.getElementById('auto-dreams').checked = settings.memory.autoDreams;
    document.getElementById('dream-generation-interval').value = settings.memory.dreamGenerationInterval;
    document.getElementById('dream-generation-interval-value').textContent = settings.memory.dreamGenerationInterval;

    // Paramètres des accélérateurs Kyber
    document.getElementById('auto-accelerator-optimization').checked = settings.accelerators.autoOptimization;
    document.getElementById('accelerator-optimization-interval').value = settings.accelerators.optimizationInterval;
    document.getElementById('accelerator-optimization-interval-value').textContent = settings.accelerators.optimizationInterval;
    document.getElementById('max-boost-factor').value = settings.accelerators.maxBoostFactor;
    document.getElementById('max-boost-factor-value').textContent = settings.accelerators.maxBoostFactor;

    // Paramètres LTX Vidéo
    document.getElementById('auto-camera').checked = settings.ltx.autoCamera;
    document.getElementById('video-width').value = settings.ltx.videoWidth;
    document.getElementById('video-height').value = settings.ltx.videoHeight;
    document.getElementById('object-detection').checked = settings.ltx.objectDetection;
    document.getElementById('thermal-analysis').checked = settings.ltx.thermalAnalysis;
    document.getElementById('facial-recognition').checked = settings.ltx.facialRecognition;

    // Paramètres de l'interface
    document.getElementById('dark-theme').checked = settings.interface.darkTheme;
    document.getElementById('animations').checked = settings.interface.animations;
    document.getElementById('font-size').value = settings.interface.fontSize;
    document.getElementById('font-size-value').textContent = settings.interface.fontSize.toFixed(1);

    // Paramètres de l'application desktop (si disponibles)
    if (document.getElementById('auto-launch')) {
        document.getElementById('auto-launch').checked = settings.desktop?.autoLaunch || false;
    }

    if (document.getElementById('start-minimized')) {
        document.getElementById('start-minimized').checked = settings.desktop?.startMinimized || false;
    }

    if (document.getElementById('minimize-to-tray')) {
        document.getElementById('minimize-to-tray').checked = settings.desktop?.minimizeToTray !== undefined ? settings.desktop.minimizeToTray : true;
    }

    // Appliquer le thème
    applyTheme(settings.interface.darkTheme);

    // Appliquer la taille de police
    applyFontSize(settings.interface.fontSize);

    // Appliquer les animations
    applyAnimations(settings.interface.animations);
}

/**
 * Récupère les paramètres depuis l'interface utilisateur
 * @returns {Object} - Les paramètres récupérés
 */
function getSettingsFromUI() {
    const settings = {
        memory: {
            autoMemoryCycles: document.getElementById('auto-memory-cycles').checked,
            memoryCycleInterval: parseInt(document.getElementById('memory-cycle-interval').value),
            autoDreams: document.getElementById('auto-dreams').checked,
            dreamGenerationInterval: parseInt(document.getElementById('dream-generation-interval').value)
        },
        accelerators: {
            autoOptimization: document.getElementById('auto-accelerator-optimization').checked,
            optimizationInterval: parseInt(document.getElementById('accelerator-optimization-interval').value),
            maxBoostFactor: parseFloat(document.getElementById('max-boost-factor').value)
        },
        ltx: {
            autoCamera: document.getElementById('auto-camera').checked,
            videoWidth: parseInt(document.getElementById('video-width').value),
            videoHeight: parseInt(document.getElementById('video-height').value),
            objectDetection: document.getElementById('object-detection').checked,
            thermalAnalysis: document.getElementById('thermal-analysis').checked,
            facialRecognition: document.getElementById('facial-recognition').checked
        },
        interface: {
            darkTheme: document.getElementById('dark-theme').checked,
            animations: document.getElementById('animations').checked,
            fontSize: parseFloat(document.getElementById('font-size').value)
        }
    };

    // Ajouter les paramètres de l'application desktop si disponibles
    if (document.getElementById('auto-launch')) {
        settings.desktop = {
            autoLaunch: document.getElementById('auto-launch').checked,
            startMinimized: document.getElementById('start-minimized').checked,
            minimizeToTray: document.getElementById('minimize-to-tray').checked
        };
    }

    return settings;
}

/**
 * Configure les écouteurs d'événements
 */
function setupEventListeners() {
    // Bouton de sauvegarde
    document.getElementById('save-btn').addEventListener('click', () => {
        const settings = getSettingsFromUI();
        saveSettings(settings);

        // Arrêter les timers existants
        stopAutomatedTasks();

        // Démarrer les nouveaux timers
        startAutomatedTasks(settings);

        // Appliquer les paramètres d'interface
        applyTheme(settings.interface.darkTheme);
        applyFontSize(settings.interface.fontSize);
        applyAnimations(settings.interface.animations);
    });

    // Bouton de réinitialisation
    document.getElementById('reset-btn').addEventListener('click', () => {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
            applySettingsToUI(DEFAULT_SETTINGS);
            saveSettings(DEFAULT_SETTINGS);

            // Arrêter les timers existants
            stopAutomatedTasks();

            // Démarrer les nouveaux timers
            startAutomatedTasks(DEFAULT_SETTINGS);
        }
    });

    // Mise à jour des valeurs des curseurs
    document.getElementById('memory-cycle-interval').addEventListener('input', (e) => {
        document.getElementById('memory-cycle-interval-value').textContent = e.target.value;
    });

    document.getElementById('dream-generation-interval').addEventListener('input', (e) => {
        document.getElementById('dream-generation-interval-value').textContent = e.target.value;
    });

    document.getElementById('accelerator-optimization-interval').addEventListener('input', (e) => {
        document.getElementById('accelerator-optimization-interval-value').textContent = e.target.value;
    });

    document.getElementById('max-boost-factor').addEventListener('input', (e) => {
        document.getElementById('max-boost-factor-value').textContent = parseFloat(e.target.value).toFixed(1);
    });

    document.getElementById('font-size').addEventListener('input', (e) => {
        const fontSize = parseFloat(e.target.value);
        document.getElementById('font-size-value').textContent = fontSize.toFixed(1);
        applyFontSize(fontSize);
    });

    // Thème
    document.getElementById('dark-theme').addEventListener('change', (e) => {
        applyTheme(e.target.checked);
    });

    // Animations
    document.getElementById('animations').addEventListener('change', (e) => {
        applyAnimations(e.target.checked);
    });
}

/**
 * Démarre les tâches automatisées
 * @param {Object} settings - Les paramètres
 */
function startAutomatedTasks(settings) {
    // Cycles de mémoire automatiques
    if (settings.memory.autoMemoryCycles) {
        const interval = settings.memory.memoryCycleInterval * 60 * 1000; // Convertir en millisecondes
        memoryCycleTimer = setInterval(async () => {
            try {
                await window.thermalMemory.cycle();
                showNotification('Cycle de mémoire automatique effectué', 'info');
            } catch (error) {
                console.error('Erreur lors du cycle de mémoire automatique:', error);
            }
        }, interval);
    }

    // Génération automatique de rêves
    if (settings.memory.autoDreams) {
        const interval = settings.memory.dreamGenerationInterval * 60 * 60 * 1000; // Convertir en millisecondes
        dreamGenerationTimer = setInterval(async () => {
            try {
                const dream = await window.thermalMemory.generateDream();
                if (dream) {
                    showNotification('Nouveau rêve généré', 'info');
                }
            } catch (error) {
                console.error('Erreur lors de la génération automatique de rêve:', error);
            }
        }, interval);
    }

    // Optimisation automatique des accélérateurs
    if (settings.accelerators.autoOptimization) {
        const interval = settings.accelerators.optimizationInterval * 60 * 1000; // Convertir en millisecondes
        acceleratorOptimizationTimer = setInterval(async () => {
            try {
                await window.kyberAccelerators.optimizeAll();
                showNotification('Optimisation automatique des accélérateurs effectuée', 'info');
            } catch (error) {
                console.error('Erreur lors de l\'optimisation automatique des accélérateurs:', error);
            }
        }, interval);
    }

    // Démarrage automatique de la caméra
    if (settings.ltx.autoCamera && window.LTXVideo) {
        try {
            const ltxVideo = new window.LTXVideo({
                captureWidth: settings.ltx.videoWidth,
                captureHeight: settings.ltx.videoHeight,
                processingEnabled: true,
                thermalAnalysisEnabled: settings.ltx.thermalAnalysis,
                objectDetectionEnabled: settings.ltx.objectDetection,
                facialRecognitionEnabled: settings.ltx.facialRecognition
            });

            ltxVideo.initialize().then(() => {
                ltxVideo.start();
                showNotification('Caméra démarrée automatiquement', 'info');
            });
        } catch (error) {
            console.error('Erreur lors du démarrage automatique de la caméra:', error);
        }
    }
}

/**
 * Arrête les tâches automatisées
 */
function stopAutomatedTasks() {
    // Arrêter les timers
    if (memoryCycleTimer) {
        clearInterval(memoryCycleTimer);
        memoryCycleTimer = null;
    }

    if (dreamGenerationTimer) {
        clearInterval(dreamGenerationTimer);
        dreamGenerationTimer = null;
    }

    if (acceleratorOptimizationTimer) {
        clearInterval(acceleratorOptimizationTimer);
        acceleratorOptimizationTimer = null;
    }
}

/**
 * Applique le thème
 * @param {boolean} isDark - True pour le thème sombre, false pour le thème clair
 */
function applyTheme(isDark) {
    document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
}

/**
 * Applique la taille de police
 * @param {number} size - Taille de police (facteur multiplicateur)
 */
function applyFontSize(size) {
    document.documentElement.style.fontSize = `${size}rem`;
}

/**
 * Applique les animations
 * @param {boolean} enabled - True pour activer les animations, false pour les désactiver
 */
function applyAnimations(enabled) {
    if (enabled) {
        document.body.classList.remove('no-animations');
    } else {
        document.body.classList.add('no-animations');
    }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Ajouter la notification au document
    document.body.appendChild(notification);

    // Afficher la notification
    setTimeout(() => {
        notification.classList.add('visible');
    }, 10);

    // Supprimer la notification après 5 secondes
    setTimeout(() => {
        notification.classList.remove('visible');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

/**
 * Initialise les fonctionnalités spécifiques à l'application desktop
 */
function initializeDesktopFeatures() {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        // Afficher les éléments spécifiques à l'application desktop
        document.querySelectorAll('.desktop-only').forEach(el => {
            el.style.display = 'block';
        });

        // Configurer les écouteurs d'événements pour les fonctionnalités desktop
        if (document.getElementById('auto-launch')) {
            // Récupérer l'état actuel du démarrage automatique
            window.electron.autoLaunch.get().then(config => {
                document.getElementById('auto-launch').checked = config.enabled;
                document.getElementById('start-minimized').checked = config.minimized;
            }).catch(error => {
                console.error('Erreur lors de la récupération de la configuration de démarrage automatique:', error);
            });

            // Configurer l'écouteur d'événement pour le démarrage automatique
            document.getElementById('auto-launch').addEventListener('change', e => {
                const enabled = e.target.checked;
                const minimized = document.getElementById('start-minimized').checked;
                window.electron.autoLaunch.set(enabled, minimized);
                showNotification(`Démarrage automatique ${enabled ? 'activé' : 'désactivé'}`, 'info');
            });

            // Configurer l'écouteur d'événement pour le démarrage minimisé
            document.getElementById('start-minimized').addEventListener('change', e => {
                const minimized = e.target.checked;
                const enabled = document.getElementById('auto-launch').checked;
                if (enabled) {
                    window.electron.autoLaunch.set(enabled, minimized);
                    showNotification(`Démarrage minimisé ${minimized ? 'activé' : 'désactivé'}`, 'info');
                }
            });
        }

        // Configurer les écouteurs d'événements pour les boutons de sauvegarde et restauration
        if (document.getElementById('backup-btn')) {
            document.getElementById('backup-btn').addEventListener('click', () => {
                window.electron.backup.save();
            });
        }

        if (document.getElementById('restore-btn')) {
            document.getElementById('restore-btn').addEventListener('click', () => {
                window.electron.backup.restore();
            });
        }
    }
}

// Initialiser les paramètres lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
    initializeSettings();
    initializeDesktopFeatures();
});
