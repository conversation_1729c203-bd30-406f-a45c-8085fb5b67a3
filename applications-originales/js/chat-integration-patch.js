/**
 * Patch d'intégration automatique pour toutes les interfaces de chat
 * Ce script s'intègre automatiquement dans toutes les interfaces existantes
 */

(function() {
    'use strict';
    
    console.log('🔧 Application du patch d\'intégration des applications...');

    // Attendre que le DOM soit chargé
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePatch);
    } else {
        initializePatch();
    }

    function initializePatch() {
        console.log('🚀 Initialisation du patch d\'intégration...');
        
        // Attendre que le gestionnaire d'applications soit disponible
        waitForAppManager().then(() => {
            patchExistingChatFunctions();
            addAppManagerToInterfaces();
            setupGlobalEventListeners();
            console.log('✅ Patch d\'intégration appliqué avec succès');
        });
    }

    function waitForAppManager() {
        return new Promise((resolve) => {
            const checkManager = () => {
                if (window.unifiedAppManager && window.unifiedAppManager.isInitialized) {
                    resolve();
                } else {
                    setTimeout(checkManager, 100);
                }
            };
            checkManager();
        });
    }

    function patchExistingChatFunctions() {
        console.log('🔧 Application des patches aux fonctions de chat existantes...');

        // Patch pour sendMessage global
        if (typeof window.sendMessage === 'function') {
            const originalSendMessage = window.sendMessage;
            window.sendMessage = async function(message, ...args) {
                if (window.unifiedAppManager.isAppCommand(message)) {
                    console.log('🖥️ Commande d\'application interceptée par le patch');
                    const result = await window.unifiedAppManager.processAppCommand(message);
                    const formattedResponse = window.unifiedAppManager.formatAppResponse(result);
                    
                    // Ajouter la réponse à l'interface de chat
                    if (typeof addMessage === 'function') {
                        addMessage('assistant', formattedResponse);
                    } else if (typeof addAssistantMessage === 'function') {
                        addAssistantMessage(formattedResponse);
                    } else {
                        console.log('📱 Résultat de la commande:', formattedResponse);
                    }
                    return;
                }
                return originalSendMessage.call(this, message, ...args);
            };
            console.log('✅ sendMessage global patché');
        }

        // Patch pour sendToAgent
        if (typeof window.sendToAgent === 'function') {
            const originalSendToAgent = window.sendToAgent;
            window.sendToAgent = async function(message, ...args) {
                if (window.unifiedAppManager.isAppCommand(message)) {
                    console.log('🖥️ Commande d\'application interceptée par sendToAgent');
                    const result = await window.unifiedAppManager.processAppCommand(message);
                    const formattedResponse = window.unifiedAppManager.formatAppResponse(result);
                    
                    // Ajouter la réponse à l'interface
                    if (typeof addMessage === 'function') {
                        addMessage('assistant', formattedResponse);
                    }
                    return { success: true, response: formattedResponse };
                }
                return originalSendToAgent.call(this, message, ...args);
            };
            console.log('✅ sendToAgent patché');
        }

        // Patch pour les formulaires de chat
        patchChatForms();
    }

    function patchChatForms() {
        // Rechercher tous les formulaires de chat
        const chatForms = document.querySelectorAll('form[id*="chat"], .chat-form, #messageForm');
        const chatInputs = document.querySelectorAll('input[id*="message"], input[placeholder*="message"], .chat-input input');
        const sendButtons = document.querySelectorAll('button[id*="send"], .send-button, .chat-send');

        // Patch des formulaires
        chatForms.forEach(form => {
            form.addEventListener('submit', async (e) => {
                const input = form.querySelector('input[type="text"], input[type="search"], textarea');
                if (input && input.value.trim()) {
                    const message = input.value.trim();
                    if (window.unifiedAppManager.isAppCommand(message)) {
                        e.preventDefault();
                        console.log('🖥️ Commande d\'application interceptée depuis le formulaire');
                        
                        const result = await window.unifiedAppManager.processAppCommand(message);
                        const formattedResponse = window.unifiedAppManager.formatAppResponse(result);
                        
                        // Ajouter à l'interface
                        addResponseToChat(formattedResponse);
                        input.value = '';
                    }
                }
            });
        });

        // Patch des boutons d'envoi
        sendButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                const input = findNearestInput(button);
                if (input && input.value.trim()) {
                    const message = input.value.trim();
                    if (window.unifiedAppManager.isAppCommand(message)) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        console.log('🖥️ Commande d\'application interceptée depuis le bouton');
                        
                        const result = await window.unifiedAppManager.processAppCommand(message);
                        const formattedResponse = window.unifiedAppManager.formatAppResponse(result);
                        
                        addResponseToChat(formattedResponse);
                        input.value = '';
                    }
                }
            });
        });

        console.log(`✅ ${chatForms.length} formulaires et ${sendButtons.length} boutons patchés`);
    }

    function findNearestInput(button) {
        // Chercher dans le même conteneur
        const container = button.closest('.chat-container, .chat-input, form, .message-input');
        if (container) {
            const input = container.querySelector('input[type="text"], input[type="search"], textarea');
            if (input) return input;
        }
        
        // Chercher par ID communs
        const commonIds = ['message-input', 'chat-input', 'messageInput', 'chatInput'];
        for (const id of commonIds) {
            const input = document.getElementById(id);
            if (input) return input;
        }
        
        return null;
    }

    function addResponseToChat(response) {
        // Essayer différentes méthodes pour ajouter la réponse
        if (typeof addMessage === 'function') {
            addMessage('assistant', response);
        } else if (typeof addAssistantMessage === 'function') {
            addAssistantMessage(response);
        } else if (typeof appendMessage === 'function') {
            appendMessage('assistant', response);
        } else {
            // Fallback : ajouter directement au DOM
            addResponseToDOM(response);
        }
    }

    function addResponseToDOM(response) {
        const chatContainer = document.querySelector('.chat-messages, #chat-messages, .messages-container, .chat-container .messages');
        if (chatContainer) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant app-response';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="app-response-header">🖥️ Gestionnaire d'Applications</div>
                    <div class="app-response-content">${response.replace(/\n/g, '<br>')}</div>
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
    }

    function addAppManagerToInterfaces() {
        console.log('🎨 Ajout des contrôles d\'applications aux interfaces...');
        
        // Chercher les conteneurs appropriés
        const containers = document.querySelectorAll('.chat-container, .sidebar, .right-panel, .controls');
        
        containers.forEach(container => {
            if (!container.querySelector('.app-quick-actions')) {
                window.unifiedAppManager.addAppButtons(container);
            }
        });
    }

    function setupGlobalEventListeners() {
        // Écouter les touches Entrée dans tous les champs de saisie
        document.addEventListener('keypress', async (e) => {
            if (e.key === 'Enter' && e.target.matches('input[id*="message"], input[placeholder*="message"], .chat-input input')) {
                const message = e.target.value.trim();
                if (message && window.unifiedAppManager.isAppCommand(message)) {
                    e.preventDefault();
                    
                    console.log('🖥️ Commande d\'application interceptée via Enter');
                    
                    const result = await window.unifiedAppManager.processAppCommand(message);
                    const formattedResponse = window.unifiedAppManager.formatAppResponse(result);
                    
                    addResponseToChat(formattedResponse);
                    e.target.value = '';
                }
            }
        });

        console.log('✅ Écouteurs d\'événements globaux configurés');
    }

    // Ajouter des styles pour les réponses d'applications
    function addAppResponseStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .app-response {
                border-left: 4px solid #ff69b4 !important;
                background: rgba(255, 105, 180, 0.1) !important;
            }
            
            .app-response-header {
                font-weight: bold;
                color: #ff69b4;
                margin-bottom: 8px;
            }
            
            .app-response-content {
                font-family: monospace;
                background: rgba(0, 0, 0, 0.2);
                padding: 8px;
                border-radius: 4px;
                margin: 4px 0;
            }
            
            .app-quick-actions {
                animation: slideIn 0.3s ease-out;
            }
            
            @keyframes slideIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .app-btn:hover {
                background: #ff1493 !important;
                transform: scale(1.05);
                transition: all 0.2s ease;
            }
        `;
        document.head.appendChild(style);
    }

    // Ajouter les styles
    addAppResponseStyles();

    // Exposer des fonctions utilitaires
    window.appManagerPatch = {
        isActive: true,
        version: '1.0.0',
        reinitialize: initializePatch,
        addAppButtons: (container) => window.unifiedAppManager.addAppButtons(container)
    };

})();
