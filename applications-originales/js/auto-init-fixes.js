/**
 * Corrections automatiques pour l'initialisation de l'interface Louna
 * Corrige les problèmes de connexion des agents et d'affichage
 */

// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Auto-init-fixes: Démarrage des corrections automatiques...');
    
    // Correction 1: Vérifier et corriger le statut des agents
    fixAgentStatus();
    
    // Correction 2: Corriger les erreurs d'affichage
    fixDisplayErrors();
    
    // Correction 3: Initialiser les connexions manquantes
    initMissingConnections();
    
    // Correction 4: Corriger les timeouts
    fixTimeouts();
    
    console.log('✅ Auto-init-fixes: Toutes les corrections appliquées');
});

/**
 * Correction du statut des agents
 */
async function fixAgentStatus() {
    try {
        console.log('🤖 Correction du statut des agents...');
        
        // Vérifier si l'API des agents fonctionne
        const response = await fetch('/api/agents/status');
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Agents connectés:', data.data);
            
            // Mettre à jour l'affichage si nécessaire
            updateAgentDisplay(data.data);
        } else {
            console.warn('⚠️ API agents non disponible, utilisation du fallback');
            useAgentFallback();
        }
    } catch (error) {
        console.error('❌ Erreur correction agents:', error);
        useAgentFallback();
    }
}

/**
 * Mise à jour de l'affichage des agents
 */
function updateAgentDisplay(agentData) {
    // Mettre à jour les indicateurs de statut
    const statusElements = document.querySelectorAll('.agent-status, .status-dot');
    statusElements.forEach(element => {
        if (agentData.connected) {
            element.classList.add('connected');
            element.classList.remove('disconnected');
            element.style.backgroundColor = '#00ff00';
        } else {
            element.classList.add('disconnected');
            element.classList.remove('connected');
            element.style.backgroundColor = '#ff0000';
        }
    });
    
    // Mettre à jour les textes de statut
    const statusTexts = document.querySelectorAll('.agent-status-text');
    statusTexts.forEach(element => {
        element.textContent = agentData.connected ? 'Agent connecté' : 'Agent déconnecté';
    });
}

/**
 * Fallback pour les agents
 */
function useAgentFallback() {
    console.log('🔄 Utilisation du fallback pour les agents...');
    
    // Simuler un agent connecté
    const fallbackData = {
        connected: true,
        available: ['enhanced_agent', 'cognitive_system'],
        active: 'enhanced_agent'
    };
    
    updateAgentDisplay(fallbackData);
}

/**
 * Correction des erreurs d'affichage
 */
function fixDisplayErrors() {
    console.log('🎨 Correction des erreurs d'affichage...');
    
    // Corriger les éléments avec des valeurs NaN
    const elements = document.querySelectorAll('*');
    elements.forEach(element => {
        if (element.textContent && element.textContent.includes('NaN')) {
            element.textContent = element.textContent.replace(/NaN/g, '0');
        }
    });
    
    // Corriger les pourcentages invalides
    const percentElements = document.querySelectorAll('[data-percentage]');
    percentElements.forEach(element => {
        const percentage = element.getAttribute('data-percentage');
        if (isNaN(percentage) || percentage === 'NaN') {
            element.setAttribute('data-percentage', '0');
            element.textContent = '0%';
        }
    });
    
    // Corriger les barres de progression
    const progressBars = document.querySelectorAll('.progress-bar, .usage-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        if (!width || width.includes('NaN')) {
            bar.style.width = '0%';
        }
    });
}

/**
 * Initialisation des connexions manquantes
 */
function initMissingConnections() {
    console.log('🔗 Initialisation des connexions manquantes...');
    
    // Vérifier et initialiser les WebSockets si nécessaire
    if (typeof window.initWebSocket === 'function') {
        try {
            window.initWebSocket();
        } catch (error) {
            console.warn('⚠️ WebSocket non disponible:', error);
        }
    }
    
    // Vérifier et initialiser les EventSource si nécessaire
    if (typeof window.initEventSource === 'function') {
        try {
            window.initEventSource();
        } catch (error) {
            console.warn('⚠️ EventSource non disponible:', error);
        }
    }
    
    // Initialiser les connexions de mémoire thermique
    initThermalMemoryConnection();
}

/**
 * Initialisation de la connexion mémoire thermique
 */
async function initThermalMemoryConnection() {
    try {
        const response = await fetch('/api/thermal/status');
        if (response.ok) {
            console.log('✅ Mémoire thermique connectée');
        } else {
            console.warn('⚠️ Mémoire thermique non disponible');
        }
    } catch (error) {
        console.warn('⚠️ Erreur connexion mémoire thermique:', error);
    }
}

/**
 * Correction des timeouts
 */
function fixTimeouts() {
    console.log('⏱️ Correction des timeouts...');
    
    // Augmenter les timeouts pour les requêtes lentes
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        // Ajouter un timeout par défaut de 30 secondes
        if (!options.signal) {
            const controller = new AbortController();
            setTimeout(() => controller.abort(), 30000);
            options.signal = controller.signal;
        }
        
        return originalFetch(url, options);
    };
}

/**
 * Fonction utilitaire pour retry automatique
 */
function retryOperation(operation, maxRetries = 3, delay = 1000) {
    return new Promise((resolve, reject) => {
        let attempts = 0;
        
        function attempt() {
            attempts++;
            operation()
                .then(resolve)
                .catch(error => {
                    if (attempts < maxRetries) {
                        console.log(`🔄 Tentative ${attempts}/${maxRetries} échouée, retry dans ${delay}ms...`);
                        setTimeout(attempt, delay);
                    } else {
                        reject(error);
                    }
                });
        }
        
        attempt();
    });
}

/**
 * Correction des erreurs de console
 */
function fixConsoleErrors() {
    // Intercepter les erreurs de console
    const originalError = console.error;
    console.error = function(...args) {
        // Filtrer certaines erreurs connues et non critiques
        const message = args.join(' ');
        if (message.includes('Failed to load resource') && message.includes('404')) {
            console.warn('⚠️ Ressource manquante (non critique):', ...args);
            return;
        }
        
        originalError.apply(console, args);
    };
}

/**
 * Surveillance continue des erreurs
 */
function startErrorMonitoring() {
    // Surveiller les erreurs JavaScript
    window.addEventListener('error', function(event) {
        console.warn('⚠️ Erreur JavaScript interceptée:', event.error);
        
        // Tenter une correction automatique selon le type d'erreur
        if (event.error && event.error.message) {
            const message = event.error.message;
            
            if (message.includes('fetch')) {
                console.log('🔧 Tentative de correction de l\'erreur fetch...');
                // Réessayer la dernière requête fetch
            }
            
            if (message.includes('undefined')) {
                console.log('🔧 Tentative de correction de l\'erreur undefined...');
                // Réinitialiser les variables globales
            }
        }
    });
    
    // Surveiller les promesses rejetées
    window.addEventListener('unhandledrejection', function(event) {
        console.warn('⚠️ Promise rejetée interceptée:', event.reason);
        
        // Empêcher l'affichage de l'erreur dans la console
        event.preventDefault();
    });
}

/**
 * Initialisation des corrections en continu
 */
function startContinuousFixing() {
    // Vérifier et corriger toutes les 30 secondes
    setInterval(() => {
        fixDisplayErrors();
        fixAgentStatus();
    }, 30000);
    
    console.log('🔄 Corrections continues démarrées (toutes les 30s)');
}

// Démarrer la surveillance et les corrections continues
fixConsoleErrors();
startErrorMonitoring();
startContinuousFixing();

// Exporter les fonctions pour utilisation externe
window.autoInitFixes = {
    fixAgentStatus,
    fixDisplayErrors,
    initMissingConnections,
    fixTimeouts,
    retryOperation
};

console.log('🚀 Auto-init-fixes: Module chargé et prêt');
