/**
 * Interface utilisateur pour la fusion de mémoire
 * 
 * Ce script gère l'interface utilisateur pour la fusion de mémoire
 * entre les agents.
 */

// Variables globales
let fusionConfig = null;
let similarPairs = [];
let fusionResults = null;
let sourceAgent = 'agent_agent local';
let targetAgent = 'agent_training';

// Éléments DOM
const configForm = document.getElementById('fusion-config-form');
const similarityThresholdInput = document.getElementById('similarity-threshold');
const importanceWeightInput = document.getElementById('importance-weight');
const recencyWeightInput = document.getElementById('recency-weight');
const categoryWeightInput = document.getElementById('category-weight');
const metadataWeightInput = document.getElementById('metadata-weight');
const enableDeepFusionInput = document.getElementById('enable-deep-fusion');
const maxFusionDepthInput = document.getElementById('max-fusion-depth');
const preserveOriginalEntriesInput = document.getElementById('preserve-original-entries');
const fusionStrategySelect = document.getElementById('fusion-strategy');

const sourceAgentSelect = document.getElementById('source-agent');
const targetAgentSelect = document.getElementById('target-agent');
const identifyBtn = document.getElementById('identify-btn');
const fuseBtn = document.getElementById('fuse-btn');
const similarPairsContainer = document.getElementById('similar-pairs');
const fusionResultsContainer = document.getElementById('fusion-results');
const loadingIndicator = document.getElementById('loading-indicator');

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Charger la configuration
    loadFusionConfig();
    
    // Charger les agents disponibles
    loadAgents();
    
    // Ajouter les écouteurs d'événements
    addEventListeners();
});

/**
 * Charge la configuration de fusion
 */
async function loadFusionConfig() {
    try {
        showLoading('Chargement de la configuration...');
        
        const response = await fetch('/api/fusion/config');
        const data = await response.json();
        
        if (data.success) {
            fusionConfig = data.config;
            updateConfigForm();
        } else {
            showNotification(`Erreur lors du chargement de la configuration: ${data.error}`, 'error');
        }
        
        hideLoading();
    } catch (error) {
        console.error('Erreur lors du chargement de la configuration:', error);
        showNotification('Erreur lors du chargement de la configuration', 'error');
        hideLoading();
    }
}

/**
 * Met à jour le formulaire de configuration
 */
function updateConfigForm() {
    if (!fusionConfig) {
        return;
    }
    
    similarityThresholdInput.value = fusionConfig.similarityThreshold;
    importanceWeightInput.value = fusionConfig.importanceWeight;
    recencyWeightInput.value = fusionConfig.recencyWeight;
    categoryWeightInput.value = fusionConfig.categoryWeight;
    metadataWeightInput.value = fusionConfig.metadataWeight;
    enableDeepFusionInput.checked = fusionConfig.enableDeepFusion;
    maxFusionDepthInput.value = fusionConfig.maxFusionDepth;
    preserveOriginalEntriesInput.checked = fusionConfig.preserveOriginalEntries;
    
    // Mettre à jour la liste déroulante de stratégie
    for (let i = 0; i < fusionStrategySelect.options.length; i++) {
        if (fusionStrategySelect.options[i].value === fusionConfig.fusionStrategy) {
            fusionStrategySelect.selectedIndex = i;
            break;
        }
    }
    
    // Mettre à jour l'état des champs dépendants
    updateDependentFields();
}

/**
 * Met à jour l'état des champs dépendants
 */
function updateDependentFields() {
    // Activer/désactiver le champ de profondeur de fusion
    maxFusionDepthInput.disabled = !enableDeepFusionInput.checked;
}

/**
 * Charge les agents disponibles
 */
async function loadAgents() {
    try {
        showLoading('Chargement des agents...');
        
        const response = await fetch('/api/agents');
        const data = await response.json();
        
        if (data.success) {
            // Vider les listes déroulantes
            sourceAgentSelect.innerHTML = '';
            targetAgentSelect.innerHTML = '';
            
            // Ajouter les agents aux listes déroulantes
            data.agents.forEach(agent => {
                const sourceOption = document.createElement('option');
                sourceOption.value = agent.id;
                sourceOption.textContent = agent.name;
                sourceAgentSelect.appendChild(sourceOption);
                
                const targetOption = document.createElement('option');
                targetOption.value = agent.id;
                targetOption.textContent = agent.name;
                targetAgentSelect.appendChild(targetOption);
            });
            
            // Sélectionner les agents par défaut
            for (let i = 0; i < sourceAgentSelect.options.length; i++) {
                if (sourceAgentSelect.options[i].value === sourceAgent) {
                    sourceAgentSelect.selectedIndex = i;
                    break;
                }
            }
            
            for (let i = 0; i < targetAgentSelect.options.length; i++) {
                if (targetAgentSelect.options[i].value === targetAgent) {
                    targetAgentSelect.selectedIndex = i;
                    break;
                }
            }
        } else {
            showNotification(`Erreur lors du chargement des agents: ${data.error}`, 'error');
        }
        
        hideLoading();
    } catch (error) {
        console.error('Erreur lors du chargement des agents:', error);
        showNotification('Erreur lors du chargement des agents', 'error');
        hideLoading();
    }
}

/**
 * Ajoute les écouteurs d'événements
 */
function addEventListeners() {
    // Écouteur pour le formulaire de configuration
    configForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        
        // Récupérer les valeurs du formulaire
        const config = {
            similarityThreshold: parseFloat(similarityThresholdInput.value),
            importanceWeight: parseFloat(importanceWeightInput.value),
            recencyWeight: parseFloat(recencyWeightInput.value),
            categoryWeight: parseFloat(categoryWeightInput.value),
            metadataWeight: parseFloat(metadataWeightInput.value),
            enableDeepFusion: enableDeepFusionInput.checked,
            maxFusionDepth: parseInt(maxFusionDepthInput.value),
            preserveOriginalEntries: preserveOriginalEntriesInput.checked,
            fusionStrategy: fusionStrategySelect.value
        };
        
        // Sauvegarder la configuration
        await saveFusionConfig(config);
    });
    
    // Écouteur pour le champ de fusion profonde
    enableDeepFusionInput.addEventListener('change', updateDependentFields);
    
    // Écouteur pour le bouton d'identification
    identifyBtn.addEventListener('click', async () => {
        // Récupérer les agents sélectionnés
        sourceAgent = sourceAgentSelect.value;
        targetAgent = targetAgentSelect.value;
        
        // Identifier les entrées similaires
        await identifySimilarEntries();
    });
    
    // Écouteur pour le bouton de fusion
    fuseBtn.addEventListener('click', async () => {
        // Récupérer les agents sélectionnés
        sourceAgent = sourceAgentSelect.value;
        targetAgent = targetAgentSelect.value;
        
        // Fusionner les entrées similaires
        await fuseAgentMemories();
    });
}

/**
 * Sauvegarde la configuration de fusion
 * @param {Object} config - Configuration de fusion
 */
async function saveFusionConfig(config) {
    try {
        showLoading('Sauvegarde de la configuration...');
        
        const response = await fetch('/api/fusion/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ config })
        });
        
        const data = await response.json();
        
        if (data.success) {
            fusionConfig = config;
            showNotification('Configuration sauvegardée avec succès', 'success');
        } else {
            showNotification(`Erreur lors de la sauvegarde de la configuration: ${data.error}`, 'error');
        }
        
        hideLoading();
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de la configuration:', error);
        showNotification('Erreur lors de la sauvegarde de la configuration', 'error');
        hideLoading();
    }
}

/**
 * Identifie les entrées similaires entre deux agents
 */
async function identifySimilarEntries() {
    try {
        showLoading('Identification des entrées similaires...');
        
        // Vérifier que les agents sont différents
        if (sourceAgent === targetAgent) {
            showNotification('Les agents source et cible doivent être différents', 'error');
            hideLoading();
            return;
        }
        
        const response = await fetch('/api/fusion/identify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sourceAgentId: sourceAgent,
                targetAgentId: targetAgent,
                options: fusionConfig
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            similarPairs = data.similarPairs;
            updateSimilarPairsUI();
            
            // Activer le bouton de fusion si des paires ont été trouvées
            fuseBtn.disabled = similarPairs.length === 0;
        } else {
            showNotification(`Erreur lors de l'identification des entrées similaires: ${data.error}`, 'error');
        }
        
        hideLoading();
    } catch (error) {
        console.error('Erreur lors de l\'identification des entrées similaires:', error);
        showNotification('Erreur lors de l\'identification des entrées similaires', 'error');
        hideLoading();
    }
}

/**
 * Met à jour l'interface utilisateur des paires similaires
 */
function updateSimilarPairsUI() {
    // Vider le conteneur
    similarPairsContainer.innerHTML = '';
    
    // Si aucune paire n'a été trouvée, afficher un message
    if (similarPairs.length === 0) {
        similarPairsContainer.innerHTML = `
            <div class="no-pairs">
                <i class="fas fa-info-circle"></i>
                <p>Aucune entrée similaire trouvée entre les agents.</p>
            </div>
        `;
        return;
    }
    
    // Créer un tableau pour afficher les paires
    const table = document.createElement('table');
    table.className = 'similar-pairs-table';
    
    // Créer l'en-tête du tableau
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Similarité</th>
            <th>Clé source</th>
            <th>Clé cible</th>
            <th>Importance</th>
            <th>Catégorie</th>
        </tr>
    `;
    table.appendChild(thead);
    
    // Créer le corps du tableau
    const tbody = document.createElement('tbody');
    
    // Ajouter les paires au tableau
    similarPairs.forEach(pair => {
        const tr = document.createElement('tr');
        
        // Formater la similarité
        const similarityClass = pair.similarity >= 0.9 ? 'high' : pair.similarity >= 0.8 ? 'medium' : 'low';
        
        tr.innerHTML = `
            <td class="similarity ${similarityClass}">${(pair.similarity * 100).toFixed(1)}%</td>
            <td>${pair.sourceEntry.key}</td>
            <td>${pair.targetEntry.key}</td>
            <td>${Math.max(pair.sourceEntry.importance, pair.targetEntry.importance).toFixed(2)}</td>
            <td>${pair.sourceEntry.category || 'Non catégorisé'}</td>
        `;
        
        tbody.appendChild(tr);
    });
    
    table.appendChild(tbody);
    similarPairsContainer.appendChild(table);
    
    // Ajouter un résumé
    const summary = document.createElement('div');
    summary.className = 'pairs-summary';
    summary.innerHTML = `
        <p>${similarPairs.length} entrées similaires trouvées entre les agents.</p>
    `;
    similarPairsContainer.appendChild(summary);
}

/**
 * Fusionne les entrées similaires entre deux agents
 */
async function fuseAgentMemories() {
    try {
        showLoading('Fusion des entrées similaires...');
        
        // Vérifier que les agents sont différents
        if (sourceAgent === targetAgent) {
            showNotification('Les agents source et cible doivent être différents', 'error');
            hideLoading();
            return;
        }
        
        const response = await fetch('/api/fusion/fuse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sourceAgentId: sourceAgent,
                targetAgentId: targetAgent,
                options: fusionConfig
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            fusionResults = data.results;
            updateFusionResultsUI();
            
            // Réinitialiser les paires similaires
            similarPairs = [];
            updateSimilarPairsUI();
            
            // Désactiver le bouton de fusion
            fuseBtn.disabled = true;
            
            showNotification('Fusion des entrées similaires terminée avec succès', 'success');
        } else {
            showNotification(`Erreur lors de la fusion des entrées similaires: ${data.error}`, 'error');
        }
        
        hideLoading();
    } catch (error) {
        console.error('Erreur lors de la fusion des entrées similaires:', error);
        showNotification('Erreur lors de la fusion des entrées similaires', 'error');
        hideLoading();
    }
}

/**
 * Met à jour l'interface utilisateur des résultats de fusion
 */
function updateFusionResultsUI() {
    // Vider le conteneur
    fusionResultsContainer.innerHTML = '';
    
    // Si aucun résultat n'est disponible, ne rien afficher
    if (!fusionResults) {
        return;
    }
    
    // Créer un résumé des résultats
    const summary = document.createElement('div');
    summary.className = 'fusion-summary';
    summary.innerHTML = `
        <h3>Résultats de la fusion</h3>
        <div class="fusion-stats">
            <div class="fusion-stat">
                <span class="stat-label">Paires identifiées:</span>
                <span class="stat-value">${fusionResults.totalPairs}</span>
            </div>
            <div class="fusion-stat">
                <span class="stat-label">Entrées fusionnées:</span>
                <span class="stat-value">${fusionResults.fusedEntries}</span>
            </div>
            <div class="fusion-stat">
                <span class="stat-label">Entrées préservées:</span>
                <span class="stat-value">${fusionResults.preservedEntries}</span>
            </div>
            <div class="fusion-stat">
                <span class="stat-label">Nouvelles entrées:</span>
                <span class="stat-value">${fusionResults.newEntries}</span>
            </div>
            <div class="fusion-stat">
                <span class="stat-label">Erreurs:</span>
                <span class="stat-value">${fusionResults.errors}</span>
            </div>
        </div>
    `;
    
    fusionResultsContainer.appendChild(summary);
    
    // Si des détails sont disponibles, les afficher
    if (fusionResults.details && fusionResults.details.length > 0) {
        const details = document.createElement('div');
        details.className = 'fusion-details';
        details.innerHTML = `
            <h4>Détails de la fusion</h4>
            <p>Nombre d'opérations: ${fusionResults.details.length}</p>
        `;
        
        // Ajouter un bouton pour afficher/masquer les détails
        const toggleButton = document.createElement('button');
        toggleButton.className = 'toggle-details-btn';
        toggleButton.innerHTML = 'Afficher les détails';
        toggleButton.addEventListener('click', () => {
            const detailsList = document.getElementById('fusion-details-list');
            if (detailsList.style.display === 'none') {
                detailsList.style.display = 'block';
                toggleButton.innerHTML = 'Masquer les détails';
            } else {
                detailsList.style.display = 'none';
                toggleButton.innerHTML = 'Afficher les détails';
            }
        });
        
        details.appendChild(toggleButton);
        
        // Créer la liste des détails
        const detailsList = document.createElement('div');
        detailsList.id = 'fusion-details-list';
        detailsList.style.display = 'none';
        
        // Limiter le nombre de détails affichés
        const maxDetails = 50;
        const displayedDetails = fusionResults.details.slice(0, maxDetails);
        
        // Ajouter les détails à la liste
        displayedDetails.forEach(detail => {
            const detailItem = document.createElement('div');
            detailItem.className = `fusion-detail-item ${detail.type}`;
            
            detailItem.innerHTML = `
                <div class="detail-header">
                    <span class="detail-type">${detail.type === 'new' ? 'Nouvelle entrée' : detail.type === 'fused' ? 'Entrée fusionnée' : 'Entrée préservée'}</span>
                    <span class="detail-similarity">${(detail.similarity * 100).toFixed(1)}% de similarité</span>
                </div>
                <div class="detail-content">
                    <div class="detail-ids">
                        <span>Source: ${detail.sourceId}</span>
                        <span>Cible: ${detail.targetId}</span>
                        ${detail.fusedId ? `<span>Fusion: ${detail.fusedId}</span>` : ''}
                    </div>
                </div>
            `;
            
            detailsList.appendChild(detailItem);
        });
        
        // Ajouter un message si tous les détails ne sont pas affichés
        if (fusionResults.details.length > maxDetails) {
            const moreDetails = document.createElement('div');
            moreDetails.className = 'more-details';
            moreDetails.innerHTML = `
                <p>... et ${fusionResults.details.length - maxDetails} autres opérations</p>
            `;
            detailsList.appendChild(moreDetails);
        }
        
        details.appendChild(detailsList);
        fusionResultsContainer.appendChild(details);
    }
}

/**
 * Affiche l'indicateur de chargement
 * @param {string} message - Message à afficher
 */
function showLoading(message) {
    loadingIndicator.querySelector('.loading-message').textContent = message;
    loadingIndicator.style.display = 'flex';
}

/**
 * Masque l'indicateur de chargement
 */
function hideLoading() {
    loadingIndicator.style.display = 'none';
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    if (window.notifications) {
        window.notifications[type](message, '', { duration: 3000 });
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}
