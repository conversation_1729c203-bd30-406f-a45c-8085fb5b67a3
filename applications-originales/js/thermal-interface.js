/**
 * Interface utilisateur pour la mémoire thermique
 * Ce script gère l'interface utilisateur de la mémoire thermique
 */

// Variables globales
let memoryChart = null;
let recentEntries = [];
let zoneMapping = {
    'instant': { id: 'instant', displayName: 'Mémoire Instantanée', temp: 'hot' },
    'shortTerm': { id: 'short-term', displayName: 'Mémoire à Court Terme', temp: 'warm' },
    'workingMemory': { id: 'working-memory', displayName: 'Mémoire de Travail', temp: 'medium' },
    'mediumTerm': { id: 'medium-term', displayName: 'Mémoire à Moyen Terme', temp: 'medium' },
    'longTerm': { id: 'long-term', displayName: 'Mémoire à Long Terme', temp: 'cool' },
    'dreamMemory': { id: 'dream-memory', displayName: 'Mémoire des Rêves', temp: 'cool' }
};

/**
 * Initialise l'interface utilisateur
 */
function initializeUI() {
    // Mettre à jour les statistiques
    updateStats();
    
    // Mettre à jour les zones de mémoire
    updateMemoryZones();
    
    // Mettre à jour les accélérateurs
    updateAccelerators();
    
    // Charger les entrées récentes
    loadRecentEntries();
    
    // Initialiser le graphique
    initMemoryChart();
    
    // Mettre à jour l'état
    updateStatus();
}

/**
 * Met à jour les statistiques de la mémoire
 */
function updateStats() {
    const stats = window.thermalMemory.stats;
    
    // Mettre à jour les valeurs
    document.getElementById('total-entries').textContent = stats.totalEntries;
    document.getElementById('avg-temperature').textContent = stats.averageTemperature.toFixed(2);
    document.getElementById('cycles-performed').textContent = stats.cyclesPerformed;
    document.getElementById('last-cycle-time').textContent = stats.lastCycleTime ? new Date(stats.lastCycleTime).toLocaleString() : 'Jamais';
    
    // Mettre à jour le badge de température
    const tempStatus = document.getElementById('temp-status');
    if (stats.averageTemperature > 0.7) {
        tempStatus.textContent = 'Élevée';
        tempStatus.style.backgroundColor = 'rgba(255, 107, 107, 0.2)';
        tempStatus.style.color = 'var(--temp-hot)';
    } else if (stats.averageTemperature > 0.4) {
        tempStatus.textContent = 'Normale';
        tempStatus.style.backgroundColor = 'rgba(29, 209, 161, 0.2)';
        tempStatus.style.color = 'var(--temp-medium)';
    } else {
        tempStatus.textContent = 'Basse';
        tempStatus.style.backgroundColor = 'rgba(84, 160, 255, 0.2)';
        tempStatus.style.color = 'var(--temp-cool)';
    }
}

/**
 * Met à jour les zones de mémoire
 */
function updateMemoryZones() {
    const zones = window.thermalMemory.zones;
    
    // Mettre à jour chaque zone
    for (const zone in zones) {
        if (zoneMapping[zone]) {
            const mapping = zoneMapping[zone];
            const zoneData = zones[zone];
            
            // Mettre à jour la température
            const tempElement = document.getElementById(`${mapping.id}-temp`);
            if (tempElement) {
                tempElement.textContent = zoneData.temperature.toFixed(2);
            }
            
            // Mettre à jour la barre de progression
            const fillElement = document.getElementById(`${mapping.id}-fill`);
            if (fillElement) {
                const percentage = (zoneData.items.length / zoneData.capacity) * 100;
                fillElement.style.width = `${percentage}%`;
            }
        }
    }
}

/**
 * Met à jour les accélérateurs
 */
function updateAccelerators() {
    const stats = window.kyberAccelerators.stats;
    
    // Mettre à jour les valeurs
    document.getElementById('reflexive-boost').textContent = `x${stats.reflexiveBoost || '3.1'}`;
    document.getElementById('thermal-boost').textContent = `x${stats.thermalBoost || '2.7'}`;
    document.getElementById('connector-boost').textContent = `x${stats.connectorBoost || '2.1'}`;
    document.getElementById('efficiency').textContent = stats.efficiency || '92%';
    
    // Mettre à jour le badge d'état
    const kyberStatus = document.getElementById('kyber-status');
    if (kyberStatus) {
        const accelerators = window.kyberAccelerators.accelerators;
        const allEnabled = Object.values(accelerators).every(acc => acc.enabled);
        
        kyberStatus.textContent = allEnabled ? 'Actifs' : 'Partiellement actifs';
    }
}

/**
 * Charge les entrées récentes
 */
async function loadRecentEntries() {
    const container = document.getElementById('recent-entries-container');
    if (!container) return;
    
    try {
        // Récupérer les entrées récentes
        recentEntries = await window.thermalMemory.getRecentMemories('', 5);
        
        // Vider le conteneur
        container.innerHTML = '';
        
        // Afficher les entrées
        if (recentEntries.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucune entrée récente</p>';
        } else {
            recentEntries.forEach(entry => {
                const entryElement = document.createElement('div');
                entryElement.className = 'recent-entry';
                entryElement.innerHTML = `
                    <div class="entry-header">
                        <div class="entry-title">${entry.key}</div>
                        <div class="entry-temp">${entry.temperature.toFixed(2)}</div>
                    </div>
                    <div class="entry-content">${formatEntryContent(entry.data)}</div>
                    <div class="entry-footer">
                        <div class="entry-category">${entry.category}</div>
                        <div class="entry-time">${new Date(entry.created).toLocaleString()}</div>
                    </div>
                `;
                container.appendChild(entryElement);
            });
        }
    } catch (error) {
        console.error('Erreur lors du chargement des entrées récentes:', error);
        container.innerHTML = '<p class="text-muted">Erreur lors du chargement des entrées</p>';
    }
}

/**
 * Formate le contenu d'une entrée pour l'affichage
 * @param {Object} data - Données de l'entrée
 * @returns {string} - Contenu formaté
 */
function formatEntryContent(data) {
    if (!data) return 'Pas de contenu';
    
    if (typeof data === 'string') {
        return data.substring(0, 100) + (data.length > 100 ? '...' : '');
    }
    
    if (data.content) {
        return data.content.substring(0, 100) + (data.content.length > 100 ? '...' : '');
    }
    
    return JSON.stringify(data).substring(0, 100) + '...';
}

/**
 * Initialise le graphique de mémoire
 */
function initMemoryChart() {
    const ctx = document.getElementById('memory-chart');
    if (!ctx) return;
    
    // Récupérer les données
    const zones = window.thermalMemory.zones;
    const labels = [];
    const data = [];
    const backgroundColors = [];
    
    for (const zone in zones) {
        if (zoneMapping[zone]) {
            const mapping = zoneMapping[zone];
            const zoneData = zones[zone];
            
            labels.push(mapping.displayName);
            data.push(zoneData.items.length);
            
            // Définir la couleur en fonction de la température
            switch (mapping.temp) {
                case 'hot':
                    backgroundColors.push('rgba(255, 107, 107, 0.7)');
                    break;
                case 'warm':
                    backgroundColors.push('rgba(255, 159, 67, 0.7)');
                    break;
                case 'medium':
                    backgroundColors.push('rgba(29, 209, 161, 0.7)');
                    break;
                case 'cool':
                    backgroundColors.push('rgba(84, 160, 255, 0.7)');
                    break;
                default:
                    backgroundColors.push('rgba(255, 255, 255, 0.7)');
            }
        }
    }
    
    // Créer le graphique
    memoryChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Nombre d\'entrées',
                data: data,
                backgroundColor: backgroundColors,
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

/**
 * Met à jour l'état de la mémoire
 */
function updateStatus() {
    const indicator = document.getElementById('memory-status-indicator');
    const text = document.getElementById('memory-status-text');
    
    if (!indicator || !text) return;
    
    if (window.thermalMemory.initialized) {
        indicator.style.backgroundColor = 'var(--success)';
        text.textContent = 'Connecté';
    } else {
        indicator.style.backgroundColor = 'var(--warning)';
        text.textContent = 'Initialisation...';
    }
}

/**
 * Effectue un cycle de mémoire
 */
async function cycleMem() {
    try {
        // Appeler l'API pour effectuer un cycle de mémoire
        const response = await fetch('/api/thermal/memory/cycle', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Mettre à jour l'interface
            await window.thermalMemory.loadMemoryStats();
            await window.thermalMemory.loadMemoryZones();
            
            // Mettre à jour le graphique
            updateMemoryChart();
            
            // Afficher un message de succès
            showNotification('Cycle de mémoire effectué avec succès', 'success');
        } else {
            showNotification('Erreur lors du cycle de mémoire: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du cycle de mémoire:', error);
        showNotification('Erreur lors du cycle de mémoire', 'error');
    }
}

/**
 * Génère un rêve
 */
async function generateDream() {
    try {
        const dream = await window.thermalMemory.generateDream();
        
        if (dream) {
            // Afficher le rêve
            showDreamModal(dream);
            
            // Mettre à jour l'interface
            await window.thermalMemory.loadMemoryZones();
            updateMemoryZones();
            updateMemoryChart();
        } else {
            showNotification('Erreur lors de la génération du rêve', 'error');
        }
    } catch (error) {
        console.error('Erreur lors de la génération du rêve:', error);
        showNotification('Erreur lors de la génération du rêve', 'error');
    }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-message">${message}</div>
        </div>
        <div class="notification-close">
            <i class="fas fa-times"></i>
        </div>
    `;
    
    // Ajouter au conteneur de notifications
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Ajouter l'événement de fermeture
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.classList.add('closing');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.add('closing');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 5000);
}

// Exporter les fonctions
window.thermalInterface = {
    initializeUI,
    updateStats,
    updateMemoryZones,
    updateAccelerators,
    loadRecentEntries,
    cycleMem,
    generateDream
};
