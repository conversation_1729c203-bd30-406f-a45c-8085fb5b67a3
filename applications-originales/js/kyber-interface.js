/**
 * Interface utilisateur pour les accélérateurs Kyber
 * Ce script gère l'interface utilisateur des accélérateurs Kyber
 */

// Variables globales
let performanceChart = null;
let temperatureChart = null;
let performanceHistory = {
    labels: [],
    reflexive: [],
    thermal: [],
    connector: [],
    average: []
};
let temperatureHistory = {
    labels: [],
    reflexive: [],
    thermal: [],
    connector: []
};

/**
 * Initialise l'interface utilisateur
 */
function initializeUI() {
    // Mettre à jour les statistiques
    updateStats();
    
    // Mettre à jour les contrôles des accélérateurs
    updateAcceleratorControls();
    
    // Initialiser les graphiques
    initPerformanceChart();
    initTemperatureChart();
    
    // Mettre à jour l'état
    updateStatus();
    
    // Configurer les écouteurs d'événements
    setupEventListeners();
}

/**
 * Met à jour les statistiques des accélérateurs
 */
function updateStats() {
    const stats = window.kyberAccelerators.stats;
    
    // Mettre à jour les valeurs
    document.getElementById('avg-boost').textContent = `x${stats.averageBoostFactor.toFixed(1)}`;
    document.getElementById('efficiency').textContent = stats.efficiency;
    document.getElementById('stability-events').textContent = stats.stabilityEvents;
    document.getElementById('last-update-time').textContent = stats.lastUpdateTime ? new Date(stats.lastUpdateTime).toLocaleString() : 'Jamais';
    
    // Mettre à jour le badge d'efficacité
    const efficiencyStatus = document.getElementById('efficiency-status');
    if (efficiencyStatus) {
        const efficiency = parseFloat(stats.efficiency);
        if (efficiency >= 90) {
            efficiencyStatus.textContent = 'Optimale';
            efficiencyStatus.style.backgroundColor = 'rgba(29, 209, 161, 0.2)';
            efficiencyStatus.style.color = 'var(--temp-medium)';
        } else if (efficiency >= 70) {
            efficiencyStatus.textContent = 'Bonne';
            efficiencyStatus.style.backgroundColor = 'rgba(255, 159, 67, 0.2)';
            efficiencyStatus.style.color = 'var(--temp-warm)';
        } else {
            efficiencyStatus.textContent = 'Faible';
            efficiencyStatus.style.backgroundColor = 'rgba(255, 107, 107, 0.2)';
            efficiencyStatus.style.color = 'var(--temp-hot)';
        }
    }
}

/**
 * Met à jour les contrôles des accélérateurs
 */
function updateAcceleratorControls() {
    const accelerators = window.kyberAccelerators.accelerators;
    
    // Mettre à jour l'accélérateur réflexif
    updateAcceleratorControl('reflexive', accelerators.reflexive);
    
    // Mettre à jour l'accélérateur thermique
    updateAcceleratorControl('thermal', accelerators.thermal);
    
    // Mettre à jour le connecteur thermique
    updateAcceleratorControl('connector', accelerators.connector);
}

/**
 * Met à jour les contrôles d'un accélérateur spécifique
 * @param {string} id - ID de l'accélérateur
 * @param {Object} accelerator - Données de l'accélérateur
 */
function updateAcceleratorControl(id, accelerator) {
    // Mettre à jour le toggle
    const toggle = document.getElementById(`${id}-toggle`);
    if (toggle) {
        toggle.checked = accelerator.enabled;
    }
    
    // Mettre à jour le statut
    const status = document.getElementById(`${id}-status`);
    if (status) {
        status.textContent = accelerator.enabled ? 'Actif' : 'Inactif';
    }
    
    // Mettre à jour la valeur du boost
    const boostValue = document.getElementById(`${id}-boost-value`);
    if (boostValue) {
        boostValue.textContent = `x${accelerator.boostFactor.toFixed(1)}`;
    }
    
    // Mettre à jour le slider
    const slider = document.getElementById(`${id}-boost-slider`);
    if (slider) {
        slider.value = accelerator.boostFactor;
    }
    
    // Mettre à jour la stabilité
    const stability = document.getElementById(`${id}-stability`);
    if (stability) {
        stability.textContent = `${Math.round(accelerator.stability * 100)}%`;
    }
    
    // Mettre à jour l'énergie
    const energy = document.getElementById(`${id}-energy`);
    if (energy) {
        energy.textContent = `${Math.round(accelerator.energy * 100)}%`;
    }
}

/**
 * Initialise le graphique de performances
 */
function initPerformanceChart() {
    const ctx = document.getElementById('performance-chart');
    if (!ctx) return;
    
    // Initialiser les données d'historique si vides
    if (performanceHistory.labels.length === 0) {
        // Ajouter des données fictives pour le démarrage
        const now = new Date();
        for (let i = 10; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60000);
            performanceHistory.labels.push(time.toLocaleTimeString());
            performanceHistory.reflexive.push(3.1);
            performanceHistory.thermal.push(2.7);
            performanceHistory.connector.push(2.1);
            performanceHistory.average.push(2.6);
        }
    }
    
    // Créer le graphique
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: performanceHistory.labels,
            datasets: [
                {
                    label: 'Réflexif',
                    data: performanceHistory.reflexive,
                    borderColor: 'rgba(255, 107, 107, 1)',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                },
                {
                    label: 'Thermique',
                    data: performanceHistory.thermal,
                    borderColor: 'rgba(29, 209, 161, 1)',
                    backgroundColor: 'rgba(29, 209, 161, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                },
                {
                    label: 'Connecteur',
                    data: performanceHistory.connector,
                    borderColor: 'rgba(84, 160, 255, 1)',
                    backgroundColor: 'rgba(84, 160, 255, 0.1)',
                    tension: 0.4,
                    borderWidth: 2
                },
                {
                    label: 'Moyenne',
                    data: performanceHistory.average,
                    borderColor: 'rgba(255, 159, 67, 1)',
                    backgroundColor: 'rgba(255, 159, 67, 0.1)',
                    tension: 0.4,
                    borderWidth: 3,
                    borderDash: [5, 5]
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 1,
                    max: 5,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
}

/**
 * Initialise le graphique de température
 */
function initTemperatureChart() {
    const ctx = document.getElementById('temperature-chart');
    if (!ctx) return;
    
    // Initialiser les données d'historique si vides
    if (temperatureHistory.labels.length === 0) {
        // Ajouter des données fictives pour le démarrage
        const now = new Date();
        for (let i = 10; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60000);
            temperatureHistory.labels.push(time.toLocaleTimeString());
            temperatureHistory.reflexive.push(45 + 0.5 * 5);
            temperatureHistory.thermal.push(50 + 0.5 * 5);
            temperatureHistory.connector.push(40 + 0.5 * 5);
        }
    }
    
    // Créer le graphique
    temperatureChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: temperatureHistory.labels,
            datasets: [
                {
                    label: 'Réflexif',
                    data: temperatureHistory.reflexive,
                    borderColor: 'rgba(255, 107, 107, 1)',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    tension: 0.4,
                    borderWidth: 2,
                    fill: true
                },
                {
                    label: 'Thermique',
                    data: temperatureHistory.thermal,
                    borderColor: 'rgba(29, 209, 161, 1)',
                    backgroundColor: 'rgba(29, 209, 161, 0.1)',
                    tension: 0.4,
                    borderWidth: 2,
                    fill: true
                },
                {
                    label: 'Connecteur',
                    data: temperatureHistory.connector,
                    borderColor: 'rgba(84, 160, 255, 1)',
                    backgroundColor: 'rgba(84, 160, 255, 0.1)',
                    tension: 0.4,
                    borderWidth: 2,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 30,
                    max: 80,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        callback: function(value) {
                            return value + '°C';
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: 'rgba(255, 255, 255, 0.7)'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y + '°C';
                        }
                    }
                }
            }
        }
    });
}

/**
 * Met à jour l'état des accélérateurs
 */
function updateStatus() {
    const indicator = document.getElementById('kyber-status-indicator');
    const text = document.getElementById('kyber-status-text');
    
    if (!indicator || !text) return;
    
    if (window.kyberAccelerators.initialized) {
        indicator.style.backgroundColor = 'var(--success)';
        text.textContent = 'Connecté';
    } else {
        indicator.style.backgroundColor = 'var(--warning)';
        text.textContent = 'Initialisation...';
    }
}

/**
 * Configure les écouteurs d'événements
 */
function setupEventListeners() {
    // Écouteurs pour les toggles
    setupToggleListener('reflexive');
    setupToggleListener('thermal');
    setupToggleListener('connector');
    
    // Écouteurs pour les sliders
    setupSliderListener('reflexive');
    setupSliderListener('thermal');
    setupSliderListener('connector');
    
    // Écouteurs pour les boutons
    document.getElementById('reset-accelerators-btn')?.addEventListener('click', resetAccelerators);
    document.getElementById('optimize-btn')?.addEventListener('click', optimizeAccelerators);
    document.getElementById('refresh-btn')?.addEventListener('click', refreshAll);
}

/**
 * Configure l'écouteur d'événements pour un toggle
 * @param {string} id - ID de l'accélérateur
 */
function setupToggleListener(id) {
    const toggle = document.getElementById(`${id}-toggle`);
    if (toggle) {
        toggle.addEventListener('change', async () => {
            const enabled = toggle.checked;
            await window.kyberAccelerators.toggleAccelerator(id, enabled);
            updateAcceleratorControl(id, window.kyberAccelerators.accelerators[id]);
        });
    }
}

/**
 * Configure l'écouteur d'événements pour un slider
 * @param {string} id - ID de l'accélérateur
 */
function setupSliderListener(id) {
    const slider = document.getElementById(`${id}-boost-slider`);
    if (slider) {
        slider.addEventListener('input', () => {
            // Mettre à jour la valeur affichée en temps réel
            const boostValue = document.getElementById(`${id}-boost-value`);
            if (boostValue) {
                boostValue.textContent = `x${parseFloat(slider.value).toFixed(1)}`;
            }
        });
        
        slider.addEventListener('change', async () => {
            const boostFactor = parseFloat(slider.value);
            await window.kyberAccelerators.adjustBoostFactor(id, boostFactor);
            updateAcceleratorControl(id, window.kyberAccelerators.accelerators[id]);
        });
    }
}

/**
 * Réinitialise les accélérateurs
 */
async function resetAccelerators() {
    try {
        const success = await window.kyberAccelerators.resetAccelerators();
        
        if (success) {
            // Mettre à jour l'interface
            updateAcceleratorControls();
            updateStats();
            
            // Afficher un message de succès
            showNotification('Accélérateurs réinitialisés avec succès', 'success');
        } else {
            showNotification('Erreur lors de la réinitialisation des accélérateurs', 'error');
        }
    } catch (error) {
        console.error('Erreur lors de la réinitialisation des accélérateurs:', error);
        showNotification('Erreur lors de la réinitialisation des accélérateurs', 'error');
    }
}

/**
 * Optimise les accélérateurs
 */
async function optimizeAccelerators() {
    try {
        // Appeler l'API pour optimiser les accélérateurs
        const response = await fetch('/api/thermal/accelerators/optimize', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Mettre à jour l'interface
            await window.kyberAccelerators.loadAcceleratorStats();
            updateAcceleratorControls();
            updateStats();
            
            // Afficher un message de succès
            showNotification('Accélérateurs optimisés avec succès', 'success');
        } else {
            showNotification('Erreur lors de l\'optimisation des accélérateurs: ' + data.error, 'error');
        }
    } catch (error) {
        console.error('Erreur lors de l\'optimisation des accélérateurs:', error);
        showNotification('Erreur lors de l\'optimisation des accélérateurs', 'error');
    }
}

/**
 * Actualise toutes les données
 */
async function refreshAll() {
    try {
        // Recharger les données
        await window.kyberAccelerators.loadAcceleratorStats();
        
        // Mettre à jour l'interface
        updateAcceleratorControls();
        updateStats();
        
        // Mettre à jour les graphiques
        updatePerformanceChart();
        updateTemperatureChart();
        
        // Afficher un message de succès
        showNotification('Données actualisées avec succès', 'success');
    } catch (error) {
        console.error('Erreur lors de l\'actualisation des données:', error);
        showNotification('Erreur lors de l\'actualisation des données', 'error');
    }
}

/**
 * Met à jour le graphique de performances
 */
function updatePerformanceChart() {
    if (!performanceChart) return;
    
    // Ajouter les nouvelles données
    const now = new Date();
    performanceHistory.labels.push(now.toLocaleTimeString());
    performanceHistory.reflexive.push(window.kyberAccelerators.accelerators.reflexive.boostFactor);
    performanceHistory.thermal.push(window.kyberAccelerators.accelerators.thermal.boostFactor);
    performanceHistory.connector.push(window.kyberAccelerators.accelerators.connector.boostFactor);
    performanceHistory.average.push(window.kyberAccelerators.stats.averageBoostFactor);
    
    // Limiter la taille de l'historique
    if (performanceHistory.labels.length > 10) {
        performanceHistory.labels.shift();
        performanceHistory.reflexive.shift();
        performanceHistory.thermal.shift();
        performanceHistory.connector.shift();
        performanceHistory.average.shift();
    }
    
    // Mettre à jour le graphique
    performanceChart.data.labels = performanceHistory.labels;
    performanceChart.data.datasets[0].data = performanceHistory.reflexive;
    performanceChart.data.datasets[1].data = performanceHistory.thermal;
    performanceChart.data.datasets[2].data = performanceHistory.connector;
    performanceChart.data.datasets[3].data = performanceHistory.average;
    performanceChart.update();
}

/**
 * Met à jour le graphique de température
 */
function updateTemperatureChart() {
    if (!temperatureChart) return;
    
    // Ajouter les nouvelles données
    const now = new Date();
    temperatureHistory.labels.push(now.toLocaleTimeString());
    
    // Simuler des températures basées sur les facteurs de boost
    const reflexiveTemp = 40 + (window.kyberAccelerators.accelerators.reflexive.boostFactor * 5);
    const thermalTemp = 45 + (window.kyberAccelerators.accelerators.thermal.boostFactor * 5);
    const connectorTemp = 35 + (window.kyberAccelerators.accelerators.connector.boostFactor * 5);
    
    temperatureHistory.reflexive.push(reflexiveTemp);
    temperatureHistory.thermal.push(thermalTemp);
    temperatureHistory.connector.push(connectorTemp);
    
    // Limiter la taille de l'historique
    if (temperatureHistory.labels.length > 10) {
        temperatureHistory.labels.shift();
        temperatureHistory.reflexive.shift();
        temperatureHistory.thermal.shift();
        temperatureHistory.connector.shift();
    }
    
    // Mettre à jour le graphique
    temperatureChart.data.labels = temperatureHistory.labels;
    temperatureChart.data.datasets[0].data = temperatureHistory.reflexive;
    temperatureChart.data.datasets[1].data = temperatureHistory.thermal;
    temperatureChart.data.datasets[2].data = temperatureHistory.connector;
    temperatureChart.update();
}

// Exporter les fonctions
window.kyberInterface = {
    initializeUI,
    updateStats,
    updateAcceleratorControls,
    resetAccelerators,
    optimizeAccelerators,
    refreshAll
};
