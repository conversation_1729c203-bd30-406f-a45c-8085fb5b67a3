/**
 * Script pour la page de résultats de formation
 */

// Variables globales
let trainingResults = null;
let memoryEntries = [];
let mainAgentPerformance = null;
let trainingAgentPerformance = null;

// Éléments DOM
const agentName = document.getElementById('agent-name');
const agentModel = document.getElementById('agent-model');
const datasetName = document.getElementById('dataset-name');
const datasetSamples = document.getElementById('dataset-samples');
const memoryEntriesContainer = document.getElementById('memory-entries');
const refreshBtn = document.getElementById('refresh-btn');

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si l'API Electron est disponible
    if (window.electron) {
        console.log('Application Electron détectée');
    } else {
        console.log('Application web standard détectée');
    }

    // Récupérer l'ID de la formation depuis l'URL
    const urlParams = new URLSearchParams(window.location.search);
    const trainingId = urlParams.get('id');

    // Initialiser l'interface
    initializeInterface(trainingId);

    // Ajouter les écouteurs d'événements
    refreshBtn.addEventListener('click', () => {
        initializeInterface(trainingId);
    });
});

/**
 * Initialise l'interface utilisateur
 * @param {string} trainingId - ID de la formation
 */
async function initializeInterface(trainingId) {
    try {
        // Charger les résultats de la formation
        if (trainingId) {
            await loadTrainingResults(trainingId);
        } else {
            await loadLatestTrainingResults();
        }

        // Charger les entrées de mémoire créées lors de la formation
        if (trainingResults) {
            await loadMemoryEntries();
        }

        // Charger les performances des agents
        await loadAgentPerformance();

        // Mettre à jour l'interface
        updateInterface();

        // Initialiser la visualisation
        if (window.trainingVisualization) {
            window.trainingVisualization.initializeVisualization(trainingResults);

            // Créer les graphiques de performance si les données sont disponibles
            if (mainAgentPerformance) {
                window.trainingVisualization.createPerformanceRadarChart(mainAgentPerformance);
            }

            // Créer le graphique de comparaison si les données sont disponibles
            if (mainAgentPerformance && trainingAgentPerformance) {
                window.trainingVisualization.createComparisonChart(mainAgentPerformance, trainingAgentPerformance);
            }
        }
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'interface:', error);
        showNotification('Erreur lors du chargement des résultats de formation', 'error');
    }
}

/**
 * Charge les résultats de la formation spécifiée
 * @param {string} trainingId - ID de la formation
 */
async function loadTrainingResults(trainingId) {
    try {
        const response = await fetch(`/api/training/results/${trainingId}`);
        const data = await response.json();

        if (data.success) {
            trainingResults = data.results;
        } else {
            showNotification(`Erreur lors du chargement des résultats de formation: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des résultats de formation:', error);
        showNotification('Erreur lors du chargement des résultats de formation', 'error');
    }
}

/**
 * Charge les résultats de la dernière formation
 */
async function loadLatestTrainingResults() {
    try {
        const response = await fetch('/api/training/results/latest');
        const data = await response.json();

        if (data.success) {
            trainingResults = data.results;
        } else {
            showNotification(`Erreur lors du chargement des résultats de formation: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des résultats de formation:', error);
        showNotification('Erreur lors du chargement des résultats de formation', 'error');
    }
}

/**
 * Charge les entrées de mémoire créées lors de la formation
 */
async function loadMemoryEntries() {
    try {
        if (!trainingResults || !trainingResults.memoryEntryIds || trainingResults.memoryEntryIds.length === 0) {
            return;
        }

        const response = await fetch('/api/memory/entries', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ids: trainingResults.memoryEntryIds
            })
        });

        const data = await response.json();

        if (data.success) {
            memoryEntries = data.entries;
        } else {
            showNotification(`Erreur lors du chargement des entrées de mémoire: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Erreur lors du chargement des entrées de mémoire:', error);
        showNotification('Erreur lors du chargement des entrées de mémoire', 'error');
    }
}

/**
 * Charge les performances des agents
 */
async function loadAgentPerformance() {
    try {
        if (!trainingResults) {
            return;
        }

        // Charger les performances de l'agent principal
        const mainAgentResponse = await fetch(`/api/agents/performance?agentId=${trainingResults.agentId}`);
        const mainAgentData = await mainAgentResponse.json();

        if (mainAgentData.success) {
            mainAgentPerformance = mainAgentData.performance;
        }

        // Charger les performances de l'agent de formation
        if (trainingResults.trainingAgentId) {
            const trainingAgentResponse = await fetch(`/api/agents/performance?agentId=${trainingResults.trainingAgentId}`);
            const trainingAgentData = await trainingAgentResponse.json();

            if (trainingAgentData.success) {
                trainingAgentPerformance = trainingAgentData.performance;
            }
        }
    } catch (error) {
        console.error('Erreur lors du chargement des performances des agents:', error);
    }
}

/**
 * Met à jour l'interface utilisateur
 */
function updateInterface() {
    if (!trainingResults) {
        showNotification('Aucun résultat de formation disponible', 'info');
        return;
    }

    // Mettre à jour les informations sur l'agent
    if (trainingResults.agent) {
        agentName.textContent = trainingResults.agent.name;
        agentModel.textContent = trainingResults.agent.type === 'ollama' ? trainingResults.agent.model : trainingResults.agent.type;
    }

    // Mettre à jour les informations sur l'ensemble de données
    if (trainingResults.dataset) {
        datasetName.textContent = trainingResults.dataset.name;
        datasetSamples.textContent = `${trainingResults.dataset.samples} échantillons`;
    }

    // Afficher les entrées de mémoire
    renderMemoryEntries();
}

/**
 * Affiche les entrées de mémoire dans l'interface
 */
function renderMemoryEntries() {
    // Vider le conteneur des entrées de mémoire
    memoryEntriesContainer.innerHTML = '';

    // Afficher les entrées de mémoire
    memoryEntries.forEach(entry => {
        const memoryEntry = document.createElement('div');
        memoryEntry.className = 'memory-entry';

        // Déterminer la couleur de la bordure en fonction de l'importance
        let borderColor = 'var(--accent)';
        if (entry.importance > 0.8) {
            borderColor = 'var(--temp-hot)';
        } else if (entry.importance > 0.6) {
            borderColor = 'var(--temp-warm)';
        } else if (entry.importance > 0.4) {
            borderColor = 'var(--temp-cool)';
        } else {
            borderColor = 'var(--temp-cold)';
        }

        memoryEntry.style.borderLeftColor = borderColor;

        // Formater la date de création
        const createdAt = new Date(entry.createdAt);
        const formattedDate = createdAt.toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        memoryEntry.innerHTML = `
            <div class="entry-header">
                <div class="entry-title">${entry.key || 'Entrée de formation'}</div>
                <div class="entry-importance">Importance: ${(entry.importance * 100).toFixed(0)}%</div>
            </div>
            <div class="entry-content">${entry.data}</div>
            <div class="entry-footer">
                <div>Catégorie: ${entry.category || 'formation'}</div>
                <div>Créée le ${formattedDate}</div>
            </div>
        `;

        memoryEntriesContainer.appendChild(memoryEntry);
    });

    // Si aucune entrée de mémoire n'est disponible, afficher un message
    if (memoryEntries.length === 0) {
        memoryEntriesContainer.innerHTML = `
            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                <p>Aucune entrée de mémoire n'a été créée lors de cette formation.</p>
            </div>
        `;
    }
}

/**
 * Affiche une notification
 * @param {string} message - Message à afficher
 * @param {string} type - Type de notification (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    if (window.notifications) {
        window.notifications[type](message, '', { duration: 3000 });
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}
