<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Visualisation 3D du Cerveau LOUNA AI</title>
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .brain-3d-container {
            min-height: 100vh;
            background: var(--bg-primary);
            overflow: hidden;
        }

        .visualization-area {
            height: 70vh;
            background: radial-gradient(circle at center, rgba(255, 105, 180, 0.1), rgba(78, 205, 196, 0.1), rgba(0, 0, 0, 0.8));
            border-radius: 15px;
            margin: 20px;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .brain-3d-model {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .brain-sphere {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, 
                rgba(255, 105, 180, 0.8), 
                rgba(78, 205, 196, 0.6), 
                rgba(138, 43, 226, 0.4));
            position: relative;
            animation: brainRotate 20s linear infinite;
            box-shadow: 
                0 0 50px rgba(255, 105, 180, 0.5),
                inset 0 0 50px rgba(78, 205, 196, 0.3);
        }

        @keyframes brainRotate {
            0% { transform: rotateY(0deg) rotateX(10deg); }
            100% { transform: rotateY(360deg) rotateX(10deg); }
        }

        .neuron-network {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .neuron {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            animation: neuronPulse 2s ease-in-out infinite;
            box-shadow: 0 0 10px #00ffff;
        }

        @keyframes neuronPulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.5); }
        }

        .synapse {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #ff69b4, transparent);
            animation: synapseFlow 3s linear infinite;
        }

        @keyframes synapseFlow {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        .brain-zones {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 280px;
            height: 280px;
            border-radius: 50%;
        }

        .zone {
            position: absolute;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .zone:hover {
            transform: scale(1.1);
            border-color: #ff69b4;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .zone1 { top: 10%; left: 40%; width: 60px; height: 60px; background: rgba(255, 0, 0, 0.3); }
        .zone2 { top: 20%; right: 10%; width: 50px; height: 50px; background: rgba(255, 165, 0, 0.3); }
        .zone3 { bottom: 30%; right: 15%; width: 55px; height: 55px; background: rgba(255, 255, 0, 0.3); }
        .zone4 { bottom: 20%; left: 20%; width: 50px; height: 50px; background: rgba(0, 255, 0, 0.3); }
        .zone5 { top: 40%; left: 10%; width: 65px; height: 65px; background: rgba(0, 0, 255, 0.3); }
        .zone6 { center: true; top: 45%; left: 45%; width: 40px; height: 40px; background: rgba(128, 0, 128, 0.3); }

        .controls-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 250px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            color: #fff;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .control-slider {
            width: 100%;
            height: 5px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: var(--primary-pink);
            cursor: pointer;
        }

        .stats-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 300px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            color: #fff;
            font-size: 0.9rem;
        }

        .stat-value {
            color: var(--primary-pink);
            font-weight: bold;
        }

        .temperature-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            padding: 15px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .temp-gauge {
            width: 100px;
            height: 10px;
            background: linear-gradient(90deg, #00ff00, #ffff00, #ff6600, #ff0000);
            border-radius: 5px;
            position: relative;
        }

        .temp-cursor {
            position: absolute;
            top: -2px;
            width: 4px;
            height: 14px;
            background: white;
            border-radius: 2px;
            transition: left 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="brain-3d-container">
        <!-- Header -->
        <header class="louna-header">
            <div class="louna-header-content">
                <div class="louna-header-title">
                    <i class="fas fa-brain louna-header-icon"></i>
                    <h1>Visualisation 3D du Cerveau</h1>
                </div>
                <div class="louna-nav">
                    <a href="../interface-originale-complete.html" class="louna-nav-btn" onclick="goToHome()">
                        <i class="fas fa-home"></i>
                        <span>Accueil</span>
                    </a>
                    <a href="brain-monitoring-complete.html" class="louna-nav-btn">
                        <i class="fas fa-chart-line"></i>
                        <span>Monitoring</span>
                    </a>
                    <a href="futuristic-interface.html" class="louna-nav-btn">
                        <i class="fas fa-fire"></i>
                        <span>Mémoire</span>
                    </a>
                </div>
                <div class="louna-status online">
                    <div class="louna-status-dot"></div>
                    <span>Visualisation Active</span>
                </div>
            </div>
        </header>

        <!-- Zone de visualisation 3D -->
        <div class="visualization-area">
            <!-- Indicateur de température -->
            <div class="temperature-indicator">
                <i class="fas fa-thermometer-half" style="color: #ff6600;"></i>
                <div>
                    <div style="color: #fff; font-size: 0.8rem;">Température</div>
                    <div style="color: #ff6600; font-weight: bold;" id="tempValue">37.2°C</div>
                </div>
                <div class="temp-gauge">
                    <div class="temp-cursor" id="tempCursor"></div>
                </div>
            </div>

            <!-- Panneau de contrôles -->
            <div class="controls-panel">
                <h3 style="color: #fff; margin-bottom: 15px;">🎛️ Contrôles</h3>
                
                <div class="control-group">
                    <label class="control-label">Vitesse de rotation</label>
                    <input type="range" class="control-slider" min="0" max="100" value="50" id="rotationSpeed">
                </div>
                
                <div class="control-group">
                    <label class="control-label">Activité neuronale</label>
                    <input type="range" class="control-slider" min="0" max="100" value="75" id="neuronActivity">
                </div>
                
                <div class="control-group">
                    <label class="control-label">Flux synaptique</label>
                    <input type="range" class="control-slider" min="0" max="100" value="60" id="synapseFlow">
                </div>
                
                <div class="control-group">
                    <label class="control-label">Zoom</label>
                    <input type="range" class="control-slider" min="50" max="150" value="100" id="zoomLevel">
                </div>
                
                <button class="louna-btn primary" onclick="resetView()" style="width: 100%; margin-top: 10px;">
                    <i class="fas fa-undo"></i> Réinitialiser
                </button>
            </div>

            <!-- Modèle 3D du cerveau -->
            <div class="brain-3d-model" id="brain3DModel">
                <div class="brain-sphere" id="brainSphere">
                    <!-- Zones du cerveau -->
                    <div class="brain-zones">
                        <div class="zone zone1" onclick="selectZone(1)" title="Zone 1: Mémoire Immédiate">Z1</div>
                        <div class="zone zone2" onclick="selectZone(2)" title="Zone 2: Mémoire Court Terme">Z2</div>
                        <div class="zone zone3" onclick="selectZone(3)" title="Zone 3: Mémoire de Travail">Z3</div>
                        <div class="zone zone4" onclick="selectZone(4)" title="Zone 4: Mémoire Intermédiaire">Z4</div>
                        <div class="zone zone5" onclick="selectZone(5)" title="Zone 5: Mémoire Long Terme">Z5</div>
                        <div class="zone zone6" onclick="selectZone(6)" title="Zone 6: Tri/Classification">Z6</div>
                    </div>
                </div>
                
                <!-- Réseau de neurones -->
                <div class="neuron-network" id="neuronNetwork">
                    <!-- Les neurones seront générés dynamiquement -->
                </div>
            </div>

            <!-- Statistiques -->
            <div class="stats-overlay">
                <h3 style="color: #fff; margin-bottom: 15px;">📊 Statistiques Temps Réel</h3>
                
                <div class="stat-row">
                    <span>Neurones Actifs:</span>
                    <span class="stat-value" id="activeNeurons">86,000,000,000</span>
                </div>
                
                <div class="stat-row">
                    <span>Synapses:</span>
                    <span class="stat-value" id="synapseCount">602,000,000,000,000</span>
                </div>
                
                <div class="stat-row">
                    <span>Activité Globale:</span>
                    <span class="stat-value" id="globalActivity">85%</span>
                </div>
                
                <div class="stat-row">
                    <span>Zone Active:</span>
                    <span class="stat-value" id="activeZone">Zone 5</span>
                </div>
                
                <div class="stat-row">
                    <span>QI Calculé:</span>
                    <span class="stat-value" id="calculatedIQ">180-300</span>
                </div>
                
                <div class="stat-row">
                    <span>Formations:</span>
                    <span class="stat-value" id="trainingCount">14 actives</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/louna-navigation.js"></script>
    <script src="js/louna-notifications.js"></script>
    <script src="js/thermal-data-api.js"></script>
    <script src="js/interface-fixes.js"></script>
    <script>
        let animationSpeed = 1;
        let neuronActivity = 0.75;
        let synapseFlow = 0.6;
        let currentZoom = 1;
        let selectedZone = null;

        // Fonction pour retourner à l'accueil
        function goToHome() {
            try {
                window.location.href = '../interface-originale-complete.html';
            } catch (error) {
                console.error('❌ Erreur navigation accueil:', error);
                window.history.back();
            }
        }

        // Générer le réseau de neurones
        function generateNeuronNetwork() {
            const network = document.getElementById('neuronNetwork');
            network.innerHTML = '';
            
            // Générer des neurones aléatoires
            for (let i = 0; i < 50; i++) {
                const neuron = document.createElement('div');
                neuron.className = 'neuron';
                neuron.style.left = 0 + '%';
                neuron.style.top = 0 + '%';
                neuron.style.animationDelay = 0 + 's';
                neuron.style.animationDuration = (1 + Math.random()) / neuronActivity + 's';
                network.appendChild(neuron);
            }
            
            // Générer des synapses
            for (let i = 0; i < 20; i++) {
                const synapse = document.createElement('div');
                synapse.className = 'synapse';
                synapse.style.left = 0 + '%';
                synapse.style.top = 0 + '%';
                synapse.style.width = (20 + 0) + 'px';
                synapse.style.transform = `rotate(${0}deg)`;
                synapse.style.animationDelay = 0 + 's';
                synapse.style.animationDuration = 3 / synapseFlow + 's';
                network.appendChild(synapse);
            }
        }

        // Sélectionner une zone
        function selectZone(zoneNumber) {
            selectedZone = zoneNumber;
            
            // Mettre en surbrillance la zone sélectionnée
            document.querySelectorAll('.zone').forEach(zone => {
                zone.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                zone.style.boxShadow = 'none';
            });
            
            const selectedZoneElement = document.querySelector(`.zone${zoneNumber}`);
            selectedZoneElement.style.borderColor = '#ff69b4';
            selectedZoneElement.style.boxShadow = '0 0 20px rgba(255, 105, 180, 0.8)';
            
            // Mettre à jour les statistiques
            document.getElementById('activeZone').textContent = `Zone ${zoneNumber}`;
            
            // Afficher les informations de la zone
            const zoneNames = {
                1: 'Mémoire Immédiate',
                2: 'Mémoire Court Terme', 
                3: 'Mémoire de Travail',
                4: 'Mémoire Intermédiaire',
                5: 'Mémoire Long Terme',
                6: 'Tri/Classification'
            };
            
            showInfo(`🧠 Zone ${zoneNumber} sélectionnée: ${zoneNames[zoneNumber]}`);
        }

        // Réinitialiser la vue
        function resetView() {
            document.getElementById('rotationSpeed').value = 50;
            document.getElementById('neuronActivity').value = 75;
            document.getElementById('synapseFlow').value = 60;
            document.getElementById('zoomLevel').value = 100;
            
            updateControls();
            showSuccess('🔄 Vue réinitialisée');
        }

        // Mettre à jour les contrôles
        function updateControls() {
            const rotationSpeed = document.getElementById('rotationSpeed').value / 50;
            const neuronActivityValue = document.getElementById('neuronActivity').value / 100;
            const synapseFlowValue = document.getElementById('synapseFlow').value / 100;
            const zoomValue = document.getElementById('zoomLevel').value / 100;
            
            // Appliquer la vitesse de rotation
            const brainSphere = document.getElementById('brainSphere');
            brainSphere.style.animationDuration = (20 / rotationSpeed) + 's';
            
            // Appliquer le zoom
            const brain3DModel = document.getElementById('brain3DModel');
            brain3DModel.style.transform = `scale(${zoomValue})`;
            
            // Mettre à jour l'activité neuronale et synaptique
            neuronActivity = neuronActivityValue;
            synapseFlow = synapseFlowValue;
            
            // Régénérer le réseau
            generateNeuronNetwork();
        }

        // Mettre à jour la température
        function updateTemperature() {
            const temp = 37.2 + (Math.random() - 0.5) * 0.4;
            document.getElementById('tempValue').textContent = temp.toFixed(1) + '°C';
            
            // Mettre à jour le curseur de température
            const tempCursor = document.getElementById('tempCursor');
            const position = ((temp - 35) / 10) * 100; // 35-45°C range
            tempCursor.style.left = Math.max(0, Math.min(96, position)) + '%';
        }

        // Mettre à jour les statistiques avec données réelles
        function updateStats() {
            try {
                // Utiliser les vraies données si disponibles
                if (window.thermalDataAPI && typeof window.thermalDataAPI.getRealThermalData === 'function') {
                    window.thermalDataAPI.getRealThermalData().then(data => {
                        if (data && data.neurones) {
                            document.getElementById('activeNeurons').textContent = data.neurones.total.toLocaleString();
                            document.getElementById('synapseCount').textContent = data.synapses.total.toLocaleString();
                            document.getElementById('globalActivity').textContent = data.activiteGlobale + '%';
                            document.getElementById('calculatedIQ').textContent = data.qi || '180-300';
                            document.getElementById('trainingCount').textContent = data.formations.actives + ' actives';
                        }
                    }).catch(() => {
                        // Fallback vers données par défaut
                        updateStatsDefault();
                    });
                } else {
                    updateStatsDefault();
                }
            } catch (error) {
                console.warn('⚠️ Erreur mise à jour stats:', error);
                updateStatsDefault();
            }
        }

        // Mise à jour par défaut
        function updateStatsDefault() {
            const baseNeurons = 86000000000;
            const variation = Math.floor(0);
            document.getElementById('activeNeurons').textContent = (baseNeurons + variation).toLocaleString();

            const activity = Math.floor(80 + 0);
            document.getElementById('globalActivity').textContent = activity + '%';

            document.getElementById('synapseCount').textContent = '602,000,000,000,000';
            document.getElementById('calculatedIQ').textContent = '180-300';
            document.getElementById('trainingCount').textContent = '14 actives';
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Générer le réseau initial
            generateNeuronNetwork();
            
            // Sélectionner la zone 5 par défaut
            selectZone(5);
            
            // Configurer les contrôles
            document.getElementById('rotationSpeed').addEventListener('input', updateControls);
            document.getElementById('neuronActivity').addEventListener('input', updateControls);
            document.getElementById('synapseFlow').addEventListener('input', updateControls);
            document.getElementById('zoomLevel').addEventListener('input', updateControls);
            
            // Démarrer les mises à jour
            setInterval(updateTemperature, 2000);
            setInterval(updateStats, 3000);
            setInterval(generateNeuronNetwork, 5000);
            
            showSuccess('🧠 Visualisation 3D du cerveau initialisée !');
        });
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
