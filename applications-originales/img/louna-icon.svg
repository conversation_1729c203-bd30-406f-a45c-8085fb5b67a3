<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- F<PERSON> du cercle -->
  <circle cx="256" cy="256" r="240" fill="#1a1a1a"/>
  
  <!-- Gradient de fond -->
  <circle cx="256" cy="256" r="220" fill="url(#gradient)"/>
  
  <!-- Ce<PERSON><PERSON> stylisé -->
  <path d="M256 120C200 120 160 160 160 220C160 280 200 320 256 320C312 320 352 280 352 220C352 160 312 120 256 120Z" fill="#f0f0f0" opacity="0.2"/>
  
  <!-- Connexions neuronales -->
  <path d="M180 200C180 200 220 240 256 240C292 240 332 200 332 200" stroke="#f0f0f0" stroke-width="4" opacity="0.6"/>
  <path d="M180 220C180 220 220 260 256 260C292 260 332 220 332 220" stroke="#f0f0f0" stroke-width="4" opacity="0.6"/>
  <path d="M180 240C180 240 220 280 256 280C292 280 332 240 332 240" stroke="#f0f0f0" stroke-width="4" opacity="0.6"/>
  
  <!-- Points de connexion -->
  <circle cx="180" cy="200" r="6" fill="#ff69b4"/>
  <circle cx="180" cy="220" r="6" fill="#ff69b4"/>
  <circle cx="180" cy="240" r="6" fill="#ff69b4"/>
  <circle cx="332" cy="200" r="6" fill="#ff69b4"/>
  <circle cx="332" cy="220" r="6" fill="#ff69b4"/>
  <circle cx="332" cy="240" r="6" fill="#ff69b4"/>
  
  <!-- Points centraux -->
  <circle cx="256" cy="240" r="8" fill="#ff69b4"/>
  <circle cx="256" cy="260" r="8" fill="#ff69b4"/>
  <circle cx="256" cy="280" r="8" fill="#ff69b4"/>
  
  <!-- Texte "LOUNA" -->
  <path d="M176 360H196V420H176V360Z" fill="#f0f0f0"/>
  <path d="M206 360H226V420H246V360H266V420H286V360H306V440H206V360Z" fill="#f0f0f0"/>
  <path d="M316 360H336V400C336 410 326 420 316 420C306 420 296 410 296 400V360H316V400H336V400C336 420 326 440 316 440C306 440 296 420 296 400V360H316Z" fill="#f0f0f0"/>
  <path d="M346 360H386V380H366V390H386V410H366V420H386V440H346V360Z" fill="#f0f0f0"/>
  
  <!-- Définition du gradient -->
  <defs>
    <linearGradient id="gradient" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ff69b4"/>
      <stop offset="1" stop-color="#9932cc"/>
    </linearGradient>
  </defs>
</svg>
