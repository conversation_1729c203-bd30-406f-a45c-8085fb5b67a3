<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Interface Mémoire Thermique - LOUNA AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden;
        }

        .thermal-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 120px);
        }

        .thermal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .thermal-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .thermal-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #ff6b35;
        }

        .panel-icon {
            font-size: 1.6rem;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .temperature-display {
            text-align: center;
            margin: 20px 0;
        }

        .temp-value {
            font-size: 3rem;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 10px;
        }

        .temp-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
        }

        .zone-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .zone-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #ff6b35;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .zone-info {
            flex: 1;
        }

        .zone-name {
            font-weight: 600;
            color: #ff6b35;
            margin-bottom: 5px;
        }

        .zone-details {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .zone-temp {
            font-size: 1.2rem;
            font-weight: bold;
            color: #f7931e;
        }

        .memory-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .thermal-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .thermal-btn.primary {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }

        .thermal-btn.secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .thermal-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .activity-feed {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 3px solid #ff6b35;
        }

        .activity-time {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: 5px;
        }

        .activity-text {
            color: rgba(255, 255, 255, 0.9);
        }

        @media (max-width: 768px) {
            .thermal-grid {
                grid-template-columns: 1fr;
            }
            
            .memory-stats {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-fire louna-header-icon"></i>
                <h1>Interface Mémoire Thermique</h1>
            </div>
            <div class="louna-nav">
                <a href="../interface-originale-complete.html" class="louna-nav-btn" onclick="goToHome()">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="thermal-memory-dashboard.html" class="louna-nav-btn">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="control-dashboard.html" class="louna-nav-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Contrôle</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Mémoire Active</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="thermal-container">
        <div class="thermal-grid">
            <!-- Température Globale -->
            <div class="thermal-panel">
                <div class="panel-title">
                    <i class="fas fa-thermometer-half panel-icon"></i>
                    Température Globale
                </div>
                
                <div class="temperature-display">
                    <div class="temp-value" id="globalTemp">37.2°C</div>
                    <div class="temp-label">Température CPU</div>
                </div>
                
                <div class="control-buttons">
                    <button class="thermal-btn primary" onclick="refreshTemperature()">
                        <i class="fas fa-sync"></i>
                        Actualiser
                    </button>
                    <button class="thermal-btn secondary" onclick="calibrateSystem()">
                        <i class="fas fa-cogs"></i>
                        Calibrer
                    </button>
                </div>
            </div>

            <!-- Zones Thermiques -->
            <div class="thermal-panel">
                <div class="panel-title">
                    <i class="fas fa-layer-group panel-icon"></i>
                    Zones Thermiques
                </div>
                
                <div class="zone-list" id="zoneList">
                    <!-- Les zones seront générées dynamiquement -->
                </div>
            </div>

            <!-- Statistiques Mémoire -->
            <div class="thermal-panel">
                <div class="panel-title">
                    <i class="fas fa-database panel-icon"></i>
                    Statistiques Mémoire
                </div>
                
                <div class="memory-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="totalMemories">0</div>
                        <div class="stat-label">Mémoires Totales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="activeMemories">0</div>
                        <div class="stat-label">Mémoires Actives</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="hotMemories">0</div>
                        <div class="stat-label">Mémoires Chaudes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficacité</div>
                    </div>
                </div>
                
                <div class="control-buttons">
                    <button class="thermal-btn primary" onclick="optimizeMemory()">
                        <i class="fas fa-magic"></i>
                        Optimiser
                    </button>
                    <button class="thermal-btn secondary" onclick="compressMemory()">
                        <i class="fas fa-compress"></i>
                        Compresser
                    </button>
                </div>
            </div>

            <!-- Activité Récente -->
            <div class="thermal-panel">
                <div class="panel-title">
                    <i class="fas fa-history panel-icon"></i>
                    Activité Récente
                </div>
                
                <div class="activity-feed" id="activityFeed">
                    <!-- L'activité sera générée dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/louna-navigation.js"></script>
    <script src="js/louna-notifications.js"></script>
    <script src="js/interface-fixes.js"></script>
    <script src="js/thermal-data-api.js"></script>
    
    <script>
        // Fonction pour retourner à l'accueil
        function goToHome() {
            try {
                window.location.href = '../interface-originale-complete.html';
            } catch (error) {
                console.error('❌ Erreur navigation accueil:', error);
                window.history.back();
            }
        }

        // Données des zones thermiques
        const thermalZones = [
            { id: 'zone1', name: 'Mémoire Immédiate', temp: 70, capacity: 1000000 },
            { id: 'zone2', name: 'Mémoire Court Terme', temp: 60, capacity: 5000000 },
            { id: 'zone3', name: 'Mémoire Travail', temp: 50, capacity: 10000000 },
            { id: 'zone4', name: 'Mémoire Intermédiaire', temp: 40, capacity: 20000000 },
            { id: 'zone5', name: 'Mémoire Long Terme', temp: 30, capacity: 50000000 },
            { id: 'zone6', name: 'Tri/Classification', temp: 20, capacity: 1000000 }
        ];

        // Charger les données thermiques
        async function loadThermalData() {
            try {
                let realData = null;
                
                if (window.thermalDataAPI) {
                    realData = await window.thermalDataAPI.getRealThermalData();
                }
                
                if (realData) {
                    updateInterface(realData);
                } else {
                    updateInterfaceWithFallback();
                }
                
            } catch (error) {
                console.error('❌ Erreur chargement données thermiques:', error);
                updateInterfaceWithFallback();
            }
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterface(data) {
            // Température globale
            document.getElementById('globalTemp').textContent = data.temperature.toFixed(1) + '°C';
            
            // Statistiques mémoire
            document.getElementById('totalMemories').textContent = data.formations?.total || 14;
            document.getElementById('activeMemories').textContent = data.formations?.actives || 3;
            document.getElementById('hotMemories').textContent = Math.floor((data.formations?.total || 14) * 0.3);
            document.getElementById('efficiency').textContent = '94%';
            
            // Zones thermiques
            updateZoneList(data.zones || {});
            
            // Activité récente
            generateActivityFeed();
        }

        // Mettre à jour avec données de fallback
        function updateInterfaceWithFallback() {
            document.getElementById('globalTemp').textContent = '37.2°C';
            document.getElementById('totalMemories').textContent = '14';
            document.getElementById('activeMemories').textContent = '3';
            document.getElementById('hotMemories').textContent = '4';
            document.getElementById('efficiency').textContent = '94%';
            
            updateZoneList({});
            generateActivityFeed();
        }

        // Mettre à jour la liste des zones
        function updateZoneList(realZones) {
            const zoneList = document.getElementById('zoneList');
            zoneList.innerHTML = thermalZones.map(zone => {
                const realZone = realZones[zone.id] || zone;
                return `
                    <div class="zone-item">
                        <div class="zone-info">
                            <div class="zone-name">${realZone.nom || zone.name}</div>
                            <div class="zone-details">${(realZone.neurones || 0).toLocaleString()} neurones</div>
                        </div>
                        <div class="zone-temp">${realZone.temp || zone.temp}°C</div>
                    </div>
                `;
            }).join('');
        }

        // Générer le flux d'activité
        function generateActivityFeed() {
            const activities = [
                'Optimisation automatique terminée',
                'Nouvelle mémoire thermique créée',
                'Compression de zone effectuée',
                'Calibrage température réussi',
                'Synchronisation zones complétée'
            ];
            
            const activityFeed = document.getElementById('activityFeed');
            activityFeed.innerHTML = activities.map((activity, index) => `
                <div class="activity-item">
                    <div class="activity-time">Il y a ${(index + 1) * 5} minutes</div>
                    <div class="activity-text">${activity}</div>
                </div>
            `).join('');
        }

        // Fonctions de contrôle
        function refreshTemperature() {
            if (window.showInfo) {
                window.showInfo('🔄 Actualisation de la température...');
            }
            setTimeout(() => {
                loadThermalData();
                if (window.showSuccess) {
                    window.showSuccess('✅ Température actualisée !');
                }
            }, 1000);
        }

        function calibrateSystem() {
            if (window.showInfo) {
                window.showInfo('🔧 Calibrage du système thermique...');
            }
            setTimeout(() => {
                if (window.showSuccess) {
                    window.showSuccess('✅ Système calibré !');
                }
            }, 2000);
        }

        function optimizeMemory() {
            if (window.showInfo) {
                window.showInfo('🔄 Optimisation de la mémoire...');
            }
            setTimeout(() => {
                if (window.showSuccess) {
                    window.showSuccess('✅ Mémoire optimisée !');
                }
            }, 2000);
        }

        function compressMemory() {
            if (window.showInfo) {
                window.showInfo('📦 Compression de la mémoire...');
            }
            setTimeout(() => {
                if (window.showSuccess) {
                    window.showSuccess('✅ Mémoire compressée !');
                }
            }, 3000);
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadThermalData();
            
            // Mise à jour automatique toutes les 10 secondes
            setInterval(loadThermalData, 10000);
            
            if (window.showSuccess) {
                window.showSuccess('🔥 Interface mémoire thermique initialisée !');
            }
        });
    </script>
</body>
</html>
