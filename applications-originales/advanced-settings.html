<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ Paramètres Avancés - LOUNA AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden;
        }

        .settings-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 120px);
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .settings-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .settings-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #ff69b4;
        }

        .panel-icon {
            font-size: 1.6rem;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .setting-item {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .setting-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #4ecdc4;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .setting-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 12px;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .setting-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
        }

        .setting-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .setting-toggle {
            position: relative;
            width: 60px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .setting-toggle.active {
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
        }

        .setting-toggle::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .setting-toggle.active::after {
            transform: translateX(30px);
        }

        .setting-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .setting-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            cursor: pointer;
        }

        .setting-value {
            min-width: 60px;
            text-align: center;
            font-weight: 600;
            color: #ff69b4;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
        }

        .action-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .action-btn.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-indicator.online {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }

        .status-indicator.offline {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header Unifié -->
    <div class="louna-header">
        <div class="louna-header-content">
            <div class="louna-header-title">
                <i class="fas fa-cogs louna-header-icon"></i>
                <h1>Paramètres Avancés</h1>
            </div>
            <div class="louna-nav">
                <a href="../interface-originale-complete.html" class="louna-nav-btn" onclick="goToHome()">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="brain-monitoring-complete.html" class="louna-nav-btn">
                    <i class="fas fa-chart-line"></i>
                    <span>Monitoring</span>
                </a>
                <a href="futuristic-interface.html" class="louna-nav-btn">
                    <i class="fas fa-fire"></i>
                    <span>Mémoire</span>
                </a>
            </div>
            <div class="louna-status online">
                <div class="louna-status-dot"></div>
                <span>Configuration Active</span>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="settings-container">
        <div class="settings-grid">
            <!-- Paramètres IA -->
            <div class="settings-panel">
                <div class="panel-title">
                    <i class="fas fa-brain panel-icon"></i>
                    Intelligence Artificielle
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-tachometer-alt"></i>
                        Niveau QI Cible
                    </div>
                    <div class="setting-description">
                        Définir le niveau d'intelligence cible pour l'évolution
                    </div>
                    <div class="setting-control">
                        <input type="range" class="setting-slider" min="100" max="300" value="185" id="qiTarget">
                        <div class="setting-value" id="qiValue">185</div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-rocket"></i>
                        Évolution Automatique
                    </div>
                    <div class="setting-description">
                        Permettre l'évolution automatique du système
                    </div>
                    <div class="setting-control">
                        <div class="setting-toggle active" id="autoEvolution"></div>
                        <span class="status-indicator online">
                            <div class="status-dot"></div>
                            Activé
                        </span>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-clock"></i>
                        Intervalle d'Évolution (minutes)
                    </div>
                    <div class="setting-description">
                        Fréquence des cycles d'évolution automatique
                    </div>
                    <div class="setting-control">
                        <input type="number" class="setting-input" value="15" min="5" max="120" id="evolutionInterval">
                    </div>
                </div>
            </div>

            <!-- Paramètres Mémoire -->
            <div class="settings-panel">
                <div class="panel-title">
                    <i class="fas fa-memory panel-icon"></i>
                    Mémoire Thermique
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-thermometer-half"></i>
                        Température Optimale (°C)
                    </div>
                    <div class="setting-description">
                        Température cible pour le fonctionnement optimal
                    </div>
                    <div class="setting-control">
                        <input type="range" class="setting-slider" min="35" max="45" value="37.2" step="0.1" id="tempTarget">
                        <div class="setting-value" id="tempValue">37.2°C</div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-compress-arrows-alt"></i>
                        Compression Mémoire
                    </div>
                    <div class="setting-description">
                        Activer la compression automatique des données
                    </div>
                    <div class="setting-control">
                        <div class="setting-toggle active" id="memoryCompression"></div>
                        <span class="status-indicator online">
                            <div class="status-dot"></div>
                            Activé
                        </span>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-save"></i>
                        Sauvegarde Auto (heures)
                    </div>
                    <div class="setting-description">
                        Intervalle de sauvegarde automatique
                    </div>
                    <div class="setting-control">
                        <input type="number" class="setting-input" value="2" min="1" max="24" id="backupInterval">
                    </div>
                </div>
            </div>

            <!-- Paramètres Sécurité -->
            <div class="settings-panel">
                <div class="panel-title">
                    <i class="fas fa-shield-alt panel-icon"></i>
                    Sécurité & Surveillance
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-eye"></i>
                        Surveillance Active
                    </div>
                    <div class="setting-description">
                        Monitoring continu des activités système
                    </div>
                    <div class="setting-control">
                        <div class="setting-toggle active" id="surveillance"></div>
                        <span class="status-indicator online">
                            <div class="status-dot"></div>
                            Activé
                        </span>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-lock"></i>
                        Code de Sécurité
                    </div>
                    <div class="setting-description">
                        Code d'accès pour les fonctions critiques
                    </div>
                    <div class="setting-control">
                        <input type="password" class="setting-input" value="2338" id="securityCode">
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-history"></i>
                        Logs Détaillés
                    </div>
                    <div class="setting-description">
                        Enregistrement détaillé des activités
                    </div>
                    <div class="setting-control">
                        <div class="setting-toggle active" id="detailedLogs"></div>
                        <span class="status-indicator online">
                            <div class="status-dot"></div>
                            Activé
                        </span>
                    </div>
                </div>
            </div>

            <!-- Paramètres Performance -->
            <div class="settings-panel">
                <div class="panel-title">
                    <i class="fas fa-bolt panel-icon"></i>
                    Performance & Accélérateurs
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-microchip"></i>
                        Accélérateurs Kyber
                    </div>
                    <div class="setting-description">
                        Nombre d'accélérateurs quantiques actifs
                    </div>
                    <div class="setting-control">
                        <input type="range" class="setting-slider" min="1" max="100" value="12" id="kyberCount">
                        <div class="setting-value" id="kyberValue">12</div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-sync-alt"></i>
                        Fréquence Mise à Jour (ms)
                    </div>
                    <div class="setting-description">
                        Intervalle de rafraîchissement des données
                    </div>
                    <div class="setting-control">
                        <input type="number" class="setting-input" value="1000" min="100" max="10000" step="100" id="updateFreq">
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <i class="fas fa-chart-line"></i>
                        Mode Performance
                    </div>
                    <div class="setting-description">
                        Optimisation maximale des performances
                    </div>
                    <div class="setting-control">
                        <div class="setting-toggle active" id="performanceMode"></div>
                        <span class="status-indicator online">
                            <div class="status-dot"></div>
                            Activé
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons d'action -->
        <div class="action-buttons">
            <button class="action-btn primary" onclick="saveSettings()">
                <i class="fas fa-save"></i>
                Sauvegarder
            </button>
            <button class="action-btn secondary" onclick="resetSettings()">
                <i class="fas fa-undo"></i>
                Réinitialiser
            </button>
            <button class="action-btn danger" onclick="exportSettings()">
                <i class="fas fa-download"></i>
                Exporter
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/thermal-data-api.js"></script>
    <script src="js/louna-navigation.js"></script>
    <script src="js/louna-notifications.js"></script>
    <script src="js/interface-fixes.js"></script>
    
    <script>
        // Fonction pour retourner à l'accueil
        function goToHome() {
            try {
                window.location.href = '../interface-originale-complete.html';
            } catch (error) {
                console.error('❌ Erreur navigation accueil:', error);
                window.history.back();
            }
        }

        // Gestion des sliders
        document.getElementById('qiTarget').addEventListener('input', function() {
            document.getElementById('qiValue').textContent = this.value;
        });

        document.getElementById('tempTarget').addEventListener('input', function() {
            document.getElementById('tempValue').textContent = this.value + '°C';
        });

        document.getElementById('kyberCount').addEventListener('input', function() {
            document.getElementById('kyberValue').textContent = this.value;
        });

        // Gestion des toggles
        document.querySelectorAll('.setting-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
                const status = this.nextElementSibling;
                if (this.classList.contains('active')) {
                    status.className = 'status-indicator online';
                    status.innerHTML = '<div class="status-dot"></div>Activé';
                } else {
                    status.className = 'status-indicator offline';
                    status.innerHTML = '<div class="status-dot"></div>Désactivé';
                }
            });
        });

        // Fonctions de gestion
        function saveSettings() {
            const settings = {
                qiTarget: document.getElementById('qiTarget').value,
                autoEvolution: document.getElementById('autoEvolution').classList.contains('active'),
                evolutionInterval: document.getElementById('evolutionInterval').value,
                tempTarget: document.getElementById('tempTarget').value,
                memoryCompression: document.getElementById('memoryCompression').classList.contains('active'),
                backupInterval: document.getElementById('backupInterval').value,
                surveillance: document.getElementById('surveillance').classList.contains('active'),
                securityCode: document.getElementById('securityCode').value,
                detailedLogs: document.getElementById('detailedLogs').classList.contains('active'),
                kyberCount: document.getElementById('kyberCount').value,
                updateFreq: document.getElementById('updateFreq').value,
                performanceMode: document.getElementById('performanceMode').classList.contains('active')
            };

            localStorage.setItem('lounaSettings', JSON.stringify(settings));
            if (window.showSuccess) {
                window.showSuccess('⚙️ Paramètres sauvegardés avec succès !');
            }
            console.log('✅ Paramètres sauvegardés:', settings);
        }

        function resetSettings() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les paramètres ?')) {
                localStorage.removeItem('lounaSettings');
                location.reload();
                if (window.showInfo) {
                    window.showInfo('🔄 Paramètres réinitialisés');
                }
            }
        }

        function exportSettings() {
            const settings = localStorage.getItem('lounaSettings') || '{}';
            const blob = new Blob([settings], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'louna-settings.json';
            a.click();
            URL.revokeObjectURL(url);
            if (window.showSuccess) {
                window.showSuccess('📁 Paramètres exportés !');
            }
        }

        // Charger les paramètres sauvegardés
        function loadSettings() {
            const saved = localStorage.getItem('lounaSettings');
            if (saved) {
                try {
                    const settings = JSON.parse(saved);
                    
                    // Appliquer les paramètres
                    if (settings.qiTarget) {
                        document.getElementById('qiTarget').value = settings.qiTarget;
                        document.getElementById('qiValue').textContent = settings.qiTarget;
                    }
                    
                    if (settings.tempTarget) {
                        document.getElementById('tempTarget').value = settings.tempTarget;
                        document.getElementById('tempValue').textContent = settings.tempTarget + '°C';
                    }
                    
                    if (settings.kyberCount) {
                        document.getElementById('kyberCount').value = settings.kyberCount;
                        document.getElementById('kyberValue').textContent = settings.kyberCount;
                    }
                    
                    console.log('✅ Paramètres chargés:', settings);
                } catch (error) {
                    console.error('❌ Erreur chargement paramètres:', error);
                }
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            if (window.showSuccess) {
                window.showSuccess('⚙️ Paramètres avancés initialisés !');
            }
        });
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
