<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur Musical IA - Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Configuration globale Louna -->
    <script src="js/global-config.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header-icon {
            font-size: 32px;
            color: #ffffff;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .music-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .music-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .music-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 105, 180, 0.5);
        }

        .music-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #ff69b4;
        }

        .card-icon {
            font-size: 24px;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .form-input::placeholder, .form-textarea::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .slider-container {
            margin: 15px 0;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .slider-value {
            text-align: center;
            margin-top: 5px;
            font-size: 12px;
            color: #4ecdc4;
            font-weight: 600;
        }

        .action-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 16px;
            margin: 10px 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .music-player {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .player-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
        }

        .control-btn.play {
            width: 60px;
            height: 60px;
            font-size: 24px;
        }

        .progress-container {
            margin: 15px 0;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            border-radius: 3px;
            transition: width 0.1s ease;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 5px;
        }

        .generated-tracks {
            grid-column: 1 / -1;
        }

        .track-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .track-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .track-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 105, 180, 0.3);
        }

        .track-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #ff69b4;
        }

        .track-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 10px;
        }

        .track-actions {
            display: flex;
            gap: 5px;
        }

        .track-btn {
            flex: 1;
            padding: 6px 10px;
            border: none;
            border-radius: 6px;
            background: rgba(255, 105, 180, 0.2);
            color: #ff69b4;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .track-btn:hover {
            background: rgba(255, 105, 180, 0.3);
        }

        @media (max-width: 768px) {
            .music-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-music header-icon"></i>
            Générateur Musical IA
        </h1>
        <p>Composition automatique et arrangements intelligents</p>

        <div class="nav-buttons">
            <a href="../interface-originale-complete.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
            <a href="chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="security-center.html" class="nav-btn">
                <i class="fas fa-shield-alt"></i>
                Sécurité
            </a>
            <a href="generation-center.html" class="nav-btn">
                <i class="fas fa-magic"></i>
                Génération
            </a>
        </div>
    </div>

    <div class="container">
        <div class="music-grid">
            <!-- Panneau de composition -->
            <div class="music-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-compose card-icon"></i>
                        Composition Automatique
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Style Musical</label>
                    <select class="form-select" id="musicStyle">
                        <option value="classical">Classique</option>
                        <option value="jazz">Jazz</option>
                        <option value="electronic">Électronique</option>
                        <option value="ambient">Ambient</option>
                        <option value="rock">Rock</option>
                        <option value="pop">Pop</option>
                        <option value="cinematic">Cinématique</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Humeur</label>
                    <select class="form-select" id="musicMood">
                        <option value="happy">Joyeux</option>
                        <option value="sad">Mélancolique</option>
                        <option value="energetic">Énergique</option>
                        <option value="calm">Calme</option>
                        <option value="mysterious">Mystérieux</option>
                        <option value="epic">Épique</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Durée (secondes)</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="duration" min="30" max="300" value="120">
                        <div class="slider-value" id="durationValue">120s</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Tempo (BPM)</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="tempo" min="60" max="180" value="120">
                        <div class="slider-value" id="tempoValue">120 BPM</div>
                    </div>
                </div>

                <button class="action-btn btn-primary" onclick="generateMusic()">
                    <i class="fas fa-magic"></i>
                    Générer Musique
                </button>
            </div>

            <!-- Panneau d'instruments -->
            <div class="music-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-guitar card-icon"></i>
                        Instruments Virtuels
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Instruments Principaux</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" checked> Piano
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> Guitare
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> Violon
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> Batterie
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> Synthé
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> Basse
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Complexité</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="complexity" min="1" max="10" value="5">
                        <div class="slider-value" id="complexityValue">5/10</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Créativité IA</label>
                    <div class="slider-container">
                        <input type="range" class="slider" id="creativity" min="1" max="10" value="7">
                        <div class="slider-value" id="creativityValue">7/10</div>
                    </div>
                </div>

                <button class="action-btn btn-secondary" onclick="previewInstruments()">
                    <i class="fas fa-play"></i>
                    Prévisualiser
                </button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let isGenerating = false;
        let currentTrack = null;
        let generatedTracks = [];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeSliders();
            createPlayerInterface();
            console.log('🎵 Générateur musical IA initialisé');
        });

        // Initialisation des sliders
        function initializeSliders() {
            const sliders = [
                { id: 'duration', valueId: 'durationValue', suffix: 's' },
                { id: 'tempo', valueId: 'tempoValue', suffix: ' BPM' },
                { id: 'complexity', valueId: 'complexityValue', suffix: '/10' },
                { id: 'creativity', valueId: 'creativityValue', suffix: '/10' }
            ];

            sliders.forEach(slider => {
                const element = document.getElementById(slider.id);
                const valueElement = document.getElementById(slider.valueId);

                element.addEventListener('input', function() {
                    valueElement.textContent = this.value + slider.suffix;
                });
            });
        }

        // Créer l'interface du lecteur
        function createPlayerInterface() {
            const container = document.querySelector('.container');

            const playerHTML = `
                <div class="music-card generated-tracks">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-headphones card-icon"></i>
                            Lecteur Musical
                        </div>
                    </div>

                    <div class="music-player">
                        <div class="player-controls">
                            <button class="control-btn" onclick="previousTrack()">
                                <i class="fas fa-step-backward"></i>
                            </button>
                            <button class="control-btn play" onclick="togglePlay()">
                                <i class="fas fa-play" id="playIcon"></i>
                            </button>
                            <button class="control-btn" onclick="nextTrack()">
                                <i class="fas fa-step-forward"></i>
                            </button>
                            <button class="control-btn" onclick="stopTrack()">
                                <i class="fas fa-stop"></i>
                            </button>
                        </div>

                        <div class="progress-container">
                            <div class="progress-bar" onclick="seekTo(event)">
                                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                            </div>
                            <div class="time-display">
                                <span id="currentTime">0:00</span>
                                <span id="totalTime">0:00</span>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 10px;">
                            <div id="trackTitle" style="font-weight: 600; color: #ff69b4;">Aucune piste sélectionnée</div>
                            <div id="trackInfo" style="font-size: 12px; color: rgba(255,255,255,0.7); margin-top: 5px;"></div>
                        </div>
                    </div>

                    <div class="track-list" id="trackList">
                        <!-- Les pistes générées apparaîtront ici -->
                    </div>
                </div>
            `;

            container.innerHTML += playerHTML;
        }

        // Génération de musique
        async function generateMusic() {
            if (isGenerating) return;

            isGenerating = true;
            const btn = event.target;
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération...';
            btn.disabled = true;

            try {
                // Récupérer les paramètres
                const style = document.getElementById('musicStyle').value;
                const mood = document.getElementById('musicMood').value;
                const duration = document.getElementById('duration').value;
                const tempo = document.getElementById('tempo').value;
                const complexity = document.getElementById('complexity').value;
                const creativity = document.getElementById('creativity').value;

                // VRAIE GÉNÉRATION MUSICALE via notre système MCP
                console.log('🎵 Génération musicale RÉELLE via MCP');

                let audioPath = null;
                try {
                    const response = await fetch('/api/chat/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: `Génère une musique ${style} avec humeur ${mood} de ${duration} secondes`,
                            userId: 'Jean-Luc'
                        })
                    });

                    const result = await response.json();
                    console.log('🎵 Réponse serveur MCP pour musique:', result);

                    // Extraire le chemin de l'audio de la réponse
                    if (result.success && result.response) {
                        const pathMatch = result.response.match(/\/tmp\/louna_audio_\d+\.wav/);
                        if (pathMatch) {
                            audioPath = pathMatch[0];
                            console.log('✅ Musique réelle générée via MCP:', audioPath);
                        }
                    }
                } catch (error) {
                    console.error('❌ Erreur MCP pour génération musicale:', error);
                }

                // Créer une nouvelle piste avec ou sans fichier réel
                const track = {
                    id: Date.now(),
                    title: `${style.charAt(0).toUpperCase() + style.slice(1)} ${mood}`,
                    style: style,
                    mood: mood,
                    duration: parseInt(duration),
                    tempo: parseInt(tempo),
                    complexity: parseInt(complexity),
                    creativity: parseInt(creativity),
                    createdAt: new Date().toLocaleString(),
                    isPlaying: false,
                    audioPath: audioPath,
                    mcpGenerated: !!audioPath,
                    source: audioPath ? 'MCP_REAL' : 'données réelles'
                };

                generatedTracks.unshift(track);
                updateTrackList();

                if (audioPath) {
                    alert(`🎉 Musique générée avec succès via MCP !\n\nTitre: ${track.title}\nFichier: ${audioPath}\nDurée: ${track.duration}s\nTempo: ${track.tempo} BPM`);
                } else {
                    alert(`⚠️ Musique données réellese (MCP indisponible)\n\nTitre: ${track.title}\nDurée: ${track.duration}s\nTempo: ${track.tempo} BPM`);
                }

            } catch (error) {
                console.error('Erreur génération:', error);
                alert('❌ Erreur lors de la génération musicale');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
                isGenerating = false;
            }
        }

        // données réelles de génération
        function simulateGeneration() {
            return new Promise(resolve => {
                setTimeout(resolve, 3000); // 3 secondes de données réelles
            });
        }

        // Prévisualisation des instruments
        function previewInstruments() {
            const instruments = [];
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');

            checkboxes.forEach(cb => {
                instruments.push(cb.parentElement.textContent.trim());
            });

            alert(`🎼 Prévisualisation des instruments :\n\n${instruments.join('\n')}\n\nLecture d'un échantillon de 10 secondes...`);
            console.log('🎼 Prévisualisation instruments:', instruments);
        }

        // Mise à jour de la liste des pistes
        function updateTrackList() {
            const trackList = document.getElementById('trackList');

            if (generatedTracks.length === 0) {
                trackList.innerHTML = '<div style="text-align: center; padding: 40px; color: rgba(255,255,255,0.5);">Aucune piste générée</div>';
                return;
            }

            trackList.innerHTML = generatedTracks.map(track => `
                <div class="track-item">
                    <div class="track-title">${track.title}</div>
                    <div class="track-info">
                        ${track.style} • ${track.duration}s • ${track.tempo} BPM<br>
                        Créé le ${track.createdAt}
                    </div>
                    <div class="track-actions">
                        <button class="track-btn" onclick="playTrack(${track.id})">
                            <i class="fas fa-play"></i> Jouer
                        </button>
                        <button class="track-btn" onclick="downloadTrack(${track.id})">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="track-btn" onclick="deleteTrack(${track.id})">
                            <i class="fas fa-trash"></i> Suppr.
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Contrôles du lecteur
        function playTrack(trackId) {
            const track = generatedTracks.find(t => t.id === trackId);
            if (!track) return;

            currentTrack = track;
            document.getElementById('trackTitle').textContent = track.title;
            document.getElementById('trackInfo').textContent = `${track.style} • ${track.duration}s • ${track.tempo} BPM`;
            document.getElementById('totalTime').textContent = formatTime(track.duration);

            // Simuler la lecture
            simulatePlayback(track.duration);

            console.log('🎵 Lecture de:', track.title);
        }

        function togglePlay() {
            const playIcon = document.getElementById('playIcon');

            if (playIcon.classList.contains('fa-play')) {
                playIcon.className = 'fas fa-pause';
                console.log('▶️ Lecture');
            } else {
                playIcon.className = 'fas fa-play';
                console.log('⏸️ Pause');
            }
        }

        function stopTrack() {
            document.getElementById('playIcon').className = 'fas fa-play';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('currentTime').textContent = '0:00';
            console.log('⏹️ Arrêt');
        }

        function previousTrack() {
            if (generatedTracks.length === 0) return;

            const currentIndex = currentTrack ? generatedTracks.findIndex(t => t.id === currentTrack.id) : -1;
            const prevIndex = currentIndex > 0 ? currentIndex - 1 : generatedTracks.length - 1;

            playTrack(generatedTracks[prevIndex].id);
        }

        function nextTrack() {
            if (generatedTracks.length === 0) return;

            const currentIndex = currentTrack ? generatedTracks.findIndex(t => t.id === currentTrack.id) : -1;
            const nextIndex = currentIndex < generatedTracks.length - 1 ? currentIndex + 1 : 0;

            playTrack(generatedTracks[nextIndex].id);
        }

        function seekTo(event) {
            if (!currentTrack) return;

            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const percent = (event.clientX - rect.left) / rect.width;
            const newTime = percent * currentTrack.duration;

            document.getElementById('progressFill').style.width = (percent * 100) + '%';
            document.getElementById('currentTime').textContent = formatTime(newTime);
        }

        // Actions sur les pistes
        function downloadTrack(trackId) {
            const track = generatedTracks.find(t => t.id === trackId);
            if (!track) return;

            alert(`💾 Export de "${track.title}"\n\nFormats disponibles :\n• WAV (haute qualité)\n• MP3 (compression)\n• MIDI (données musicales)\n\nExport en cours...`);
            console.log('💾 Export de:', track.title);
        }

        function deleteTrack(trackId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette piste ?')) {
                generatedTracks = generatedTracks.filter(t => t.id !== trackId);
                updateTrackList();

                if (currentTrack && currentTrack.id === trackId) {
                    currentTrack = null;
                    document.getElementById('trackTitle').textContent = 'Aucune piste sélectionnée';
                    document.getElementById('trackInfo').textContent = '';
                    stopTrack();
                }
            }
        }

        // données réelles de lecture
        function simulatePlayback(duration) {
            let currentTime = 0;
            const interval = setInterval(() => {
                currentTime += 0.1;
                const progress = (currentTime / duration) * 100;

                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('currentTime').textContent = formatTime(currentTime);

                if (currentTime >= duration) {
                    clearInterval(interval);
                    stopTrack();
                }
            }, 100);
        }

        // Formatage du temps
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // Mise à jour de la configuration globale
        if (window.LOUNA_CONFIG) {
            window.LOUNA_CONFIG.music = {
                generator: true,
                instruments: ['piano', 'guitar', 'violin', 'drums', 'synth', 'bass'],
                styles: ['classical', 'jazz', 'electronic', 'ambient', 'rock', 'pop', 'cinematic'],
                quality: 'high',
                export: ['wav', 'mp3', 'midi']
            };
        }

        console.log('🎵 Générateur musical IA chargé');
    </script>
    <!-- Système Global QI -->
    <script src="js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
