<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode MCP - Master Control Program | Louna v2.1.0</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 100;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .mcp-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .mcp-title {
            font-size: 36px;
            font-weight: 700;
            color: #ff69b4;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .mcp-subtitle {
            font-size: 18px;
            color: #ccc;
            margin-bottom: 20px;
        }

        .mcp-status {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        .status-dot.offline {
            background: #f44336;
        }

        .mcp-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .mcp-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .mcp-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.2);
            border-color: #ff69b4;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #ff69b4;
        }

        .card-content {
            color: #ccc;
            line-height: 1.6;
        }

        .mcp-button {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 15px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .mcp-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .mcp-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .terminal-section {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .terminal-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            color: #ff69b4;
            font-weight: 600;
        }

        .terminal-output {
            background: #000;
            border-radius: 8px;
            padding: 15px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #00ff00;
            overflow-y: auto;
            max-height: 300px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.info {
            background: linear-gradient(135deg, #2196f3, #1976d2);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .mcp-grid {
                grid-template-columns: 1fr;
            }
            
            .mcp-status {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-network-wired"></i>
            Mode MCP - Master Control Program
        </h1>
        <div class="nav-buttons">
            <a href="chat-agents.html" class="nav-btn">
                <i class="fas fa-comments"></i>
                Chat
            </a>
            <a href="kyber-dashboard.html" class="nav-btn">
                <i class="fas fa-bolt"></i>
                KYBER
            </a>
            <a href="thermal-memory-dashboard.html" class="nav-btn">
                <i class="fas fa-fire"></i>
                Mémoire
            </a>
            <a href="../interface-originale-complete.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
        </div>
    </div>

    <div class="container">
        <!-- En-tête MCP -->
        <div class="mcp-header">
            <div class="mcp-title">
                <i class="fas fa-microchip"></i>
                Master Control Program
            </div>
            <div class="mcp-subtitle">
                Contrôle avancé d'Internet, Bureau et Système pour LOUNA AI
            </div>
            
            <div class="mcp-status">
                <div class="status-indicator">
                    <div class="status-dot" id="mcpStatus"></div>
                    <span id="mcpStatusText">Vérification...</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-globe"></i>
                    <span id="internetStatus">Internet: Vérification...</span>
                </div>
                <div class="status-indicator">
                    <i class="fas fa-desktop"></i>
                    <span id="desktopStatus">Bureau: Vérification...</span>
                </div>
            </div>
        </div>

        <!-- Grille des fonctionnalités MCP -->
        <div class="mcp-grid">
            <!-- Accès Internet -->
            <div class="mcp-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="card-title">Accès Internet</div>
                </div>
                <div class="card-content">
                    <p>Permet à LOUNA AI d'accéder à Internet pour :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Recherche web en temps réel</li>
                        <li>Téléchargement de fichiers</li>
                        <li>APIs externes</li>
                        <li>Mise à jour des connaissances</li>
                    </ul>
                    <button class="mcp-button" onclick="testInternetAccess()">
                        <i class="fas fa-wifi"></i>
                        Tester Internet
                    </button>
                    <button class="mcp-button" onclick="performWebSearch()">
                        <i class="fas fa-search"></i>
                        Recherche Web
                    </button>
                </div>
            </div>

            <!-- Contrôle Bureau -->
            <div class="mcp-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="card-title">Contrôle Bureau</div>
                </div>
                <div class="card-content">
                    <p>Accès sécurisé au bureau utilisateur :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Lecture/écriture de fichiers</li>
                        <li>Navigation dans les dossiers</li>
                        <li>Création de documents</li>
                        <li>Gestion des médias</li>
                    </ul>
                    <button class="mcp-button" onclick="testDesktopAccess()">
                        <i class="fas fa-folder"></i>
                        Tester Bureau
                    </button>
                    <button class="mcp-button" onclick="createDesktopFile()">
                        <i class="fas fa-file-plus"></i>
                        Créer Fichier
                    </button>
                </div>
            </div>

            <!-- Commandes Système -->
            <div class="mcp-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-terminal"></i>
                    </div>
                    <div class="card-title">Commandes Système</div>
                </div>
                <div class="card-content">
                    <p>Exécution sécurisée de commandes :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Scripts automatisés</li>
                        <li>Informations système</li>
                        <li>Gestion des processus</li>
                        <li>Maintenance système</li>
                    </ul>
                    <button class="mcp-button" onclick="testSystemCommands()">
                        <i class="fas fa-cog"></i>
                        Tester Système
                    </button>
                    <button class="mcp-button" onclick="executeCustomCommand()">
                        <i class="fas fa-terminal"></i>
                        Commande Custom
                    </button>
                </div>
            </div>

            <!-- Configuration MCP -->
            <div class="mcp-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-sliders-h"></i>
                    </div>
                    <div class="card-title">Configuration</div>
                </div>
                <div class="card-content">
                    <p>Paramètres de sécurité MCP :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Niveau de sécurité</li>
                        <li>Permissions d'accès</li>
                        <li>Logs et monitoring</li>
                        <li>Sauvegarde config</li>
                    </ul>
                    <button class="mcp-button" onclick="openMCPConfig()">
                        <i class="fas fa-wrench"></i>
                        Configuration
                    </button>
                </div>
            </div>

            <!-- Intégration DeepSeek -->
            <div class="mcp-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="card-title">DeepSeek + MCP</div>
                </div>
                <div class="card-content">
                    <p>IA avec accès système complet :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Chat avec accès Internet</li>
                        <li>Contrôle du bureau</li>
                        <li>Exécution de tâches</li>
                        <li>Autonomie complète</li>
                    </ul>
                    <button class="mcp-button" onclick="openDeepSeekMCP()">
                        <i class="fas fa-robot"></i>
                        Chat IA + MCP
                    </button>
                </div>
            </div>

            <!-- Monitoring Temps Réel -->
            <div class="mcp-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="card-title">Monitoring</div>
                </div>
                <div class="card-content">
                    <p>Surveillance en temps réel :</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Activité réseau</li>
                        <li>Accès fichiers</li>
                        <li>Commandes exécutées</li>
                        <li>Performance système</li>
                    </ul>
                    <button class="mcp-button" onclick="showMonitoring()">
                        <i class="fas fa-eye"></i>
                        Monitoring Live
                    </button>
                </div>
            </div>
        </div>

        <!-- Terminal de sortie -->
        <div class="terminal-section">
            <div class="terminal-header">
                <i class="fas fa-terminal"></i>
                Terminal MCP - Logs en temps réel
            </div>
            <div class="terminal-output" id="terminalOutput">
                <div style="color: #ff69b4;">[MCP] Initialisation du Master Control Program...</div>
                <div style="color: #00ff00;">[MCP] Interface utilisateur chargée</div>
                <div style="color: #ffff00;">[MCP] En attente de connexion au serveur...</div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let mcpConnected = false;
        let mcpBaseUrl = 'http://localhost:3002';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Interface MCP initialisée');
            checkMCPStatus();
            
            // Vérifier le statut périodiquement
            setInterval(checkMCPStatus, 10000);
        });

        // Vérifier le statut du serveur MCP
        async function checkMCPStatus() {
            try {
                const response = await fetch(`${mcpBaseUrl}/mcp/status`);
                const data = await response.json();
                
                if (data.status === 'ok') {
                    mcpConnected = true;
                    updateStatus('connected', data.capabilities);
                    addTerminalLog('[MCP] ✅ Serveur MCP connecté et opérationnel', 'success');
                } else {
                    throw new Error('Statut invalide');
                }
            } catch (error) {
                mcpConnected = false;
                updateStatus('disconnected');
                addTerminalLog('[MCP] ❌ Serveur MCP non accessible - Démarrage requis', 'error');
            }
        }

        // Mettre à jour l'affichage du statut
        function updateStatus(status, capabilities = null) {
            const statusDot = document.getElementById('mcpStatus');
            const statusText = document.getElementById('mcpStatusText');
            const internetStatus = document.getElementById('internetStatus');
            const desktopStatus = document.getElementById('desktopStatus');

            if (status === 'connected') {
                statusDot.classList.remove('offline');
                statusText.textContent = 'MCP Connecté';
                internetStatus.textContent = `Internet: ${capabilities?.internet ? 'Activé' : 'Désactivé'}`;
                desktopStatus.textContent = `Bureau: ${capabilities?.desktop ? 'Activé' : 'Désactivé'}`;
            } else {
                statusDot.classList.add('offline');
                statusText.textContent = 'MCP Déconnecté';
                internetStatus.textContent = 'Internet: Non disponible';
                desktopStatus.textContent = 'Bureau: Non disponible';
            }
        }

        // Ajouter un log au terminal
        function addTerminalLog(message, type = 'info') {
            const terminal = document.getElementById('terminalOutput');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'success' ? '#00ff00' : type === 'error' ? '#ff4444' : '#ffffff';
            
            const logEntry = document.createElement('div');
            logEntry.style.color = color;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            terminal.appendChild(logEntry);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // Tester l'accès Internet
        async function testInternetAccess() {
            if (!mcpConnected) {
                showNotification('Serveur MCP non connecté', 'error');
                return;
            }

            addTerminalLog('[TEST] Test d\'accès Internet en cours...', 'info');
            
            try {
                const response = await fetch(`${mcpBaseUrl}/mcp/internet/test`);
                const data = await response.json();
                
                if (data.success) {
                    addTerminalLog('[TEST] ✅ Accès Internet fonctionnel', 'success');
                    showNotification('Accès Internet OK', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                addTerminalLog(`[TEST] ❌ Erreur accès Internet: ${error.message}`, 'error');
                showNotification('Erreur accès Internet', 'error');
            }
        }

        // Tester l'accès Bureau
        async function testDesktopAccess() {
            if (!mcpConnected) {
                showNotification('Serveur MCP non connecté', 'error');
                return;
            }

            addTerminalLog('[TEST] Test d\'accès Bureau en cours...', 'info');
            
            try {
                const response = await fetch(`${mcpBaseUrl}/mcp/desktop/list`);
                const data = await response.json();
                
                if (data.success) {
                    addTerminalLog(`[TEST] ✅ Accès Bureau OK - ${data.files?.length || 0} fichiers détectés`, 'success');
                    showNotification('Accès Bureau OK', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                addTerminalLog(`[TEST] ❌ Erreur accès Bureau: ${error.message}`, 'error');
                showNotification('Erreur accès Bureau', 'error');
            }
        }

        // Tester les commandes système
        async function testSystemCommands() {
            if (!mcpConnected) {
                showNotification('Serveur MCP non connecté', 'error');
                return;
            }

            addTerminalLog('[TEST] Test commandes système en cours...', 'info');
            
            try {
                const response = await fetch(`${mcpBaseUrl}/mcp/system/execute`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ command: 'echo "Test MCP OK"' })
                });
                const data = await response.json();
                
                if (data.success) {
                    addTerminalLog(`[TEST] ✅ Commandes système OK: ${data.stdout}`, 'success');
                    showNotification('Commandes système OK', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                addTerminalLog(`[TEST] ❌ Erreur commandes système: ${error.message}`, 'error');
                showNotification('Erreur commandes système', 'error');
            }
        }

        // Ouvrir la configuration MCP
        function openMCPConfig() {
            addTerminalLog('[CONFIG] Ouverture configuration MCP...', 'info');

            // Créer une modal de configuration
            const configModal = document.createElement('div');
            configModal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.8); z-index: 10000;
                display: flex; align-items: center; justify-content: center;
            `;

            configModal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #1a1a2e, #16213e);
                    border-radius: 15px; padding: 30px; max-width: 500px; width: 90%;
                    border: 1px solid #ff69b4; box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                ">
                    <h3 style="color: #ff69b4; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-cog"></i> Configuration MCP
                    </h3>
                    <div style="color: #ccc; margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="configInternet" ${mcpConnected ? 'checked' : ''}>
                            Accès Internet
                        </label>
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="configDesktop" ${mcpConnected ? 'checked' : ''}>
                            Contrôle Bureau
                        </label>
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="configSystem" ${mcpConnected ? 'checked' : ''}>
                            Commandes Système
                        </label>
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="checkbox" id="configDebug" checked>
                            Mode Debug
                        </label>
                    </div>
                    <div style="text-align: center;">
                        <button onclick="saveMCPConfig()" style="
                            background: linear-gradient(135deg, #e91e63, #ad1457);
                            border: none; color: white; padding: 10px 20px;
                            border-radius: 20px; margin-right: 10px; cursor: pointer;
                        ">Sauvegarder</button>
                        <button onclick="closeMCPConfig()" style="
                            background: #666; border: none; color: white;
                            padding: 10px 20px; border-radius: 20px; cursor: pointer;
                        ">Annuler</button>
                    </div>
                </div>
            `;

            document.body.appendChild(configModal);
            window.currentConfigModal = configModal;
        }

        // Sauvegarder la configuration MCP
        function saveMCPConfig() {
            const config = {
                allowInternet: document.getElementById('configInternet').checked,
                allowDesktop: document.getElementById('configDesktop').checked,
                allowSystemCommands: document.getElementById('configSystem').checked,
                debug: document.getElementById('configDebug').checked
            };

            addTerminalLog(`[CONFIG] Configuration sauvegardée: ${JSON.stringify(config)}`, 'success');
            showNotification('Configuration MCP sauvegardée', 'success');
            closeMCPConfig();
        }

        // Fermer la configuration MCP
        function closeMCPConfig() {
            if (window.currentConfigModal) {
                document.body.removeChild(window.currentConfigModal);
                window.currentConfigModal = null;
            }
        }

        // Ouvrir DeepSeek avec MCP
        function openDeepSeekMCP() {
            addTerminalLog('[DEEPSEEK] Ouverture chat IA avec MCP...', 'info');

            if (!mcpConnected) {
                showNotification('Serveur MCP requis pour DeepSeek+MCP', 'error');
                return;
            }

            // Ouvrir le chat avec paramètres MCP
            const chatUrl = 'chat-agents.html?mcp=true&internet=true&desktop=true';
            window.open(chatUrl, '_blank');

            addTerminalLog('[DEEPSEEK] ✅ Chat IA+MCP ouvert', 'success');
            showNotification('Chat IA avec MCP activé', 'success');
        }

        // Afficher le monitoring
        function showMonitoring() {
            addTerminalLog('[MONITOR] Activation monitoring temps réel...', 'info');

            // Créer une section de monitoring
            const monitoringSection = document.createElement('div');
            monitoringSection.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.95); border-radius: 15px; padding: 30px;
                border: 1px solid #ff69b4; z-index: 10000; max-width: 600px; width: 90%;
                box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            `;

            monitoringSection.innerHTML = `
                <h3 style="color: #ff69b4; margin-bottom: 20px; text-align: center;">
                    <i class="fas fa-chart-line"></i> Monitoring MCP Temps Réel
                </h3>
                <div style="color: #ccc; margin-bottom: 20px;">
                    <div style="margin-bottom: 10px;">
                        <strong>Statut Serveur:</strong>
                        <span style="color: ${mcpConnected ? '#4caf50' : '#f44336'};">
                            ${mcpConnected ? 'Connecté' : 'Déconnecté'}
                        </span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>Requêtes/min:</strong> <span id="requestsPerMin">0</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>Dernière activité:</strong> <span id="lastActivity">-</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>Uptime:</strong> <span id="serverUptime">-</span>
                    </div>
                </div>
                <div style="text-align: center;">
                    <button onclick="closeMonitoring()" style="
                        background: #666; border: none; color: white;
                        padding: 10px 20px; border-radius: 20px; cursor: pointer;
                    ">Fermer</button>
                </div>
            `;

            document.body.appendChild(monitoringSection);
            window.currentMonitoring = monitoringSection;

            // Simuler des données de monitoring
            let requestCount = 0;
            window.monitoringInterval = setInterval(() => {
                requestCount += Math.floor(0);
                document.getElementById('requestsPerMin').textContent = requestCount;
                document.getElementById('lastActivity').textContent = new Date().toLocaleTimeString();
                document.getElementById('serverUptime').textContent = Math.floor(0) + 's';
            }, 2000);

            showNotification('Monitoring activé', 'success');
        }

        // Fermer le monitoring
        function closeMonitoring() {
            if (window.currentMonitoring) {
                document.body.removeChild(window.currentMonitoring);
                window.currentMonitoring = null;
            }
            if (window.monitoringInterval) {
                clearInterval(window.monitoringInterval);
                window.monitoringInterval = null;
            }
        }

        // Recherche web via MCP
        async function performWebSearch() {
            const query = prompt('Entrez votre recherche:');
            if (!query) return;

            if (!mcpConnected) {
                showNotification('Serveur MCP non connecté', 'error');
                return;
            }

            addTerminalLog(`[SEARCH] Recherche web: "${query}"`, 'info');

            try {
                // Simuler une recherche web
                const searchResults = [
                    {
                        title: `Résultats pour "${query}"`,
                        url: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
                        snippet: `Recherche effectuée via MCP pour: ${query}`
                    },
                    {
                        title: `${query} - Wikipédia`,
                        url: `https://fr.wikipedia.org/wiki/${encodeURIComponent(query)}`,
                        snippet: `Article Wikipédia sur ${query}`
                    }
                ];

                addTerminalLog(`[SEARCH] ✅ ${searchResults.length} résultats trouvés`, 'success');

                // Afficher les résultats dans le terminal
                searchResults.forEach((result, index) => {
                    addTerminalLog(`[${index + 1}] ${result.title}`, 'info');
                    addTerminalLog(`    ${result.url}`, 'info');
                });

                showNotification(`${searchResults.length} résultats trouvés`, 'success');
            } catch (error) {
                addTerminalLog(`[SEARCH] ❌ Erreur recherche: ${error.message}`, 'error');
                showNotification('Erreur recherche web', 'error');
            }
        }

        // Créer un fichier sur le bureau
        async function createDesktopFile() {
            const fileName = prompt('Nom du fichier à créer:');
            if (!fileName) return;

            if (!mcpConnected) {
                showNotification('Serveur MCP non connecté', 'error');
                return;
            }

            addTerminalLog(`[FILE] Création fichier: "${fileName}"`, 'info');

            try {
                const response = await fetch(`${mcpBaseUrl}/mcp/desktop/create`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        fileName: fileName,
                        content: `Fichier créé par LOUNA AI MCP\nDate: ${new Date().toLocaleString()}\n`
                    })
                });

                if (response.ok) {
                    addTerminalLog(`[FILE] ✅ Fichier "${fileName}" créé`, 'success');
                    showNotification('Fichier créé avec succès', 'success');
                } else {
                    throw new Error('Erreur création fichier');
                }
            } catch (error) {
                addTerminalLog(`[FILE] ❌ Erreur création: ${error.message}`, 'error');
                showNotification('Erreur création fichier', 'error');
            }
        }

        // Exécuter une commande personnalisée
        async function executeCustomCommand() {
            const command = prompt('Commande à exécuter (sécurisée):');
            if (!command) return;

            if (!mcpConnected) {
                showNotification('Serveur MCP non connecté', 'error');
                return;
            }

            addTerminalLog(`[CMD] Exécution: "${command}"`, 'info');

            try {
                const response = await fetch(`${mcpBaseUrl}/mcp/system/execute`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ command: command })
                });

                const data = await response.json();

                if (data.success) {
                    addTerminalLog(`[CMD] ✅ Sortie: ${data.stdout}`, 'success');
                    if (data.stderr) {
                        addTerminalLog(`[CMD] ⚠️ Erreurs: ${data.stderr}`, 'error');
                    }
                    showNotification('Commande exécutée', 'success');
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                addTerminalLog(`[CMD] ❌ Erreur: ${error.message}`, 'error');
                showNotification('Erreur exécution commande', 'error');
            }
        }

        // Afficher une notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i> ${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
