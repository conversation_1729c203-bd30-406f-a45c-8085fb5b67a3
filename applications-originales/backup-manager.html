<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💾 Gestionnaire de Sauvegardes - Louna AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .backup-controls {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-btn {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .control-btn.danger:hover {
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
        }

        .backup-list {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .list-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ff69b4;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .backup-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #4ecdc4;
            transition: all 0.3s ease;
        }

        .backup-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .backup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .backup-id {
            font-weight: 600;
            color: #4ecdc4;
            font-family: 'Courier New', monospace;
        }

        .backup-size {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .backup-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            font-weight: 600;
        }

        .info-value {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .backup-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .action-btn.restore {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .action-btn.export {
            background: rgba(33, 150, 243, 0.2);
            color: #2196f3;
        }

        .action-btn.delete {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(76, 175, 80, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            color: #4caf50;
            margin-bottom: 20px;
        }

        .pulse {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(255, 255, 255, 0.5);
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        }

        .notification.success {
            background: linear-gradient(135deg, #4caf50, #388e3c);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .backup-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-save"></i>
            Gestionnaire de Sauvegardes
        </h1>
        <p>Gestion et restauration des données de génération</p>

        <div class="nav-buttons">
            <a href="../interface-originale-complete.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
            <a href="generation-center.html" class="nav-btn">
                <i class="fas fa-magic"></i>
                Centre Génération
            </a>
            <a href="generation-monitor.html" class="nav-btn">
                <i class="fas fa-chart-line"></i>
                Moniteur
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Contrôles de sauvegarde -->
        <div class="backup-controls">
            <div class="status-indicator">
                <div class="pulse"></div>
                Sauvegardes automatiques actives
            </div>

            <div class="controls-grid">
                <button class="control-btn" onclick="createManualBackup()">
                    <i class="fas fa-save"></i>
                    Sauvegarde Manuelle
                </button>
                
                <button class="control-btn" onclick="importBackup()">
                    <i class="fas fa-upload"></i>
                    Importer Sauvegarde
                </button>
                
                <button class="control-btn" onclick="exportAllBackups()">
                    <i class="fas fa-download"></i>
                    Exporter Tout
                </button>
                
                <button class="control-btn danger" onclick="clearAllBackups()">
                    <i class="fas fa-trash"></i>
                    Vider Tout
                </button>
            </div>
        </div>

        <!-- Liste des sauvegardes -->
        <div class="backup-list">
            <div class="list-title">
                <i class="fas fa-history"></i>
                Sauvegardes Disponibles
            </div>

            <div id="backupListContainer">
                <div class="empty-state">
                    <i class="fas fa-archive"></i>
                    <h3>Aucune sauvegarde trouvée</h3>
                    <p>Créez votre première sauvegarde pour commencer</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        class BackupManager {
            constructor() {
                this.backupSystem = null;
                this.init();
            }

            init() {
                console.log('💾 Gestionnaire de sauvegardes initialisé');
                
                // Attendre que le système de sauvegarde soit prêt
                this.waitForBackupSystem();
                
                // Charger la liste des sauvegardes
                this.loadBackupList();
                
                // Écouter les événements de sauvegarde
                this.setupEventListeners();
            }

            waitForBackupSystem() {
                const checkSystem = () => {
                    if (window.generationBackupSystem) {
                        this.backupSystem = window.generationBackupSystem;
                        this.loadBackupList();
                    } else {
                        setTimeout(checkSystem, 100);
                    }
                };
                checkSystem();
            }

            setupEventListeners() {
                document.addEventListener('backupRestored', (event) => {
                    this.showNotification('Sauvegarde restaurée avec succès', 'success');
                    this.loadBackupList();
                });
            }

            loadBackupList() {
                if (!this.backupSystem) return;

                const container = document.getElementById('backupListContainer');
                const backups = this.backupSystem.getBackupList();

                if (backups.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-archive"></i>
                            <h3>Aucune sauvegarde trouvée</h3>
                            <p>Créez votre première sauvegarde pour commencer</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = backups.map(backup => this.createBackupItem(backup)).join('');
            }

            createBackupItem(backup) {
                return `
                    <div class="backup-item">
                        <div class="backup-header">
                            <div class="backup-id">${backup.id}</div>
                            <div class="backup-size">${backup.sizeFormatted}</div>
                        </div>
                        
                        <div class="backup-info">
                            <div class="info-item">
                                <div class="info-label">Date</div>
                                <div class="info-value">${backup.date}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Compression</div>
                                <div class="info-value">${backup.compressed ? 'Activée' : 'Désactivée'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Taille</div>
                                <div class="info-value">${backup.sizeFormatted}</div>
                            </div>
                        </div>
                        
                        <div class="backup-actions">
                            <button class="action-btn restore" onclick="restoreBackup('${backup.id}')">
                                <i class="fas fa-undo"></i>
                                Restaurer
                            </button>
                            <button class="action-btn export" onclick="exportBackup('${backup.id}')">
                                <i class="fas fa-download"></i>
                                Exporter
                            </button>
                            <button class="action-btn delete" onclick="deleteBackup('${backup.id}')">
                                <i class="fas fa-trash"></i>
                                Supprimer
                            </button>
                        </div>
                    </div>
                `;
            }

            showNotification(message, type = 'success') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'times-circle'}"></i>
                    ${message}
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.classList.add('show');
                }, 100);
                
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 4000);
            }
        }

        // Fonctions globales
        async function createManualBackup() {
            if (!window.generationBackupSystem) {
                backupManager.showNotification('Système de sauvegarde non disponible', 'error');
                return;
            }

            try {
                await window.generationBackupSystem.performBackup();
                backupManager.showNotification('Sauvegarde manuelle créée', 'success');
                backupManager.loadBackupList();
            } catch (error) {
                backupManager.showNotification('Erreur lors de la sauvegarde', 'error');
            }
        }

        async function restoreBackup(backupId) {
            if (!confirm('Êtes-vous sûr de vouloir restaurer cette sauvegarde ?')) {
                return;
            }

            try {
                await window.generationBackupSystem.restoreBackup(backupId);
                backupManager.showNotification('Sauvegarde restaurée', 'success');
            } catch (error) {
                backupManager.showNotification('Erreur lors de la restauration', 'error');
            }
        }

        function exportBackup(backupId) {
            try {
                window.generationBackupSystem.exportBackup(backupId);
                backupManager.showNotification('Sauvegarde exportée', 'success');
            } catch (error) {
                backupManager.showNotification('Erreur lors de l\'export', 'error');
            }
        }

        function deleteBackup(backupId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette sauvegarde ?')) {
                return;
            }

            try {
                localStorage.removeItem(`louna_backup_${backupId}`);
                backupManager.showNotification('Sauvegarde supprimée', 'success');
                backupManager.loadBackupList();
            } catch (error) {
                backupManager.showNotification('Erreur lors de la suppression', 'error');
            }
        }

        function clearAllBackups() {
            if (!confirm('ATTENTION: Cela supprimera TOUTES les sauvegardes. Continuer ?')) {
                return;
            }

            try {
                const keys = Object.keys(localStorage).filter(key => key.startsWith('louna_backup_'));
                keys.forEach(key => localStorage.removeItem(key));
                localStorage.removeItem('lounaBackupHistory');
                
                backupManager.showNotification('Toutes les sauvegardes supprimées', 'success');
                backupManager.loadBackupList();
            } catch (error) {
                backupManager.showNotification('Erreur lors du nettoyage', 'error');
            }
        }

        function importBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const backupData = JSON.parse(e.target.result);
                            const backupId = `imported_${Date.now()}`;
                            localStorage.setItem(`louna_backup_${backupId}`, e.target.result);
                            
                            backupManager.showNotification('Sauvegarde importée', 'success');
                            backupManager.loadBackupList();
                        } catch (error) {
                            backupManager.showNotification('Fichier de sauvegarde invalide', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportAllBackups() {
            try {
                const allBackups = {};
                const keys = Object.keys(localStorage).filter(key => key.startsWith('louna_backup_'));
                
                keys.forEach(key => {
                    allBackups[key] = localStorage.getItem(key);
                });

                const blob = new Blob([JSON.stringify(allBackups, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `louna_all_backups_${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                backupManager.showNotification('Toutes les sauvegardes exportées', 'success');
            } catch (error) {
                backupManager.showNotification('Erreur lors de l\'export', 'error');
            }
        }

        // Initialisation
        const backupManager = new BackupManager();
    </script>
    
    <!-- Système de sauvegarde automatique -->
    <script src="js/generation-backup-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
