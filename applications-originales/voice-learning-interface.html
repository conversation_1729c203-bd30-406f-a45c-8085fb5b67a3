<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Apprentissage Vocal Louna AI - Voix Féminine Naturelle</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .learning-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .learning-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .control-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        .control-card i {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #feca57;
        }

        .control-card h3 {
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .control-card p {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .progress-section {
            margin: 30px 0;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            height: 30px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            background: linear-gradient(90deg, #ff6b6b, #feca57);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .voice-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .mode-card.active {
            border-color: #feca57;
            background: rgba(254, 202, 87, 0.2);
        }

        .mode-card:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .mode-card h4 {
            margin-bottom: 8px;
            color: #feca57;
        }

        .learning-sources {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .source-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #ff6b6b;
        }

        .source-card h4 {
            color: #ff6b6b;
            margin-bottom: 8px;
        }

        .source-card ul {
            list-style: none;
            padding-left: 0;
        }

        .source-card li {
            padding: 3px 0;
            opacity: 0.9;
        }

        .source-card li:before {
            content: "🎤 ";
            margin-right: 5px;
        }

        .btn {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .status-display h4 {
            color: #feca57;
            margin-bottom: 10px;
        }

        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .home-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .learning-active {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <button class="home-btn" onclick="window.location.href = '../interface-originale-complete.html'">
        <i class="fas fa-home"></i>
    </button>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microphone"></i> Apprentissage Vocal Louna AI</h1>
            <p>🎭 Système d'apprentissage de voix féminine naturelle depuis YouTube</p>
        </div>

        <div class="learning-section">
            <h2><i class="fas fa-graduation-cap"></i> Contrôles d'Apprentissage</h2>
            
            <div class="learning-controls">
                <div class="control-card" onclick="startLearning()">
                    <i class="fas fa-play-circle"></i>
                    <h3>Démarrer l'Apprentissage</h3>
                    <p>Rechercher et analyser des voix féminines sur YouTube</p>
                </div>
                
                <div class="control-card" onclick="testVoice()">
                    <i class="fas fa-volume-up"></i>
                    <h3>Tester la Voix</h3>
                    <p>Écouter la voix actuelle avec le mode sélectionné</p>
                </div>
                
                <div class="control-card" onclick="resetLearning()">
                    <i class="fas fa-redo"></i>
                    <h3>Réinitialiser</h3>
                    <p>Effacer l'apprentissage et recommencer</p>
                </div>
                
                <div class="control-card" onclick="exportProfiles()">
                    <i class="fas fa-download"></i>
                    <h3>Exporter Profils</h3>
                    <p>Sauvegarder les profils vocaux appris</p>
                </div>
            </div>

            <div class="progress-section">
                <h3><i class="fas fa-chart-line"></i> Progression de l'Apprentissage</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="learningProgress">0%</div>
                </div>
                <p id="progressText">Prêt à démarrer l'apprentissage</p>
            </div>
        </div>

        <div class="learning-section">
            <h2><i class="fas fa-theater-masks"></i> Modes Vocaux Adaptatifs</h2>
            <p>Louna adapte automatiquement sa voix selon le contexte</p>
            
            <div class="voice-modes" id="voiceModes">
                <div class="mode-card active" data-mode="detente">
                    <h4>😊 Mode Détente</h4>
                    <p>Voix décontractée avec rires et sourires</p>
                </div>
                <div class="mode-card" data-mode="serieux">
                    <h4>🎯 Mode Sérieux</h4>
                    <p>Voix professionnelle et concentrée</p>
                </div>
                <div class="mode-card" data-mode="applique">
                    <h4>📚 Mode Appliqué</h4>
                    <p>Voix précise et méthodique</p>
                </div>
                <div class="mode-card" data-mode="rieuse">
                    <h4>😄 Mode Rieuse</h4>
                    <p>Voix joyeuse et espiègle</p>
                </div>
            </div>
        </div>

        <div class="learning-section">
            <h2><i class="fas fa-youtube"></i> Sources d'Apprentissage YouTube</h2>
            <p>Types de vidéos analysées pour l'apprentissage vocal</p>
            
            <div class="learning-sources">
                <div class="source-card">
                    <h4>📺 Présentatrices TV</h4>
                    <ul>
                        <li>Journalistes françaises</li>
                        <li>Animatrices télé</li>
                        <li>Voix professionnelles</li>
                    </ul>
                </div>
                <div class="source-card">
                    <h4>🎭 Comédiennes</h4>
                    <ul>
                        <li>Actrices en interview</li>
                        <li>Humoristes femmes</li>
                        <li>Expressions naturelles</li>
                    </ul>
                </div>
                <div class="source-card">
                    <h4>👩‍🏫 Professeures</h4>
                    <ul>
                        <li>Enseignantes</li>
                        <li>Formatrices</li>
                        <li>Voix pédagogiques</li>
                    </ul>
                </div>
                <div class="source-card">
                    <h4>🎥 YouTubeuses</h4>
                    <ul>
                        <li>Créatrices de contenu</li>
                        <li>Vlogueuses</li>
                        <li>Voix spontanées</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="learning-section">
            <h2><i class="fas fa-terminal"></i> Journal d'Apprentissage</h2>
            <div class="status-display">
                <h4>📊 Statut du Système</h4>
                <div id="learningLog">
                    <div class="log-entry">🎤 Système d'apprentissage vocal initialisé</div>
                    <div class="log-entry">🎭 Mode détente activé par défaut</div>
                    <div class="log-entry">✅ Prêt pour l'apprentissage depuis YouTube</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/thermal-data-api.js"></script>
    <script src="/js/unified-voice-system.js"></script>
    <script src="/js/voice-learning-system.js"></script>
    <script>
        let currentMode = 'detente';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎤 Interface d\'apprentissage vocal chargée');
            
            // Configurer les modes vocaux
            setupVoiceModes();
            
            // Mettre à jour l'affichage
            updateDisplay();
            
            // Démarrer l'apprentissage automatique si demandé
            if (new URLSearchParams(window.location.search).get('autostart') === 'true') {
                setTimeout(startLearning, 2000);
            }
        });

        function setupVoiceModes() {
            const modeCards = document.querySelectorAll('.mode-card');
            modeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const mode = this.dataset.mode;
                    switchVoiceMode(mode);
                });
            });
        }

        function switchVoiceMode(mode) {
            currentMode = mode;
            
            // Mettre à jour l'interface
            document.querySelectorAll('.mode-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');
            
            // Appliquer le mode au système vocal
            if (window.LounaVoiceLearning) {
                window.LounaVoiceLearning.switchMode(mode);
                addLogEntry(`🎭 Mode vocal changé: ${mode}`);
            }
        }

        async function startLearning() {
            if (!window.LounaVoiceLearning) {
                addLogEntry('❌ Système d\'apprentissage non disponible');
                return;
            }

            addLogEntry('🎓 Démarrage de l\'apprentissage vocal...');
            
            // Ajouter animation
            document.querySelector('.container').classList.add('learning-active');
            
            try {
                await window.LounaVoiceLearning.startLearningFromYouTube();
                addLogEntry('✅ Apprentissage terminé avec succès !');
            } catch (error) {
                addLogEntry(`❌ Erreur apprentissage: ${error.message}`);
            } finally {
                document.querySelector('.container').classList.remove('learning-active');
            }
        }

        function testVoice() {
            if (window.LounaVoice && window.LounaVoice.isAvailable()) {
                const modeInfo = window.LounaVoiceLearning?.getModeInfo(currentMode);
                const testText = `Bonjour Jean-Luc ! Je teste ma voix en mode ${modeInfo?.name || currentMode}. 
                                 ${currentMode === 'rieuse' ? 'Ahaha ! Je suis très joyeuse !' : ''}
                                 ${currentMode === 'detente' ? 'Je suis décontractée et amicale !' : ''}
                                 ${currentMode === 'serieux' ? 'Je suis professionnelle et concentrée.' : ''}
                                 ${currentMode === 'applique' ? 'Je suis précise et méthodique dans mes explications.' : ''}`;
                
                window.LounaVoice.speak(testText);
                addLogEntry(`🗣️ Test vocal en mode ${currentMode}`);
            } else {
                addLogEntry('❌ Système vocal non disponible');
            }
        }

        function resetLearning() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser l\'apprentissage ?')) {
                localStorage.removeItem('lounaVoiceProfiles');
                addLogEntry('🔄 Apprentissage réinitialisé');
                updateProgress(0);
            }
        }

        function exportProfiles() {
            if (window.LounaVoiceLearning) {
                const profiles = {};
                window.LounaVoiceLearning.getAvailableModes().forEach(mode => {
                    profiles[mode] = window.LounaVoiceLearning.getModeInfo(mode);
                });
                
                const dataStr = JSON.stringify(profiles, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = 'louna-voice-profiles.json';
                link.click();
                
                addLogEntry('💾 Profils vocaux exportés');
            }
        }

        function updateProgress(percentage) {
            const progressFill = document.getElementById('learningProgress');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${percentage}%`;
            progressFill.textContent = `${percentage}%`;
            
            if (percentage === 0) {
                progressText.textContent = 'Prêt à démarrer l\'apprentissage';
            } else if (percentage === 100) {
                progressText.textContent = 'Apprentissage terminé avec succès !';
            } else {
                progressText.textContent = `Apprentissage en cours... ${percentage}%`;
            }
        }

        function addLogEntry(message) {
            const log = document.getElementById('learningLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            log.appendChild(entry);
            
            // Garder seulement les 10 dernières entrées
            while (log.children.length > 10) {
                log.removeChild(log.firstChild);
            }
            
            console.log(message);
        }

        function updateDisplay() {
            // Mettre à jour l'affichage avec les informations actuelles
            if (window.LounaVoiceLearning) {
                const progress = window.LounaVoiceLearning.getLearningProgress();
                updateProgress(progress);
                
                const currentModeInfo = window.LounaVoiceLearning.getModeInfo(currentMode);
                if (currentModeInfo) {
                    addLogEntry(`🎭 Mode actuel: ${currentModeInfo.name}`);
                }
            }
        }

        // Mise à jour périodique
        setInterval(() => {
            if (window.LounaVoiceLearning && window.LounaVoiceLearning.isLearningActive()) {
                const progress = window.LounaVoiceLearning.getLearningProgress();
                updateProgress(progress);
            }
        }, 1000);
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
