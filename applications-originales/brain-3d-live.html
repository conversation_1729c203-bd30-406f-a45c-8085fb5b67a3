<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Cerveau 3D VIVANT - Louna</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: 'Roboto', sans-serif;
            overflow: hidden;
            height: 100vh;
        }

        /* Header */
        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 2px solid #ff69b4;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .header-icon {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff69b4, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: pulse 2s infinite;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            border: none;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }

        /* Container 3D */
        #brain-container {
            position: absolute;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, #1a1a2e 0%, #0a0a0a 100%);
        }

        /* Panneau de contrôle */
        .control-panel {
            position: absolute;
            top: 100px;
            left: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid #ff69b4;
            border-radius: 15px;
            padding: 20px;
            z-index: 100;
            max-height: calc(100vh - 140px);
            overflow-y: auto;
        }

        .control-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ff69b4;
            margin-bottom: 20px;
            text-align: center;
        }

        .zone-control {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid var(--zone-color);
        }

        .zone-control.instant { --zone-color: #ff4444; }
        .zone-control.short-term { --zone-color: #ff8800; }
        .zone-control.working { --zone-color: #ffdd00; }
        .zone-control.medium-term { --zone-color: #88ff00; }
        .zone-control.long-term { --zone-color: #00ff88; }
        .zone-control.creative { --zone-color: #8800ff; }

        .zone-name {
            font-weight: 600;
            color: var(--zone-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .zone-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9rem;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .stat-value {
            font-weight: 700;
            color: var(--zone-color);
            font-size: 1.1rem;
            margin-bottom: 2px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        /* Panneau d'informations */
        .info-panel {
            position: absolute;
            top: 100px;
            right: 20px;
            width: 280px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid #4ecdc4;
            border-radius: 15px;
            padding: 20px;
            z-index: 100;
        }

        .info-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #4ecdc4;
            margin-bottom: 20px;
            text-align: center;
        }

        .global-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .global-stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .global-stat-value {
            color: #4ecdc4;
            font-weight: 700;
            font-size: 1.1rem;
        }

        /* Contrôles de visualisation */
        .view-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 100;
        }

        .view-btn {
            padding: 12px 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #ff69b4;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .view-btn:hover, .view-btn.active {
            background: #ff69b4;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }

        /* Indicateur de chargement */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 200;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 105, 180, 0.3);
            border-top: 4px solid #ff69b4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .control-panel, .info-panel {
                width: 250px;
                padding: 15px;
            }

            .header {
                padding: 10px 15px;
            }

            .header-title {
                font-size: 1.2rem;
            }

            .view-controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-title">
            <i class="fas fa-brain header-icon"></i>
            <span>Cerveau 3D VIVANT</span>
        </div>
        <div class="nav-buttons">
            <a href="../interface-originale-complete.html" class="nav-btn">
                <i class="fas fa-home"></i>
                Accueil
            </a>
            <a href="brain-dashboard-live.html" class="nav-btn">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            <a href="brain-monitoring-complete.html" class="nav-btn">
                <i class="fas fa-chart-line"></i>
                Monitoring
            </a>
        </div>
    </div>

    <!-- Indicateur de chargement -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>Initialisation du cerveau 3D...</div>
    </div>

    <!-- Container 3D -->
    <div id="brain-container"></div>

    <!-- Panneau de contrôle des zones -->
    <div class="control-panel" id="control-panel" style="display: none;">
        <div class="control-title">🧠 Zones Cérébrales</div>
        <div id="zone-controls">
            <!-- Généré dynamiquement -->
        </div>
    </div>

    <!-- Panneau d'informations -->
    <div class="info-panel" id="info-panel" style="display: none;">
        <div class="info-title">📊 Statistiques Globales</div>
        <div id="global-stats">
            <!-- Généré dynamiquement -->
        </div>
    </div>

    <!-- Contrôles de visualisation -->
    <div class="view-controls" id="view-controls" style="display: none;">
        <div class="view-btn active" onclick="setViewMode('overview')">Vue d'ensemble</div>
        <div class="view-btn" onclick="setViewMode('zones')">Zones détaillées</div>
        <div class="view-btn" onclick="setViewMode('flow')">Flux de données</div>
        <div class="view-btn" onclick="toggleAnimation()">
            <i class="fas fa-play" id="animation-icon"></i>
            <span id="animation-text">Pause</span>
        </div>
    </div>

    <script>
        // Variables globales
        let scene, camera, renderer, controls;
        let brainGroup, zoneObjects = {};
        let dataParticles = [];
        let animationRunning = true;
        let currentViewMode = 'overview';

        // Données des zones
        const brainZones = {
            instant: { name: 'Mémoire Instantanée', color: 0xff4444, position: [0, 2, 0], size: 0.8 },
            'short-term': { name: 'Mémoire Court Terme', color: 0xff8800, position: [-2, 1, 0], size: 1.0 },
            working: { name: 'Mémoire de Travail', color: 0xffdd00, position: [0, 0, 0], size: 1.2 },
            'medium-term': { name: 'Mémoire Moyen Terme', color: 0x88ff00, position: [2, 1, 0], size: 1.1 },
            'long-term': { name: 'Mémoire Long Terme', color: 0x00ff88, position: [0, -1, 2], size: 1.3 },
            creative: { name: 'Zone Créative', color: 0x8800ff, position: [0, 1, -2], size: 0.9 }
        };

        // Statistiques globales
        let globalStats = {
            totalMemories: 0,
            activeNeurons: 71,
            qiLevel: 225, // VALEUR FIXE JEAN-LUC
            systemTemperature: 42.3,
            efficiency: 95.2,
            kyberAccelerators: 8
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeScene();
            loadRealData();
            startAnimation();
            startRealTimeUpdates();

            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('control-panel').style.display = 'block';
                document.getElementById('info-panel').style.display = 'block';
                document.getElementById('view-controls').style.display = 'flex';
            }, 2000);
        });

        // Initialiser la scène 3D
        function initializeScene() {
            // Scène
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0a0a0a);

            // Caméra
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 8);

            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight - 80);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            document.getElementById('brain-container').appendChild(renderer.domElement);

            // Contrôles
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // Éclairage
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);

            // Groupe principal du cerveau
            brainGroup = new THREE.Group();
            scene.add(brainGroup);

            // Créer les zones cérébrales
            createBrainZones();

            // Créer les connexions
            createConnections();

            // Créer les particules de données
            createDataParticles();

            // Gestion du redimensionnement
            window.addEventListener('resize', onWindowResize);
        }

        // Créer les zones cérébrales
        function createBrainZones() {
            Object.keys(brainZones).forEach(zoneName => {
                const zone = brainZones[zoneName];

                // Géométrie de la zone (sphère avec variations)
                const geometry = new THREE.SphereGeometry(zone.size, 32, 32);

                // Matériau avec effet de pulsation
                const material = new THREE.MeshPhongMaterial({
                    color: zone.color,
                    transparent: true,
                    opacity: 0.7,
                    emissive: zone.color,
                    emissiveIntensity: 0.2
                });

                const zoneMesh = new THREE.Mesh(geometry, material);
                zoneMesh.position.set(...zone.position);
                zoneMesh.castShadow = true;
                zoneMesh.receiveShadow = true;
                zoneMesh.userData = { zoneName, originalSize: zone.size };

                // Ajouter un halo autour de la zone
                const haloGeometry = new THREE.SphereGeometry(zone.size * 1.2, 16, 16);
                const haloMaterial = new THREE.MeshBasicMaterial({
                    color: zone.color,
                    transparent: true,
                    opacity: 0.1,
                    side: THREE.BackSide
                });

                const haloMesh = new THREE.Mesh(haloGeometry, haloMaterial);
                haloMesh.position.set(...zone.position);

                brainGroup.add(zoneMesh);
                brainGroup.add(haloMesh);

                zoneObjects[zoneName] = { mesh: zoneMesh, halo: haloMesh, activity: 0 };
            });
        }

        // Créer les connexions entre zones
        function createConnections() {
            const connections = [
                ['instant', 'short-term'],
                ['short-term', 'working'],
                ['working', 'medium-term'],
                ['medium-term', 'long-term'],
                ['working', 'creative'],
                ['creative', 'long-term']
            ];

            connections.forEach(([from, to]) => {
                const fromPos = new THREE.Vector3(...brainZones[from].position);
                const toPos = new THREE.Vector3(...brainZones[to].position);

                // Créer une courbe entre les deux points
                const curve = new THREE.QuadraticBezierCurve3(
                    fromPos,
                    fromPos.clone().lerp(toPos, 0.5).add(new THREE.Vector3(0, 1, 0)),
                    toPos
                );

                const points = curve.getPoints(50);
                const geometry = new THREE.BufferGeometry().setFromPoints(points);

                const material = new THREE.LineBasicMaterial({
                    color: 0x4ecdc4,
                    transparent: true,
                    opacity: 0.3
                });

                const line = new THREE.Line(geometry, material);
                brainGroup.add(line);
            });
        }

        // Créer les particules de données
        function createDataParticles() {
            const particleCount = 100;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            const velocities = [];

            for (let i = 0; i < particleCount; i++) {
                // Position aléatoire autour du cerveau
                positions[i * 3] = (Math.random() - 0.5) * 10;
                positions[i * 3 + 1] = (Math.random() - 0.5) * 10;
                positions[i * 3 + 2] = (Math.random() - 0.5) * 10;

                // Couleur aléatoire
                const color = new THREE.Color();
                color.setHSL(Math.random(), 0.8, 0.6);
                colors[i * 3] = color.r;
                colors[i * 3 + 1] = color.g;
                colors[i * 3 + 2] = color.b;

                // Vélocité aléatoire
                velocities.push({
                    x: (Math.random() - 0.5) * 0.02,
                    y: (Math.random() - 0.5) * 0.02,
                    z: (Math.random() - 0.5) * 0.02
                });
            }

            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const material = new THREE.PointsMaterial({
                size: 0.05,
                vertexColors: true,
                transparent: true,
                opacity: 0.8
            });

            const particles = new THREE.Points(geometry, material);
            particles.userData = { velocities };
            scene.add(particles);

            dataParticles.push(particles);
        }

        // Charger les données réelles
        async function loadRealData() {
            try {
                const response = await fetch('/api/thermal-memory/status');
                if (response.ok) {
                    const data = await response.json();
                    updateZoneData(data);
                }
            } catch (error) {
                console.warn('⚠️ Utilisation de données données réelleses:', error);
                generateSimulatedData();
            }

            updateControlPanels();
        }

        // Mettre à jour les données des zones
        function updateZoneData(data) {
            if (data.zones) {
                Object.keys(brainZones).forEach(zoneName => {
                    if (data.zones[zoneName] && zoneObjects[zoneName]) {
                        const zone = data.zones[zoneName];
                        const activity = (zone.length || 0) / brainZones[zoneName].capacity;
                        zoneObjects[zoneName].activity = activity;

                        // Mettre à jour la taille et l'intensité
                        const mesh = zoneObjects[zoneName].mesh;
                        const targetScale = 1 + activity * 0.5;
                        mesh.scale.setScalar(targetScale);

                        // Mettre à jour l'émission
                        mesh.material.emissiveIntensity = 0.2 + activity * 0.3;
                    }
                });
            }
        }

        // Générer des données données réelleses
        function generateSimulatedData() {
            Object.keys(brainZones).forEach(zoneName => {
                if (zoneObjects[zoneName]) {
                    const activity = 0 + 0.2;
                    zoneObjects[zoneName].activity = activity;
                }
            });
        }

        // Mettre à jour les panneaux de contrôle
        function updateControlPanels() {
            // Panneau des zones
            const zoneControls = document.getElementById('zone-controls');
            zoneControls.innerHTML = '';

            Object.keys(brainZones).forEach(zoneName => {
                const zone = brainZones[zoneName];
                const activity = zoneObjects[zoneName]?.activity || 0;
                const temperature = 20 + activity * 60;
                const entries = Math.floor(activity * zone.capacity);

                zoneControls.innerHTML += `
                    <div class="zone-control ${zoneName}">
                        <div class="zone-name">
                            <i class="fas fa-circle" style="color: #${zone.color.toString(16).padStart(6, '0')}"></i>
                            ${zone.name}
                        </div>
                        <div class="zone-stats">
                            <div class="stat-item">
                                <div class="stat-value">${entries}</div>
                                <div class="stat-label">Entrées</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${temperature.toFixed(1)}°</div>
                                <div class="stat-label">Température</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${(activity * 100).toFixed(1)}%</div>
                                <div class="stat-label">Activité</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${zone.capacity}</div>
                                <div class="stat-label">Capacité</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Panneau des statistiques globales
            const globalStatsEl = document.getElementById('global-stats');
            globalStatsEl.innerHTML = `
                <div class="global-stat">
                    <span class="global-stat-label">Mémoires Totales</span>
                    <span class="global-stat-value">${globalStats.totalMemories}</span>
                </div>
                <div class="global-stat">
                    <span class="global-stat-label">Neurones Actifs</span>
                    <span class="global-stat-value">${globalStats.activeNeurons}</span>
                </div>
                <div class="global-stat">
                    <span class="global-stat-label">Niveau QI</span>
                    <span class="global-stat-value">${globalStats.qiLevel}</span>
                </div>
                <div class="global-stat">
                    <span class="global-stat-label">Température</span>
                    <span class="global-stat-value">${globalStats.systemTemperature.toFixed(1)}°C</span>
                </div>
                <div class="global-stat">
                    <span class="global-stat-label">Efficacité</span>
                    <span class="global-stat-value">${globalStats.efficiency.toFixed(1)}%</span>
                </div>
                <div class="global-stat">
                    <span class="global-stat-label">Accélérateurs KYBER</span>
                    <span class="global-stat-value">${globalStats.kyberAccelerators}</span>
                </div>
            `;
        }

        // Démarrer l'animation
        function startAnimation() {
            animate();
        }

        // Boucle d'animation
        function animate() {
            requestAnimationFrame(animate);

            if (animationRunning) {
                // Rotation du groupe principal
                brainGroup.rotation.y += 0.005;

                // Animation des zones
                Object.keys(zoneObjects).forEach(zoneName => {
                    const zoneObj = zoneObjects[zoneName];
                    const time = Date.now() * 0.001;

                    // Pulsation basée sur l'activité
                    const pulse = 1 + Math.sin(time * 2 + zoneObj.activity * 10) * 0.1 * zoneObj.activity;
                    zoneObj.mesh.scale.setScalar(pulse);

                    // Variation de l'émission
                    zoneObj.mesh.material.emissiveIntensity = 0.2 + zoneObj.activity * 0.3 + Math.sin(time * 3) * 0.1;

                    // Rotation individuelle
                    zoneObj.mesh.rotation.x += 0.01 * zoneObj.activity;
                    zoneObj.mesh.rotation.z += 0.005 * zoneObj.activity;
                });

                // Animation des particules
                dataParticles.forEach(particles => {
                    const positions = particles.geometry.attributes.position.array;
                    const velocities = particles.userData.velocities;

                    for (let i = 0; i < velocities.length; i++) {
                        positions[i * 3] += velocities[i].x;
                        positions[i * 3 + 1] += velocities[i].y;
                        positions[i * 3 + 2] += velocities[i].z;

                        // Rebond sur les limites
                        if (Math.abs(positions[i * 3]) > 5) velocities[i].x *= -1;
                        if (Math.abs(positions[i * 3 + 1]) > 5) velocities[i].y *= -1;
                        if (Math.abs(positions[i * 3 + 2]) > 5) velocities[i].z *= -1;
                    }

                    particles.geometry.attributes.position.needsUpdate = true;
                });
            }

            controls.update();
            renderer.render(scene, camera);
        }

        // Démarrer les mises à jour en temps réel
        function startRealTimeUpdates() {
            setInterval(() => {
                if (Math.random() > 0.7) { // 30% de chance de mise à jour
                    generateSimulatedData();
                    updateControlPanels();
                }
            }, 3000);
        }

        // Changer le mode de vue
        function setViewMode(mode) {
            currentViewMode = mode;

            // Mettre à jour les boutons
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Ajuster la caméra selon le mode
            switch (mode) {
                case 'overview':
                    camera.position.set(0, 0, 8);
                    controls.target.set(0, 0, 0);
                    break;
                case 'zones':
                    camera.position.set(5, 3, 5);
                    controls.target.set(0, 0, 0);
                    break;
                case 'flow':
                    camera.position.set(0, 8, 0);
                    controls.target.set(0, 0, 0);
                    break;
            }

            controls.update();
        }

        // Basculer l'animation
        function toggleAnimation() {
            animationRunning = !animationRunning;

            const icon = document.getElementById('animation-icon');
            const text = document.getElementById('animation-text');

            if (animationRunning) {
                icon.className = 'fas fa-pause';
                text.textContent = 'Pause';
            } else {
                icon.className = 'fas fa-play';
                text.textContent = 'Play';
            }
        }

        // Gestion du redimensionnement
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight - 80);
        }
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
