/* Thème ROSE et NOIR pour toutes les interfaces Louna */
/* Coh<PERSON>rence parfaite avec le design principal */

/* Variables du thème rose et noir */
:root {
    --pink-primary: #ff69b4;
    --pink-light: #c8a2c8;
    --pink-dark: #ff1493;
    --black-primary: #000000;
    --black-light: #333333;
    --pink-shadow: rgba(255, 105, 180, 0.4);
}

/* Corrections générales - NOIR avec ombres roses */
* {
    color: var(--black-primary) !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
}

/* Navigation - Thème rose et noir */
.top-navbar {
    background-color: var(--pink-light) !important;
}

.nav-item, .logo-text {
    color: var(--black-primary) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
}

.nav-item:hover {
    color: var(--pink-primary) !important;
}

.nav-item.active {
    color: var(--pink-primary) !important;
}

/* Icônes - NOIR pour cohérence */
.fas, .fab, .far, i {
    color: var(--black-primary) !important;
}

/* Icônes actives et hover - ROSE */
.nav-item.active i, .nav-item:hover i {
    color: var(--pink-primary) !important;
}

/* Labels et textes importants - NOIR avec ombre rose */
.zone-name, .stat-label, .accelerator-name, 
.monitoring-label, .file-item, .editor-tab,
.toolbar-btn, .nav-item span, .logo-text,
.interface-title, .card-title, .section-title,
h1, h2, h3, h4, h5, h6 {
    color: var(--black-primary) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
}

/* Valeurs et métriques - NOIR plus gras */
.zone-temp, .stat-value, .accelerator-value,
.monitoring-value, .neuron-count, .qi-level,
.metric-value, .temperature-value, .neuron-value {
    color: var(--black-primary) !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    text-shadow: 0 1px 3px var(--pink-shadow) !important;
}

/* Boutons et liens - NOIR avec bordures roses */
.action-button, .toolbar-btn, .modal-btn,
button, .control-btn {
    color: var(--black-primary) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
    border: 1px solid var(--pink-primary) !important;
    background: rgba(255, 105, 180, 0.1) !important;
}

.action-button:hover, .toolbar-btn:hover, .modal-btn:hover,
button:hover, .control-btn:hover {
    background: rgba(255, 105, 180, 0.2) !important;
    color: var(--black-primary) !important;
    border: 1px solid var(--pink-dark) !important;
}

/* Cartes et conteneurs - Rose foncé avec bordures roses */
.card, .editor-sidebar, .editor-tabs, .editor-toolbar,
.thermal-card, .metric-card, .chart-section, .modal-content {
    background: rgba(200, 162, 200, 0.2) !important;
    border: 1px solid var(--pink-primary) !important;
    backdrop-filter: blur(15px) !important;
}

/* Onglets d'éditeur - Thème rose et noir */
.editor-tab {
    color: var(--black-primary) !important;
    background: rgba(255, 105, 180, 0.1) !important;
    border: 1px solid rgba(255, 105, 180, 0.3) !important;
}

.editor-tab.active {
    background: rgba(255, 105, 180, 0.3) !important;
    border-bottom: 2px solid var(--pink-primary) !important;
    color: var(--black-primary) !important;
}

.editor-tab:hover {
    background: rgba(255, 105, 180, 0.2) !important;
    color: var(--black-primary) !important;
}

/* Noms de fichiers dans l'explorateur */
.file-item span {
    color: var(--black-primary) !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
}

.file-item:hover span {
    color: var(--pink-primary) !important;
}

.file-item.active span {
    color: var(--pink-primary) !important;
    font-weight: 700 !important;
}

/* Scrollbars personnalisées - ROSE */
::-webkit-scrollbar {
    width: 8px;
    background: rgba(200, 162, 200, 0.3);
}

::-webkit-scrollbar-thumb {
    background: var(--pink-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--pink-dark);
}

/* États de focus - ROSE */
input:focus, textarea:focus, select:focus, button:focus {
    outline: 2px solid var(--pink-primary) !important;
    background: rgba(255, 105, 180, 0.1) !important;
    color: var(--black-primary) !important;
}

/* Inputs et formulaires */
input, textarea, select {
    background: rgba(255, 105, 180, 0.1) !important;
    border: 1px solid var(--pink-primary) !important;
    color: var(--black-primary) !important;
    font-weight: 500 !important;
}

input::placeholder, textarea::placeholder {
    color: rgba(0, 0, 0, 0.6) !important;
    font-weight: 500 !important;
}

/* Terminal - Garder le style original mais avec accents roses */
.terminal-panel {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 1px solid var(--pink-primary) !important;
}

.terminal-line {
    color: #00ff00 !important;
}

.terminal-prompt {
    color: var(--pink-primary) !important;
    font-weight: bold !important;
}

/* Barres de progression */
.progress-bar {
    background: rgba(255, 105, 180, 0.2) !important;
    border: 1px solid var(--pink-primary) !important;
}

/* Badges et étiquettes */
.badge, .tag, .chip, .stat-badge, .card-badge {
    background: rgba(255, 105, 180, 0.3) !important;
    color: var(--black-primary) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
    border: 1px solid var(--pink-primary) !important;
}

/* Liens */
a {
    color: var(--pink-primary) !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

a:hover {
    color: var(--pink-dark) !important;
}

/* Alertes et notifications */
.alert, .message, .info-box, .notification {
    background: rgba(255, 105, 180, 0.15) !important;
    border: 1px solid var(--pink-primary) !important;
    color: var(--black-primary) !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
}

/* Tableaux */
table, tr, td, th {
    color: var(--black-primary) !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px var(--pink-shadow) !important;
}

/* Animations fluides */
* {
    transition: all 0.3s ease !important;
}

/* Effets de survol */
.hoverable:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px var(--pink-shadow) !important;
}

/* Code et monospace */
code, pre, .monospace {
    background: rgba(0, 0, 0, 0.6) !important;
    color: #00ff00 !important;
    border: 1px solid var(--pink-primary) !important;
}

/* Séparateurs */
hr, .divider {
    border-color: var(--pink-primary) !important;
    background: var(--pink-primary) !important;
}
