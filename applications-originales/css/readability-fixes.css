/* Corrections de lisibilité globales pour toutes les interfaces Louna */

/* Amélioration du contraste et de la lisibilité du texte */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Texte principal - Contraste amélioré */
.interface-title,
.interface-subtitle,
.nav-item span,
.logo-text,
h1, h2, h3, h4, h5, h6 {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* Labels et textes secondaires */
.label,
.stat-label,
.metric-label,
.zone-label,
.entry-type,
.secondary-metric .label,
.progress-text,
.status-label,
.file-item span,
.editor-tab span,
.toolbar-btn {
    color: #ffffff !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9) !important;
}

/* Valeurs et métriques importantes */
.stat-value,
.metric-value,
.zone-count,
.secondary-metric .value,
.temp-value,
.entry-content {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}

/* Amélioration des boutons */
.control-btn,
.toolbar-btn,
.action-button,
.modal-btn,
.nav-item {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.control-btn:hover,
.toolbar-btn:hover,
.action-button:hover,
.nav-item:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}

/* Amélioration des cartes et conteneurs */
.thermal-card,
.metric-card,
.chart-section,
.editor-sidebar,
.modal-content {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    backdrop-filter: blur(20px) !important;
}

/* Amélioration des entrées de mémoire */
.memory-entry,
.file-item,
.editor-tab {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.memory-entry:hover,
.file-item:hover,
.editor-tab:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.4) !important;
}

/* Amélioration des zones de température */
.zone-temp {
    font-size: 26px !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}

/* Amélioration des statuts */
.thermal-status,
.metric-status,
.status-text {
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration des inputs et formulaires */
input, textarea, select {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
}

input::placeholder,
textarea::placeholder {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

/* Amélioration des barres de progression */
.progress-bar {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Amélioration des légendes de graphiques */
.legend-item,
.chart-legend span {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration du terminal */
.terminal-panel {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.terminal-line {
    color: #00ff00 !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.terminal-prompt {
    color: #ff69b4 !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration des onglets actifs */
.editor-tab.active,
.nav-item.active {
    background: rgba(255, 105, 180, 0.3) !important;
    border-bottom: 2px solid #ff69b4 !important;
    color: #ffffff !important;
}

/* Amélioration des icônes */
.fas, .fab, .far {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration des tableaux et listes */
table, tr, td, th {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration des notifications */
.notification {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration des tooltips */
.tooltip {
    background: rgba(0, 0, 0, 0.9) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Amélioration des scrollbars */
::-webkit-scrollbar {
    width: 12px;
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 105, 180, 0.6);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 105, 180, 0.8);
}

/* Amélioration des focus states */
input:focus,
textarea:focus,
select:focus,
button:focus {
    outline: 2px solid #ff69b4 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 10px rgba(255, 105, 180, 0.5) !important;
}

/* Amélioration des états de survol */
.hoverable:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3) !important;
}

/* Amélioration des animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Amélioration des états de chargement */
.loading-indicator {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

.loading-indicator i {
    color: #ff69b4 !important;
}

/* Amélioration des badges et étiquettes */
.badge,
.tag,
.chip {
    background: rgba(255, 105, 180, 0.3) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Amélioration des séparateurs */
hr,
.divider {
    border-color: rgba(255, 255, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.3) !important;
}

/* Amélioration des codes et texte monospace */
code,
pre,
.monospace {
    background: rgba(0, 0, 0, 0.6) !important;
    color: #00ff00 !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-weight: 500 !important;
}

/* Amélioration des alertes et messages */
.alert,
.message,
.info-box {
    background: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Amélioration des liens */
a {
    color: #ff69b4 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

a:hover {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.9) !important;
}
