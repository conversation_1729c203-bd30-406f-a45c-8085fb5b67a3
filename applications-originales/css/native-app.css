/**
 * Styles spécifiques pour l'application native <PERSON>na
 * Ces styles sont appliqués uniquement lorsque l'application est exécutée en mode natif
 */

:root {
  /* Couleurs principales */
  --primary: #ff69b4;
  --primary-light: #ffb6c1;
  --primary-dark: #c71585;
  --bg-primary: #1a1a2e;
  --bg-secondary: #16213e;
  --bg-card: #0f3460;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-muted: #a0a0a0;
  --accent: #00ffff;
  --success: #4caf50;
  --warning: #ff9800;
  --danger: #f44336;
  --info: #2196f3;
  
  /* Variables spécifiques à macOS */
  --macos-window-bg: rgba(28, 28, 30, 0.95);
  --macos-sidebar-bg: rgba(40, 40, 45, 0.8);
  --macos-titlebar-height: 38px;
  --macos-toolbar-height: 52px;
  --macos-border-radius: 10px;
  --macos-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  --macos-blur: 10px;
}

/* Styles généraux pour l'application native */
.electron-app {
  /* Désactiver la sélection de texte par défaut */
  user-select: none;
  
  /* Appliquer des styles spécifiques à macOS */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  
  /* Améliorer le rendu du texte */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Barre de titre personnalisée */
.titlebar {
  -webkit-app-region: drag;
  height: var(--macos-titlebar-height);
  background-color: var(--bg-card);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.titlebar-title {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.titlebar-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.titlebar-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 8px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.titlebar-button:hover {
  opacity: 0.8;
}

.titlebar-close {
  background-color: #ff5f57;
}

.titlebar-minimize {
  background-color: #ffbd2e;
}

.titlebar-maximize {
  background-color: #28c940;
}

/* Ajustement du contenu principal pour la barre de titre */
.main-content {
  margin-top: var(--macos-titlebar-height);
  padding: 20px;
  height: calc(100vh - var(--macos-titlebar-height));
  overflow-y: auto;
}

/* Styles pour les cartes */
.card {
  background-color: var(--bg-card);
  border-radius: var(--macos-border-radius);
  box-shadow: var(--macos-shadow);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.6);
}

/* Styles pour les boutons */
.btn {
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* Styles pour les entrées */
input, select, textarea {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: var(--text-primary);
  padding: 8px 12px;
  transition: all 0.2s ease;
}

input:focus, select:focus, textarea:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 105, 180, 0.3);
}

/* Styles pour les barres de défilement */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Styles pour les animations */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Styles pour les notifications */
.notification {
  position: fixed;
  top: calc(var(--macos-titlebar-height) + 10px);
  right: 10px;
  background-color: var(--bg-card);
  border-radius: var(--macos-border-radius);
  box-shadow: var(--macos-shadow);
  padding: 12px 16px;
  max-width: 300px;
  z-index: 1100;
  transform: translateX(120%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.notification-body {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Styles pour les tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  background-color: var(--bg-card);
  color: var(--text-primary);
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  box-shadow: var(--macos-shadow);
  font-size: 12px;
  white-space: nowrap;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Styles pour les badges */
.badge {
  display: inline-block;
  padding: 3px 6px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 10px;
  background-color: var(--primary);
  color: white;
}

/* Styles pour les modales */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: var(--bg-secondary);
  border-radius: var(--macos-border-radius);
  box-shadow: var(--macos-shadow);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
