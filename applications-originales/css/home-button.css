/* Styles pour le bouton d'accueil */
.home-button {
    display: flex !important;
    align-items: center;
    gap: 8px;
    padding: 10px 18px;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
    z-index: 1000;
    position: relative;
    margin-right: 15px;
    text-decoration: none;
    min-width: 100px;
    justify-content: center;
}

.home-button:hover {
    background: linear-gradient(135deg, #5ba0f2 0%, #4080cd 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
    color: white !important;
}

.home-button:active {
    transform: translateY(0);
}

.home-button i {
    font-size: 16px;
    margin-right: 5px;
}

.home-button span {
    font-weight: 600;
}

/* Assurer que le bouton est visible dans l'en-tête */
.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}
