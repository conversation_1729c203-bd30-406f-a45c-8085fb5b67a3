/* ===== LOUNA UNIFIED DESIGN SYSTEM ===== */

/* Variables CSS pour la cohérence */
:root {
    /* Couleurs principales */
    --primary-pink: #ff69b4;
    --primary-dark-pink: #e91e63;
    --secondary-cyan: #4ecdc4;
    --secondary-dark-cyan: #44a08d;

    /* Couleurs de fond */
    --bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --bg-secondary: rgba(255, 255, 255, 0.1);
    --bg-tertiary: rgba(255, 255, 255, 0.05);
    --bg-glass: rgba(255, 255, 255, 0.1);

    /* Couleurs de texte */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-muted: rgba(255, 255, 255, 0.7);
    --text-disabled: rgba(255, 255, 255, 0.5);

    /* Couleurs d'état */
    --success: #28a745;
    --warning: #ffc107;
    --error: #dc3545;
    --info: #17a2b8;

    /* Espacements */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --spacing-xxl: 40px;

    /* Bordures */
    --border-radius-sm: 5px;
    --border-radius-md: 10px;
    --border-radius-lg: 15px;
    --border-radius-xl: 25px;

    /* Ombres */
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 15px 40px rgba(0, 0, 0, 0.4);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Typographie */
    --font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    --font-size-xxxl: 2rem;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
}

/* ===== COMPOSANTS HEADER UNIFIÉ ===== */
.louna-header {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.louna-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.louna-header-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.louna-header-title h1 {
    font-size: var(--font-size-xxxl);
    background: linear-gradient(135deg, var(--primary-pink), var(--secondary-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.louna-header-icon {
    font-size: 2.5rem;
    color: var(--secondary-cyan);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* ===== NAVIGATION UNIFIÉE ===== */
.louna-nav {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.louna-nav-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: var(--border-radius-xl);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

.louna-nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.louna-nav-btn.primary {
    background: linear-gradient(135deg, var(--primary-pink), var(--primary-dark-pink));
}

.louna-nav-btn.secondary {
    background: linear-gradient(135deg, var(--secondary-cyan), var(--secondary-dark-cyan));
}

/* ===== CONTENEUR PRINCIPAL UNIFIÉ ===== */
.louna-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
}

.louna-container.full-width {
    max-width: none;
    padding: var(--spacing-xl);
}

/* ===== CARTES UNIFIÉES ===== */
.louna-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-lg);
}

.louna-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(255, 255, 255, 0.4);
}

.louna-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.louna-card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.louna-card-icon {
    font-size: var(--font-size-xxl);
    background: linear-gradient(135deg, var(--primary-pink), var(--secondary-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== BOUTONS UNIFIÉS ===== */
.louna-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-md);
    text-decoration: none;
}

.louna-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.louna-btn.primary {
    background: linear-gradient(135deg, var(--primary-pink), var(--primary-dark-pink));
    color: var(--text-primary);
}

.louna-btn.secondary {
    background: linear-gradient(135deg, var(--secondary-cyan), var(--secondary-dark-cyan));
    color: var(--text-primary);
}

.louna-btn.success {
    background: linear-gradient(135deg, var(--success), #20c997);
    color: var(--text-primary);
}

.louna-btn.warning {
    background: linear-gradient(135deg, var(--warning), #fd7e14);
    color: var(--text-primary);
}

.louna-btn.error {
    background: linear-gradient(135deg, var(--error), #c82333);
    color: var(--text-primary);
}

.louna-btn.ghost {
    background: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

/* ===== INDICATEURS D'ÉTAT UNIFIÉS ===== */
.louna-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.louna-status.online {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
    border: 1px solid var(--success);
}

.louna-status.warning {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning);
    border: 1px solid var(--warning);
}

.louna-status.error {
    background: rgba(220, 53, 69, 0.2);
    color: var(--error);
    border: 1px solid var(--error);
}

.louna-status.processing {
    background: rgba(78, 205, 196, 0.2);
    color: var(--secondary-cyan);
    border: 1px solid var(--secondary-cyan);
}

.louna-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.louna-status.online .louna-status-dot {
    background: var(--success);
}

.louna-status.warning .louna-status-dot {
    background: var(--warning);
}

.louna-status.error .louna-status-dot {
    background: var(--error);
}

.louna-status.processing .louna-status-dot {
    background: var(--secondary-cyan);
}

/* ===== GRILLES UNIFIÉES ===== */
.louna-grid {
    display: grid;
    gap: var(--spacing-lg);
}

.louna-grid.cols-1 { grid-template-columns: 1fr; }
.louna-grid.cols-2 { grid-template-columns: repeat(2, 1fr); }
.louna-grid.cols-3 { grid-template-columns: repeat(3, 1fr); }
.louna-grid.cols-4 { grid-template-columns: repeat(4, 1fr); }

/* ===== RESPONSIVE UNIFIÉ ===== */
@media (max-width: 1200px) {
    .louna-grid.cols-4 { grid-template-columns: repeat(2, 1fr); }
    .louna-grid.cols-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
    .louna-header-content {
        flex-direction: column;
        text-align: center;
    }

    .louna-nav {
        justify-content: center;
    }

    .louna-container {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .louna-grid.cols-4,
    .louna-grid.cols-3,
    .louna-grid.cols-2 {
        grid-template-columns: 1fr;
    }

    .louna-card {
        padding: var(--spacing-lg);
    }
}

/* ===== ANIMATIONS UNIFIÉES ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.louna-fade-in {
    animation: fadeIn var(--transition-slow);
}

.louna-slide-in {
    animation: slideIn var(--transition-normal);
}

/* ===== UTILITAIRES ===== */
.louna-text-center { text-align: center; }
.louna-text-left { text-align: left; }
.louna-text-right { text-align: right; }

.louna-mb-sm { margin-bottom: var(--spacing-sm); }
.louna-mb-md { margin-bottom: var(--spacing-md); }
.louna-mb-lg { margin-bottom: var(--spacing-lg); }
.louna-mb-xl { margin-bottom: var(--spacing-xl); }

.louna-mt-sm { margin-top: var(--spacing-sm); }
.louna-mt-md { margin-top: var(--spacing-md); }
.louna-mt-lg { margin-top: var(--spacing-lg); }
.louna-mt-xl { margin-top: var(--spacing-xl); }

.louna-p-sm { padding: var(--spacing-sm); }
.louna-p-md { padding: var(--spacing-md); }
.louna-p-lg { padding: var(--spacing-lg); }
.louna-p-xl { padding: var(--spacing-xl); }

.louna-hidden { display: none; }
.louna-visible { display: block; }

.louna-flex { display: flex; }
.louna-flex-center { display: flex; justify-content: center; align-items: center; }
.louna-flex-between { display: flex; justify-content: space-between; align-items: center; }
.louna-flex-column { flex-direction: column; }
.louna-flex-wrap { flex-wrap: wrap; }
.louna-gap-sm { gap: var(--spacing-sm); }
.louna-gap-md { gap: var(--spacing-md); }
.louna-gap-lg { gap: var(--spacing-lg); }

/* ===== COMPOSANTS DE NAVIGATION AVANCÉS ===== */

/* Header avec sous-titre */
.louna-header-subtitle {
    text-align: center;
    padding: var(--spacing-md) 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.louna-header-subtitle p {
    color: var(--text-muted);
    font-size: var(--font-size-md);
    margin: 0;
    font-style: italic;
}

/* Breadcrumb */
.louna-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) 0;
    margin-bottom: var(--spacing-lg);
}

.louna-breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.louna-breadcrumb-item:hover {
    color: var(--text-primary);
}

.louna-breadcrumb-item.active {
    color: var(--primary-pink);
    font-weight: 500;
}

.louna-breadcrumb-separator {
    color: var(--text-disabled);
    font-size: var(--font-size-xs);
}

/* Sidebar */
.louna-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 90;
    overflow-y: auto;
    transition: var(--transition-normal);
}

.louna-sidebar-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.louna-sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--primary-pink);
    margin-bottom: var(--spacing-sm);
}

.louna-sidebar-logo i {
    font-size: 2rem;
    color: var(--secondary-cyan);
}

.louna-sidebar-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-style: italic;
}

.louna-sidebar-nav {
    padding: var(--spacing-lg);
}

.louna-sidebar-section {
    margin-bottom: var(--spacing-xl);
}

.louna-sidebar-section-title {
    font-size: var(--font-size-xs);
    font-weight: 700;
    color: var(--text-disabled);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-sm);
}

.louna-sidebar-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.louna-sidebar-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    transform: translateX(5px);
}

.louna-sidebar-item.active {
    background: linear-gradient(135deg, var(--primary-pink), var(--primary-dark-pink));
    color: var(--text-primary);
    font-weight: 500;
    box-shadow: var(--shadow-md);
}

.louna-sidebar-item i {
    width: 20px;
    text-align: center;
    font-size: var(--font-size-md);
}

/* Contenu principal avec sidebar */
.louna-main-with-sidebar {
    margin-left: 280px;
    transition: var(--transition-normal);
}

/* Sidebar mobile */
@media (max-width: 768px) {
    .louna-sidebar {
        transform: translateX(-100%);
    }

    .louna-sidebar.open {
        transform: translateX(0);
    }

    .louna-main-with-sidebar {
        margin-left: 0;
    }
}

/* Toggle sidebar button */
.louna-sidebar-toggle {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: 100;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: none;
}

.louna-sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
    .louna-sidebar-toggle {
        display: block;
    }
}

/* ===== COMPOSANTS SPÉCIALISÉS ===== */

/* Métriques en temps réel */
.louna-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.louna-metric {
    background: var(--bg-glass);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-normal);
}

.louna-metric:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.louna-metric-value {
    font-size: var(--font-size-xxxl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-pink), var(--secondary-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xs);
}

.louna-metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.louna-metric-change {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    font-weight: 500;
}

.louna-metric-change.positive {
    color: var(--success);
}

.louna-metric-change.negative {
    color: var(--error);
}

/* Notifications toast */
.louna-toast {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    min-width: 300px;
    animation: slideIn var(--transition-normal);
}

.louna-toast.success {
    border-left: 4px solid var(--success);
}

.louna-toast.warning {
    border-left: 4px solid var(--warning);
}

.louna-toast.error {
    border-left: 4px solid var(--error);
}

.louna-toast.info {
    border-left: 4px solid var(--info);
}

.louna-toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.louna-toast-title {
    font-weight: 600;
    color: var(--text-primary);
}

.louna-toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: var(--font-size-lg);
    transition: var(--transition-fast);
}

.louna-toast-close:hover {
    color: var(--text-primary);
}

.louna-toast-message {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* Loading spinner */
.louna-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-pink);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.louna-spinner.large {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

/* Progress bar */
.louna-progress {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.louna-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-pink), var(--secondary-cyan));
    border-radius: var(--border-radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}

.louna-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Badges */
.louna-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.louna-badge.primary {
    background: rgba(255, 105, 180, 0.2);
    color: var(--primary-pink);
    border: 1px solid var(--primary-pink);
}

.louna-badge.success {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success);
    border: 1px solid var(--success);
}

.louna-badge.warning {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning);
    border: 1px solid var(--warning);
}

.louna-badge.error {
    background: rgba(220, 53, 69, 0.2);
    color: var(--error);
    border: 1px solid var(--error);
}

.louna-badge.info {
    background: rgba(78, 205, 196, 0.2);
    color: var(--secondary-cyan);
    border: 1px solid var(--secondary-cyan);
}
