/**
 * Styles pour le système d'alertes de performance
 */

/* Conteneur des alertes */
.performance-alerts-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 5px;
}

/* Barre de défilement personnalisée */
.performance-alerts-container::-webkit-scrollbar {
    width: 5px;
}

.performance-alerts-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.performance-alerts-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
}

.performance-alerts-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Alerte individuelle */
.performance-alert {
    color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    transition: transform 0.2s, opacity 0.2s;
}

.performance-alert:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Variantes de sévérité */
.performance-alert-critical {
    background-color: rgba(220, 53, 69, 0.9);
    border-left: 4px solid #dc3545;
}

.performance-alert-warning {
    background-color: rgba(255, 193, 7, 0.9);
    border-left: 4px solid #ffc107;
    color: #212529;
}

.performance-alert-warning .alert-action-btn {
    color: #212529 !important;
}

.performance-alert-info {
    background-color: rgba(23, 162, 184, 0.9);
    border-left: 4px solid #17a2b8;
}

/* En-tête de l'alerte */
.performance-alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.performance-alert-title {
    font-weight: bold;
    font-size: 16px;
}

.performance-alert-actions {
    display: flex;
    gap: 5px;
}

/* Contenu de l'alerte */
.performance-alert-content {
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.4;
}

/* Pied de l'alerte */
.performance-alert-footer {
    font-size: 12px;
    opacity: 0.8;
    display: flex;
    justify-content: space-between;
}

/* Boutons d'action */
.alert-action-btn {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    opacity: 0.8;
    transition: opacity 0.2s;
    padding: 2px;
}

.alert-action-btn:hover {
    opacity: 1;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

/* Tableau de bord des alertes */
.alerts-dashboard {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.alerts-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.alerts-dashboard-title {
    font-size: 20px;
    font-weight: bold;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.alerts-dashboard-title i {
    color: var(--accent);
}

.alerts-dashboard-actions {
    display: flex;
    gap: 10px;
}

.alerts-dashboard-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 10px;
}

.alerts-dashboard-tab {
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 500;
    color: var(--text-secondary);
}

.alerts-dashboard-tab:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.alerts-dashboard-tab.active {
    background-color: var(--accent);
    color: #000;
    font-weight: bold;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alert-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 15px;
    transition: background-color 0.2s;
    border-left: 4px solid transparent;
}

.alert-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.alert-item.critical {
    border-left-color: #dc3545;
}

.alert-item.warning {
    border-left-color: #ffc107;
}

.alert-item.info {
    border-left-color: #17a2b8;
}

.alert-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.alert-item-title {
    font-weight: bold;
    color: var(--text-primary);
}

.alert-item-badge {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
}

.alert-item-badge.critical {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.alert-item-badge.warning {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.alert-item-badge.info {
    background-color: rgba(23, 162, 184, 0.2);
    color: #17a2b8;
}

.alert-item-content {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 10px;
    line-height: 1.4;
}

.alert-item-footer {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-secondary);
}

.alert-item-category {
    display: flex;
    align-items: center;
    gap: 5px;
}

.alert-item-category i {
    color: var(--accent);
}

.alert-item-date {
    opacity: 0.8;
}

.alert-item-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    justify-content: flex-end;
}

.alert-item-action {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-primary);
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.alert-item-action:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.alert-item-action i {
    font-size: 14px;
}

.alert-item-action.primary {
    background-color: var(--accent);
    color: #000;
}

.alert-item-action.primary:hover {
    background-color: var(--primary-light);
}

.alert-item-action.danger {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.alert-item-action.danger:hover {
    background-color: rgba(220, 53, 69, 0.3);
}

.no-alerts {
    padding: 30px;
    text-align: center;
    color: var(--text-secondary);
}

.no-alerts i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-alerts p {
    font-size: 16px;
}

/* Compteur d'alertes */
.alerts-counter {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.alerts-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
