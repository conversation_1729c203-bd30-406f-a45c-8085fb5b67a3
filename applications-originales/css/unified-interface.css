/**
 * Style unifié pour toutes les interfaces Louna
 * Cohérence visuelle avec la page d'accueil
 */

/* ===== VARIABLES GLOBALES ===== */
:root {
    /* Couleurs principales */
    --primary-pink: #ff69b4;
    --primary-blue: #54a0ff;
    --accent-cyan: #00ffff;
    --bg-dark: #1a1a2e;
    --bg-card: rgba(255, 255, 255, 0.15);
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --border-color: rgba(255, 255, 255, 0.3);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #ff6b6b, #54a0ff);
    --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    --gradient-button: linear-gradient(135deg, #ff69b4, #ff1493);

    /* Ombres */
    --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.3);
    --shadow-button: 0 5px 15px rgba(255, 105, 180, 0.4);
    --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

/* ===== HEADER UNIFIÉ ===== */
.unified-header {
    background: var(--gradient-primary);
    padding: 8px 20px;
    border-radius: 15px;
    margin-bottom: 5px;
    box-shadow: var(--shadow-card);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.unified-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%);
    background-size: 20px 20px;
    animation: shimmer 20s linear infinite;
}

@keyframes shimmer {
    0% { background-position: 0 0; }
    100% { background-position: 40px 40px; }
}

.unified-header h1 {
    font-size: 2.5rem;
    margin: 0 0 10px 0;
    color: var(--text-primary);
    text-shadow: var(--text-shadow);
    font-weight: bold;
    position: relative;
    z-index: 1;
}

.unified-header .subtitle {
    font-size: 1.2rem;
    color: var(--text-primary);
    text-shadow: var(--text-shadow);
    margin: 0;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* ===== NAVIGATION UNIFIÉE ===== */
.unified-nav {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 8px;
    margin-bottom: 5px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-card);
}

.unified-nav .nav-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.unified-nav .nav-button {
    padding: 12px 24px;
    background: var(--gradient-button);
    color: var(--text-primary);
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    text-shadow: var(--text-shadow);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.unified-nav .nav-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.6);
    background: linear-gradient(135deg, #ff1493, #ff69b4);
}

.unified-nav .nav-button.active {
    background: linear-gradient(135deg, #00ffff, #0080ff);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
}

.unified-nav .nav-button i {
    font-size: 1.1rem;
}

/* ===== CONTENEUR PRINCIPAL UNIFIÉ ===== */
.unified-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 5px 15px;
    min-height: calc(100vh - 80px);
}

/* ===== CARTES UNIFIÉES ===== */
.unified-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-card);
    transition: all 0.3s ease;
}

.unified-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.unified-card h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin: 0 0 15px 0;
    text-shadow: var(--text-shadow);
    font-weight: bold;
}

.unified-card h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    margin: 0 0 10px 0;
    text-shadow: var(--text-shadow);
    font-weight: 600;
}

.unified-card p {
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0 0 15px 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    font-weight: 400;
}

/* ===== BOUTONS UNIFIÉS ===== */
.unified-button {
    padding: 12px 24px;
    background: var(--gradient-button);
    color: var(--text-primary);
    border: none;
    border-radius: 25px;
    font-weight: bold;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    text-shadow: var(--text-shadow);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin: 5px;
}

.unified-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.6);
    background: linear-gradient(135deg, #ff1493, #ff69b4);
}

.unified-button.secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border: 1px solid var(--border-color);
}

.unified-button.secondary:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    border-color: rgba(255, 255, 255, 0.5);
}

.unified-button.success {
    background: linear-gradient(135deg, #00ff88, #00cc66);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
}

.unified-button.danger {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
}

.unified-button.info {
    background: linear-gradient(135deg, #00ffff, #0080ff);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
}

/* ===== GRILLES UNIFIÉES ===== */
.unified-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 30px;
}

.unified-grid.cols-2 {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.unified-grid.cols-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.unified-grid.cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* ===== FORMULAIRES UNIFIÉS ===== */
.unified-form {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-card);
}

.unified-form .form-group {
    margin-bottom: 20px;
}

.unified-form label {
    display: block;
    color: var(--text-primary);
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: var(--text-shadow);
}

.unified-form input,
.unified-form textarea,
.unified-form select {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.unified-form input:focus,
.unified-form textarea:focus,
.unified-form select:focus {
    outline: none;
    border-color: var(--primary-pink);
    box-shadow: 0 0 15px rgba(255, 105, 180, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.unified-form input::placeholder,
.unified-form textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* ===== STATUTS ET BADGES UNIFIÉS ===== */
.unified-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: bold;
    text-shadow: var(--text-shadow);
}

.unified-status.active {
    background: linear-gradient(135deg, #00ff88, #00cc66);
    color: var(--text-primary);
}

.unified-status.inactive {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: var(--text-primary);
}

.unified-status.warning {
    background: linear-gradient(135deg, #ffa502, #ff6348);
    color: var(--text-primary);
}

.unified-status.info {
    background: linear-gradient(135deg, #00ffff, #0080ff);
    color: var(--text-primary);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .unified-header h1 {
        font-size: 2rem;
    }

    .unified-header .subtitle {
        font-size: 1rem;
    }

    .unified-nav .nav-buttons {
        justify-content: center;
    }

    .unified-grid.cols-2,
    .unified-grid.cols-3,
    .unified-grid.cols-4 {
        grid-template-columns: 1fr;
    }

    .unified-container {
        padding: 15px;
    }

    .unified-card {
        padding: 20px;
    }
}

/* ===== ANIMATIONS GLOBALES ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.unified-card,
.unified-header,
.unified-nav {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== SCROLLBAR UNIFIÉE ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-button);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff1493, #ff69b4);
}

/* ===== CORRECTIONS GLOBALES POUR COHÉRENCE ===== */

/* Forcer le style unifié sur tous les éléments de carte existants */
.card, .thermal-card, .kyber-card, .monitor-card, .studio-card,
.video-card, .editor-card, .performance-card, .presentation-card,
.memory-card, .brain-card, .qi-card, .neuron-card {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 15px !important;
    padding: 25px !important;
    margin-bottom: 25px !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: var(--shadow-card) !important;
    transition: all 0.3s ease !important;
}

/* Forcer le style unifié sur tous les boutons existants */
.btn, .button, .control-btn, .action-btn, .submit-btn, .cancel-btn,
.primary-btn, .secondary-btn, .success-btn, .danger-btn, .info-btn,
.warning-btn, .thermal-btn, .kyber-btn, .monitor-btn, .studio-btn,
.video-btn, .editor-btn, .performance-btn, .presentation-btn,
.memory-btn, .brain-btn, .qi-btn, .neuron-btn {
    padding: 12px 24px !important;
    background: var(--gradient-button) !important;
    color: var(--text-primary) !important;
    border: none !important;
    border-radius: 25px !important;
    font-weight: bold !important;
    font-size: 0.95rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: var(--shadow-button) !important;
    text-shadow: var(--text-shadow) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    margin: 5px !important;
}

.btn:hover, .button:hover, .control-btn:hover, .action-btn:hover,
.submit-btn:hover, .cancel-btn:hover, .primary-btn:hover,
.secondary-btn:hover, .success-btn:hover, .danger-btn:hover,
.info-btn:hover, .warning-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.6) !important;
    background: linear-gradient(135deg, #ff1493, #ff69b4) !important;
}

/* Forcer le style unifié sur tous les titres */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
    text-shadow: var(--text-shadow) !important;
    font-weight: bold !important;
}

/* Forcer le style unifié sur tous les textes */
p, span, div, label, .text, .description, .content {
    color: var(--text-primary) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
}

/* Forcer le style unifié sur tous les conteneurs principaux */
.container, .main-container, .content-container, .page-container,
.thermal-container, .kyber-container, .monitor-container,
.studio-container, .video-container, .editor-container,
.performance-container, .presentation-container, .memory-container,
.brain-container, .qi-container, .neuron-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 20px !important;
    min-height: calc(100vh - 200px) !important;
}

/* Forcer le style unifié sur toutes les grilles */
.grid, .main-grid, .content-grid, .feature-grid, .thermal-grid,
.kyber-grid, .monitor-grid, .studio-grid, .video-grid, .editor-grid,
.performance-grid, .presentation-grid, .memory-grid, .brain-grid,
.qi-grid, .neuron-grid {
    display: grid !important;
    gap: 25px !important;
    margin-bottom: 30px !important;
}

/* Styles spécifiques pour les grilles 2 colonnes */
.grid-2, .cols-2 {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
}

/* Styles spécifiques pour les grilles 3 colonnes */
.grid-3, .cols-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
}

/* Styles spécifiques pour les grilles 4 colonnes */
.grid-4, .cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
}

/* Forcer le style unifié sur tous les formulaires */
.form, .form-container, .input-container {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 15px !important;
    padding: 25px !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: var(--shadow-card) !important;
}

/* Forcer le style unifié sur tous les inputs */
input, textarea, select {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 10px !important;
    color: var(--text-primary) !important;
    padding: 12px 16px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
}

input:focus, textarea:focus, select:focus {
    outline: none !important;
    border-color: var(--primary-pink) !important;
    box-shadow: 0 0 15px rgba(255, 105, 180, 0.3) !important;
    background: rgba(255, 255, 255, 0.15) !important;
}

/* Forcer le style unifié sur tous les statuts */
.status, .badge, .tag, .label {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 6px 12px !important;
    border-radius: 20px !important;
    font-size: 0.85rem !important;
    font-weight: bold !important;
    text-shadow: var(--text-shadow) !important;
}

.status.active, .badge.active, .tag.active, .label.active {
    background: linear-gradient(135deg, #00ff88, #00cc66) !important;
    color: var(--text-primary) !important;
}

.status.inactive, .badge.inactive, .tag.inactive, .label.inactive {
    background: linear-gradient(135deg, #ff4757, #ff3742) !important;
    color: var(--text-primary) !important;
}

/* Corrections spécifiques pour la lisibilité */
.interface-title, .page-title, .section-title {
    color: var(--text-primary) !important;
    text-shadow: var(--text-shadow) !important;
    font-weight: bold !important;
}

.interface-subtitle, .page-subtitle, .section-subtitle {
    color: var(--text-primary) !important;
    text-shadow: var(--text-shadow) !important;
    opacity: 0.9 !important;
}

/* Assurer la cohérence des icônes */
i, .icon, .fa, .fas, .far, .fab {
    text-shadow: var(--text-shadow) !important;
}

/* Corrections pour les panneaux latéraux */
.sidebar, .panel, .widget {
    background: var(--bg-card) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: var(--shadow-card) !important;
}

/* Corrections pour les tableaux */
table {
    background: var(--bg-card) !important;
    border-radius: 15px !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-card) !important;
}

th, td {
    color: var(--text-primary) !important;
    text-shadow: var(--text-shadow) !important;
    border-color: var(--border-color) !important;
}

/* Corrections pour les listes */
ul, ol, li {
    color: var(--text-primary) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
}

/* Corrections pour les liens */
a {
    color: var(--primary-pink) !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

a:hover {
    color: var(--primary-blue) !important;
    text-shadow: 0 0 10px currentColor !important;
}
