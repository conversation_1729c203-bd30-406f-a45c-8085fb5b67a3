/* ===== THÈME UNIFIÉ LOUNA 2025 ===== */
/* Système de design cohérent pour toutes les interfaces */

:root {
    /* Couleurs principales */
    --primary-color: #e91e63;
    --primary-dark: #ad1457;
    --secondary-color: #4ecdc4;
    --accent-color: #ff6b6b;
    
    /* Couleurs de fond */
    --bg-primary: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    --bg-secondary: rgba(255, 255, 255, 0.1);
    --bg-card: rgba(255, 255, 255, 0.15);
    --bg-nav: rgba(0, 0, 0, 0.8);
    
    /* Couleurs de texte */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-muted: rgba(255, 255, 255, 0.7);
    
    /* Bordures et ombres */
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-nav: 0 4px 20px rgba(0, 0, 0, 0.5);
    
    /* Espacements */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    
    /* Rayons de bordure */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== RESET ET BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== NAVIGATION UNIFIÉE ===== */
.louna-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--bg-nav);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-nav);
    z-index: 1000;
    padding: var(--spacing-sm) var(--spacing-md);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-logo i {
    font-size: 1.8rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--text-secondary);
    transition: var(--transition-normal);
    font-size: 0.9rem;
}

.nav-item:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.nav-home-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
    transition: var(--transition-normal);
}

.nav-home-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

/* ===== CONTENEUR PRINCIPAL ===== */
.louna-container {
    margin-top: 80px;
    padding: var(--spacing-md);
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== EN-TÊTE DE PAGE ===== */
.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg) 0;
}

.page-title {
    font-size: 3rem;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== CARTES UNIFIÉES ===== */
.louna-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-card);
    transition: var(--transition-normal);
}

.louna-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--text-primary);
}

.card-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* ===== GRILLES UNIFIÉES ===== */
.louna-grid {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* ===== BOUTONS UNIFIÉS ===== */
.louna-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-card);
    border-color: var(--primary-color);
}

.btn-success {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

/* ===== NAVIGATION RAPIDE ===== */
.quick-nav {
    position: fixed;
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: 999;
}

.quick-nav-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
}

.quick-nav-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .louna-container {
        padding: var(--spacing-sm);
    }
    
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .quick-nav {
        bottom: var(--spacing-sm);
        right: var(--spacing-sm);
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== UTILITAIRES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.hidden { display: none; }
.visible { display: block; }
