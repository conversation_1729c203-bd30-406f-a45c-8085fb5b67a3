<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Générateur 3D IA - Louna QI 235</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh; color: white;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .nav-buttons { display: flex; justify-content: center; gap: 15px; margin-bottom: 30px; flex-wrap: wrap; }
        .nav-btn {
            background: rgba(255,255,255,0.2); border: none; padding: 10px 20px; border-radius: 25px;
            color: white; text-decoration: none; transition: all 0.3s ease; backdrop-filter: blur(10px);
        }
        .nav-btn:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }
        .main-content { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .panel {
            background: rgba(255,255,255,0.1); border-radius: 20px; padding: 30px;
            backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%; padding: 12px; border: none; border-radius: 10px;
            background: rgba(255,255,255,0.2); color: white; font-size: 16px;
        }
        .generate-btn {
            width: 100%; padding: 15px; background: linear-gradient(45deg, #3498db, #2980b9);
            border: none; border-radius: 15px; color: white; font-size: 18px; font-weight: 600;
            cursor: pointer; transition: all 0.3s ease;
        }
        .generate-btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0,0,0,0.2); }
        .model-viewer {
            background: rgba(0,0,0,0.3); border-radius: 15px; height: 300px;
            display: flex; align-items: center; justify-content: center; margin-bottom: 20px;
        }
        .rotating-cube {
            width: 80px; height: 80px; position: relative; transform-style: preserve-3d;
            animation: rotate 4s linear infinite;
        }
        .cube-face {
            position: absolute; width: 80px; height: 80px;
            background: rgba(52, 152, 219, 0.8); border: 2px solid rgba(255,255,255,0.3);
        }
        .cube-face.front { transform: rotateY(0deg) translateZ(40px); }
        .cube-face.back { transform: rotateY(180deg) translateZ(40px); }
        .cube-face.right { transform: rotateY(90deg) translateZ(40px); }
        .cube-face.left { transform: rotateY(-90deg) translateZ(40px); }
        .cube-face.top { transform: rotateX(90deg) translateZ(40px); }
        .cube-face.bottom { transform: rotateX(-90deg) translateZ(40px); }
        @keyframes rotate { 0% { transform: rotateX(0deg) rotateY(0deg); } 100% { transform: rotateX(360deg) rotateY(360deg); } }
        .progress-bar { width: 100%; height: 8px; background: rgba(255,255,255,0.2); border-radius: 4px; overflow: hidden; margin: 20px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #3498db, #2980b9); width: 0%; transition: width 0.3s ease; }
        @media (max-width: 768px) { .main-content { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Générateur 3D IA</h1>
            <p>Louna QI 235 - Modélisation 3D Avancée</p>
        </div>

        <div class="nav-buttons">
            <a href="../interface-originale-complete.html" class="nav-btn"><i class="fas fa-home"></i> Accueil</a>
            <a href="generation-center.html" class="nav-btn"><i class="fas fa-magic"></i> Centre Génération</a>
            <a href="music-generator.html" class="nav-btn"><i class="fas fa-music"></i> Musique IA</a>
        </div>

        <div class="main-content">
            <div class="panel">
                <h2><i class="fas fa-cube"></i> Créer un Modèle 3D</h2>
                
                <form id="modelForm">
                    <div class="form-group">
                        <label for="prompt">Description du modèle :</label>
                        <textarea id="prompt" placeholder="Ex: Un château médiéval avec des tours et des remparts..." rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="type">Type de modèle :</label>
                        <select id="type">
                            <option value="object">Objet</option>
                            <option value="character">Personnage</option>
                            <option value="building">Bâtiment</option>
                            <option value="vehicle">Véhicule</option>
                            <option value="nature">Nature</option>
                            <option value="scene">Scène complète</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="format">Format d'export :</label>
                        <select id="format">
                            <option value="OBJ">OBJ (Standard)</option>
                            <option value="FBX">FBX (Animation)</option>
                            <option value="GLTF">GLTF (Web)</option>
                            <option value="STL">STL (Impression 3D)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="quality">Qualité :</label>
                        <select id="quality">
                            <option value="low">Basse (Rapide)</option>
                            <option value="medium">Moyenne (Équilibrée)</option>
                            <option value="high">Haute (Détaillée)</option>
                            <option value="ultra">Ultra (Maximum)</option>
                        </select>
                    </div>

                    <button type="submit" class="generate-btn" id="generateBtn">
                        <i class="fas fa-magic"></i> Générer le Modèle 3D
                    </button>
                </form>
            </div>

            <div class="panel">
                <h2><i class="fas fa-eye"></i> Aperçu 3D</h2>
                
                <div class="model-viewer" id="modelViewer">
                    <div class="rotating-cube">
                        <div class="cube-face front"></div>
                        <div class="cube-face back"></div>
                        <div class="cube-face right"></div>
                        <div class="cube-face left"></div>
                        <div class="cube-face top"></div>
                        <div class="cube-face bottom"></div>
                    </div>
                </div>

                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <p id="statusText">Prêt à générer un modèle 3D</p>
            </div>
        </div>
    </div>

    <script>
        let currentModel = null;

        async function generateModel(formData) {
            document.getElementById('generateBtn').disabled = true;
            document.getElementById('progressBar').style.display = 'block';
            document.getElementById('statusText').textContent = 'Génération en cours...';
            
            try {
                const response = await fetch('/api/generation/3d', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentModel = result.model;
                    simulateProgress();
                } else {
                    document.getElementById('statusText').textContent = 'Erreur de génération';
                    resetForm();
                }
            } catch (error) {
                document.getElementById('statusText').textContent = 'Erreur de connexion';
                resetForm();
            }
        }

        function simulateProgress() {
            const progressFill = document.getElementById('progressFill');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += 2;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('statusText').textContent = 'Modèle 3D généré avec succès!';
                    resetForm();
                }
            }, 50);
        }

        function resetForm() {
            document.getElementById('generateBtn').disabled = false;
            setTimeout(() => {
                document.getElementById('progressBar').style.display = 'none';
                document.getElementById('progressFill').style.width = '0%';
            }, 2000);
        }

        document.getElementById('modelForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                prompt: document.getElementById('prompt').value,
                type: document.getElementById('type').value,
                format: document.getElementById('format').value,
                quality: document.getElementById('quality').value
            };
            
            await generateModel(formData);
        });
    
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
