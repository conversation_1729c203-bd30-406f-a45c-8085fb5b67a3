<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Louna AI Mobile - Reconnaissance Vocale <PERSON></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #1a1a1a;
            color: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .mobile-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: #2d2d2d;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .conversation {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 12px;
            max-width: 85%;
        }
        
        .message.user {
            background: #007AFF;
            margin-left: auto;
            text-align: right;
        }
        
        .message.ai {
            background: #333;
        }
        
        .message.system {
            background: #444;
            text-align: center;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .input-area {
            background: #2d2d2d;
            padding: 15px;
            display: flex;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 20px;
            background: #444;
            color: white;
            font-size: 16px;
            outline: none;
        }
        
        .send-btn, .camera-btn, .voice-btn {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .camera-btn.active {
            background: #FF9500;
        }
        
        .voice-btn.recording {
            background: #FF3B30;
            animation: pulse 1s infinite;
        }
        
        .camera-preview {
            width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin: 10px 0;
            display: none;
        }
        
        .voice-status {
            background: #333;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            text-align: center;
            font-size: 14px;
        }
        
        .voice-status.listening {
            background: #FF3B30;
            animation: pulse 1s infinite;
        }
        
        .voice-status.speaking {
            background: #4CAF50;
        }
        
        .learning-stats {
            background: #2d2d2d;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .sleep-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: #FF9500;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Bouton retour ajouté automatiquement -->
    <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
        <a href="../interface-originale-complete.html" style="
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255,105,180,0.3);
            transition: all 0.3s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
            🏠 Accueil
        </a>
    </div>
    <div class="mobile-container">
        <div class="header">
            <div class="status">
                <div class="status-dot"></div>
                <span>Louna AI - Vocal Avancé</span>
            </div>
            <div>
                <button onclick="toggleSleep()" id="sleep-btn" class="sleep-btn">💤 Veille</button>
            </div>
        </div>
        
        <div class="voice-status" id="voice-status">
            🎤 Reconnaissance vocale prête - Cliquez pour parler
        </div>
        
        <div class="learning-stats" id="learning-stats">
            📚 Apprentissage: 0 mots | 🎯 Précision: 85% | 🧠 Compréhension: 85%
        </div>
        
        <div class="conversation" id="conversation">
            <div class="message system">
                📱 Louna AI Mobile avec reconnaissance vocale avancée
            </div>
            <div class="message ai">
                Bonjour ! Je suis équipé d'une reconnaissance vocale avancée avec apprentissage continu. Vous pouvez me parler directement et je m'améliore à chaque conversation !
            </div>
        </div>
        
        <video class="camera-preview" id="camera-preview" autoplay muted></video>
        
        <div class="input-area">
            <input 
                type="text" 
                class="message-input" 
                id="message-input" 
                placeholder="Tapez ou parlez à Louna..."
            >
            <button class="voice-btn" id="voice-btn" onclick="toggleVoice()">🎤</button>
            <button class="camera-btn" id="camera-btn" onclick="toggleCamera()">📷</button>
            <button class="send-btn" onclick="sendMessage()">➤</button>
        </div>
    </div>

    <script>
        // Variables globales
        let cameraStream = null;
        let isSleeping = false;
        
        // Variables pour reconnaissance vocale avancée
        let speechRecognition = null;
        let isListening = false;
        let isSpeaking = false;
        let learningStats = {
            wordsLearned: 0,
            accuracy: 85,
            comprehension: 85,
            sessionsCount: 0
        };
        
        // Vocabulaire d'apprentissage
        let vocabulary = new Map();
        let speechPatterns = new Map();
        
        // Initialisation
        window.addEventListener('load', function() {
            initializeSpeechRecognition();
            loadLearningData();
            updateLearningStats();
        });
        
        // Initialiser la reconnaissance vocale avancée
        function initializeSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                speechRecognition = new SpeechRecognition();
                
                // Configuration avancée pour apprentissage
                speechRecognition.continuous = false;
                speechRecognition.interimResults = true;
                speechRecognition.lang = 'fr-FR';
                speechRecognition.maxAlternatives = 3;
                
                // Gestionnaires d'événements
                speechRecognition.onstart = function() {
                    isListening = true;
                    updateVoiceStatus('listening', '🎤 Écoute en cours... Parlez maintenant !');
                    updateVoiceButton();
                };
                
                speechRecognition.onresult = function(event) {
                    handleSpeechResult(event);
                };
                
                speechRecognition.onerror = function(event) {
                    addMessage('system', '❌ Erreur reconnaissance: ' + event.error);
                    isListening = false;
                    updateVoiceStatus('ready', '🎤 Reconnaissance vocale prête');
                    updateVoiceButton();
                };
                
                speechRecognition.onend = function() {
                    isListening = false;
                    updateVoiceStatus('ready', '🎤 Reconnaissance vocale prête');
                    updateVoiceButton();
                };
                
                addMessage('system', '✅ Reconnaissance vocale avancée initialisée');
                return true;
            } else {
                addMessage('system', '❌ Reconnaissance vocale non supportée');
                return false;
            }
        }
        
        // Traitement avancé des résultats de reconnaissance
        function handleSpeechResult(event) {
            let finalTranscript = '';
            let interimTranscript = '';
            
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                const confidence = event.results[i][0].confidence;
                
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                    
                    // Apprentissage du vocabulaire
                    learnFromSpeech(finalTranscript, confidence);
                    
                    // Afficher la transcription
                    addMessage('user', finalTranscript);
                    addMessage('system', `🎤 Transcription (${(confidence * 100).toFixed(1)}% confiance)`);
                    
                    // Traitement et réponse
                    setTimeout(() => {
                        const response = generateAdvancedResponse(finalTranscript);
                        addMessage('ai', response);
                        
                        // Synthèse vocale de la réponse
                        speakResponse(response);
                    }, 500);
                    
                } else {
                    interimTranscript += transcript;
                    // Affichage optionnel de la transcription en cours
                    updateVoiceStatus('listening', `🎤 "${interimTranscript}"`);
                }
            }
        }
        
        // Apprentissage depuis la parole
        function learnFromSpeech(transcript, confidence) {
            const words = transcript.toLowerCase().split(/\s+/);
            let newWordsCount = 0;
            
            words.forEach(word => {
                const cleanWord = word.replace(/[^\w]/g, '');
                if (cleanWord.length < 2) return;
                
                if (vocabulary.has(cleanWord)) {
                    const data = vocabulary.get(cleanWord);
                    data.frequency += 1;
                    data.confidence = (data.confidence + confidence) / 2;
                    data.lastUsed = Date.now();
                } else {
                    vocabulary.set(cleanWord, {
                        frequency: 1,
                        confidence: confidence,
                        lastUsed: Date.now(),
                        context: 'learned'
                    });
                    newWordsCount++;
                }
            });
            
            // Mettre à jour les statistiques
            learningStats.wordsLearned += newWordsCount;
            learningStats.sessionsCount++;
            
            // Améliorer la précision basée sur la confiance
            if (confidence > 0.8) {
                learningStats.accuracy = Math.min(99, learningStats.accuracy + 0.5);
                learningStats.comprehension = Math.min(99, learningStats.comprehension + 0.3);
            }
            
            updateLearningStats();
            saveLearningData();
            
            if (newWordsCount > 0) {
                addMessage('system', `📚 ${newWordsCount} nouveau(x) mot(s) appris !`);
            }
        }
        
        // Génération de réponse avancée basée sur l'apprentissage
        function generateAdvancedResponse(message) {
            const lower = message.toLowerCase();
            const words = lower.split(/\s+/);
            
            // Analyser le contexte basé sur le vocabulaire appris
            let context = 'general';
            let confidence = 0.5;
            
            words.forEach(word => {
                if (vocabulary.has(word)) {
                    const data = vocabulary.get(word);
                    confidence += data.confidence * 0.1;
                    if (data.context) {
                        context = data.context;
                    }
                }
            });
            
            // Réponses adaptées au niveau d'apprentissage
            const responses = {
                greeting: [
                    `Bonjour ! J'ai appris ${learningStats.wordsLearned} mots grâce à nos conversations. Comment puis-je vous aider ?`,
                    `Salut ! Ma compréhension s'améliore (${learningStats.comprehension}%). Que souhaitez-vous faire ?`,
                    `Hello ! Avec ${vocabulary.size} mots en mémoire, je suis prêt à vous assister !`
                ],
                camera: [
                    "Activez la caméra et je pourrai analyser ce que vous me montrez avec ma vision artificielle.",
                    "Parfait ! Montrez-moi ce que vous voulez que j'examine visuellement.",
                    "Excellente idée ! Ma vision artificielle est prête pour l'analyse."
                ],
                help: [
                    `Je peux vous aider avec diverses tâches. Mon niveau de compréhension est de ${learningStats.comprehension}%.`,
                    "Je suis là pour vous assister ! Que voulez-vous faire ?",
                    "Comment puis-je utiliser mes capacités pour vous aider ?"
                ],
                learning: [
                    `J'apprends continuellement ! J'ai déjà mémorisé ${learningStats.wordsLearned} mots.`,
                    `Mon apprentissage progresse : ${learningStats.accuracy}% de précision vocale.`,
                    "Je m'améliore à chaque conversation grâce à l'apprentissage automatique !"
                ]
            };
            
            // Détection d'intention améliorée
            if (lower.includes('bonjour') || lower.includes('salut') || lower.includes('hello')) {
                context = 'greeting';
            } else if (lower.includes('caméra') || lower.includes('voir') || lower.includes('regarder')) {
                context = 'camera';
            } else if (lower.includes('aide') || lower.includes('help') || lower.includes('capacités')) {
                context = 'help';
            } else if (lower.includes('apprend') || lower.includes('apprentissage') || lower.includes('progrès')) {
                context = 'learning';
            }
            
            const responseList = responses[context] || responses.help;
            return responseList[Math.floor(Math.random() * responseList.length)];
        }
        
        // Synthèse vocale avancée
        function speakResponse(text) {
            if ('speechSynthesis' in window && !isSpeaking) {
                isSpeaking = true;
                updateVoiceStatus('speaking', '🔊 Louna parle...');
                
                const utterance = new SpeechSynthesisUtterance(text);
                
                // Sélection de la meilleure voix française
                const voices = speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => 
                    voice.lang.startsWith('fr') && voice.localService
                ) || voices.find(voice => voice.lang.startsWith('fr'));
                
                if (frenchVoice) {
                    utterance.voice = frenchVoice;
                }
                
                // Configuration optimisée
                utterance.rate = 0.9;
                utterance.pitch = 1.0;
                utterance.volume = 0.8;
                
                utterance.onend = function() {
                    isSpeaking = false;
                    updateVoiceStatus('ready', '🎤 Reconnaissance vocale prête');
                };
                
                utterance.onerror = function() {
                    isSpeaking = false;
                    updateVoiceStatus('ready', '🎤 Reconnaissance vocale prête');
                };
                
                speechSynthesis.speak(utterance);
            }
        }
        
        // Gestion de l'interface vocale
        function toggleVoice() {
            if (!speechRecognition) {
                addMessage('system', '❌ Reconnaissance vocale non disponible');
                return;
            }
            
            if (!isListening && !isSpeaking) {
                try {
                    speechRecognition.start();
                } catch (error) {
                    addMessage('system', '❌ Erreur démarrage: ' + error.message);
                }
            } else if (isListening) {
                speechRecognition.stop();
            }
        }
        
        function updateVoiceButton() {
            const btn = document.getElementById('voice-btn');
            if (isListening) {
                btn.classList.add('recording');
                btn.innerHTML = '⏹️';
            } else {
                btn.classList.remove('recording');
                btn.innerHTML = '🎤';
            }
        }
        
        function updateVoiceStatus(status, message) {
            const statusEl = document.getElementById('voice-status');
            statusEl.textContent = message;
            statusEl.className = 'voice-status ' + status;
        }
        
        function updateLearningStats() {
            const statsEl = document.getElementById('learning-stats');
            statsEl.textContent = `📚 Apprentissage: ${learningStats.wordsLearned} mots | 🎯 Précision: ${learningStats.accuracy}% | 🧠 Compréhension: ${learningStats.comprehension}%`;
        }
        
        // Sauvegarde et chargement des données d'apprentissage
        function saveLearningData() {
            try {
                const data = {
                    vocabulary: Array.from(vocabulary.entries()),
                    stats: learningStats,
                    timestamp: Date.now()
                };
                localStorage.setItem('louna_learning_data', JSON.stringify(data));
            } catch (error) {
                console.error('Erreur sauvegarde:', error);
            }
        }
        
        function loadLearningData() {
            try {
                const data = localStorage.getItem('louna_learning_data');
                if (data) {
                    const parsed = JSON.parse(data);
                    vocabulary = new Map(parsed.vocabulary || []);
                    learningStats = { ...learningStats, ...parsed.stats };
                    addMessage('system', `📚 ${vocabulary.size} mots chargés depuis la mémoire`);
                }
            } catch (error) {
                console.error('Erreur chargement:', error);
            }
        }
        
        // Fonctions existantes adaptées
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            if (!message) return;
            
            addMessage('user', message);
            input.value = '';
            
            // Apprentissage depuis le texte aussi
            learnFromSpeech(message, 0.95);
            
            setTimeout(() => {
                const response = generateAdvancedResponse(message);
                addMessage('ai', response);
            }, 1000);
        }
        
        function addMessage(type, content) {
            const conversation = document.getElementById('conversation');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = content;
            conversation.appendChild(messageDiv);
            conversation.scrollTop = conversation.scrollHeight;
        }
        
        function toggleCamera() {
            const btn = document.getElementById('camera-btn');
            const preview = document.getElementById('camera-preview');
            
            if (!cameraStream) {
                navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        facingMode: 'environment',
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    } 
                })
                .then(stream => {
                    cameraStream = stream;
                    preview.srcObject = stream;
                    preview.style.display = 'block';
                    btn.classList.add('active');
                    addMessage('system', '📷 Caméra activée - Louna peut voir');
                })
                .catch(error => {
                    addMessage('system', '❌ Erreur caméra: Vérifiez les permissions');
                });
            } else {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                preview.style.display = 'none';
                btn.classList.remove('active');
                addMessage('system', '📷 Caméra désactivée');
            }
        }
        
        function toggleSleep() {
            const btn = document.getElementById('sleep-btn');
            isSleeping = !isSleeping;
            
            if (isSleeping) {
                btn.textContent = '😴 Endormi';
                btn.style.background = '#666';
                addMessage('system', '💤 Louna en veille - Parlez pour le réveiller');
            } else {
                btn.textContent = '💤 Veille';
                btn.style.background = '#FF9500';
                addMessage('system', '👁️ Louna réveillé et prêt à apprendre');
            }
        }
        
        // Gestion des événements clavier
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Réveil automatique par la voix
        if (speechRecognition) {
            speechRecognition.addEventListener('result', function() {
                if (isSleeping) {
                    toggleSleep();
                    addMessage('system', '🎤 Réveil vocal détecté !');
                }
            });
        }
        
        console.log('📱 Louna AI Mobile avec reconnaissance vocale avancée initialisé');
    </script>
    <!-- Script de mise à jour automatique du QI -->
    <script src="/js/auto-update-qi.js"></script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
