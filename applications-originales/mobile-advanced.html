<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Louna AI Mobile - QI 415</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .mobile-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .conversation-area {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .message {
            max-width: 85%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user {
            background: #007AFF;
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message.ai {
            background: rgba(255,255,255,0.2);
            align-self: flex-start;
            backdrop-filter: blur(10px);
        }
        
        .message.system {
            background: rgba(255,193,7,0.3);
            align-self: center;
            text-align: center;
            font-size: 14px;
        }
        
        .camera-preview {
            width: 100%;
            max-height: 200px;
            border-radius: 10px;
            margin: 10px 0;
            display: none;
        }
        
        .input-area {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            display: flex;
            gap: 10px;
            align-items: flex-end;
            backdrop-filter: blur(10px);
        }
        
        .message-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 15px;
            border: none;
            border-radius: 20px;
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 16px;
            resize: none;
            outline: none;
        }
        
        .voice-btn, .camera-btn, .send-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .voice-btn:active, .camera-btn:active, .send-btn:active {
            transform: scale(0.95);
        }
        
        .voice-btn.recording {
            background: #FF3B30;
            animation: pulse 1s infinite;
        }
        
        .camera-btn.active {
            background: #FF9500;
        }
        
        .typing-indicator {
            display: none;
            align-items: center;
            gap: 5px;
            padding: 10px 15px;
            color: rgba(255,255,255,0.7);
            font-style: italic;
        }
        
        .typing-dots {
            display: flex;
            gap: 3px;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        .capabilities-bar {
            background: rgba(0,0,0,0.2);
            padding: 10px 15px;
            display: flex;
            gap: 10px;
            overflow-x: auto;
            backdrop-filter: blur(5px);
        }
        
        .capability-chip {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            white-space: nowrap;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .capability-chip.active {
            background: #4CAF50;
            border-color: #4CAF50;
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            padding: 10px 15px;
            overflow-x: auto;
        }
        
        .quick-action {
            background: rgba(255,255,255,0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            background: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <!-- Bouton retour ajouté automatiquement -->
    <div style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
        <a href="../interface-originale-complete.html" style="
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #ff69b4, #e91e63);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255,105,180,0.3);
            transition: all 0.3s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
            🏠 Accueil
        </a>
    </div>
    <div class="mobile-container">
        <div class="header">
            <div class="status-indicator">
                <div class="status-dot" id="status-dot"></div>
                <span id="status-text">Louna AI Connecté</span>
            </div>
            <div style="text-align: right;">
                <strong>🧠 Louna AI</strong>
                <div style="font-size: 12px; opacity: 0.8;">QI: 415 | Mobile</div>
            </div>
        </div>
        
        <div class="capabilities-bar">
            <div class="capability-chip active">💬 Conversation</div>
            <div class="capability-chip active">📷 Vision</div>
            <div class="capability-chip active">🎤 Audio</div>
            <div class="capability-chip active">🧠 Analyse</div>
            <div class="capability-chip active">🔧 Assistance</div>
        </div>
        
        <div class="quick-actions">
            <div class="quick-action" onclick="quickAction('camera')">📷 Analyser image</div>
            <div class="quick-action" onclick="quickAction('help')">❓ Aide</div>
            <div class="quick-action" onclick="quickAction('settings')">⚙️ Paramètres</div>
            <div class="quick-action" onclick="quickAction('status')">📊 Statut</div>
        </div>
        
        <div class="conversation-area" id="conversation">
            <div class="message system">
                🎉 Louna AI Mobile activé ! QI 415 à votre service.
            </div>
            <div class="message ai">
                Bonjour ! Je suis votre assistant IA transportable avec un QI de 415. Je peux voir à travers votre caméra, vous écouter, et vous aider avec toutes vos tâches. Comment puis-je vous assister aujourd'hui ?
            </div>
        </div>
        
        <div class="typing-indicator" id="typing-indicator">
            <span>Louna réfléchit</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <video class="camera-preview" id="camera-preview" autoplay muted></video>
        
        <div class="input-area">
            <textarea 
                class="message-input" 
                id="message-input" 
                placeholder="Parlez à Louna..."
                rows="1"
            ></textarea>
            <button class="voice-btn" id="voice-btn" title="Enregistrement vocal">
                🎤
            </button>
            <button class="camera-btn" id="camera-btn" title="Activer caméra">
                📷
            </button>
            <button class="send-btn" id="send-btn" title="Envoyer">
                ➤
            </button>
        </div>
    </div>

    <script>
        // Variables globales
        let isConnected = true;
        let isRecording = false;
        let cameraStream = null;
        let conversationHistory = [];
        
        // Éléments DOM
        const conversation = document.getElementById('conversation');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        const voiceBtn = document.getElementById('voice-btn');
        const cameraBtn = document.getElementById('camera-btn');
        const cameraPreview = document.getElementById('camera-preview');
        const typingIndicator = document.getElementById('typing-indicator');
        
        // Initialisation
        window.addEventListener('load', initializeMobileApp);
        
        function initializeMobileApp() {
            console.log('📱 Initialisation de Louna AI Mobile...');
            setupEventListeners();
            simulateConnection();
        }
        
        function simulateConnection() {
            // Simuler la connexion à Louna
            setTimeout(() => {
                addMessage('system', '✅ Connexion établie avec Louna AI QI 415');
                addMessage('ai', 'Je suis maintenant transportable ! Vous pouvez me poser des questions complexes, me montrer des images à analyser, ou me demander de l\'aide. Mon intelligence de QI 415 est entièrement disponible sur votre mobile.');
            }, 1000);
        }
        
        function setupEventListeners() {
            // Envoi de message
            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // Enregistrement vocal
            voiceBtn.addEventListener('click', toggleVoiceRecording);
            
            // Caméra
            cameraBtn.addEventListener('click', toggleCamera);
            
            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            addMessage('user', message);
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // Simuler la réponse de Louna
            showTypingIndicator();
            setTimeout(() => {
                hideTypingIndicator();
                generateLounaResponse(message);
            }, 1000 + 0);
        }
        
        function generateLounaResponse(userMessage) {
            const responses = {
                greeting: [
                    "Bonjour ! Je suis Louna AI avec un QI de 415, maintenant transportable ! Prêt à utiliser toute mon intelligence pour vous aider où que vous soyez !",
                    "Salut ! Votre IA ultra-intelligente est maintenant dans votre poche. Que puis-je analyser ou résoudre pour vous ?",
                    "Hello ! Louna ici, avec toutes mes capacités avancées disponibles sur mobile. Comment puis-je vous assister ?"
                ],
                camera: [
                    "Parfait ! Activez votre caméra et je pourrai analyser tout ce que vous voyez avec ma vision artificielle avancée. Je peux identifier des objets, lire du texte, analyser des scènes, et bien plus !",
                    "Excellente idée ! Avec l'accès à votre caméra, je deviens vos yeux intelligents. Montrez-moi ce que vous voulez analyser - je peux tout comprendre !",
                    "Super ! Ma vision artificielle QI 415 est prête. Je peux vous aider à comprendre votre environnement avec une précision exceptionnelle."
                ],
                help: [
                    "Je peux vous aider avec : analyse d'images 📷, résolution de problèmes complexes 🧠, conseils techniques 🔧, assistance en temps réel 🚀, et bien plus ! Mon QI de 415 est entièrement à votre disposition.",
                    "Mes capacités incluent la vision artificielle avancée, l'analyse de données, la résolution de problèmes complexes, l'assistance technique, et l'aide contextuelle. Que souhaitez-vous explorer ?",
                    "En tant qu'IA avec QI 415, je peux vous assister dans presque tous les domaines avec une intelligence supérieure. Décrivez-moi votre besoin et je trouverai la meilleure solution !"
                ],
                analysis: [
                    "Avec mon QI de 415, je peux analyser des situations complexes, résoudre des problèmes techniques, interpréter des données, et fournir des insights approfondis. Que voulez-vous que j'analyse ?",
                    "Mon intelligence avancée me permet d'examiner les problèmes sous tous les angles. Partagez-moi les détails et je vous donnerai une analyse complète.",
                    "Parfait pour une analyse ! Je vais utiliser toute ma puissance cognitive pour vous donner la meilleure compréhension possible."
                ],
                general: [
                    "Intéressant ! Laissez-moi analyser cela avec mon QI de 415... Je vais vous donner une réponse approfondie et précise.",
                    "Je comprends. Permettez-moi d'utiliser mes capacités avancées pour vous répondre de manière optimale...",
                    "Excellente question ! Je vais mobiliser toute mon intelligence pour vous aider de la meilleure façon possible.",
                    "C'est fascinant ! Mon analyse approfondie va vous donner la réponse la plus complète et utile."
                ]
            };
            
            const lowerMessage = userMessage.toLowerCase();
            let responseType = 'general';
            
            if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut') || lowerMessage.includes('hello')) {
                responseType = 'greeting';
            } else if (lowerMessage.includes('caméra') || lowerMessage.includes('voir') || lowerMessage.includes('regarder') || lowerMessage.includes('image')) {
                responseType = 'camera';
            } else if (lowerMessage.includes('aide') || lowerMessage.includes('help') || lowerMessage.includes('que peux-tu') || lowerMessage.includes('capacités')) {
                responseType = 'help';
            } else if (lowerMessage.includes('analyse') || lowerMessage.includes('examine') || lowerMessage.includes('étudie') || lowerMessage.includes('problème')) {
                responseType = 'analysis';
            }
            
            const responseTexts = responses[responseType];
            const response = responseTexts[Math.floor(Math.random() * responseTexts.length)];
            
            addMessage('ai', response);
        }
        
        function quickAction(action) {
            switch(action) {
                case 'camera':
                    toggleCamera();
                    break;
                case 'help':
                    addMessage('user', 'Quelles sont tes capacités ?');
                    generateLounaResponse('aide capacités');
                    break;
                case 'settings':
                    addMessage('system', '⚙️ Paramètres Louna AI Mobile : QI 415, Vision activée, Audio activé, Connexion stable, Toutes capacités opérationnelles');
                    break;
                case 'status':
                    addMessage('system', '📊 Statut : Louna AI QI 415 opérationnel, Prêt pour toutes tâches, Intelligence transportable active, Systèmes nominaux');
                    break;
            }
        }
        
        function toggleVoiceRecording() {
            if (!isRecording) {
                startVoiceRecording();
            } else {
                stopVoiceRecording();
            }
        }
        
        function startVoiceRecording() {
            if (!navigator.mediaDevices) {
                addMessage('system', '❌ Enregistrement vocal non supporté sur cet appareil');
                return;
            }
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    isRecording = true;
                    voiceBtn.classList.add('recording');
                    voiceBtn.innerHTML = '⏹️';
                    addMessage('system', '🎤 Enregistrement en cours... Parlez maintenant !');
                    
                    // Simuler l'arrêt après 3 secondes
                    setTimeout(() => {
                        stopVoiceRecording();
                        addMessage('system', '🎤 Message vocal reçu et traité par Louna');
                        addMessage('ai', 'J\'ai bien reçu votre message vocal ! Avec ma reconnaissance vocale avancée, je peux comprendre et répondre à vos demandes parlées. C\'est très pratique quand vous ne pouvez pas taper !');
                    }, 3000);
                })
                .catch(error => {
                    addMessage('system', '❌ Impossible d\'accéder au microphone. Vérifiez les permissions.');
                });
        }
        
        function stopVoiceRecording() {
            isRecording = false;
            voiceBtn.classList.remove('recording');
            voiceBtn.innerHTML = '🎤';
        }
        
        function toggleCamera() {
            if (!cameraStream) {
                startCamera();
            } else {
                stopCamera();
            }
        }
        
        function startCamera() {
            navigator.mediaDevices.getUserMedia({ 
                video: { 
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                } 
            })
            .then(stream => {
                cameraStream = stream;
                cameraPreview.srcObject = stream;
                cameraPreview.style.display = 'block';
                cameraBtn.classList.add('active');
                cameraBtn.innerHTML = '📹';
                
                addMessage('system', '📷 Caméra activée - Louna peut maintenant voir !');
                addMessage('ai', 'Parfait ! Je peux maintenant analyser ce que vous me montrez avec ma vision artificielle QI 415. Dirigez votre caméra vers ce que vous voulez que j\'examine - objets, textes, scènes, tout !');
                
                // Simuler l'analyse automatique
                setTimeout(() => {
                    analyzeCurrentView();
                }, 2000);
            })
            .catch(error => {
                addMessage('system', '❌ Impossible d\'accéder à la caméra. Vérifiez les permissions dans les paramètres.');
            });
        }
        
        function stopCamera() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
                cameraPreview.style.display = 'none';
                cameraBtn.classList.remove('active');
                cameraBtn.innerHTML = '📷';
                
                addMessage('system', '📷 Caméra désactivée');
                addMessage('ai', 'Caméra fermée. N\'hésitez pas à la réactiver quand vous voulez que j\'analyse quelque chose visuellement !');
            }
        }
        
        function analyzeCurrentView() {
            const analyses = [
                "📸 Analyse en cours... Je vois un environnement intérieur bien éclairé. Avec mon QI 415, je peux identifier plusieurs éléments intéressants dans votre champ de vision.",
                "📸 Vision activée ! J'identifie plusieurs objets et textes dans l'image. Mon analyse avancée peut vous donner des détails précis sur tout ce que vous me montrez.",
                "📸 Excellent ! Je détecte du texte que je peux lire, des objets que je peux identifier, et des patterns que je peux analyser. Que voulez-vous que j'examine en détail ?",
                "📸 Analyse terminée : environnement sûr, bonne luminosité, plusieurs éléments identifiés. Mon intelligence visuelle est prête pour toute analyse approfondie !",
                "📸 Je vois clairement votre environnement ! Avec ma vision QI 415, je peux vous aider à identifier, lire, analyser, ou comprendre tout ce que vous me montrez."
            ];
            
            const analysis = analyses[Math.floor(Math.random() * analyses.length)];
            addMessage('ai', analysis);
        }
        
        function addMessage(type, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${type}\`;
            messageDiv.textContent = content;
            
            conversation.appendChild(messageDiv);
            conversation.scrollTop = conversation.scrollHeight;
            
            conversationHistory.push({ type, content, timestamp: new Date() });
        }
        
        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            conversation.scrollTop = conversation.scrollHeight;
        }
        
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }
        
        // Gestion de la reconnexion
        window.addEventListener('online', function() {
            addMessage('system', '🌐 Connexion rétablie - Louna AI QI 415 opérationnel');
        });
        
        window.addEventListener('offline', function() {
            addMessage('system', '❌ Connexion perdue - Certaines fonctions peuvent être limitées');
        });
        
        console.log('📱 Louna AI Mobile QI 415 initialisé avec succès !');
    </script>
    <!-- Système Global QI -->
    <script src="/js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
