<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Gestionnaire de Connexion Directe - Louna</title>
    <link rel="stylesheet" href="css/louna-unified-design.css">
    <style>
        .direct-connection-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            min-height: 100vh;
            color: #ffffff;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 107, 157, 0.2);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background: #00ff88; }
        .status-inactive { background: #ff4757; }
        .status-warning { background: #ffa502; }

        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #00ff88 0%, #00d4aa 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffa502 0%, #ff7675 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b9d;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            text-transform: uppercase;
        }

        .api-list {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .api-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .api-item:last-child {
            border-bottom: none;
        }

        .api-name {
            font-weight: 600;
        }

        .api-latency {
            color: #00ff88;
            font-size: 14px;
        }

        .test-results {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .test-item {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-family: monospace;
            font-size: 14px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b9d;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .navigation-bar {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 107, 157, 0.9);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 107, 157, 1);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="navigation-bar">
        <a href="../interface-originale-complete.html" class="nav-btn">
            🏠 Accueil
        </a>
    </div>

    <div class="direct-connection-container">
        <div class="header">
            <h1>🚀 Gestionnaire de Connexion Directe</h1>
            <p>Optimisation de vitesse sans Ollama - Connexions directes aux APIs d'IA</p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>📡 Statut de Connexion</h3>
                <div id="connection-status">
                    <div class="loading"></div> Vérification...
                </div>
            </div>

            <div class="status-card">
                <h3>⚡ Optimiseur de Vitesse</h3>
                <div id="optimizer-status">
                    <div class="loading"></div> Vérification...
                </div>
            </div>

            <div class="status-card">
                <h3>🎯 API Active</h3>
                <div id="active-api">
                    <div class="loading"></div> Détection...
                </div>
            </div>

            <div class="status-card">
                <h3>📊 Niveau d'Optimisation</h3>
                <div id="optimization-level">
                    <div class="loading"></div> Calcul...
                </div>
            </div>
        </div>

        <div class="control-panel">
            <h3>🎛️ Panneau de Contrôle</h3>
            
            <div class="button-group">
                <button class="btn btn-success" onclick="enableTurboMode()">
                    🚀 Mode TURBO
                </button>
                <button class="btn btn-warning" onclick="disableOllama()">
                    ⚡ Désactiver Ollama
                </button>
                <button class="btn btn-primary" onclick="runSpeedTest()">
                    🧪 Test de Vitesse
                </button>
                <button class="btn btn-danger" onclick="restoreSystem()">
                    🔄 Restaurer Système
                </button>
            </div>

            <div class="button-group">
                <button class="btn btn-primary" onclick="switchToAPI('OpenAI')">
                    🤖 OpenAI
                </button>
                <button class="btn btn-primary" onclick="switchToAPI('Agent Local LOUNA API')">
                    🧠 Agent Local LOUNA
                </button>
                <button class="btn btn-primary" onclick="switchToAPI('DeepSeek API')">
                    🔬 DeepSeek
                </button>
                <button class="btn btn-primary" onclick="switchToAPI('Local LLM Server')">
                    🏠 Local
                </button>
            </div>

            <div id="performance-stats" class="performance-stats">
                <!-- Stats will be loaded here -->
            </div>

            <div id="api-list" class="api-list">
                <h4>📋 APIs Disponibles</h4>
                <!-- API list will be loaded here -->
            </div>

            <div id="test-results" class="test-results" style="display: none;">
                <h4>🧪 Résultats des Tests</h4>
                <!-- Test results will be loaded here -->
            </div>
        </div>

        <div class="control-panel">
            <h3>💬 Test de Message Direct</h3>
            <div style="margin-bottom: 15px;">
                <textarea id="test-message" placeholder="Tapez votre message de test ici..." 
                         style="width: 100%; height: 80px; padding: 10px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3); background: rgba(255,255,255,0.1); color: white; resize: vertical;"></textarea>
            </div>
            <button class="btn btn-success" onclick="sendTestMessage()">
                📤 Envoyer via Connexion Directe
            </button>
            <div id="message-result" style="margin-top: 15px; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 8px; display: none;">
                <!-- Message result will be shown here -->
            </div>
        </div>
    </div>

    <script>
        let currentStats = null;

        // Charger le statut initial
        async function loadStatus() {
            try {
                const response = await fetch('/api/direct-connection/status');
                const data = await response.json();
                
                if (data.success) {
                    updateStatusDisplay(data.data);
                    currentStats = data.data;
                }
            } catch (error) {
                console.error('Erreur lors du chargement du statut:', error);
            }
        }

        // Mettre à jour l'affichage du statut
        function updateStatusDisplay(data) {
            // Statut de connexion
            const connectionEl = document.getElementById('connection-status');
            if (data.directConnection.available) {
                connectionEl.innerHTML = `<span class="status-indicator status-active"></span>Connecté (${data.directConnection.availableAPIs} APIs)`;
            } else {
                connectionEl.innerHTML = `<span class="status-indicator status-inactive"></span>Non disponible`;
            }

            // Statut optimiseur
            const optimizerEl = document.getElementById('optimizer-status');
            if (data.speedOptimizer.available) {
                optimizerEl.innerHTML = `<span class="status-indicator status-active"></span>Actif`;
            } else {
                optimizerEl.innerHTML = `<span class="status-indicator status-inactive"></span>Inactif`;
            }

            // API active
            const apiEl = document.getElementById('active-api');
            apiEl.innerHTML = `<span class="status-indicator status-active"></span>${data.directConnection.activeAPI || 'Aucune'}`;

            // Niveau d'optimisation
            const levelEl = document.getElementById('optimization-level');
            const level = data.speedOptimizer.stats?.optimizationLevel || 'unknown';
            const levelColor = level === 'ultra-fast' ? 'status-active' : level === 'fast' ? 'status-warning' : 'status-inactive';
            levelEl.innerHTML = `<span class="status-indicator ${levelColor}"></span>${level}`;

            // Statistiques de performance
            updatePerformanceStats(data);
        }

        // Mettre à jour les statistiques de performance
        function updatePerformanceStats(data) {
            const statsEl = document.getElementById('performance-stats');
            const stats = data.speedOptimizer.stats || {};
            
            statsEl.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${stats.averageLatency?.toFixed(0) || '0'}ms</div>
                    <div class="stat-label">Latence Moyenne</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.fastestResponse?.toFixed(0) || '0'}ms</div>
                    <div class="stat-label">Plus Rapide</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.optimizationGains?.toFixed(1) || '0'}%</div>
                    <div class="stat-label">Gain d'Optimisation</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.cacheHitRate?.toFixed(1) || '0'}%</div>
                    <div class="stat-label">Cache Hit Rate</div>
                </div>
            `;
        }

        // Activer le mode turbo
        async function enableTurboMode() {
            try {
                const response = await fetch('/api/direct-connection/turbo-mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ enabled: true })
                });
                
                const data = await response.json();
                if (data.success) {
                    alert('🚀 Mode TURBO activé ! Vitesse maximale.');
                    loadStatus();
                } else {
                    alert('❌ Erreur: ' + data.error);
                }
            } catch (error) {
                alert('❌ Erreur lors de l\'activation du mode turbo: ' + error.message);
            }
        }

        // Désactiver Ollama
        async function disableOllama() {
            if (confirm('Êtes-vous sûr de vouloir désactiver Ollama ? Seules les connexions directes seront utilisées.')) {
                try {
                    const response = await fetch('/api/direct-connection/disable-ollama', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        alert('⚡ Ollama désactivé ! Connexions directes uniquement.');
                        loadStatus();
                    } else {
                        alert('❌ Erreur: ' + data.error);
                    }
                } catch (error) {
                    alert('❌ Erreur lors de la désactivation d\'Ollama: ' + error.message);
                }
            }
        }

        // Lancer un test de vitesse
        async function runSpeedTest() {
            const testResultsEl = document.getElementById('test-results');
            testResultsEl.style.display = 'block';
            testResultsEl.innerHTML = '<h4>🧪 Test de Vitesse en Cours...</h4><div class="loading"></div>';

            try {
                const response = await fetch('/api/direct-connection/speed-test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        testMessage: 'Test de vitesse de connexion directe',
                        iterations: 5 
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    displayTestResults(data.data);
                } else {
                    testResultsEl.innerHTML = '<h4>❌ Erreur lors du test</h4><p>' + data.error + '</p>';
                }
            } catch (error) {
                testResultsEl.innerHTML = '<h4>❌ Erreur lors du test</h4><p>' + error.message + '</p>';
            }
        }

        // Afficher les résultats de test
        function displayTestResults(data) {
            const testResultsEl = document.getElementById('test-results');
            
            let resultsHtml = `
                <h4>🧪 Résultats du Test de Vitesse</h4>
                <div class="performance-stats">
                    <div class="stat-item">
                        <div class="stat-value">${data.summary.successRate.toFixed(1)}%</div>
                        <div class="stat-label">Taux de Succès</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.summary.averageLatency.toFixed(0)}ms</div>
                        <div class="stat-label">Latence Moyenne</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.summary.totalTime}ms</div>
                        <div class="stat-label">Temps Total</div>
                    </div>
                </div>
                <div style="margin-top: 15px;">
            `;

            data.results.forEach(result => {
                const statusIcon = result.success ? '✅' : '❌';
                resultsHtml += `
                    <div class="test-item">
                        ${statusIcon} Test ${result.iteration}: ${result.latency}ms 
                        ${result.source ? `(${result.source})` : ''} 
                        ${result.api ? `[${result.api}]` : ''}
                    </div>
                `;
            });

            resultsHtml += '</div>';
            testResultsEl.innerHTML = resultsHtml;
        }

        // Basculer vers une API spécifique
        async function switchToAPI(apiName) {
            try {
                const response = await fetch('/api/direct-connection/switch-api', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ apiName })
                });
                
                const data = await response.json();
                if (data.success) {
                    alert(`✅ Basculement vers ${apiName} réussi !`);
                    loadStatus();
                } else {
                    alert('❌ Erreur: ' + data.error);
                }
            } catch (error) {
                alert('❌ Erreur lors du basculement: ' + error.message);
            }
        }

        // Restaurer le système
        async function restoreSystem() {
            if (confirm('Voulez-vous restaurer toutes les fonctionnalités du système ?')) {
                try {
                    const response = await fetch('/api/features/restore-all', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        alert('🔄 Système restauré avec succès !');
                        loadStatus();
                    } else {
                        alert('❌ Erreur lors de la restauration: ' + data.error);
                    }
                } catch (error) {
                    alert('❌ Erreur lors de la restauration: ' + error.message);
                }
            }
        }

        // Envoyer un message de test
        async function sendTestMessage() {
            const messageEl = document.getElementById('test-message');
            const resultEl = document.getElementById('message-result');
            const message = messageEl.value.trim();

            if (!message) {
                alert('Veuillez saisir un message de test.');
                return;
            }

            resultEl.style.display = 'block';
            resultEl.innerHTML = '<div class="loading"></div> Envoi en cours...';

            try {
                const startTime = Date.now();
                const response = await fetch('/api/direct-connection/message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        message: message,
                        options: { maxTokens: 500, temperature: 0.7 }
                    })
                });
                
                const data = await response.json();
                const totalTime = Date.now() - startTime;

                if (data.success) {
                    resultEl.innerHTML = `
                        <h4>✅ Réponse Reçue</h4>
                        <p><strong>Réponse:</strong> ${data.data.response}</p>
                        <p><strong>Source:</strong> ${data.data.source}</p>
                        <p><strong>API:</strong> ${data.data.api || 'N/A'}</p>
                        <p><strong>Latence:</strong> ${data.data.latency}ms</p>
                        <p><strong>Temps total:</strong> ${totalTime}ms</p>
                        ${data.data.reflection ? `<p><strong>Réflexion:</strong> ${JSON.stringify(data.data.reflection)}</p>` : ''}
                    `;
                } else {
                    resultEl.innerHTML = `
                        <h4>❌ Erreur</h4>
                        <p><strong>Erreur:</strong> ${data.error}</p>
                        ${data.fallback ? `<p><strong>Fallback:</strong> ${data.fallback}</p>` : ''}
                        <p><strong>Latence:</strong> ${data.latency || totalTime}ms</p>
                    `;
                }
            } catch (error) {
                resultEl.innerHTML = `
                    <h4>❌ Erreur de Connexion</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // Actualiser le statut toutes les 10 secondes
        setInterval(loadStatus, 10000);

        // Charger le statut initial
        loadStatus();
    </script>
    <!-- Système Global QI -->
    <script src="js/global-qi-system.js">
        // Récupérer les données réelles
        async function getRealData() {
            try {
                const response = await fetch('/api/real-data');
                if (response.ok) {
                    const result = await response.json();
                    return result.data;
                }
            } catch (error) {
                if (window.thermalDataAPI) {
                    return await window.thermalDataAPI.getRealThermalData();
                }
            }
            throw new Error('Aucune source de données réelles disponible');
        }

        // Mettre à jour l'interface avec les vraies données
        function updateInterfaceWithRealData(realData) {
            if (!realData) return;
            
            // Mettre à jour les éléments avec les vraies valeurs
            const updates = {
                'qiLevel': realData.qi || 185,
                'temperature': (realData.temperature || 37.2).toFixed(1) + '°C',
                'neuronCount': Math.round((realData.neurones?.total || 86000000000) / 1000000000) + 'B',
                'kyberActive': realData.accelerateurs?.actifs || 12,
                'memoryUsage': (realData.memoire?.utilise || 2.4).toFixed(1) + 'TB'
            };
            
            Object.entries(updates).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mettre à jour les stats avec les vraies données
        function updateStatsWithRealData(realData) {
            updateInterfaceWithRealData(realData);
        }
            </script>
</body>
</html>
