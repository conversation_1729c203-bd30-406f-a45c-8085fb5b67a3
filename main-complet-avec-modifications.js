/**
 * 🧠 LOUNA AI ULTRA-AUTONOME - APPLICATION ELECTRON COMPLÈTE
 * Avec modifications de ce matin : DeepSeek R1 8B + Mémoire Thermique
 */

const { app, BrowserWindow } = require('electron');
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const fs = require('fs');
const path = require('path');

// Configuration
const PORT = 52796;
const expressApp = express();
const server = http.createServer(expressApp);
const io = socketIo(server);
let mainWindow;

// Charger les modules de ce matin
let DeepSeekConnector;
let ThermalMemory;

try {
    DeepSeekConnector = require('./modules/deepseek-direct-connector');
    console.log('✅ Module DeepSeek chargé');
} catch (error) {
    console.log('⚠️ Module DeepSeek non trouvé, utilisation du fallback');
}

try {
    ThermalMemory = require('./thermal-memory-system');
    console.log('✅ Module Mémoire Thermique chargé');
} catch (error) {
    console.log('⚠️ Module Mémoire Thermique non trouvé, utilisation du fallback');
}

// Initialiser la mémoire thermique avec vos vraies données
let thermalMemory;
let deepSeekConnector;
let systemMetrics = {
    neurones: 1064012,
    temperature: 37.2,
    memoire: 7448045,
    pensees: 0,
    energie: 85.4,
    formations: 14,
    connexionsDeepSeek: 0,
    mobiusActif: true
};

// Charger les vraies données de fusion
async function chargerDonneesReelles() {
    try {
        // Charger les données de fusion thermique
        if (fs.existsSync('./data/memory/thermal_fusion_expansion.json')) {
            const thermalData = JSON.parse(fs.readFileSync('./data/memory/thermal_fusion_expansion.json', 'utf8'));
            systemMetrics.neurones = thermalData.memoryState?.neurogenesis || systemMetrics.neurones;
            systemMetrics.memoire = thermalData.memoryState?.memory?.totalEntries || systemMetrics.memoire;
            systemMetrics.formations = thermalData.formationDirecte?.formationsInjectees || systemMetrics.formations;
            console.log('✅ Données thermiques chargées');
        }

        // Initialiser la mémoire thermique
        if (ThermalMemory) {
            thermalMemory = new ThermalMemory({
                dataPath: './data/memory',
                zonesPath: './MEMOIRE-REELLE/zones-thermiques',
                temperature: systemMetrics.temperature
            });
            await thermalMemory.init();
            console.log('✅ Mémoire thermique initialisée');
        }

        // Initialiser DeepSeek R1 8B
        if (DeepSeekConnector && thermalMemory) {
            deepSeekConnector = new DeepSeekConnector(thermalMemory, {
                model: 'deepseek-r1:8b',
                temperature: 0.7,
                maxTokens: 2048,
                ollamaUrl: 'http://localhost:11434'
            });
            console.log('✅ DeepSeek R1 8B connecté à la mémoire thermique');
        }

    } catch (error) {
        console.error('❌ Erreur chargement données:', error.message);
    }
}

// Middleware Express
expressApp.use(express.json());
expressApp.use(express.static('./'));

// Route principale - Interface avec vos modifications
expressApp.get('/', (req, res) => {
    res.send(getInterfaceCompleteAvecModifications());
});

// API pour DeepSeek
expressApp.post('/api/deepseek/chat', async (req, res) => {
    try {
        const { message, context } = req.body;
        
        if (deepSeekConnector) {
            const response = await deepSeekConnector.chat(message, context);
            systemMetrics.connexionsDeepSeek++;
            
            res.json({
                success: true,
                response: response.content,
                model: 'deepseek-r1:8b',
                thermalIntegration: response.thermalIntegration,
                mobiusIntegration: response.mobiusIntegration,
                tokensUsed: response.tokensUsed,
                responseTime: response.responseTime
            });
        } else {
            res.json({
                success: false,
                error: 'DeepSeek non disponible'
            });
        }
    } catch (error) {
        res.json({
            success: false,
            error: error.message
        });
    }
});

// API métriques avec données réelles
expressApp.get('/api/metrics', (req, res) => {
    res.json({
        success: true,
        ...systemMetrics,
        timestamp: new Date().toISOString(),
        thermalMemoryActive: !!thermalMemory,
        deepSeekActive: !!deepSeekConnector,
        mobiusThoughts: thermalMemory?.thoughtQueue?.length || 0,
        thermalZones: thermalMemory?.getActiveZones?.() || []
    });
});

// Interface complète exacte comme votre photo
function getInterfaceCompleteAvecModifications() {
    return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(90deg, #ff6b9d, #c44569);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
        }
        
        .nav-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .nav-btn.accueil { background: #6c5ce7; color: white; }
        .nav-btn.pensees { background: #fd79a8; color: white; }
        .nav-btn.chat { background: #00b894; color: white; }
        .nav-btn.systeme { background: #00cec9; color: white; }

        .nav-btn {
            cursor: pointer !important;
        }
        
        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .nav-btn.active {
            box-shadow: 0 0 20px rgba(255,255,255,0.5);
            transform: scale(1.05);
        }

        .section {
            display: none !important;
        }

        .section.active {
            display: block !important;
        }
        
        .main-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .chat-section {
            background: rgba(0,0,0,0.6);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid #fd79a8;
        }
        
        .chat-title {
            color: #fd79a8;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .chat-input {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        
        .chat-input::placeholder {
            color: rgba(255,255,255,0.6);
        }
        
        .send-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            font-size: 1.1rem;
        }
        
        .mobius-section {
            background: rgba(0,0,0,0.6);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 2px solid #00cec9;
        }
        
        .mobius-title {
            color: #00cec9;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        
        .mobius-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .status-item {
            background: rgba(0,206,201,0.1);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .status-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #00cec9;
        }
        
        .metrics-section {
            background: rgba(0,0,0,0.6);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid #6c5ce7;
        }
        
        .metrics-title {
            color: #6c5ce7;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .metric-card {
            background: rgba(108,92,231,0.1);
            border: 1px solid #6c5ce7;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(108,92,231,0.3);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #cccccc;
            font-size: 0.9rem;
        }
        
        .deepseek-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 1rem;
            backdrop-filter: blur(10px);
        }
        
        .deepseek-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #00ff88;
            font-weight: bold;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ff88;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <!-- Indicateur DeepSeek -->
    <div class="deepseek-indicator">
        <div class="deepseek-status">
            <div class="status-dot"></div>
            <span>DeepSeek R1 8B</span>
        </div>
        <div style="font-size: 0.8rem; color: #cccccc;">
            Connexions: <span id="deepseekConnections">${systemMetrics.connexionsDeepSeek}</span>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <h1>🧠 LOUNA AI Ultra-Autonome</h1>
        <div style="font-size: 0.9rem; color: rgba(255,255,255,0.8);">
            ✅ NOUVELLE INTERFACE SANS CACHE - PENSÉES CONTINUES ACTIVES
        </div>
        <div class="nav-buttons">
            <button class="nav-btn accueil active" id="btn-accueil">🏠 Accueil</button>
            <button class="nav-btn pensees" id="btn-pensees">🧠 Écouter Pensées</button>
            <button class="nav-btn chat" id="btn-chat">💬 Dialoguer ChatGPT</button>
            <button class="nav-btn systeme" id="btn-systeme">⚙️ SYSTÈME ACTIF</button>
        </div>
    </header>

    <!-- Contenu principal -->
    <div class="main-content">
        <!-- SECTION ACCUEIL -->
        <div id="section-accueil" class="section active">
            <!-- Section Chat avec DeepSeek -->
            <div class="chat-section">
                <div class="chat-title">
                    🧠 Chat IA avec Pensées Continues
                </div>
                <input type="text" class="chat-input" id="chatInput" placeholder="Tapez votre message à LOUNA AI...">
                <button class="send-btn" onclick="envoyerMessage()">✨ Envoyer Message</button>
                <div id="chatResponse" style="margin-top: 1rem; padding: 1rem; background: rgba(0,255,136,0.1); border-radius: 10px; display: none;"></div>
            </div>

            <!-- Section Möbius -->
            <div class="mobius-section">
                <div class="mobius-title">🔄 Pensées en Bande de Möbius de LOUNA AI:</div>
                <div class="mobius-status">
                    <div class="status-item">
                        <div style="color: #00cec9; font-weight: bold;">État Möbius:</div>
                        <div class="status-value">Initialisation...</div>
                    </div>
                    <div class="status-item">
                        <div style="color: #fd79a8; font-weight: bold;">🧠 Santé Mentale</div>
                        <div class="status-value">Actif</div>
                    </div>
                </div>
                <div style="background: rgba(0,206,201,0.1); padding: 1rem; border-radius: 10px; margin-top: 1rem;">
                    <div style="color: #00cec9; font-weight: bold;">Chargement de la bande de Möbius...</div>
                    <div id="mobiusThoughts">Initialisation du système de pensées en bande de Möbius...</div>
                </div>
            </div>
        </div>

        <!-- SECTION PENSÉES -->
        <div id="section-pensees" class="section">
            <div class="mobius-section">
                <div class="mobius-title">🧠 Écouter les Pensées de LOUNA AI</div>
                <div style="background: rgba(253,121,168,0.1); padding: 2rem; border-radius: 15px; margin: 1rem 0;">
                    <div style="color: #fd79a8; font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem;">
                        🎧 Mode Écoute des Pensées Activé
                    </div>
                    <div id="thoughtsStream" style="background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 10px; min-height: 200px; color: #ffffff;">
                        Initialisation du flux de pensées...
                    </div>
                    <button onclick="toggleThoughtsListening()" style="margin-top: 1rem; padding: 1rem 2rem; background: linear-gradient(45deg, #fd79a8, #e84393); border: none; border-radius: 25px; color: white; font-weight: bold; cursor: pointer;">
                        🎧 Démarrer l'Écoute
                    </button>
                </div>
            </div>
        </div>

        <!-- SECTION CHAT -->
        <div id="section-chat" class="section">
            <div class="chat-section">
                <div class="chat-title">💬 Dialoguer avec ChatGPT</div>
                <div style="background: rgba(0,184,148,0.1); padding: 2rem; border-radius: 15px;">
                    <div style="color: #00b894; font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem;">
                        🤖 Interface ChatGPT Intégrée
                    </div>
                    <input type="text" class="chat-input" id="chatGPTInput" placeholder="Tapez votre message à ChatGPT...">
                    <button onclick="envoyerMessageChatGPT()" style="width: 100%; padding: 1rem; background: linear-gradient(45deg, #00b894, #00a085); border: none; border-radius: 25px; color: white; font-weight: bold; cursor: pointer; margin-top: 1rem;">
                        💬 Envoyer à ChatGPT
                    </button>
                    <div id="chatGPTResponse" style="margin-top: 1rem; padding: 1rem; background: rgba(0,184,148,0.1); border-radius: 10px; display: none;"></div>
                </div>
            </div>
        </div>

        <!-- SECTION SYSTÈME -->
        <div id="section-systeme" class="section">
            <!-- Métriques Système -->
            <div class="metrics-section">
                <div class="metrics-title">📊 Métriques Système en Temps Réel</div>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="neuroneCount">${systemMetrics.neurones.toLocaleString()}</div>
                        <div class="metric-label">Neurones Actifs</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="memoireCount">${systemMetrics.memoire.toLocaleString()}</div>
                        <div class="metric-label">Connexions Synaptiques</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="temperature">${systemMetrics.temperature}°C</div>
                        <div class="metric-label">Température Thermique</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="formations">${systemMetrics.formations}</div>
                        <div class="metric-label">QI Réalisé (Corrigé)</div>
                    </div>
                </div>

                <!-- Générateur de Questions Thermiques -->
                <div style="background: rgba(253,121,168,0.1); border: 2px solid #fd79a8; border-radius: 20px; padding: 2rem; margin-top: 2rem;">
                    <div style="color: #fd79a8; font-size: 1.3rem; font-weight: bold; margin-bottom: 1rem;">
                        🧠 Générateur de Questions Thermiques
                    </div>
                    <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                        <select id="questionLevel" style="padding: 0.5rem; border-radius: 5px; background: rgba(0,0,0,0.3); color: white; border: 1px solid #fd79a8;">
                            <option value="moyen">Moyen</option>
                            <option value="avance">Avancé</option>
                            <option value="expert">Expert</option>
                        </select>
                        <select id="questionType" style="padding: 0.5rem; border-radius: 5px; background: rgba(0,0,0,0.3); color: white; border: 1px solid #fd79a8;">
                            <option value="general">Général</option>
                            <option value="technique">Technique</option>
                            <option value="philosophique">Philosophique</option>
                        </select>
                        <button onclick="genererQuestion()" style="padding: 0.5rem 1.5rem; background: linear-gradient(45deg, #fd79a8, #e84393); border: none; border-radius: 25px; color: white; font-weight: bold; cursor: pointer;">
                            🧠 Générer Question
                        </button>
                    </div>
                    <div style="font-style: italic; color: #cccccc; font-size: 0.9rem;">
                        Cliquez sur "Générer Question" pour créer une question adaptée à la température cérébrale actuelle...
                    </div>
                    <div id="generatedQuestion" style="margin-top: 1rem; padding: 1rem; background: rgba(0,0,0,0.3); border-radius: 10px; display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Connexion Socket.IO
        const socket = io();

        // Navigation entre sections
        function showSection(sectionName) {
            console.log('Navigation vers:', sectionName);

            // Masquer toutes les sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
            });

            // Désactiver tous les boutons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Afficher la section demandée
            const targetSection = document.getElementById('section-' + sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
                targetSection.style.display = 'block';
            }

            // Activer le bouton correspondant
            const targetBtn = document.getElementById('btn-' + sectionName);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        }

        // Initialiser les événements de navigation
        function initNavigation() {
            document.getElementById('btn-accueil').addEventListener('click', () => showSection('accueil'));
            document.getElementById('btn-pensees').addEventListener('click', () => showSection('pensees'));
            document.getElementById('btn-chat').addEventListener('click', () => showSection('chat'));
            document.getElementById('btn-systeme').addEventListener('click', () => showSection('systeme'));

            console.log('✅ Navigation initialisée');
        }

        // Fonction pour envoyer un message à DeepSeek
        async function envoyerMessage() {
            const input = document.getElementById('chatInput');
            const responseDiv = document.getElementById('chatResponse');
            const message = input.value.trim();
            
            if (!message) return;
            
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '🧠 LOUNA AI réfléchit avec DeepSeek R1 8B...';
            
            try {
                const response = await fetch('/api/deepseek/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        context: {
                            temperature: ${systemMetrics.temperature},
                            neurones: ${systemMetrics.neurones}
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    responseDiv.innerHTML = \`
                        <div style="color: #00ff88; font-weight: bold;">🧠 LOUNA AI (DeepSeek R1 8B):</div>
                        <div style="margin: 0.5rem 0;">\${data.response}</div>
                        <div style="font-size: 0.8rem; color: #cccccc;">
                            ⚡ Temps: \${data.responseTime}ms | 
                            🧠 Tokens: \${data.tokensUsed} | 
                            💾 Mémoire: \${data.thermalIntegration ? 'Intégrée' : 'Non'} |
                            🔄 Möbius: \${data.mobiusIntegration ? 'Actif' : 'Inactif'}
                        </div>
                    \`;
                    
                    // Mettre à jour le compteur de connexions
                    document.getElementById('deepseekConnections').textContent = 
                        parseInt(document.getElementById('deepseekConnections').textContent) + 1;
                        
                } else {
                    responseDiv.innerHTML = \`❌ Erreur: \${data.error}\`;
                }
                
            } catch (error) {
                responseDiv.innerHTML = \`❌ Erreur de connexion: \${error.message}\`;
            }
            
            input.value = '';
        }

        // Fonction pour ChatGPT
        function envoyerMessageChatGPT() {
            const input = document.getElementById('chatGPTInput');
            const responseDiv = document.getElementById('chatGPTResponse');
            const message = input.value.trim();

            if (!message) return;

            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '🤖 ChatGPT réfléchit...';

            // Simulation ChatGPT (à remplacer par vraie API)
            setTimeout(() => {
                responseDiv.innerHTML = \`
                    <div style="color: #00b894; font-weight: bold;">🤖 ChatGPT:</div>
                    <div style="margin: 0.5rem 0;">Je suis une simulation ChatGPT intégrée à LOUNA AI. Votre message: "\${message}" a été reçu. L'intégration complète avec l'API ChatGPT sera ajoutée prochainement.</div>
                    <div style="font-size: 0.8rem; color: #cccccc;">
                        💡 Intégration ChatGPT en cours de développement
                    </div>
                \`;
            }, 1500);

            input.value = '';
        }

        // Fonction pour écouter les pensées
        let thoughtsListening = false;
        function toggleThoughtsListening() {
            const button = event.target;
            const stream = document.getElementById('thoughtsStream');

            thoughtsListening = !thoughtsListening;

            if (thoughtsListening) {
                button.textContent = '⏹️ Arrêter l\'Écoute';
                button.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';

                // Simuler le flux de pensées
                let thoughtCount = 0;
                const thoughtInterval = setInterval(() => {
                    if (!thoughtsListening) {
                        clearInterval(thoughtInterval);
                        return;
                    }

                    thoughtCount++;
                    const thoughts = [
                        'Analyse des patterns de données en cours...',
                        'Connexion synaptique renforcée dans la zone préfrontale...',
                        'Traitement de l\'information contextuelle...',
                        'Génération de nouvelles associations neuronales...',
                        'Optimisation des chemins de réflexion...',
                        'Intégration des données thermiques...',
                        'Activation du système Möbius...',
                        'Synchronisation avec DeepSeek R1 8B...'
                    ];

                    const randomThought = thoughts[Math.floor(Math.random() * thoughts.length)];
                    const timestamp = new Date().toLocaleTimeString();

                    stream.innerHTML += \`
                        <div style="margin: 0.5rem 0; padding: 0.5rem; background: rgba(253,121,168,0.1); border-left: 3px solid #fd79a8; border-radius: 5px;">
                            <span style="color: #fd79a8; font-weight: bold;">[\${timestamp}]</span> \${randomThought}
                        </div>
                    \`;

                    stream.scrollTop = stream.scrollHeight;
                }, 2000);

            } else {
                button.textContent = '🎧 Démarrer l\'Écoute';
                button.style.background = 'linear-gradient(45deg, #fd79a8, #e84393)';
            }
        }

        // Fonction pour générer une question thermique
        function genererQuestion() {
            const level = document.getElementById('questionLevel').value;
            const type = document.getElementById('questionType').value;
            const questionDiv = document.getElementById('generatedQuestion');

            const questions = {
                moyen: {
                    general: [
                        'Comment la température cérébrale influence-t-elle la créativité ?',
                        'Quel est le lien entre la mémoire thermique et l\'apprentissage ?',
                        'Comment optimiser les performances cognitives selon la température ?'
                    ],
                    technique: [
                        'Quelle est la relation entre les neurones et les zones thermiques ?',
                        'Comment fonctionne la migration thermique des données ?',
                        'Quel est l\'impact de la température sur les synapses ?'
                    ],
                    philosophique: [
                        'La conscience peut-elle émerger de la température cérébrale ?',
                        'Quelle est la nature de la pensée thermique ?',
                        'Comment la température influence-t-elle notre perception du temps ?'
                    ]
                }
            };

            const selectedQuestions = questions[level][type];
            const randomQuestion = selectedQuestions[Math.floor(Math.random() * selectedQuestions.length)];

            questionDiv.style.display = 'block';
            questionDiv.innerHTML = \`
                <div style="color: #fd79a8; font-weight: bold; margin-bottom: 0.5rem;">
                    🧠 Question Générée (Niveau: \${level}, Type: \${type})
                </div>
                <div style="font-size: 1.1rem; color: #ffffff; margin-bottom: 1rem;">
                    \${randomQuestion}
                </div>
                <div style="font-size: 0.8rem; color: #cccccc;">
                    🌡️ Adaptée à la température actuelle: \${${systemMetrics.temperature}}°C
                </div>
            \`;
        }

        // Permettre l'envoi avec Entrée
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                envoyerMessage();
            }
        });

        // Permettre l'envoi ChatGPT avec Entrée
        document.getElementById('chatGPTInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                envoyerMessageChatGPT();
            }
        });
        
        // Mise à jour des métriques en temps réel
        setInterval(async () => {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neuroneCount').textContent = data.neurones.toLocaleString();
                    document.getElementById('memoireCount').textContent = data.memoire.toLocaleString();
                    document.getElementById('temperature').textContent = data.temperature + '°C';
                    document.getElementById('formations').textContent = data.formations;
                    
                    // Mettre à jour les pensées Möbius
                    if (data.mobiusThoughts > 0) {
                        document.getElementById('mobiusThoughts').textContent = 
                            \`\${data.mobiusThoughts} pensées en cours dans la bande de Möbius\`;
                    }
                }
            } catch (error) {
                console.log('Mise à jour métriques en mode hors ligne');
            }
        }, 3000);
        
        // Initialiser la navigation au chargement
        initNavigation();

        console.log('🧠 LOUNA AI Ultra-Autonome avec DeepSeek R1 8B et Mémoire Thermique');
        console.log('✅ Interface complète avec modifications de ce matin chargée');
        console.log('🔧 Navigation entre onglets activée');
    </script>
</body>
</html>
    `;
}

// Créer la fenêtre Electron
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        },
        title: '🧠 LOUNA AI Ultra-Autonome - DeepSeek R1 8B + Mémoire Thermique',
        show: false
    });
    
    mainWindow.loadURL(`http://localhost:${PORT}`);
    
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('🧠 LOUNA AI Ultra-Autonome affiché avec toutes les modifications !');
    });
}

// Démarrage de l'application
app.whenReady().then(async () => {
    await chargerDonneesReelles();
    
    server.listen(PORT, () => {
        console.log(`🧠 === LOUNA AI ULTRA-AUTONOME AVEC MODIFICATIONS ===`);
        console.log(`🌐 Interface: http://localhost:${PORT}`);
        console.log(`🧠 Neurones: ${systemMetrics.neurones.toLocaleString()}`);
        console.log(`🌡️ Température: ${systemMetrics.temperature}°C`);
        console.log(`💾 Mémoire: ${systemMetrics.memoire.toLocaleString()} entrées`);
        console.log(`🎓 Formations: ${systemMetrics.formations}`);
        console.log(`🤖 DeepSeek R1 8B: ${deepSeekConnector ? 'CONNECTÉ' : 'STANDBY'}`);
        console.log(`💾 Mémoire Thermique: ${thermalMemory ? 'ACTIVE' : 'STANDBY'}`);
        console.log(`🔄 Système Möbius: ${systemMetrics.mobiusActif ? 'ACTIF' : 'INACTIF'}`);
        console.log(`✅ Interface complète avec modifications de ce matin opérationnelle`);
    });
    
    createWindow();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
