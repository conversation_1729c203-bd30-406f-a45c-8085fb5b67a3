// Script de test pour vérifier tous les boutons de l'interface
console.log('🧪 === TEST DE TOUS LES BOUTONS ===');

// Fonction pour tester un bouton
function testerBouton(selector, nom) {
    const bouton = document.querySelector(selector);
    if (bouton) {
        if (bouton.onclick || bouton.getAttribute('onclick')) {
            console.log(`✅ ${nom}: FONCTIONNEL`);
            return true;
        } else {
            console.log(`❌ ${nom}: PAS DE FONCTION`);
            return false;
        }
    } else {
        console.log(`⚠️ ${nom}: BOUTON NON TROUVÉ`);
        return false;
    }
}

// Fonction pour tester tous les boutons
function testerTousLesBoutons() {
    console.log('🔍 Test des boutons de sécurité...');
    
    const boutonsSecurite = [
        ['.btn-hibernation', 'Hibernation'],
        ['.btn-sleep', 'Sommeil'],
        ['.btn-wakeup', 'Réveil'],
        ['.btn-surveillance', 'Surveillance'],
        ['.btn-backup', 'Sauvegarde'],
        ['.btn-memory', 'Mé<PERSON>ire'],
        ['.btn-fix', 'Corriger']
    ];
    
    let securityOK = 0;
    boutonsSecurite.forEach(([selector, nom]) => {
        if (testerBouton(selector, nom)) securityOK++;
    });
    
    console.log(`📊 Boutons sécurité: ${securityOK}/${boutonsSecurite.length} fonctionnels`);
    
    console.log('🔍 Test des boutons d\'évolution...');
    
    const boutonsEvolution = [
        ['#pauseEvolution', 'Pause Évolution'],
        ['#resumeEvolution', 'Reprendre Évolution'],
        ['#analyzeSystem', 'Analyser Système'],
        ['#forceEvolution', 'Forcer Évolution'],
        ['#openVerification', 'Vérification']
    ];
    
    let evolutionOK = 0;
    boutonsEvolution.forEach(([selector, nom]) => {
        if (testerBouton(selector, nom)) evolutionOK++;
    });
    
    console.log(`📊 Boutons évolution: ${evolutionOK}/${boutonsEvolution.length} fonctionnels`);
    
    console.log('🔍 Test des boutons de navigation...');
    
    const boutonsNavigation = document.querySelectorAll('.quick-start-card[onclick]');
    let navigationOK = 0;
    
    boutonsNavigation.forEach((bouton, index) => {
        const onclick = bouton.getAttribute('onclick');
        if (onclick && onclick.includes('window.open')) {
            navigationOK++;
        }
    });
    
    console.log(`📊 Boutons navigation: ${navigationOK}/${boutonsNavigation.length} fonctionnels`);
    
    console.log('🔍 Test du bouton DeepSeek...');
    const deepseekOK = testerBouton('button[onclick="envoyerMessageDeepSeek()"]', 'DeepSeek Chat');
    
    console.log('🔍 Test du bouton retour accueil...');
    const accueilOK = testerBouton('button[onclick="retourAccueil()"]', 'Retour Accueil');
    
    // Résumé final
    const totalBoutons = boutonsSecurite.length + boutonsEvolution.length + boutonsNavigation.length + 2;
    const totalOK = securityOK + evolutionOK + navigationOK + (deepseekOK ? 1 : 0) + (accueilOK ? 1 : 0);
    
    console.log('🎯 === RÉSUMÉ DU TEST ===');
    console.log(`✅ Boutons fonctionnels: ${totalOK}/${totalBoutons}`);
    console.log(`📊 Taux de réussite: ${Math.round((totalOK/totalBoutons)*100)}%`);
    
    if (totalOK === totalBoutons) {
        console.log('🎉 TOUS LES BOUTONS FONCTIONNENT PARFAITEMENT !');
    } else {
        console.log('⚠️ Certains boutons nécessitent une correction');
    }
    
    return {
        total: totalBoutons,
        fonctionnels: totalOK,
        pourcentage: Math.round((totalOK/totalBoutons)*100)
    };
}

// Fonction pour corriger automatiquement les boutons cassés
function corrigerBoutonsCasses() {
    console.log('🔧 Correction automatique des boutons cassés...');
    
    // Corriger les boutons sans onclick
    const boutonsVides = document.querySelectorAll('button:not([onclick])');
    boutonsVides.forEach(bouton => {
        if (!bouton.onclick) {
            bouton.onclick = () => {
                console.log('⚠️ Bouton temporairement désactivé:', bouton.textContent);
            };
        }
    });
    
    // Corriger les liens vides
    const liensVides = document.querySelectorAll('a[href="#"], a[href=""]');
    liensVides.forEach(lien => {
        lien.href = 'javascript:void(0)';
        lien.onclick = () => {
            console.log('⚠️ Lien temporairement désactivé:', lien.textContent);
        };
    });
    
    console.log('✅ Correction automatique terminée');
}

// Exporter les fonctions pour utilisation dans la console
window.testerTousLesBoutons = testerTousLesBoutons;
window.corrigerBoutonsCasses = corrigerBoutonsCasses;

// Lancer le test automatiquement après chargement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🚀 Lancement du test automatique des boutons...');
        testerTousLesBoutons();
    }, 2000);
});

console.log('📝 Script de test chargé. Utilisez testerTousLesBoutons() pour tester manuellement.');
