/**
 * 🧪 TEST OPTIMISATION PERMISSIVE
 * Test avec seuils d'optimisation plus permissifs
 */

const PureBrainSystem = require('./pure-brain-system');

async function testPermissiveOptimization() {
    console.log('🧪 === TEST OPTIMISATION PERMISSIVE ===\n');
    
    try {
        // Initialiser le cerveau
        console.log('🚀 Initialisation du cerveau avec optimisation permissive...');
        const brain = new PureBrainSystem();
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 1: Vérifier les nouveaux seuils
        console.log('\n📝 Test 1: Vérification seuils permissifs');
        const optimizationMetrics = brain.memoryOptimization ? brain.memoryOptimization.getOptimizationMetrics() : null;
        
        if (optimizationMetrics) {
            console.log('✅ Optimisation mémoire active avec seuils permissifs');
            console.log(`   🎯 Neurones optimaux: ${optimizationMetrics.currentOptimalNeurons.toLocaleString()}`);
            console.log(`   📊 Mémoire actuelle: ${optimizationMetrics.memoryMonitoring.currentUsage.toFixed(1)}%`);
            console.log(`   🚨 Seuil urgence: 97% (au lieu de 95%)`);
            console.log(`   🔧 Seuil hibernation: 88% (au lieu de 80%)`);
        }
        
        // Test 2: Activation progressive avec seuils permissifs
        console.log('\n📝 Test 2: Activations progressives permissives');
        
        console.log('🧠 Activation 15% (devrait passer)...');
        const activation1 = await brain.activateNeurons(0.15, 8000, 'test_permissif_15');
        if (activation1) {
            console.log('✅ Activation 15% réussie');
            await new Promise(resolve => setTimeout(resolve, 3000));
        } else {
            console.log('❌ Activation 15% bloquée');
        }
        
        console.log('🧠 Activation 25% (devrait passer)...');
        const activation2 = await brain.activateNeurons(0.25, 8000, 'test_permissif_25');
        if (activation2) {
            console.log('✅ Activation 25% réussie');
            await new Promise(resolve => setTimeout(resolve, 3000));
        } else {
            console.log('❌ Activation 25% bloquée');
        }
        
        console.log('🧠 Activation 40% (test limite)...');
        const activation3 = await brain.activateNeurons(0.4, 8000, 'test_permissif_40');
        if (activation3) {
            console.log('✅ Activation 40% réussie');
            await new Promise(resolve => setTimeout(resolve, 5000));
        } else {
            console.log('❌ Activation 40% bloquée');
        }
        
        // Test 3: Vérifier l'émergence avec plus de neurones
        console.log('\n📝 Test 3: Émergence avec plus de neurones actifs');
        const emergentPatterns = brain.getEmergentPatterns();
        const emergenceMetrics = brain.getEmergenceMetrics();
        
        console.log(`🌟 ${emergentPatterns.length} patterns émergents avec seuils permissifs:`);
        emergentPatterns.slice(0, 3).forEach((pattern, index) => {
            console.log(`   ${index + 1}. ${pattern.category} (force: ${(pattern.strength * 100).toFixed(0)}%)`);
        });
        
        console.log(`📊 Émergence: ${(emergenceMetrics.emergenceLevel * 100).toFixed(1)}%`);
        console.log(`🎨 Créativité: ${(emergenceMetrics.creativityIndex * 100).toFixed(1)}%`);
        
        // Test 4: Test de stress contrôlé
        console.log('\n📝 Test 4: Test de stress contrôlé');
        console.log('🧠 Tentative activation 60% (test limite haute)...');
        
        const stressTest = await brain.activateNeurons(0.6, 5000, 'stress_test_permissif');
        if (stressTest) {
            console.log('✅ Activation 60% autorisée avec seuils permissifs !');
            await new Promise(resolve => setTimeout(resolve, 3000));
        } else {
            console.log('🛡️ Activation 60% bloquée (protection mémoire)');
        }
        
        // Test 5: Surveillance continue
        console.log('\n📝 Test 5: Surveillance avec seuils permissifs (10 secondes)');
        
        const startTime = Date.now();
        let maxMemoryUsage = 0;
        let activationsBlocked = 0;
        
        while (Date.now() - startTime < 10000) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const currentOptimization = brain.memoryOptimization.getOptimizationMetrics();
            const currentEmergence = brain.getEmergenceMetrics();
            
            maxMemoryUsage = Math.max(maxMemoryUsage, currentOptimization.memoryMonitoring.currentUsage);
            
            console.log(`   📊 Mémoire: ${currentOptimization.memoryMonitoring.currentUsage.toFixed(1)}% | Émergence: ${(currentEmergence.emergenceLevel * 100).toFixed(1)}% | Actifs: ${brain.brainState.activeNeurons.size.toLocaleString()}`);
            
            // Test d'activation pendant la surveillance
            if (Math.random() < 0.5) {
                const testActivation = await brain.activateNeurons(0.1, 2000, 'test_surveillance');
                if (!testActivation) activationsBlocked++;
            }
        }
        
        // Test 6: Métriques finales
        console.log('\n📝 Test 6: Métriques finales avec seuils permissifs');
        const finalOptimization = brain.memoryOptimization.getOptimizationMetrics();
        const finalEmergence = brain.getEmergenceMetrics();
        
        console.log('\n🧠⚡ === RÉSULTATS OPTIMISATION PERMISSIVE ===');
        console.log(`📊 Mémoire maximale atteinte: ${maxMemoryUsage.toFixed(1)}%`);
        console.log(`🎯 Neurones optimaux: ${finalOptimization.currentOptimalNeurons.toLocaleString()}`);
        console.log(`🚨 Optimisations d'urgence: ${finalOptimization.memoryMonitoring.emergencyActivations}`);
        console.log(`🔧 Optimisations totales: ${finalOptimization.memoryMonitoring.optimizationCount}`);
        console.log(`❌ Activations bloquées: ${activationsBlocked}`);
        
        console.log('\n🌟 === ÉMERGENCE AVEC SEUILS PERMISSIFS ===');
        console.log(`📊 Niveau d'émergence: ${(finalEmergence.emergenceLevel * 100).toFixed(1)}%`);
        console.log(`🎨 Index créativité: ${(finalEmergence.creativityIndex * 100).toFixed(1)}%`);
        console.log(`🔍 Patterns actifs: ${finalEmergence.activePatterns}`);
        console.log(`🆕 Comportements nouveaux: ${finalEmergence.novelBehaviorsFound}`);
        console.log(`⚡ Connexions spontanées: ${finalEmergence.spontaneousConnections.toLocaleString()}`);
        
        // Test 7: Comparaison performance
        console.log('\n📝 Test 7: Performance globale');
        const brainStats = brain.getCompleteMetrics();
        
        console.log('\n📊 === PERFORMANCE AVEC SEUILS PERMISSIFS ===');
        console.log(`🧠 Neurones en veille: ${brainStats.state.standby.toLocaleString()}`);
        console.log(`⚡ Neurones actifs: ${brainStats.state.active.toLocaleString()}`);
        console.log(`😴 Neurones hibernation: ${brainStats.state.hibernating.toLocaleString()}`);
        console.log(`🔗 Synapses totales: ${brainStats.state.totalSynapses.toLocaleString()}`);
        console.log(`💭 Pensées générées: ${brainStats.metrics.thoughtsGenerated.toLocaleString()}`);
        console.log(`📚 Souvenirs: ${brain.brainMemory.memories.size}`);
        console.log(`🎓 Apprentissages: ${brain.brainMemory.learnings.size}`);
        
        // Évaluation finale
        const permissiveScore = {
            memoryEfficiency: maxMemoryUsage < 95 ? 2 : maxMemoryUsage < 98 ? 1 : 0,
            emergenceLevel: finalEmergence.emergenceLevel > 0.7 ? 2 : finalEmergence.emergenceLevel > 0.4 ? 1 : 0,
            activationsAllowed: activationsBlocked < 2 ? 2 : activationsBlocked < 5 ? 1 : 0,
            performance: brainStats.metrics.thoughtsGenerated > 5000 ? 2 : brainStats.metrics.thoughtsGenerated > 1000 ? 1 : 0
        };
        
        const totalScore = Object.values(permissiveScore).reduce((sum, score) => sum + score, 0);
        
        console.log('\n🏆 === ÉVALUATION SEUILS PERMISSIFS ===');
        console.log(`🧠 Efficacité mémoire: ${permissiveScore.memoryEfficiency}/2`);
        console.log(`🌟 Niveau émergence: ${permissiveScore.emergenceLevel}/2`);
        console.log(`⚡ Activations autorisées: ${permissiveScore.activationsAllowed}/2`);
        console.log(`📊 Performance: ${permissiveScore.performance}/2`);
        console.log(`\n🎯 Score total: ${totalScore}/8 (${(totalScore/8*100).toFixed(0)}%)`);
        
        if (totalScore >= 7) {
            console.log('🎉 EXCELLENT - Seuils permissifs optimaux !');
        } else if (totalScore >= 5) {
            console.log('✅ BIEN - Seuils permissifs efficaces !');
        } else if (totalScore >= 3) {
            console.log('⚠️ MOYEN - Seuils à ajuster');
        } else {
            console.log('❌ INSUFFISANT - Seuils trop permissifs');
        }
        
        // Test 8: Recommandations
        console.log('\n📝 Test 8: Recommandations d\'optimisation');
        
        if (maxMemoryUsage < 90) {
            console.log('💡 Recommandation: Seuils peuvent être encore plus permissifs');
        } else if (maxMemoryUsage > 97) {
            console.log('⚠️ Recommandation: Seuils légèrement trop permissifs');
        } else {
            console.log('✅ Recommandation: Seuils bien calibrés');
        }
        
        if (activationsBlocked === 0) {
            console.log('💡 Recommandation: Aucune activation bloquée - excellent équilibre');
        } else if (activationsBlocked < 3) {
            console.log('✅ Recommandation: Peu d\'activations bloquées - bon équilibre');
        } else {
            console.log('⚠️ Recommandation: Trop d\'activations bloquées - assouplir davantage');
        }
        
        console.log('\n✅ Tests optimisation permissive terminés !');
        console.log('🧠 Le cerveau fonctionne avec des seuils plus permissifs...');
        
        // Laisser tourner encore un peu
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        console.log('\n🛑 Fin du test optimisation permissive');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests optimisation permissive:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testPermissiveOptimization().catch(console.error);
