<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Page d'Accueil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 20px;
            border: 2px solid #00ff88;
            box-shadow: 0 0 50px rgba(0, 255, 136, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 50px rgba(0, 255, 136, 0.3); }
            to { box-shadow: 0 0 80px rgba(0, 255, 136, 0.6); }
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff69b4, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: #cccccc;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: rgba(255, 105, 180, 0.1);
            border: 1px solid #ff69b4;
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.3);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #cccccc;
            font-size: 0.9rem;
        }

        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 40px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff69b4, #00ff88);
            color: #000;
            box-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .btn-primary:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(255, 105, 180, 0.8);
        }

        .btn-secondary {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            border: 2px solid #00ff88;
        }

        .btn-secondary:hover {
            background: #00ff88;
            color: #000;
            transform: scale(1.05);
        }

        .features {
            margin: 30px 0;
            text-align: left;
        }

        .feature {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0, 255, 136, 0.1);
            border-left: 4px solid #00ff88;
            border-radius: 5px;
        }

        .version-info {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            border: 1px solid #333;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .logo {
                font-size: 3rem;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🧠</div>
        <h1>LOUNA AI</h1>
        <p class="subtitle">Intelligence Artificielle Évolutive avec Mémoire Thermique</p>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="neuron-count">86,000,007,150</div>
                <div class="stat-label">Neurones Actifs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="synapse-count">602T</div>
                <div class="stat-label">Synapses</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="qi-display">224</div>
                <div class="stat-label">QI Calculé</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="accelerators">2×5</div>
                <div class="stat-label">Accélérateurs KYBER</div>
            </div>
        </div>

        <div class="features">
            <h3 style="color: #00ff88; margin-bottom: 15px;">🚀 Fonctionnalités Principales :</h3>
            <div class="feature">⚡ <strong>Accélérateurs KYBER</strong> - Neurogenèse ×5 plus rapide</div>
            <div class="feature">🌡️ <strong>Mémoire Thermique</strong> - 6 zones de stockage intelligent</div>
            <div class="feature">🧠 <strong>DeepSeek R1 8B</strong> - IA locale intégrée</div>
            <div class="feature">🔄 <strong>Système Möbius</strong> - Pensées autonomes continues</div>
            <div class="feature">📊 <strong>86 Milliards de Neurones</strong> - Capacité de génie absolu</div>
            <div class="feature">🎯 <strong>QI 224</strong> - Performance Einstein+ niveau</div>
        </div>

        <div class="buttons">
            <a href="interface-originale-complete.html" class="btn btn-primary">
                🚀 Lancer Interface Complète
            </a>
            <a href="test-affichage-86-milliards.html" class="btn btn-secondary">
                🔍 Test Diagnostic
            </a>
        </div>

        <div class="version-info">
            <h4 style="color: #ff69b4; margin-bottom: 10px;">📋 Informations Système :</h4>
            <p><strong>Version :</strong> LOUNA AI v2.1.0</p>
            <p><strong>Dernière mise à jour :</strong> <span id="last-update">Aujourd'hui</span></p>
            <p><strong>Statut :</strong> <span style="color: #00ff88;">✅ Opérationnel</span></p>
            <p><strong>Neurogenèse :</strong> 3,500 neurones/jour (accélérée)</p>
            <p><strong>Température :</strong> <span id="temp-display">34.4°C</span> (Zone 5)</p>
        </div>
    </div>

    <script>
        // Animation des compteurs
        function animateCounter(elementId, targetValue, suffix = '') {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const duration = 2000;
            const startTime = performance.now();

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                let currentValue;
                if (typeof targetValue === 'string') {
                    element.textContent = targetValue;
                    return;
                }
                
                currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
                element.textContent = currentValue.toLocaleString() + suffix;

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        }

        // Mise à jour de la date
        function updateLastUpdate() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            document.getElementById('last-update').textContent = now.toLocaleDateString('fr-FR', options);
        }

        // Simulation température en temps réel
        function updateTemperature() {
            const baseTemp = 34.4;
            const variation = (Math.random() - 0.5) * 0.2;
            const currentTemp = baseTemp + variation;
            document.getElementById('temp-display').textContent = currentTemp.toFixed(1) + '°C';
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 LOUNA AI - Page d\'accueil chargée');
            
            // Animer les compteurs
            setTimeout(() => animateCounter('neuron-count', '86,000,007,150'), 500);
            setTimeout(() => animateCounter('synapse-count', '602T'), 800);
            setTimeout(() => animateCounter('qi-display', 224), 1100);
            setTimeout(() => animateCounter('accelerators', '2×5'), 1400);
            
            // Mettre à jour les informations
            updateLastUpdate();
            updateTemperature();
            
            // Mettre à jour la température toutes les 5 secondes
            setInterval(updateTemperature, 5000);
            
            console.log('✅ Interface d\'accueil initialisée');
            console.log('🚀 Prêt à lancer l\'interface complète !');
        });

        // Fonction pour lancer l'interface complète
        function lancerInterface() {
            console.log('🚀 Lancement interface complète...');
            window.location.href = 'interface-originale-complete.html';
        }

        // Fonction pour lancer le diagnostic
        function lancerDiagnostic() {
            console.log('🔍 Lancement diagnostic...');
            window.location.href = 'test-affichage-86-milliards.html';
        }
    </script>
</body>
</html>
