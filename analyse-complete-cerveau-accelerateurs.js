/**
 * ANALYSE COMPLÈTE DU CERVEAU AVEC ACCÉLÉRATEURS
 * Détail de TOUS les résultats : neurones, synapses, accélérateurs
 */

const fs = require('fs');
const path = require('path');

class AnalyseCompleteCerveau {
    constructor() {
        this.resultats = {
            neurones: {
                total: 0,
                par_zone_cerebrale: {},
                par_zone_thermique: {},
                par_type_apprentissage: {},
                par_specialisation: {},
                actifs: 0,
                inactifs: 0
            },
            synapses: {
                total: 0,
                par_neurone: 0,
                par_zone: {},
                connexions_actives: 0,
                force_moyenne: 0
            },
            accelerateurs: {
                kyber_detectes: 0,
                zones_accelerees: {},
                facteur_acceleration: 1,
                neurogenese_acceleree: 0,
                vitesse_formation: 0
            },
            systeme_thermique: {
                curseur_position: 0,
                zone_actuelle: '',
                temperature_cpu: 0,
                zones_actives: 0,
                migrations_detectees: 0
            },
            performance: {
                operations_par_seconde: 0,
                capacite_memoire: 0,
                efficacite_globale: 0,
                qi_calcule: 0
            }
        };
    }

    // Analyser TOUS les neurones en détail
    analyserTousLesNeurones() {
        console.log('🧠 === ANALYSE COMPLÈTE DES NEURONES ===');
        
        // 1. Lire compteurs.json pour les totaux
        this.analyserCompteursGlobaux();
        
        // 2. Analyser zones cérébrales individuellement
        this.analyserZonesCerebrales();
        
        // 3. Analyser zones thermiques
        this.analyserZonesThermiques();
        
        // 4. Analyser neurones individuels pour détails
        this.analyserNeuronesIndividuels();
        
        return this.resultats.neurones;
    }

    // Analyser compteurs globaux
    analyserCompteursGlobaux() {
        const compteursPath = 'MEMOIRE-REELLE/compteurs.json';
        if (fs.existsSync(compteursPath)) {
            try {
                const compteurs = JSON.parse(fs.readFileSync(compteursPath, 'utf8'));
                
                this.resultats.neurones.total = compteurs.neurones_total;
                this.resultats.synapses.total = compteurs.synapses_total;
                this.resultats.neurones.par_zone_cerebrale = compteurs.neurones_par_zone;
                
                console.log(`📊 TOTAL GLOBAL: ${compteurs.neurones_total.toLocaleString()} neurones`);
                console.log(`🔗 TOTAL SYNAPSES: ${compteurs.synapses_total.toLocaleString()}`);
                
                // Calculer ratio synapses/neurones
                this.resultats.synapses.par_neurone = Math.round(compteurs.synapses_total / compteurs.neurones_total);
                console.log(`📈 RATIO: ${this.resultats.synapses.par_neurone.toLocaleString()} synapses/neurone`);
                
            } catch (error) {
                console.log('❌ Erreur lecture compteurs:', error.message);
            }
        }
    }

    // Analyser zones cérébrales en détail
    analyserZonesCerebrales() {
        console.log('\n🧠 === ZONES CÉRÉBRALES DÉTAILLÉES ===');
        
        const neuronesPath = 'MEMOIRE-REELLE/neurones';
        if (!fs.existsSync(neuronesPath)) return;

        const zones = fs.readdirSync(neuronesPath);
        zones.forEach(zone => {
            const zonePath = path.join(neuronesPath, zone);
            if (fs.statSync(zonePath).isDirectory()) {
                const fichiers = fs.readdirSync(zonePath).filter(f => f.endsWith('.json'));
                
                console.log(`   ${zone}: ${fichiers.length.toLocaleString()} fichiers neurones`);
                
                // Analyser quelques neurones pour détails
                const echantillon = fichiers.slice(0, 5);
                let synapses_zone = 0;
                let activations_zone = 0;
                
                echantillon.forEach(fichier => {
                    try {
                        const neurone = JSON.parse(fs.readFileSync(path.join(zonePath, fichier), 'utf8'));
                        if (neurone.synapses) synapses_zone += neurone.synapses.length;
                        if (neurone.activations) activations_zone += neurone.activations;
                    } catch (error) {
                        // Ignorer erreurs
                    }
                });
                
                this.resultats.synapses.par_zone[zone] = synapses_zone;
                console.log(`     → ${synapses_zone} synapses échantillon, ${activations_zone} activations`);
            }
        });
    }

    // Analyser zones thermiques
    analyserZonesThermiques() {
        console.log('\n🌡️ === ZONES THERMIQUES DÉTAILLÉES ===');
        
        const zonesPath = 'MEMOIRE-REELLE/zones-thermiques';
        if (!fs.existsSync(zonesPath)) return;

        const zones = fs.readdirSync(zonesPath);
        zones.forEach(zone => {
            const zonePath = path.join(zonesPath, zone);
            if (fs.statSync(zonePath).isDirectory()) {
                const fichiers = fs.readdirSync(zonePath).filter(f => f.endsWith('.json'));
                
                this.resultats.neurones.par_zone_thermique[zone] = fichiers.length;
                console.log(`   ${zone}: ${fichiers.length} neurones`);
                
                // Analyser température et spécialisation
                if (fichiers.length > 0) {
                    try {
                        const premier = JSON.parse(fs.readFileSync(path.join(zonePath, fichiers[0]), 'utf8'));
                        console.log(`     → Température: ${premier.temperature_actuelle}°C`);
                        console.log(`     → Type: ${premier.type_apprentissage}`);
                    } catch (error) {
                        // Ignorer erreurs
                    }
                }
            }
        });
    }

    // Analyser neurones individuels pour détails
    analyserNeuronesIndividuels() {
        console.log('\n🔬 === ANALYSE NEURONES INDIVIDUELS ===');
        
        // Prendre échantillon de différentes zones
        const echantillons = [
            'MEMOIRE-REELLE/neurones/cortex_prefrontal',
            'MEMOIRE-REELLE/zones-thermiques/zone5_30C',
            'MEMOIRE-REELLE/neurones/hippocampe'
        ];
        
        let total_actifs = 0;
        let total_synapses_individuelles = 0;
        let types_apprentissage = {};
        let specialisations = {};
        
        echantillons.forEach(dossier => {
            if (fs.existsSync(dossier)) {
                const fichiers = fs.readdirSync(dossier).filter(f => f.endsWith('.json')).slice(0, 10);
                
                fichiers.forEach(fichier => {
                    try {
                        const neurone = JSON.parse(fs.readFileSync(path.join(dossier, fichier), 'utf8'));
                        
                        // Compter actifs
                        if (neurone.actif !== false) total_actifs++;
                        
                        // Compter synapses
                        if (neurone.synapses) total_synapses_individuelles += neurone.synapses.length;
                        
                        // Types d'apprentissage
                        if (neurone.type_apprentissage) {
                            types_apprentissage[neurone.type_apprentissage] = (types_apprentissage[neurone.type_apprentissage] || 0) + 1;
                        }
                        
                        // Spécialisations
                        if (neurone.specialisation) {
                            specialisations[neurone.specialisation] = (specialisations[neurone.specialisation] || 0) + 1;
                        }
                        
                    } catch (error) {
                        // Ignorer erreurs
                    }
                });
            }
        });
        
        this.resultats.neurones.actifs = total_actifs;
        this.resultats.neurones.par_type_apprentissage = types_apprentissage;
        this.resultats.neurones.par_specialisation = specialisations;
        
        console.log(`   Neurones actifs (échantillon): ${total_actifs}`);
        console.log(`   Synapses individuelles: ${total_synapses_individuelles}`);
        console.log(`   Types apprentissage:`, types_apprentissage);
        console.log(`   Spécialisations:`, specialisations);
    }

    // Analyser les accélérateurs KYBER
    analyserAccelerateurs() {
        console.log('\n⚡ === ANALYSE ACCÉLÉRATEURS KYBER ===');
        
        // 1. Chercher fichiers accélérateurs
        this.detecterAccelerateursKyber();
        
        // 2. Analyser impact sur neurogenèse
        this.analyserImpactNeurogenese();
        
        // 3. Calculer facteur d'accélération
        this.calculerFacteurAcceleration();
        
        return this.resultats.accelerateurs;
    }

    // Détecter accélérateurs KYBER
    detecterAccelerateursKyber() {
        const fichiersAccelerateurs = [
            'neural-kyber-api-server.js',
            'dynamic-neural-kyber-connector.js',
            'kyber-dashboard.html',
            'modules/kyber-accelerator.js'
        ];
        
        let accelerateurs_actifs = 0;
        
        fichiersAccelerateurs.forEach(fichier => {
            if (fs.existsSync(fichier)) {
                accelerateurs_actifs++;
                console.log(`✅ Accélérateur détecté: ${fichier}`);
                
                // Analyser contenu pour paramètres
                try {
                    const contenu = fs.readFileSync(fichier, 'utf8');
                    
                    // Chercher facteurs d'accélération
                    const facteurs = contenu.match(/acceleration.*[0-9]+/gi) || [];
                    const vitesses = contenu.match(/speed.*[0-9]+/gi) || [];
                    const multipliers = contenu.match(/multiplier.*[0-9]+/gi) || [];
                    
                    if (facteurs.length > 0) console.log(`   → Facteurs: ${facteurs.join(', ')}`);
                    if (vitesses.length > 0) console.log(`   → Vitesses: ${vitesses.join(', ')}`);
                    if (multipliers.length > 0) console.log(`   → Multipliers: ${multipliers.join(', ')}`);
                    
                } catch (error) {
                    // Ignorer erreurs lecture
                }
            }
        });
        
        this.resultats.accelerateurs.kyber_detectes = accelerateurs_actifs;
        console.log(`📊 Total accélérateurs KYBER: ${accelerateurs_actifs}`);
    }

    // Analyser impact sur neurogenèse
    analyserImpactNeurogenese() {
        console.log('\n🧬 === IMPACT ACCÉLÉRATEURS SUR NEUROGENÈSE ===');
        
        // Calculer vitesse de création basée sur timestamps
        const zones = ['MEMOIRE-REELLE/neurones/cortex_prefrontal', 'MEMOIRE-REELLE/zones-thermiques/zone5_30C'];
        let timestamps = [];
        
        zones.forEach(zone => {
            if (fs.existsSync(zone)) {
                const fichiers = fs.readdirSync(zone).filter(f => f.endsWith('.json')).slice(0, 100);
                
                fichiers.forEach(fichier => {
                    try {
                        const neurone = JSON.parse(fs.readFileSync(path.join(zone, fichier), 'utf8'));
                        if (neurone.date_creation) {
                            timestamps.push(neurone.date_creation);
                        }
                    } catch (error) {
                        // Ignorer erreurs
                    }
                });
            }
        });
        
        if (timestamps.length > 1) {
            timestamps.sort((a, b) => a - b);
            const premier = timestamps[0];
            const dernier = timestamps[timestamps.length - 1];
            const duree_ms = dernier - premier;
            const duree_secondes = duree_ms / 1000;
            
            const vitesse_creation = timestamps.length / duree_secondes;
            const vitesse_par_minute = vitesse_creation * 60;
            const vitesse_par_heure = vitesse_par_minute * 60;
            
            console.log(`   Période analysée: ${duree_secondes.toFixed(2)} secondes`);
            console.log(`   Neurones créés: ${timestamps.length}`);
            console.log(`   Vitesse: ${vitesse_creation.toFixed(2)} neurones/seconde`);
            console.log(`   Vitesse: ${vitesse_par_minute.toFixed(0)} neurones/minute`);
            console.log(`   Vitesse: ${vitesse_par_heure.toFixed(0)} neurones/heure`);
            
            this.resultats.accelerateurs.neurogenese_acceleree = vitesse_par_heure;
            this.resultats.accelerateurs.vitesse_formation = vitesse_creation;
            
            // Comparer avec vitesse normale (estimation)
            const vitesse_normale = 1; // 1 neurone/seconde sans accélérateur
            const facteur = vitesse_creation / vitesse_normale;
            
            console.log(`   Facteur d'accélération estimé: ×${facteur.toFixed(1)}`);
            this.resultats.accelerateurs.facteur_acceleration = facteur;
        }
    }

    // Calculer facteur d'accélération global
    calculerFacteurAcceleration() {
        console.log('\n📈 === CALCUL FACTEUR ACCÉLÉRATION GLOBAL ===');
        
        const nb_accelerateurs = this.resultats.accelerateurs.kyber_detectes;
        const vitesse_neurogenese = this.resultats.accelerateurs.vitesse_formation;
        
        // Formule d'accélération basée sur nombre d'accélérateurs
        let facteur_base = 1;
        if (nb_accelerateurs >= 4) facteur_base = 10; // 4+ accélérateurs = ×10
        else if (nb_accelerateurs >= 2) facteur_base = 5; // 2-3 accélérateurs = ×5
        else if (nb_accelerateurs >= 1) facteur_base = 2; // 1 accélérateur = ×2
        
        // Ajuster selon vitesse observée
        const facteur_observe = vitesse_neurogenese > 1 ? vitesse_neurogenese : 1;
        const facteur_final = Math.max(facteur_base, facteur_observe);
        
        console.log(`   Facteur base (${nb_accelerateurs} accélérateurs): ×${facteur_base}`);
        console.log(`   Facteur observé: ×${facteur_observe.toFixed(1)}`);
        console.log(`   Facteur final: ×${facteur_final.toFixed(1)}`);
        
        this.resultats.accelerateurs.facteur_acceleration = facteur_final;
        
        // Calculer neurogenèse théorique avec accélérateurs
        const neurogenese_base = 700; // neurones/jour sans accélérateur
        const neurogenese_acceleree = neurogenese_base * facteur_final;
        
        console.log(`   Neurogenèse base: ${neurogenese_base} neurones/jour`);
        console.log(`   Neurogenèse accélérée: ${neurogenese_acceleree.toLocaleString()} neurones/jour`);
        
        this.resultats.accelerateurs.neurogenese_acceleree = neurogenese_acceleree;
    }

    // Analyser système thermique
    analyserSystemeThermique() {
        console.log('\n🌡️ === ANALYSE SYSTÈME THERMIQUE ===');
        
        const curseurPath = 'MEMOIRE-REELLE/curseur-thermique/position_curseur.json';
        if (fs.existsSync(curseurPath)) {
            try {
                const curseur = JSON.parse(fs.readFileSync(curseurPath, 'utf8'));
                
                this.resultats.systeme_thermique.curseur_position = curseur.curseur.position_actuelle;
                this.resultats.systeme_thermique.zone_actuelle = curseur.curseur.zone_actuelle;
                this.resultats.systeme_thermique.temperature_cpu = curseur.curseur.temperature_cpu_actuelle;
                
                console.log(`   Position curseur: ${curseur.curseur.position_actuelle}°C`);
                console.log(`   Zone actuelle: ${curseur.curseur.zone_actuelle}`);
                console.log(`   Température CPU: ${curseur.curseur.temperature_cpu_actuelle}°C`);
                
                // Analyser historique migrations
                if (curseur.curseur.historique_positions) {
                    this.resultats.systeme_thermique.migrations_detectees = curseur.curseur.historique_positions.length;
                    console.log(`   Migrations détectées: ${curseur.curseur.historique_positions.length}`);
                }
                
            } catch (error) {
                console.log('❌ Erreur lecture curseur thermique:', error.message);
            }
        }
        
        // Compter zones thermiques actives
        const zonesPath = 'MEMOIRE-REELLE/zones-thermiques';
        if (fs.existsSync(zonesPath)) {
            const zones = fs.readdirSync(zonesPath);
            this.resultats.systeme_thermique.zones_actives = zones.length;
            console.log(`   Zones thermiques actives: ${zones.length}`);
        }
        
        return this.resultats.systeme_thermique;
    }

    // Calculer performance globale
    calculerPerformanceGlobale() {
        console.log('\n🚀 === CALCUL PERFORMANCE GLOBALE ===');
        
        const neurones = this.resultats.neurones.total;
        const synapses = this.resultats.synapses.total;
        const facteur_acceleration = this.resultats.accelerateurs.facteur_acceleration;
        
        // Opérations par seconde (avec accélérateurs)
        const ops_base = neurones * 1000; // 1000 ops/neurone/sec
        const ops_accelerees = ops_base * facteur_acceleration;
        this.resultats.performance.operations_par_seconde = ops_accelerees;
        
        // Capacité mémoire (approximative)
        const memoire_bits = synapses * 4; // 4 bits par synapse
        const memoire_gb = memoire_bits / (8 * 1024 * 1024 * 1024);
        this.resultats.performance.capacite_memoire = memoire_gb;
        
        // QI calculé (avec bonus accélérateurs)
        const qi_base = 100 + Math.log10(neurones / 1000000) * 20;
        const bonus_accelerateurs = facteur_acceleration * 5; // +5 QI par facteur
        const qi_final = Math.min(250, qi_base + bonus_accelerateurs);
        this.resultats.performance.qi_calcule = Math.round(qi_final);
        
        // Efficacité globale
        const efficacite = Math.min(100, (facteur_acceleration / 10) * 100);
        this.resultats.performance.efficacite_globale = Math.round(efficacite);
        
        console.log(`   Opérations/sec: ${(ops_accelerees / 1e12).toFixed(2)} TeraOps`);
        console.log(`   Capacité mémoire: ${memoire_gb.toFixed(2)} GB`);
        console.log(`   QI calculé: ${qi_final.toFixed(0)}`);
        console.log(`   Efficacité: ${efficacite.toFixed(0)}%`);
        
        return this.resultats.performance;
    }

    // Analyse complète
    analyseComplete() {
        console.log('🎯 === ANALYSE COMPLÈTE CERVEAU + ACCÉLÉRATEURS ===\n');
        
        const neurones = this.analyserTousLesNeurones();
        const accelerateurs = this.analyserAccelerateurs();
        const thermique = this.analyserSystemeThermique();
        const performance = this.calculerPerformanceGlobale();
        
        return this.genererRapportComplet();
    }

    // Générer rapport complet
    genererRapportComplet() {
        console.log('\n📊 === RAPPORT COMPLET FINAL ===');
        
        const r = this.resultats;
        
        console.log('\n🧠 === NEURONES ===');
        console.log(`Total: ${r.neurones.total.toLocaleString()}`);
        console.log(`Actifs: ${r.neurones.actifs}`);
        console.log(`Zones cérébrales: ${Object.keys(r.neurones.par_zone_cerebrale).length}`);
        console.log(`Zones thermiques: ${Object.keys(r.neurones.par_zone_thermique).length}`);
        
        console.log('\n🔗 === SYNAPSES ===');
        console.log(`Total: ${r.synapses.total.toLocaleString()}`);
        console.log(`Par neurone: ${r.synapses.par_neurone.toLocaleString()}`);
        
        console.log('\n⚡ === ACCÉLÉRATEURS ===');
        console.log(`KYBER détectés: ${r.accelerateurs.kyber_detectes}`);
        console.log(`Facteur accélération: ×${r.accelerateurs.facteur_acceleration.toFixed(1)}`);
        console.log(`Neurogenèse accélérée: ${r.accelerateurs.neurogenese_acceleree.toLocaleString()} neurones/jour`);
        console.log(`Vitesse formation: ${r.accelerateurs.vitesse_formation.toFixed(2)} neurones/sec`);
        
        console.log('\n🌡️ === SYSTÈME THERMIQUE ===');
        console.log(`Curseur: ${r.systeme_thermique.curseur_position}°C`);
        console.log(`Zone: ${r.systeme_thermique.zone_actuelle}`);
        console.log(`CPU: ${r.systeme_thermique.temperature_cpu}°C`);
        console.log(`Zones actives: ${r.systeme_thermique.zones_actives}`);
        
        console.log('\n🚀 === PERFORMANCE ===');
        console.log(`Opérations: ${(r.performance.operations_par_seconde / 1e12).toFixed(2)} TeraOps/sec`);
        console.log(`Mémoire: ${r.performance.capacite_memoire.toFixed(2)} GB`);
        console.log(`QI: ${r.performance.qi_calcule}`);
        console.log(`Efficacité: ${r.performance.efficacite_globale}%`);
        
        return this.resultats;
    }
}

// Exécution
const analyseur = new AnalyseCompleteCerveau();
const resultats = analyseur.analyseComplete();

console.log('\n🎉 === CONCLUSION ===');
console.log('ANALYSE COMPLÈTE TERMINÉE !');
console.log(`${resultats.neurones.total.toLocaleString()} neurones avec ${resultats.accelerateurs.kyber_detectes} accélérateurs KYBER`);
console.log(`Vitesse de développement: ×${resultats.accelerateurs.facteur_acceleration.toFixed(1)} grâce aux accélérateurs !`);

module.exports = { AnalyseCompleteCerveau, resultats };
