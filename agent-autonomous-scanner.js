/**
 * Agent Autonome Scanner - LOUNA AI
 * Permet à l'agent de scanner le système de manière autonome
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

class AutonomousAgentScanner {
    constructor() {
        this.agentName = 'LOUNA AI Agent QI 185 (<PERSON><PERSON><PERSON>)';
        this.scanResults = {
            timestamp: new Date().toISOString(),
            agent: this.agentName,
            systemStatus: 'SCANNING',
            modules: {},
            recommendations: [],
            score: 0
        };
        
        console.log(`🤖 ${this.agentName} - Scanner Autonome Initialisé`);
    }

    // Log agent avec timestamp
    agentLog(message, type = 'INFO') {
        const timestamp = new Date().toLocaleTimeString();
        const icon = type === 'SUCCESS' ? '✅' : type === 'ERROR' ? '❌' : type === 'WARNING' ? '⚠️' : '🔍';
        console.log(`[${timestamp}] ${icon} [AGENT] ${message}`);
    }

    // Scanner les applications
    async scanApplications() {
        this.agentLog('Démarrage scan applications autonome...', 'INFO');
        
        const appsDir = './applications-originales';
        const mainInterface = './interface-originale-complete.html';
        
        let appResults = {
            totalApps: 0,
            workingApps: 0,
            brokenLinks: 0,
            missingFiles: [],
            navigationScore: 0
        };

        try {
            // Scanner le répertoire applications
            if (fs.existsSync(appsDir)) {
                const files = fs.readdirSync(appsDir).filter(file => file.endsWith('.html'));
                appResults.totalApps = files.length;
                
                this.agentLog(`Analyse de ${files.length} applications...`, 'INFO');
                
                // Vérifier chaque application
                for (const file of files) {
                    const filePath = path.join(appsDir, file);
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        
                        // Vérifier navigation retour
                        if (content.includes('../interface-originale-complete.html')) {
                            appResults.workingApps++;
                        }
                        
                        // Vérifier liens cassés
                        const brokenLinks = (content.match(/href=["']\/[^"']*["']/g) || []).length;
                        appResults.brokenLinks += brokenLinks;
                        
                    } catch (error) {
                        appResults.missingFiles.push(file);
                        this.agentLog(`Erreur lecture ${file}: ${error.message}`, 'ERROR');
                    }
                }
                
                appResults.navigationScore = Math.round((appResults.workingApps / appResults.totalApps) * 100);
                
            } else {
                this.agentLog('Répertoire applications non trouvé', 'ERROR');
            }
            
            // Vérifier interface principale
            if (fs.existsSync(mainInterface)) {
                const mainContent = fs.readFileSync(mainInterface, 'utf8');
                const appLinksCount = (mainContent.match(/applications-originales\/[^"']+\.html/g) || []).length;
                this.agentLog(`Interface principale: ${appLinksCount} liens vers applications`, 'SUCCESS');
            }
            
            this.scanResults.modules.applications = appResults;
            this.agentLog(`Applications: ${appResults.workingApps}/${appResults.totalApps} fonctionnelles (${appResults.navigationScore}%)`, 'SUCCESS');
            
        } catch (error) {
            this.agentLog(`Erreur scan applications: ${error.message}`, 'ERROR');
        }
    }

    // Scanner les interfaces de chat
    async scanChatInterfaces() {
        this.agentLog('Analyse interfaces de chat...', 'INFO');
        
        const chatFiles = [
            'applications-originales/chat-agents.html',
            'applications-originales/chat-cognitif-complet.html',
            'applications-originales/chat.html'
        ];
        
        let chatResults = {
            totalInterfaces: chatFiles.length,
            workingInterfaces: 0,
            deepSeekIntegration: false,
            internetCapability: false,
            qiCorrect: false
        };
        
        for (const chatFile of chatFiles) {
            if (fs.existsSync(chatFile)) {
                try {
                    const content = fs.readFileSync(chatFile, 'utf8');
                    
                    // Vérifier navigation
                    if (content.includes('../interface-originale-complete.html')) {
                        chatResults.workingInterfaces++;
                    }
                    
                    // Vérifier DeepSeek
                    if (content.includes('DeepSeek') || content.includes('deepseek')) {
                        chatResults.deepSeekIntegration = true;
                    }
                    
                    // Vérifier capacités Internet
                    if (content.includes('fetch(') || content.includes('search') || content.includes('internet')) {
                        chatResults.internetCapability = true;
                    }
                    
                    // Vérifier QI correct
                    if (content.includes('QI 185') || content.includes('QI: 185')) {
                        chatResults.qiCorrect = true;
                    }
                    
                } catch (error) {
                    this.agentLog(`Erreur lecture ${chatFile}: ${error.message}`, 'ERROR');
                }
            }
        }
        
        this.scanResults.modules.chat = chatResults;
        this.agentLog(`Chat: ${chatResults.workingInterfaces}/${chatResults.totalInterfaces} interfaces fonctionnelles`, 'SUCCESS');
    }

    // Scanner le mode MCP
    async scanMCPMode() {
        this.agentLog('Vérification mode MCP...', 'INFO');
        
        let mcpResults = {
            serverActive: false,
            interfaceExists: false,
            internetAccess: false,
            desktopAccess: false,
            systemCommands: false,
            port: 3002
        };
        
        // Vérifier interface MCP
        if (fs.existsSync('applications-originales/mcp-interface.html')) {
            mcpResults.interfaceExists = true;
            this.agentLog('Interface MCP trouvée', 'SUCCESS');
        }
        
        // Tester serveur MCP
        try {
            const response = await this.testMCPServer(mcpResults.port);
            if (response.success) {
                mcpResults.serverActive = true;
                mcpResults.internetAccess = response.data.capabilities?.internet || false;
                mcpResults.desktopAccess = response.data.capabilities?.desktop || false;
                mcpResults.systemCommands = response.data.capabilities?.systemCommands || false;
                this.agentLog('Serveur MCP actif et fonctionnel', 'SUCCESS');
            } else {
                this.agentLog('Serveur MCP non accessible', 'WARNING');
            }
        } catch (error) {
            this.agentLog(`Erreur test MCP: ${error.message}`, 'ERROR');
        }
        
        this.scanResults.modules.mcp = mcpResults;
    }

    // Tester serveur MCP
    testMCPServer(port) {
        return new Promise((resolve) => {
            const req = http.get(`http://localhost:${port}/mcp/status`, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        resolve({ success: true, data: response });
                    } catch (error) {
                        resolve({ success: false, error: error.message });
                    }
                });
            });
            
            req.on('error', (error) => {
                resolve({ success: false, error: error.message });
            });
            
            req.setTimeout(3000, () => {
                req.destroy();
                resolve({ success: false, error: 'Timeout' });
            });
        });
    }

    // Scanner mémoire thermique
    async scanThermalMemory() {
        this.agentLog('Analyse mémoire thermique...', 'INFO');
        
        let thermalResults = {
            dashboardExists: false,
            temperatureCorrect: false,
            neuronsCorrect: false,
            qiCorrect: false,
            mobiusSystem: false
        };
        
        const thermalFiles = [
            'applications-originales/thermal-memory-dashboard.html',
            'applications-originales/memory-persistence-system.html',
            'applications-originales/memory-control.html'
        ];
        
        for (const file of thermalFiles) {
            if (fs.existsSync(file)) {
                thermalResults.dashboardExists = true;
                try {
                    const content = fs.readFileSync(file, 'utf8');
                    
                    if (content.includes('37.2°C')) {
                        thermalResults.temperatureCorrect = true;
                    }
                    
                    if (content.includes('1,064,012')) {
                        thermalResults.neuronsCorrect = true;
                    }
                    
                    if (content.includes('QI 185') || content.includes('QI: 185')) {
                        thermalResults.qiCorrect = true;
                    }
                    
                    if (content.includes('Möbius') || content.includes('mobius')) {
                        thermalResults.mobiusSystem = true;
                    }
                    
                } catch (error) {
                    this.agentLog(`Erreur lecture ${file}: ${error.message}`, 'ERROR');
                }
            }
        }
        
        this.scanResults.modules.thermal = thermalResults;
        this.agentLog('Mémoire thermique analysée', 'SUCCESS');
    }

    // Générer recommandations
    generateRecommendations() {
        this.agentLog('Génération recommandations...', 'INFO');
        
        const { applications, chat, mcp, thermal } = this.scanResults.modules;
        
        // Recommandations applications
        if (applications && applications.navigationScore < 100) {
            this.scanResults.recommendations.push({
                priority: 'HIGH',
                module: 'Applications',
                issue: `${applications.totalApps - applications.workingApps} applications sans navigation retour`,
                solution: 'Corriger les liens vers ../interface-originale-complete.html'
            });
        }
        
        if (applications && applications.brokenLinks > 0) {
            this.scanResults.recommendations.push({
                priority: 'MEDIUM',
                module: 'Applications',
                issue: `${applications.brokenLinks} liens cassés détectés`,
                solution: 'Corriger les liens href="/" vers les bonnes destinations'
            });
        }
        
        // Recommandations chat
        if (chat && !chat.qiCorrect) {
            this.scanResults.recommendations.push({
                priority: 'HIGH',
                module: 'Chat',
                issue: 'QI incorrect dans les interfaces de chat',
                solution: 'Corriger QI vers 185 (Jean-Luc) dans toutes les interfaces'
            });
        }
        
        // Recommandations MCP
        if (mcp && !mcp.serverActive) {
            this.scanResults.recommendations.push({
                priority: 'HIGH',
                module: 'MCP',
                issue: 'Serveur MCP non actif',
                solution: 'Démarrer le serveur MCP: node mcp-server-local.js'
            });
        }
        
        // Recommandations mémoire thermique
        if (thermal && !thermal.qiCorrect) {
            this.scanResults.recommendations.push({
                priority: 'MEDIUM',
                module: 'Thermal',
                issue: 'QI incorrect dans mémoire thermique',
                solution: 'Mettre à jour QI vers 185 dans thermal-memory-dashboard.html'
            });
        }
    }

    // Calculer score global
    calculateGlobalScore() {
        let totalScore = 0;
        let moduleCount = 0;
        
        const { applications, chat, mcp, thermal } = this.scanResults.modules;
        
        if (applications) {
            totalScore += applications.navigationScore || 0;
            moduleCount++;
        }
        
        if (chat) {
            const chatScore = (chat.workingInterfaces / chat.totalInterfaces) * 100;
            totalScore += chatScore;
            moduleCount++;
        }
        
        if (mcp) {
            const mcpScore = (mcp.serverActive ? 50 : 0) + (mcp.interfaceExists ? 50 : 0);
            totalScore += mcpScore;
            moduleCount++;
        }
        
        if (thermal) {
            const thermalScore = (thermal.dashboardExists ? 25 : 0) + 
                                (thermal.temperatureCorrect ? 25 : 0) + 
                                (thermal.neuronsCorrect ? 25 : 0) + 
                                (thermal.qiCorrect ? 25 : 0);
            totalScore += thermalScore;
            moduleCount++;
        }
        
        this.scanResults.score = moduleCount > 0 ? Math.round(totalScore / moduleCount) : 0;
    }

    // Scanner complet autonome
    async performFullScan() {
        this.agentLog('🚀 DÉMARRAGE SCAN COMPLET AUTONOME', 'INFO');
        this.agentLog(`Agent: ${this.agentName}`, 'INFO');
        
        try {
            await this.scanApplications();
            await this.scanChatInterfaces();
            await this.scanMCPMode();
            await this.scanThermalMemory();
            
            this.generateRecommendations();
            this.calculateGlobalScore();
            
            this.scanResults.systemStatus = 'COMPLETED';
            
            // Rapport final
            this.agentLog('📊 RAPPORT FINAL AGENT:', 'SUCCESS');
            this.agentLog(`Score Global: ${this.scanResults.score}%`, 'SUCCESS');
            this.agentLog(`Applications: ${this.scanResults.modules.applications?.workingApps}/${this.scanResults.modules.applications?.totalApps}`, 'SUCCESS');
            this.agentLog(`Chat: ${this.scanResults.modules.chat?.workingInterfaces}/${this.scanResults.modules.chat?.totalInterfaces}`, 'SUCCESS');
            this.agentLog(`MCP: ${this.scanResults.modules.mcp?.serverActive ? 'Actif' : 'Inactif'}`, this.scanResults.modules.mcp?.serverActive ? 'SUCCESS' : 'WARNING');
            this.agentLog(`Mémoire Thermique: ${this.scanResults.modules.thermal?.dashboardExists ? 'OK' : 'Problème'}`, this.scanResults.modules.thermal?.dashboardExists ? 'SUCCESS' : 'WARNING');
            
            if (this.scanResults.recommendations.length > 0) {
                this.agentLog(`${this.scanResults.recommendations.length} recommandations générées`, 'INFO');
                this.scanResults.recommendations.forEach((rec, index) => {
                    this.agentLog(`${index + 1}. [${rec.priority}] ${rec.module}: ${rec.issue}`, 'WARNING');
                });
            } else {
                this.agentLog('Aucune recommandation - Système optimal !', 'SUCCESS');
            }
            
            // Sauvegarder rapport
            fs.writeFileSync('agent-scan-report.json', JSON.stringify(this.scanResults, null, 2));
            this.agentLog('Rapport sauvegardé: agent-scan-report.json', 'SUCCESS');
            
            this.agentLog('🎉 SCAN AUTONOME TERMINÉ AVEC SUCCÈS', 'SUCCESS');
            
        } catch (error) {
            this.agentLog(`Erreur scan autonome: ${error.message}`, 'ERROR');
            this.scanResults.systemStatus = 'ERROR';
        }
        
        return this.scanResults;
    }
}

// Exécuter le scan si ce fichier est lancé directement
if (require.main === module) {
    const agent = new AutonomousAgentScanner();
    
    agent.performFullScan()
        .then(results => {
            console.log('\n🤖 === SCAN AUTONOME AGENT TERMINÉ ===');
            console.log(`📊 Score Final: ${results.score}%`);
            console.log(`📋 Recommandations: ${results.recommendations.length}`);
            console.log('📄 Rapport détaillé: agent-scan-report.json');
        })
        .catch(error => {
            console.error('❌ Erreur scan autonome:', error.message);
        });
}

module.exports = AutonomousAgentScanner;
