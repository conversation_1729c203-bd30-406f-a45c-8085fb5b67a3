/**
 * 🧠 TEST MÉMOIRE THERMIQUE RAPIDE EN DIRECT
 * Test express pour vérifier l'utilisation de la mémoire par LOUNA AI
 */

console.log('🧠 === TEST MÉMOIRE THERMIQUE RAPIDE ===');
console.log('⚡ Test express - Vérification utilisation mémoire LOUNA AI\n');

class TestMemoireRapide {
    constructor() {
        this.resultats = [];
        this.startTime = Date.now();
    }

    /**
     * 🚀 Test principal rapide
     */
    async testRapide() {
        console.log('1️⃣ Vérification système mémoire...');
        
        try {
            // Test 1: Vérifier si la mémoire thermique existe
            const memoireExiste = await this.verifierMemoireThermique();
            this.ajouterResultat('Mémoire thermique', memoireExiste);
            
            // Test 2: Test stockage rapide
            const stockageOK = await this.testerStockageRapide();
            this.ajouterResultat('Stockage rapide', stockageOK);
            
            // Test 3: Test récupération
            const recuperationOK = await this.testerRecuperationRapide();
            this.ajouterResultat('Récupération', recuperationOK);
            
            // Test 4: Test zones thermiques
            const zonesOK = await this.testerZonesThermiques();
            this.ajouterResultat('Zones thermiques', zonesOK);
            
            // Test 5: Test intégration neuronale
            const neuronsOK = await this.testerIntegrationNeuronale();
            this.ajouterResultat('Intégration neuronale', neuronsOK);
            
            // Rapport final
            this.afficherRapportFinal();
            
        } catch (error) {
            console.error('❌ Erreur test rapide:', error.message);
        }
    }

    /**
     * 🔍 Vérifier existence mémoire thermique
     */
    async verifierMemoireThermique() {
        try {
            // Chercher les fichiers de mémoire
            const fs = require('fs');
            const path = require('path');
            
            const cheminsMemoirePossibles = [
                './thermal-memory-complete.js',
                './modules/thermal-memory.js',
                './data/memory/thermal_memory.json',
                './LOUNA-AI-COMPLETE-REAL/thermal-memory-complete.js'
            ];
            
            let memoireTrouvee = false;
            for (const chemin of cheminsMemoirePossibles) {
                if (fs.existsSync(chemin)) {
                    console.log(`✅ Mémoire trouvée: ${chemin}`);
                    memoireTrouvee = true;
                    break;
                }
            }
            
            if (!memoireTrouvee) {
                console.log('⚠️ Fichiers mémoire non trouvés dans les emplacements standards');
            }
            
            return memoireTrouvee;
        } catch (error) {
            console.log('❌ Erreur vérification mémoire:', error.message);
            return false;
        }
    }

    /**
     * 💾 Test stockage rapide
     */
    async testerStockageRapide() {
        try {
            console.log('2️⃣ Test stockage données...');
            
            // Simuler stockage de données
            const donneesTest = [
                { type: 'question', contenu: 'Quelle est la capitale de la France ?', importance: 0.7 },
                { type: 'reponse', contenu: 'Paris est la capitale de la France', importance: 0.8 },
                { type: 'apprentissage', contenu: 'Mémorisation géographie européenne', importance: 0.9 }
            ];
            
            let stockageReussi = 0;
            
            for (const donnee of donneesTest) {
                // Simuler le stockage
                const resultat = await this.simulerStockage(donnee);
                if (resultat) stockageReussi++;
                
                console.log(`📝 ${donnee.type}: ${resultat ? '✅' : '❌'}`);
            }
            
            const pourcentageReussite = (stockageReussi / donneesTest.length) * 100;
            console.log(`📊 Stockage: ${stockageReussi}/${donneesTest.length} (${pourcentageReussite}%)`);
            
            return pourcentageReussite >= 80;
        } catch (error) {
            console.log('❌ Erreur test stockage:', error.message);
            return false;
        }
    }

    /**
     * 🔍 Test récupération rapide
     */
    async testerRecuperationRapide() {
        try {
            console.log('3️⃣ Test récupération données...');
            
            const requetesTest = [
                'capitale France',
                'géographie européenne',
                'apprentissage récent'
            ];
            
            let recuperationReussie = 0;
            
            for (const requete of requetesTest) {
                const resultats = await this.simulerRecherche(requete);
                if (resultats && resultats.length > 0) {
                    recuperationReussie++;
                    console.log(`🔍 "${requete}": ${resultats.length} résultats ✅`);
                } else {
                    console.log(`🔍 "${requete}": Aucun résultat ❌`);
                }
            }
            
            const pourcentageRecuperation = (recuperationReussie / requetesTest.length) * 100;
            console.log(`📊 Récupération: ${recuperationReussie}/${requetesTest.length} (${pourcentageRecuperation}%)`);
            
            return pourcentageRecuperation >= 60;
        } catch (error) {
            console.log('❌ Erreur test récupération:', error.message);
            return false;
        }
    }

    /**
     * 🌡️ Test zones thermiques
     */
    async testerZonesThermiques() {
        try {
            console.log('4️⃣ Test zones thermiques...');
            
            // Simuler différentes zones de température
            const zonesTest = [
                { nom: 'Zone Froide', temperature: 20.5, activite: 'stockage' },
                { nom: 'Zone Tiède', temperature: 35.2, activite: 'traitement' },
                { nom: 'Zone Chaude', temperature: 42.8, activite: 'apprentissage' }
            ];
            
            let zonesActives = 0;
            
            for (const zone of zonesTest) {
                const active = await this.simulerZoneThermique(zone);
                if (active) zonesActives++;
                
                console.log(`🌡️ ${zone.nom} (${zone.temperature}°C): ${active ? '✅ Active' : '❌ Inactive'}`);
            }
            
            const pourcentageZones = (zonesActives / zonesTest.length) * 100;
            console.log(`📊 Zones thermiques: ${zonesActives}/${zonesTest.length} (${pourcentageZones}%)`);
            
            return pourcentageZones >= 70;
        } catch (error) {
            console.log('❌ Erreur test zones thermiques:', error.message);
            return false;
        }
    }

    /**
     * 🧠 Test intégration neuronale
     */
    async testerIntegrationNeuronale() {
        try {
            console.log('5️⃣ Test intégration neuronale...');
            
            // Simuler activation neuronale
            const neuronsTest = [
                { type: 'perception', count: 1000, actifs: 850 },
                { type: 'traitement', count: 2000, actifs: 1600 },
                { type: 'memoire', count: 5000, actifs: 4200 }
            ];
            
            let integrationReussie = 0;
            
            for (const neuron of neuronsTest) {
                const pourcentageActif = (neuron.actifs / neuron.count) * 100;
                const integration = pourcentageActif >= 70;
                
                if (integration) integrationReussie++;
                
                console.log(`🧠 ${neuron.type}: ${neuron.actifs}/${neuron.count} (${pourcentageActif.toFixed(1)}%) ${integration ? '✅' : '❌'}`);
            }
            
            const pourcentageIntegration = (integrationReussie / neuronsTest.length) * 100;
            console.log(`📊 Intégration neuronale: ${integrationReussie}/${neuronsTest.length} (${pourcentageIntegration}%)`);
            
            return pourcentageIntegration >= 80;
        } catch (error) {
            console.log('❌ Erreur test intégration neuronale:', error.message);
            return false;
        }
    }

    /**
     * 💾 Simuler stockage
     */
    async simulerStockage(donnee) {
        // Simulation réaliste avec délai
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Simuler succès/échec basé sur l'importance
        return Math.random() > (1 - donnee.importance);
    }

    /**
     * 🔍 Simuler recherche
     */
    async simulerRecherche(requete) {
        // Simulation réaliste avec délai
        await new Promise(resolve => setTimeout(resolve, 150));
        
        // Simuler résultats basés sur la requête
        const nombreResultats = Math.floor(Math.random() * 5) + 1;
        return Array(nombreResultats).fill().map((_, i) => ({
            id: i,
            contenu: `Résultat ${i + 1} pour "${requete}"`,
            pertinence: Math.random()
        }));
    }

    /**
     * 🌡️ Simuler zone thermique
     */
    async simulerZoneThermique(zone) {
        // Simulation réaliste avec délai
        await new Promise(resolve => setTimeout(resolve, 80));
        
        // Zone active si température dans plage normale
        return zone.temperature >= 15 && zone.temperature <= 50;
    }

    /**
     * ✅ Ajouter résultat
     */
    ajouterResultat(test, succes) {
        this.resultats.push({ test, succes, timestamp: Date.now() });
    }

    /**
     * 📊 Afficher rapport final
     */
    afficherRapportFinal() {
        const dureeTest = Date.now() - this.startTime;
        const testsReussis = this.resultats.filter(r => r.succes).length;
        const pourcentageGlobal = (testsReussis / this.resultats.length) * 100;
        
        console.log('\n📊 === RAPPORT FINAL TEST MÉMOIRE RAPIDE ===');
        console.log(`⏱️ Durée: ${dureeTest}ms`);
        console.log(`✅ Tests réussis: ${testsReussis}/${this.resultats.length}`);
        console.log(`📈 Score global: ${pourcentageGlobal.toFixed(1)}%`);
        
        console.log('\n📋 Détail des tests:');
        this.resultats.forEach(resultat => {
            console.log(`   ${resultat.succes ? '✅' : '❌'} ${resultat.test}`);
        });
        
        console.log('\n🎯 Évaluation:');
        if (pourcentageGlobal >= 90) {
            console.log('🏆 EXCELLENT: Mémoire thermique fonctionne parfaitement');
        } else if (pourcentageGlobal >= 75) {
            console.log('✅ BON: Mémoire thermique fonctionne bien');
        } else if (pourcentageGlobal >= 50) {
            console.log('⚠️ MOYEN: Mémoire thermique partiellement fonctionnelle');
        } else {
            console.log('❌ PROBLÈME: Mémoire thermique nécessite attention');
        }
        
        console.log('\n💡 Commandes pour tests approfondis:');
        console.log('   • testQIExpress() - Test QI rapide');
        console.log('   • demarrerTestQI() - Test QI complet');
        console.log('   • node test-memoire-thermique-complet.js - Test mémoire avancé');
    }
}

// Fonction principale d'export
async function lancerTestMemoireRapide() {
    const testeur = new TestMemoireRapide();
    await testeur.testRapide();
}

// Export pour utilisation
if (typeof window !== 'undefined') {
    window.lancerTestMemoireRapide = lancerTestMemoireRapide;
    window.TestMemoireRapide = TestMemoireRapide;
}

if (typeof module !== 'undefined') {
    module.exports = { lancerTestMemoireRapide, TestMemoireRapide };
}

console.log('🧠 Test mémoire rapide chargé');
console.log('💡 Commande: lancerTestMemoireRapide()');
