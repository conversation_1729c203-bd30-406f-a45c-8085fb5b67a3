/**
 * 🚀 DÉMARRAGE AUTOMATIQUE TEST QI LOUNA AI
 * Lance automatiquement le test QI dans la console
 */

console.log('🚀 === DÉMARRAGE AUTOMATIQUE TEST QI ===');

// Attendre que tous les scripts soient chargés
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        lancerTestQIAutomatique();
    }, 3000); // Attendre 3 secondes pour que tout soit chargé
});

/**
 * 🧠 Lancer le test QI automatiquement
 */
function lancerTestQIAutomatique() {
    console.log('\n🧠 === LANCEMENT AUTOMATIQUE TEST QI LOUNA AI ===');
    console.log('🎯 Test de QI complexe niveau expert');
    console.log('📊 10 questions de difficulté maximale');
    console.log('⚠️ IMPORTANT: Observer les réponses de LOUNA AI, ne pas répondre à sa place !');
    
    // Vérifier que les scripts sont chargés
    if (typeof QIQuestionPresenter === 'undefined') {
        console.log('❌ Scripts QI non chargés, tentative de rechargement...');
        setTimeout(lancerTestQIAutomatique, 2000);
        return;
    }
    
    // Créer le présentateur
    window.currentQIPresenter = new QIQuestionPresenter();
    
    // Démarrer la session
    const session = window.currentQIPresenter.startTestSession();
    
    console.log('\n🎯 === DÉBUT DU TEST ===');
    console.log('👀 LOUNA AI va maintenant recevoir 10 questions complexes');
    console.log('🤔 Observez attentivement ses réponses et son raisonnement');
    console.log('📝 Vous pouvez enregistrer ses réponses avec: enregistrerReponse(id, answer, reasoning, confidence)');
    
    // Présenter toutes les questions
    window.currentQIPresenter.presentAllQuestions();
    
    console.log('\n📋 === INSTRUCTIONS POUR L\'OBSERVATEUR ===');
    console.log('1. 📝 Notez les réponses de LOUNA AI pour chaque question');
    console.log('2. 🤔 Observez son processus de raisonnement');
    console.log('3. 📊 Évaluez son niveau de confiance');
    console.log('4. ✅ Utilisez analyserResultats() quand toutes les réponses sont données');
    
    console.log('\n💡 === COMMANDES UTILES ===');
    console.log('• presenterQuestion(1-10) - Représenter une question');
    console.log('• enregistrerReponse(id, "A", "raisonnement", 8) - Enregistrer une réponse');
    console.log('• analyserResultats() - Analyser les résultats finaux');
    console.log('• window.currentQIPresenter.showDetailedSummary() - Résumé détaillé');
    
    // Démarrer l'observation automatique
    demarrerObservationAutomatique();
}

/**
 * 👀 Démarrer l'observation automatique des réponses
 */
function demarrerObservationAutomatique() {
    console.log('\n👀 === OBSERVATION AUTOMATIQUE ACTIVÉE ===');
    console.log('🔍 Surveillance des réponses de LOUNA AI...');
    
    // Simuler l'observation (en réalité, on observerait les vraies réponses)
    let questionIndex = 1;
    
    const observationInterval = setInterval(() => {
        if (questionIndex > 10) {
            clearInterval(observationInterval);
            console.log('\n🏁 Observation terminée - Prêt pour l\'analyse');
            console.log('💡 Utilisez analyserResultats() pour voir les résultats');
            return;
        }
        
        console.log(`\n👀 Observation question ${questionIndex}...`);
        console.log(`🤔 En attente de la réponse de LOUNA AI pour la question ${questionIndex}`);
        console.log(`💭 LOUNA AI, quelle est votre analyse de cette question ?`);
        
        questionIndex++;
    }, 10000); // Toutes les 10 secondes
    
    // Arrêter l'observation après 2 minutes
    setTimeout(() => {
        clearInterval(observationInterval);
        console.log('\n⏰ Temps d\'observation écoulé');
        console.log('📊 Passez à l\'analyse des résultats disponibles');
    }, 120000);
}

/**
 * 📊 Exemple d'enregistrement de réponses (pour démonstration)
 */
function demonstrationEnregistrement() {
    console.log('\n📝 === DÉMONSTRATION ENREGISTREMENT ===');
    console.log('Voici comment enregistrer les réponses de LOUNA AI :');
    
    // Exemples d'enregistrement
    const exemples = [
        {
            id: 1,
            answer: "A",
            reasoning: "La séquence suit la règle: chaque terme = 2×précédent + 1. Donc 95×2+1=191, 191×2+1=383, 383×2+1=767",
            confidence: 9
        },
        {
            id: 2, 
            answer: "D",
            reasoning: "⊕ est l'opérateur XOR et ⊙ est l'équivalence. L'expression est vraie quand A≠B et C=D",
            confidence: 8
        }
    ];
    
    exemples.forEach(exemple => {
        console.log(`\nExemple question ${exemple.id}:`);
        console.log(`enregistrerReponse(${exemple.id}, "${exemple.answer}", "${exemple.reasoning}", ${exemple.confidence})`);
    });
    
    console.log('\n💡 Adaptez ces exemples aux vraies réponses de LOUNA AI');
}

/**
 * 🎯 Analyser les progrès de LOUNA AI
 */
function analyserProgresLounaAI() {
    console.log('\n📈 === ANALYSE PROGRÈS LOUNA AI ===');
    
    if (!window.currentQIPresenter || !window.currentQIPresenter.currentSession) {
        console.log('❌ Aucune session de test active');
        return;
    }
    
    const session = window.currentQIPresenter.currentSession;
    const responses = Object.values(session.responses);
    const answeredQuestions = responses.filter(r => r.answer);
    
    console.log(`📊 Questions répondues: ${answeredQuestions.length}/10`);
    
    if (answeredQuestions.length > 0) {
        const correctAnswers = answeredQuestions.filter(r => r.isCorrect);
        const accuracy = (correctAnswers.length / answeredQuestions.length) * 100;
        const avgConfidence = answeredQuestions.reduce((sum, r) => sum + (r.confidence || 5), 0) / answeredQuestions.length;
        
        console.log(`✅ Précision actuelle: ${accuracy.toFixed(1)}%`);
        console.log(`🎯 Confiance moyenne: ${avgConfidence.toFixed(1)}/10`);
        
        // Analyse par catégorie
        const categories = {};
        answeredQuestions.forEach(response => {
            const category = response.question.category;
            if (!categories[category]) {
                categories[category] = { correct: 0, total: 0 };
            }
            categories[category].total++;
            if (response.isCorrect) {
                categories[category].correct++;
            }
        });
        
        console.log('\n🎯 Performance par catégorie:');
        Object.entries(categories).forEach(([category, stats]) => {
            const categoryAccuracy = (stats.correct / stats.total) * 100;
            console.log(`   ${category}: ${stats.correct}/${stats.total} (${categoryAccuracy.toFixed(1)}%)`);
        });
        
        // Recommandations
        console.log('\n💡 Recommandations:');
        if (accuracy >= 80) {
            console.log('🌟 Excellente performance ! LOUNA AI montre des capacités cognitives avancées');
        } else if (accuracy >= 60) {
            console.log('👍 Bonne performance ! Quelques domaines à améliorer');
        } else {
            console.log('📈 Potentiel d\'amélioration significatif détecté');
        }
        
        if (avgConfidence < 6) {
            console.log('🤔 Niveau de confiance faible - LOUNA AI pourrait bénéficier de plus d\'entraînement');
        } else if (avgConfidence > 8) {
            console.log('🎯 Niveau de confiance élevé - LOUNA AI semble sûr de ses réponses');
        }
    }
}

/**
 * 🔄 Redémarrer le test
 */
function redemarrerTestQI() {
    console.log('\n🔄 === REDÉMARRAGE TEST QI ===');
    
    // Nettoyer la session précédente
    if (window.currentQIPresenter) {
        window.currentQIPresenter = null;
    }
    
    // Relancer le test
    setTimeout(() => {
        lancerTestQIAutomatique();
    }, 1000);
}

/**
 * 📋 Afficher l'aide
 */
function afficherAideTestQI() {
    console.log('\n📋 === AIDE TEST QI LOUNA AI ===');
    console.log('\n🎯 OBJECTIF:');
    console.log('   Évaluer les capacités cognitives de LOUNA AI avec 10 questions complexes');
    
    console.log('\n📝 PROCÉDURE:');
    console.log('   1. Le test présente automatiquement toutes les questions');
    console.log('   2. Observez les réponses de LOUNA AI');
    console.log('   3. Enregistrez ses réponses avec enregistrerReponse()');
    console.log('   4. Analysez les résultats avec analyserResultats()');
    
    console.log('\n💡 COMMANDES PRINCIPALES:');
    console.log('   • lancerTestQIAutomatique() - Démarrer le test');
    console.log('   • presenterQuestion(1-10) - Représenter une question');
    console.log('   • enregistrerReponse(id, answer, reasoning, confidence) - Enregistrer');
    console.log('   • analyserResultats() - Analyser les résultats');
    console.log('   • analyserProgresLounaAI() - Voir les progrès en cours');
    console.log('   • redemarrerTestQI() - Recommencer le test');
    
    console.log('\n🎯 EXEMPLE D\'UTILISATION:');
    console.log('   enregistrerReponse(1, "A", "Séquence arithmétique", 8)');
    
    console.log('\n⚠️ IMPORTANT:');
    console.log('   Ne répondez PAS à la place de LOUNA AI !');
    console.log('   Observez et analysez ses réponses seulement.');
}

// Export des fonctions
window.lancerTestQIAutomatique = lancerTestQIAutomatique;
window.analyserProgresLounaAI = analyserProgresLounaAI;
window.redemarrerTestQI = redemarrerTestQI;
window.afficherAideTestQI = afficherAideTestQI;
window.demonstrationEnregistrement = demonstrationEnregistrement;

console.log('🚀 Script de démarrage automatique test QI chargé');
console.log('💡 Le test se lancera automatiquement dans 3 secondes');
console.log('💡 Utilisez afficherAideTestQI() pour voir l\'aide complète');
