/**
 * 🔍 VÉRIFICATION FINALE DES BOUTONS DE SÉCURITÉ
 * Script de vérification finale pour s'assurer que tous les boutons fonctionnent
 */

console.log('🔍 === VÉRIFICATION FINALE BOUTONS SÉCURITÉ ===');

/**
 * Vérification finale complète
 */
function verificationFinale() {
    console.log('🔍 Démarrage vérification finale...');
    
    const verification = {
        timestamp: new Date().toISOString(),
        boutons: [],
        fonctions: [],
        scripts: [],
        recommandations: [],
        score: 0
    };
    
    // 1. Vérifier la présence des boutons
    console.log('\n📝 Étape 1: Vérification présence des boutons');
    verification.boutons = verifierPresenceBoutons();
    
    // 2. Vérifier la disponibilité des fonctions
    console.log('\n📝 Étape 2: Vérification fonctions JavaScript');
    verification.fonctions = verifierFonctionsJavaScript();
    
    // 3. Vérifier le chargement des scripts
    console.log('\n📝 Étape 3: Vérification scripts chargés');
    verification.scripts = verifierScriptsCharges();
    
    // 4. Calculer le score final
    verification.score = calculerScoreFinal(verification);
    
    // 5. Générer les recommandations
    verification.recommandations = genererRecommandations(verification);
    
    // 6. Afficher le rapport final
    afficherRapportFinal(verification);
    
    return verification;
}

/**
 * Vérifier la présence des boutons dans le DOM
 */
function verifierPresenceBoutons() {
    const boutonsAttendu = [
        { selector: '.btn-hibernation', nom: 'Hibernation', critique: true },
        { selector: '.btn-sleep', nom: 'Sommeil', critique: true },
        { selector: '.btn-wakeup', nom: 'Réveil', critique: true },
        { selector: '.btn-surveillance', nom: 'Surveillance', critique: true },
        { selector: '.btn-backup', nom: 'Sauvegarde', critique: true },
        { selector: '.btn-memory', nom: 'Mémoire', critique: true },
        { selector: 'button[onclick*="toggleDeepSeekChat"]', nom: 'DeepSeek', critique: false },
        { selector: 'button[onclick*="testerTousLesBoutons"]', nom: 'Test', critique: false },
        { selector: 'button[onclick*="diagnostiquerInterface"]', nom: 'Diagnostic', critique: false },
        { selector: 'button[onclick*="forcerCorrectionBoutons"]', nom: 'Corriger', critique: false }
    ];
    
    const resultats = [];
    
    boutonsAttendu.forEach(bouton => {
        const element = document.querySelector(bouton.selector);
        const present = !!element;
        const hasOnclick = element ? (element.onclick || element.getAttribute('onclick')) : false;
        
        const resultat = {
            nom: bouton.nom,
            selector: bouton.selector,
            present: present,
            hasOnclick: hasOnclick,
            critique: bouton.critique,
            fonctionnel: present && hasOnclick,
            element: element
        };
        
        resultats.push(resultat);
        
        const status = resultat.fonctionnel ? '✅' : '❌';
        const critique = bouton.critique ? '🔴' : '🟡';
        console.log(`${status} ${critique} ${bouton.nom}: ${present ? 'Présent' : 'Absent'} | ${hasOnclick ? 'Fonction OK' : 'Pas de fonction'}`);
    });
    
    return resultats;
}

/**
 * Vérifier la disponibilité des fonctions JavaScript
 */
function verifierFonctionsJavaScript() {
    const fonctionsAttendues = [
        { nom: 'activateHibernation', critique: true },
        { nom: 'activateSleep', critique: true },
        { nom: 'wakeupAgent', critique: true },
        { nom: 'openSurveillance', critique: true },
        { nom: 'openBackup', critique: true },
        { nom: 'openMemoryControl', critique: true },
        { nom: 'toggleDeepSeekChat', critique: false },
        { nom: 'testerTousLesBoutons', critique: false },
        { nom: 'diagnostiquerInterface', critique: false },
        { nom: 'forcerCorrectionBoutons', critique: false },
        { nom: 'corrigerTousLesBoutonsSecurite', critique: false },
        { nom: 'testerBoutonsSecuriteComplet', critique: false }
    ];
    
    const resultats = [];
    
    fonctionsAttendues.forEach(fonction => {
        const disponible = typeof window[fonction.nom] === 'function';
        
        const resultat = {
            nom: fonction.nom,
            disponible: disponible,
            critique: fonction.critique,
            type: typeof window[fonction.nom]
        };
        
        resultats.push(resultat);
        
        const status = disponible ? '✅' : '❌';
        const critique = fonction.critique ? '🔴' : '🟡';
        console.log(`${status} ${critique} ${fonction.nom}: ${disponible ? 'Disponible' : 'Non disponible'} (${resultat.type})`);
    });
    
    return resultats;
}

/**
 * Vérifier le chargement des scripts
 */
function verifierScriptsCharges() {
    const scriptsAttendus = [
        'test-boutons.js',
        'diagnostic-interface.js',
        'correction-boutons-securite.js',
        'test-boutons-securite-complet.js'
    ];
    
    const resultats = [];
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    
    scriptsAttendus.forEach(scriptNom => {
        const charge = scripts.some(script => script.src.includes(scriptNom));
        
        const resultat = {
            nom: scriptNom,
            charge: charge,
            src: charge ? scripts.find(s => s.src.includes(scriptNom)).src : null
        };
        
        resultats.push(resultat);
        
        const status = charge ? '✅' : '❌';
        console.log(`${status} Script ${scriptNom}: ${charge ? 'Chargé' : 'Non chargé'}`);
    });
    
    return resultats;
}

/**
 * Calculer le score final
 */
function calculerScoreFinal(verification) {
    let score = 0;
    let maxScore = 0;
    
    // Score des boutons (50% du total)
    verification.boutons.forEach(bouton => {
        const poids = bouton.critique ? 2 : 1;
        maxScore += poids;
        if (bouton.fonctionnel) score += poids;
    });
    
    // Score des fonctions (30% du total)
    verification.fonctions.forEach(fonction => {
        const poids = fonction.critique ? 2 : 1;
        maxScore += poids;
        if (fonction.disponible) score += poids;
    });
    
    // Score des scripts (20% du total)
    verification.scripts.forEach(script => {
        maxScore += 1;
        if (script.charge) score += 1;
    });
    
    return Math.round((score / maxScore) * 100);
}

/**
 * Générer les recommandations
 */
function genererRecommandations(verification) {
    const recommandations = [];
    
    // Boutons manquants ou non fonctionnels
    const boutonsDefaillants = verification.boutons.filter(b => !b.fonctionnel);
    if (boutonsDefaillants.length > 0) {
        const critiques = boutonsDefaillants.filter(b => b.critique);
        if (critiques.length > 0) {
            recommandations.push({
                type: 'critique',
                message: `${critiques.length} boutons critiques non fonctionnels`,
                action: 'Exécuter forcerCorrectionBoutons() immédiatement'
            });
        }
        
        const nonCritiques = boutonsDefaillants.filter(b => !b.critique);
        if (nonCritiques.length > 0) {
            recommandations.push({
                type: 'warning',
                message: `${nonCritiques.length} boutons non critiques à corriger`,
                action: 'Vérifier les fonctions JavaScript associées'
            });
        }
    }
    
    // Fonctions manquantes
    const fonctionsManquantes = verification.fonctions.filter(f => !f.disponible);
    if (fonctionsManquantes.length > 0) {
        const critiques = fonctionsManquantes.filter(f => f.critique);
        if (critiques.length > 0) {
            recommandations.push({
                type: 'critique',
                message: `${critiques.length} fonctions critiques manquantes`,
                action: 'Vérifier le chargement des scripts et la définition des fonctions'
            });
        }
    }
    
    // Scripts manquants
    const scriptsManquants = verification.scripts.filter(s => !s.charge);
    if (scriptsManquants.length > 0) {
        recommandations.push({
            type: 'warning',
            message: `${scriptsManquants.length} scripts non chargés`,
            action: 'Vérifier les balises <script> dans le HTML'
        });
    }
    
    // Recommandation globale
    if (verification.score >= 95) {
        recommandations.push({
            type: 'success',
            message: 'Système de sécurité entièrement fonctionnel',
            action: 'Aucune action requise'
        });
    } else if (verification.score >= 80) {
        recommandations.push({
            type: 'info',
            message: 'Système de sécurité majoritairement fonctionnel',
            action: 'Corrections mineures recommandées'
        });
    } else {
        recommandations.push({
            type: 'critique',
            message: 'Système de sécurité nécessite des corrections importantes',
            action: 'Exécuter une correction complète'
        });
    }
    
    return recommandations;
}

/**
 * Afficher le rapport final
 */
function afficherRapportFinal(verification) {
    console.log('\n🎯 === RAPPORT FINAL DE VÉRIFICATION ===');
    console.log(`📊 Score global: ${verification.score}%`);
    
    // Statistiques
    const boutonsOK = verification.boutons.filter(b => b.fonctionnel).length;
    const fonctionsOK = verification.fonctions.filter(f => f.disponible).length;
    const scriptsOK = verification.scripts.filter(s => s.charge).length;
    
    console.log(`✅ Boutons fonctionnels: ${boutonsOK}/${verification.boutons.length}`);
    console.log(`✅ Fonctions disponibles: ${fonctionsOK}/${verification.fonctions.length}`);
    console.log(`✅ Scripts chargés: ${scriptsOK}/${verification.scripts.length}`);
    
    // Recommandations
    console.log('\n💡 === RECOMMANDATIONS ===');
    verification.recommandations.forEach(rec => {
        const icon = rec.type === 'critique' ? '🚨' : rec.type === 'warning' ? '⚠️' : rec.type === 'success' ? '🎉' : 'ℹ️';
        console.log(`${icon} ${rec.message}`);
        console.log(`   👉 ${rec.action}`);
    });
    
    // Évaluation finale
    if (verification.score >= 95) {
        console.log('\n🎉 EXCELLENT ! Système de sécurité parfaitement fonctionnel !');
    } else if (verification.score >= 80) {
        console.log('\n✅ BIEN ! Système de sécurité majoritairement fonctionnel');
    } else if (verification.score >= 60) {
        console.log('\n⚠️ MOYEN ! Corrections nécessaires');
    } else {
        console.log('\n🚨 CRITIQUE ! Corrections urgentes requises');
    }
}

/**
 * Correction automatique basée sur la vérification
 */
function correctionAutomatique() {
    console.log('🔧 === CORRECTION AUTOMATIQUE ===');
    
    const verification = verificationFinale();
    
    if (verification.score < 80) {
        console.log('🔧 Score insuffisant, lancement correction automatique...');
        
        if (typeof forcerCorrectionBoutons === 'function') {
            forcerCorrectionBoutons();
            console.log('✅ Correction des boutons exécutée');
        }
        
        // Re-vérifier après correction
        setTimeout(() => {
            console.log('🔍 Re-vérification après correction...');
            const nouvelleVerification = verificationFinale();
            
            if (nouvelleVerification.score > verification.score) {
                console.log(`🎉 Amélioration détectée ! Score: ${verification.score}% → ${nouvelleVerification.score}%`);
            } else {
                console.log('⚠️ Aucune amélioration détectée, intervention manuelle requise');
            }
        }, 2000);
    } else {
        console.log('✅ Score suffisant, aucune correction automatique nécessaire');
    }
}

// Exporter les fonctions
window.verificationFinale = verificationFinale;
window.correctionAutomatique = correctionAutomatique;

// Lancer la vérification automatiquement
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🔍 Lancement vérification finale automatique...');
        verificationFinale();
    }, 4000);
});

console.log('🔍 Script de vérification finale chargé');
console.log('💡 Utilisez verificationFinale() pour vérifier manuellement');
console.log('💡 Utilisez correctionAutomatique() pour corriger automatiquement');
