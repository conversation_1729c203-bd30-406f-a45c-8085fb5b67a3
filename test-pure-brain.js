/**
 * 🧪 TEST CERVEAU PUR
 * Test du système cerveau avec veille/activation à la demande
 */

const PureBrainSystem = require('./pure-brain-system');

async function testPureBrain() {
    console.log('🧪 === TEST CERVEAU PUR ===\n');
    
    try {
        // Initialiser le cerveau
        console.log('🚀 Initialisation du cerveau...');
        const brain = new PureBrainSystem();
        
        // Attendre l'initialisation
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test 1: Activation légère
        console.log('\n📝 Test 1: Activation légère (10%)');
        await brain.activateNeurons(0.1, 15000, 'light_thinking');
        
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 2: Génération de pensée
        console.log('\n📝 Test 2: Génération de pensée');
        const thought = await brain.generateThought(0.3);
        console.log(`💭 Pensée générée:`, thought);
        
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Test 3: Activation moyenne
        console.log('\n📝 Test 3: Activation moyenne (30%)');
        await brain.activateNeurons(0.3, 20000, 'medium_thinking');
        
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // Test 4: Métriques finales
        console.log('\n📝 Test 4: Métriques finales');
        const metrics = brain.getCompleteMetrics();
        console.log('\n📊 === MÉTRIQUES FINALES ===');
        console.log(`🧠 Neurones en veille: ${metrics.state.standby.toLocaleString()}`);
        console.log(`⚡ Neurones actifs: ${metrics.state.active.toLocaleString()}`);
        console.log(`😴 Neurones hibernation: ${metrics.state.hibernating.toLocaleString()}`);
        console.log(`🔗 Synapses totales: ${metrics.state.totalSynapses.toLocaleString()}`);
        console.log(`🔄 Phase Möbius: ${metrics.mobius.currentPhase}`);
        console.log(`🔄 Cycles Möbius: ${metrics.mobius.cycleCount}`);
        console.log(`⚡ Kyber turbos: ${metrics.kyber.total} (boost: ${metrics.kyber.boost.toFixed(1)}x)`);
        console.log(`📊 Mémoire utilisée: ${metrics.metrics.memoryUsage.toFixed(2)}%`);
        console.log(`💭 Pensées générées: ${metrics.metrics.thoughtsGenerated}`);
        console.log(`🎯 Efficacité: ${(metrics.metrics.efficiency * 100).toFixed(1)}%`);
        
        // Test 5: Test d'activation intensive (optionnel)
        console.log('\n📝 Test 5: Voulez-vous tester l\'activation intensive? (50% = 10M neurones)');
        console.log('⚠️  Cela peut consommer beaucoup de mémoire...');
        
        // Attendre un peu avant le test intensif
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log('\n🚀 Test d\'activation intensive...');
        await brain.activateNeurons(0.5, 30000, 'intensive_thinking');
        
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // Métriques après activation intensive
        const finalMetrics = brain.getCompleteMetrics();
        console.log('\n📊 === MÉTRIQUES APRÈS ACTIVATION INTENSIVE ===');
        console.log(`⚡ Neurones actifs: ${finalMetrics.state.active.toLocaleString()}`);
        console.log(`📊 Mémoire utilisée: ${finalMetrics.metrics.memoryUsage.toFixed(2)}%`);
        console.log(`💭 Pensées générées: ${finalMetrics.metrics.thoughtsGenerated}`);
        
        console.log('\n✅ Tests terminés avec succès !');
        console.log('🧠 Le cerveau continue de fonctionner en boucle Möbius...');
        
        // Laisser tourner un peu pour voir la boucle Möbius
        await new Promise(resolve => setTimeout(resolve, 15000));
        
        console.log('\n🛑 Arrêt du test');
        
    } catch (error) {
        console.error('❌ Erreur lors des tests:', error.message);
        console.error(error.stack);
    }
}

// Lancer le test
testPureBrain().catch(console.error);
